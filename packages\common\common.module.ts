import { Module } from '@nestjs/common'
// import { OssService } from './oss.service'
import { VersionService } from './version.service'
import { CacheKeyService } from './cacheKeyService'
import { TianyiyunOssService } from './tianyiyun-oss.service'
import { StatisticCommonService } from './statistic-common.service'
import { UserUtils } from './user-utils'

@Module({
  exports: [
    // OssService,
    VersionService,
    CacheKeyService,
    TianyiyunOssService,
    StatisticCommonService,
    UserUtils
  ],
  providers: [
    // OssService,
    VersionService,
    CacheKeyService,
    TianyiyunOssService,
    StatisticCommonService,
    UserUtils
  ]
})
export class CommonModule {}
