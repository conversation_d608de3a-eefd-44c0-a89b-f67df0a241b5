import { ApiProperty } from '@nestjs/swagger'
import { IsDateString, IsNumber, IsString, Min, IsOptional } from 'class-validator'
import { Transform } from 'class-transformer'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

/**
 * 修改团队过期时间请求DTO
 */
export class UpdateTeamExpirationRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '新的过期时间（ISO 8601格式）',
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsDateString()
  expiredAt: string
}

/**
 * 设置团队资源配额请求DTO
 */
export class SetTeamQuotaRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '账号数量限制',
    example: 50,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  accountCapacityLimit: number

  @ApiProperty({
    description: '流量配额（KB）',
    example: 1048576,
    minimum: 0
  })
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => parseInt(value))
  networkTraffic: number
}

/**
 * 团队过期时间更新响应DTO
 */
export class UpdateTeamExpirationResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '原过期时间',
    example: *************
  })
  oldExpiredAt: number

  @ApiProperty({
    description: '新过期时间',
    example: *************
  })
  newExpiredAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 账号冻结信息DTO
 */
export class FrozenAccountInfoDto {
  @ApiProperty({
    description: '账号ID',
    example: '507f1f77bcf86cd799439011'
  })
  accountId: string

  @ApiProperty({
    description: '平台账号名称',
    example: 'user123'
  })
  platformAccountName: string

  @ApiProperty({
    description: '平台名称',
    example: 'WeChat'
  })
  platformName: string

  @ApiProperty({
    description: '最后登录时间',
    example: *************
  })
  lastLoginTime: number
}

/**
 * 团队资源配额设置响应DTO
 */
export class SetTeamQuotaResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '账号数量限制',
    example: 50
  })
  accountCapacityLimit: number

  @ApiProperty({
    description: '新流量配额（KB）',
    example: 1048576
  })
  newNetworkTraffic: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 修改团队过期时间响应DTO
 */
export class UpdateTeamExpirationApiResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: UpdateTeamExpirationResponseDto
  })
  data: UpdateTeamExpirationResponseDto
}

/**
 * 设置团队资源配额响应DTO
 */
export class SetTeamQuotaApiResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: SetTeamQuotaResponseDto
  })
  data: SetTeamQuotaResponseDto
}
