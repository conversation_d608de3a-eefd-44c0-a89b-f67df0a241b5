import { ModelDefinition, MongooseModule, Prop, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

export enum LoginStatus {
  /// <summary>
  /// 未曾登录
  /// </summary>
  // Never = 0,

  /// <summary>
  /// 登录成功
  /// </summary>
  Succesed = 1,

  /// <summary>
  /// 登录过期
  /// </summary>
  Expired = 2,

  /// <summary>
  /// 登录失败
  /// </summary>
  // Failed = 3,

  /// <summary>
  /// 取消授权
  /// </summary>
  CancelAuth = 4
}

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformAccountEntity {
  @Prop({
    type: String,
    required: true
  })
  platformName: string

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  platformAvatar?: string

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  principalId: Types.ObjectId

  @Prop({
    type: String,
    required: true
  })
  platformAccountName: string

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  remark?: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  spaceId?: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  parentId?: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 平台类型
   */
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  platformType: number

  // 公众号服务类型ID
  @Prop({
    type: Number,
    required: false
  })
  serviceTypeId?: number

  // 公众号服务类型说明
  @Prop({
    type: String,
    required: false
  })
  serviceTypeName?: string

  // 1：正常,14：已注销,16：已封禁,18：已告警,19：已冻结
  @Prop({
    type: Number,
    required: false
  })
  accountStatus?: number

  // 主体名称
  @Prop({
    type: String,
    required: false
  })
  principalName?: string

  //公众号认证类型 -1未认证 0微信认证 1新浪微博认证 3已资质认证通过但还未通过名称认证 4已资质认证通过、还未通过名称认证，但通过了新浪微博认证
  @Prop({
    type: String,
    required: false
  })
  verifyTypeInfo?: string

  @Prop({
    type: Boolean,
    required: false,
    default: true
  })
  isRealNameVerified?: boolean

  /**
   * 微信公众号存了authorizer_refresh_token
   */
  @Prop({
    type: String,
    required: false
  })
  token: string

  /**
   * 开放平台账号保存了 access_token
   */
  @Prop({
    type: Types.Map,
    required: false
  })
  credentials?: unknown

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    required: false
  })
  localStorage?: string

  /**
   * 开放平台账号保存了账号 openId
   */
  @Prop({
    type: String,
    required: false
  })
  platformAuthorId?: string

  @Prop({
    type: String,
    required: false,
    default: null
  })
  wxid?: string

  @Prop({
    type: String,
    required: false,
    default: null
  })
  finderUsername?: string

  @Prop({
    type: [String],
    required: true
  })
  members: string[]

  @Prop({
    type: [String],
    required: false
  })
  groups?: string[]

  @Prop({
    type: Number,
    enum: LoginStatus,
    required: true,
    default: LoginStatus.Succesed
  })
  status: LoginStatus

  @Prop({
    type: String,
    required: false
  })
  statusChecksum?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: Boolean,
    default: false,
    required: false
  })
  isFreeze?: boolean

  /**
   * 代理地区
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  kuaidailiArea?: string

  @Prop({
    type: String,
    required: false,
    default: null
  })
  kuaidailiIp?: string

  /**
   * 第三方微信设备ID
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  deviceId?: string

  /**
   * 第三方微信uuid
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  uuid?: string

  @Prop({
    type: Date,
    required: false,
    default: null
  })
  loginStatusUpdatedAt?: Date

  /**
   * 云端最后检测时间戳
   */
  @Prop({
    type: Number,
    required: false
  })
  cloudCheckTime?: number

  /**
   * 其他平台类型浏览器显示颜色
   */
  @Prop({
    type: String,
    required: false
  })
  color?: string

  /**
   * 其他平台类型访问地址
   */
  @Prop({
    type: String,
    required: false
  })
  spaceUrl?: string

  /**
   * 账号占用点数
   */
  @Prop({
    type: Number,
    required: true,
    min: 1,
    max: Number.MAX_SAFE_INTEGER,
    default: 1
  })
  capacity: number
}

export const PlatformAccountSchema: ModelDefinition = {
  name: PlatformAccountEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountEntity)
}

export const PlatformAccountMongoose = MongooseModule.forFeature([PlatformAccountSchema])
