import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { MaterialTypeEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class MaterialLibraryEntity {
  /**
   * 素材类型
   */
  @Prop({
    type: String,
    enum: MaterialTypeEnum,
    required: true
  })
  type: MaterialTypeEnum

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 分组ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  groupId?: Types.ObjectId

  /**
   * 缩略图地址
   */
  @Prop({
    type: String,
    required: false
  })
  thumbPath: string

  /**
   * 素材地址
   */
  @Prop({
    type: String,
    required: false
  })
  filePath: string

  //宽度
  @Prop({
    type: Number,
    required: false
  })
  width: number

  //长度
  @Prop({
    type: Number,
    required: false
  })
  height: number

  //大小
  @Prop({
    type: Number,
    required: false
  })
  size: number

  //格式 video|image
  @Prop({
    type: String,
    required: false
  })
  format: string

  //格式 png|jpg|jpeg...
  @Prop({
    type: String,
    required: false
  })
  fileFormat: string

  //文件名称
  @Prop({
    type: String,
    required: false
  })
  fileName: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const MaterialLibrarySchema: ModelDefinition = {
  name: MaterialLibraryEntity.name,
  schema: SchemaFactory.createForClass(MaterialLibraryEntity)
}

export const MaterialLibraryMongoose = MongooseModule.forFeature([MaterialLibrarySchema])
