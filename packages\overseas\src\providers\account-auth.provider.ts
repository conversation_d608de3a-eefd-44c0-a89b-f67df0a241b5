import { OverseasContext, PlatformAccountInfo } from './types'

/**
 * 账号授权器
 * 各平台账号授权相关的服务需要实现此接口
 */
export abstract class AccountAuthProvider {
  /**
   * 各平台的注册token
   * @param name 状态
   */
  static register_token(name: string): string {
    return `${AccountAuthProvider.name}:${name}`.toLowerCase()
  }

  /**
   * 生成授权连接
   * @param state
   */
  abstract generateAuthorizationUrl(state: string): Promise<string>

  /**
   * 通过OAuth授权码交换平台账号信息
   *
   * @description 处理OAuth回调的code/state参数，向平台交换获取用户授权的账号信息。
   * 注意：部分平台会返回用户授权的多个关联账号。
   *
   * @param context 执行上下文（包含环境配置等）
   * @param code OAuth授权码（从回调URL获取）
   * @param state 防CSRF的安全参数
   *
   * @returns 授权通过的平台账号信息数组
   */
  abstract exchangeAuthCodeForAccounts(
    context: OverseasContext,
    code: string,
    state: string
  ): Promise<PlatformAccountInfo[]>
}
