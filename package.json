{"name": "yixiaoer-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build:socket": "nest build @yxr/socket", "build:gateway": "nest build @yxr/gateway", "build:admin": "nest build @yxr/admin", "build:overseavice": "nest build @yxr/overseavice", "start-admin:local": "cross-env NODE_ENV=dev nest start @yxr/admin --debug --watch", "start-admin:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-admin:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-admin:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/admin/ecosystem.config.js", "start-socket:local": "cross-env NODE_ENV=local nest start @yxr/socket --debug --watch", "start-socket:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-socket:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-socket:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/socket/ecosystem.config.js", "start-gateway:local": "cross-env NODE_ENV=dev nest start @yxr/gateway --debug --watch", "start-gateway:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-gateway:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-gateway:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/gateway/ecosystem.config.js", "start-overseavice:local": "cross-env NODE_ENV=local nest start @yxr/overseavice --debug --watch", "start-overseavice:dev": "cross-env NODE_ENV=dev NO_COLOR=1 pm2 start --no-daemon apps/overseavice/ecosystem.config.js", "start-overseavice:test": "cross-env NODE_ENV=test NO_COLOR=1 pm2 start --no-daemon apps/overseavice/ecosystem.config.js", "start-overseavice:prod": "cross-env NODE_ENV=prod NO_COLOR=1 pm2 start --no-daemon apps/overseavice/ecosystem.config.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@alicloud/captcha20230305": "1.1.3", "@alicloud/dysmsapi20170525": "^3.1.1", "@alicloud/openapi-client": "^0.4.13", "@alicloud/tea-util": "^1.4.10", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.3.1", "@fastify/static": "^7.0.4", "@grpc/grpc-js": "^1.13.1", "@nestjs/axios": "^4.0.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^10.4.15", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-express": "^10.4.15", "@nestjs/platform-fastify": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^7.4.2", "@nestjs/throttler": "^6.4.0", "@volcengine/openapi": "^1.30.1", "@yixiaoer/platform-service": "2.11.3", "ali-oss": "^6.22.0", "alipay-sdk": "^4.13.0", "aliyun-sdk": "^1.12.10", "axios": "^1.8.4", "bullmq": "^5.44.4", "cache-manager": "^5.7.6", "cache-manager-ioredis-yet": "^2.1.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-writer": "^1.6.0", "dayjs": "^1.11.13", "fast-xml-parser": "^4.5.3", "file-type": "16.5.4", "form-data": "^4.0.2", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "mongoose": "^8.13.0", "nanoid": "^3.3.11", "node-weixin-pay": "^0.5.1", "pako": "^2.1.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "sharp": "^0.33.5", "speakeasy": "^2.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.2.3", "@nestjs/testing": "^10.4.15", "@swc/cli": "^0.4.0", "@swc/core": "^1.11.13", "@swc/jest": "^0.2.37", "@types/ali-oss": "^6.16.11", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.17.27", "@types/pako": "2.0.3", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "cross-env": "^7.0.3", "cz-git": "^1.11.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.4", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "swc-loader": "^0.2.6", "ts-jest": "^29.3.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "webpack": "^5.98.0", "wechatify-sdk": "^1.0.20"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}