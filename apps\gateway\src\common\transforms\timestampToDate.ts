
// 自定义转换函数
export const timestampToDate = ({ value }) => {
  const timestamp = parseInt(value, 10);
  if (isNaN(timestamp)) {
    throw new Error('Invalid timestamp');
  }

  // 判断是毫秒级还是秒级时间戳
  const isMilliseconds = timestamp.toString().length > 10;
  const date = isMilliseconds ? new Date(timestamp) : new Date(timestamp * 1000);

  if (isNaN(date.getTime())) {
    throw new Error('Invalid timestamp');
  }

  return date;
};
