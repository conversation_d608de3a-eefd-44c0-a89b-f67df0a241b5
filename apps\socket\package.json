{"name": "@yxr/socket", "version": "1.0.0", "description": "", "main": "index.js", "keywords": [], "author": "", "license": "ISC", "scripts": {"typecheck": "tsc --noEmit -p tsconfig.json"}, "dependencies": {"ioredis": "^4.6.13", "socket.io": "^4.7.5", "@yxr/proto": "workspace:*", "@yxr/redis": "workspace:*", "@yxr/utils": "workspace:*", "@yxr/mongo": "workspace:*", "@socket.io/redis-adapter": "^8.3.0", "@nestjs/platform-socket.io": "^10.3.7", "@nestjs/websockets": "^10.3.7"}}