import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class AdminEntity {
  @Prop({
    type: String,
    required: true,
    unique: true
  })
  username: string

  @Prop({
    type: String,
    required: true,
    unique: false
  })
  name: string

  @Prop({
    type: String,
    required: true
  })
  password: string

  /**
   * 角色: 0超管 1管理员 2客服
   */
  @Prop({
    type: Number,
    required: true
  })
  role: number

  /**
   * 客服二维码
   */
  @Prop({
    type: String,
    required: false
  })
  qrCode: string

  /**
   * 客服分配状态 0不可分配 1可以分配
   */
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  status: number

  @Prop({
    type: String,
    required: true
  })
  salt: string

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  mfaSecret?: string

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  secretIv?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const AdminSchema: ModelDefinition = {
  name: AdminEntity.name,
  schema: SchemaFactory.createForClass(AdminEntity)
}

export const AdminMongoose = MongooseModule.forFeature([AdminSchema])
