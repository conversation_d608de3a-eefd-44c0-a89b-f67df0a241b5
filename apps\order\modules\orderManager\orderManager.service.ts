import { ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, Model, Types } from 'mongoose'
import {
  ContractEntity,
  InterestEntity,
  MemberEntity,
  OrderEntity,
  PlatformAccountEntity,
  RefundEntity,
  TeamEntity,
  TeamExpiredLogEntity,
  UserEntity
} from '@yxr/mongo'
import {
  OrderPriceResponseDto,
  PostCalculateOrderPriceRequestDTO,
  PostCreateOrderRequestDTO,
  PostRefundRequestDTO,
  PostRenewOrderRequestDTO,
  PostUpgradeOrderRequestDTO
} from './orderManager.dto'
import {
  MemberStatusEnum,
  OrderSource,
  OrderStatus,
  OrderType,
  PayType,
  SalesType,
  StatisticCommonService,
  TeamFeatures,
  TeamRoleNames
} from '@yxr/common'
import { VIPOften } from './constant'
import { customAlphabet } from 'nanoid'
import dayjs from 'dayjs'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { AlipaySdk } from 'alipay-sdk'
import WeixinPay from 'node-weixin-pay'

@Injectable()
export class OrderManagerService {
  logger = new Logger('order')

  private firstMonthOrderPrice = 49 //首月权益包单价
  private yearOrderPrice = 490 //年付权益包单价

  constructor(
    @InjectConnection() private readonly connection: Connection,
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(InterestEntity.name) private interestModel: Model<InterestEntity>,
    @InjectModel(ContractEntity.name) private ContractModel: Model<ContractEntity>,
    @InjectModel(RefundEntity.name) private refundModel: Model<RefundEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(TeamExpiredLogEntity.name)
    private teamExpiredLogModel: Model<TeamExpiredLogEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  nanoid = customAlphabet('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 8)

  generateNo(): string {
    const now = new Date()
    const year = now.getFullYear().toString()
    const month = (now.getMonth() + 1).toString().padStart(2, '0')
    const day = now.getDate().toString().padStart(2, '0')
    const hours = now.getHours().toString().padStart(2, '0')
    const minutes = now.getMinutes().toString().padStart(2, '0')

    return `${year}${month}${day}${hours}${minutes}${this.nanoid()}`
  }

  async calculateOrderPrice({
    userId,
    teamId,
    orderType,
    interestId,
    interestCount,
    month,
    days
  }: PostCalculateOrderPriceRequestDTO): Promise<OrderPriceResponseDto> {
    if (month === 0 && days === 0) {
      throw new ForbiddenException('参数错误')
    }
    const teamMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId),
      status: MemberStatusEnum.Joined
    })
    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }
    const interest = await this.interestModel.findById(new Types.ObjectId(interestId))
    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    let orderAmount: number
    let discountAmount: number = 0
    let tipsCn = ''
    let tips = ''
    let expiredTime = 0 // 过期时间
    const dayPrice =
      orderType === OrderType.Upgrade
        ? ((interestCount - team.interestCount) * interest.price) / 30
        : (interestCount * interest.price) / 30

    switch (orderType) {
      case OrderType.Create: {
        if (team.isVip && team.expiredAt && team.expiredAt.getTime() > Date.now()) {
          throw new ForbiddenException('VIP未过期无需开通VIP')
        }

        if (month > 0) {
          const vipOften = VIPOften.find((item) => item.mount === month)
          if (!vipOften) {
            throw new NotFoundException('权益月份不存在')
          }
          orderAmount = Math.trunc(interest.price * interestCount * vipOften.mount)
          expiredTime = dayjs(Date.now())
            .add(vipOften.mount + vipOften.present, 'month')
            .valueOf()

          switch (vipOften.mount) {
            case 1:
              if (team.salesType === SalesType.NotBuy) {
                //首月购买
                discountAmount = orderAmount - Math.trunc(this.firstMonthOrderPrice * interestCount)
              }
              break
            case 12:
              //年付优惠
              discountAmount = orderAmount - Math.trunc(this.yearOrderPrice * interestCount)
              break
          }
        } else {
          orderAmount = Math.trunc(dayPrice * days)
          expiredTime = dayjs(Date.now()).add(days, 'day').valueOf()
        }
        break
      }

      case OrderType.Upgrade: {
        if (
          !team.expiredAt ||
          (!team.isVip && team.expiredAt && team.expiredAt.getTime() < Date.now())
        ) {
          throw new ForbiddenException('您的VIP已过期')
        }
        const remainingDay = StatisticCommonService.remainingDay(team.expiredAt)
        const needPayInterestCount = interestCount - team.interestCount
        const needPayAmount = Math.trunc(
          Math.floor(remainingDay / 365) * this.yearOrderPrice * needPayInterestCount +
            (remainingDay % 365) * dayPrice
        )
        // 升级需要的费用, 需要升级到的数量-当前VIP的权益包的数量 * 权益包单价 / 30天 * 剩余天数
        orderAmount = Math.trunc(dayPrice * remainingDay)
        discountAmount = orderAmount - needPayAmount

        tipsCn = '升级权益包金额/30*剩余时长'
        tips = `${interest.price}*${interestCount - team.interestCount}/30*${remainingDay}=￥${orderAmount}`
        expiredTime = team.expiredAt.getTime()
        break
      }

      case OrderType.Renew: {
        if (!team.expiredAt || (!team.isVip && team.expiredAt.getTime() < Date.now())) {
          throw new ForbiddenException('您的VIP已过期')
        }

        if (month) {
          const vipOften = VIPOften.find((item) => item.mount === month)
          if (!vipOften) {
            throw new NotFoundException('权益月份不存在')
          }
          orderAmount = Math.trunc(interest.price * vipOften.mount * team.interestCount)
          expiredTime = team?.expiredAt
            ? dayjs(team?.expiredAt)
                .add(vipOften.mount + vipOften.present, 'month')
                .valueOf()
            : 0

          switch (vipOften.mount) {
            case 1:
              if (team.salesType === SalesType.NotBuy) {
                //首月购买
                discountAmount =
                  orderAmount - Math.trunc(this.firstMonthOrderPrice * team.interestCount)
              }
              break
            case 12:
              //年付优惠
              discountAmount = orderAmount - Math.trunc(this.yearOrderPrice * team.interestCount)
              break
          }
        } else {
          orderAmount = Math.trunc(dayPrice * days)
          expiredTime = team?.expiredAt ? dayjs(team?.expiredAt).add(days, 'day').valueOf() : 0
        }

        break
      }

      case OrderType.Gift: {
        if (!team.expiredAt || (!team.isVip && team.expiredAt.getTime() < Date.now())) {
          throw new ForbiddenException('您的VIP已过期')
        }
        if (days <= 0) {
          throw new ForbiddenException('赠送天数错误')
        }
        orderAmount = 0
        expiredTime = dayjs(team.expiredAt).add(days, 'day').valueOf()
      }
    }

    return {
      orderAmount,
      discountAmount,
      expiredTime,
      tips,
      tipsCn
    }
  }

  async createOrder({
    teamId,
    userId,
    interestCount,
    interestId,
    month,
    days,
    isCorporateTransfer,
    creatorId,
    creatorName,
    remark,
    payAmount,
    isPay,
    applicationId,
    openPlatformUserId
  }: PostCreateOrderRequestDTO) {
    if (month <= 0 && days <= 0) {
      throw new ForbiddenException('参数不合法')
    }

    const teamMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId),
      status: MemberStatusEnum.Joined
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const interest = await this.interestModel.findById(new Types.ObjectId(interestId))

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    let vipOften: {
      mount: number
      present: number
    } = { mount: 0, present: 0 }
    if (month > 0) {
      vipOften = VIPOften.find((item) => item.mount === month)

      if (!vipOften) {
        throw new NotFoundException('权益月份不存在')
      }
    }
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }
    if (team.isVip && team.expiredAt && team.expiredAt.getTime() > Date.now()) {
      throw new ForbiddenException('VIP未过期无需开通VIP')
    }

    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 60 * 60 * 1000)
    const orderNo = this.generateNo()
    const dayPrice = (interestCount * interest.price) / 30
    const price = month > 0 ? interest.price * vipOften.mount * interestCount : dayPrice * days
    let payableAmount = price
    switch (vipOften.mount) {
      case 1:
        if (team.salesType === SalesType.NotBuy) {
          //首月购买
          payableAmount = Math.trunc(this.firstMonthOrderPrice * interestCount)
        }
        break
      case 12:
        //年付优惠
        payableAmount = Math.trunc(this.yearOrderPrice * interestCount)
        break
    }

    if (payAmount >= 0 && creatorId) {
      // 后台管理系统创建订单
      payableAmount = payAmount
    }

    const customerId = team.customerId ?? (await this.getTeamCustomerId(teamId))
    const user = await this.userModel
      .findById(new Types.ObjectId(userId))
      .select('phone channelCode')
    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    let weixinUrl = ''
    let alipayUrl = ''

    if (isPay && payableAmount > 0) {
      alipayUrl = await this.alipayTradePagePay(
        orderNo,
        '蚁小二lite权益开通',
        Math.trunc(payableAmount).toString()
      )
      weixinUrl = await this.wechatNativePay(
        Math.trunc(payableAmount),
        '蚁小二lite权益开通',
        orderNo,
        '127.0.0.1'
      )
    }

    await this.orderModel.create({
      orderNo,
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId),
      orderSource: creatorId ? OrderSource.System : OrderSource.Online,
      totalAmount: Math.trunc(price),
      payableAmount: Math.trunc(payableAmount),
      createdAt: currentDate,
      expiredAt: towHours,
      orderStatus: OrderStatus.Pending,
      vipMonth: vipOften.mount,
      freeMonth: vipOften.present,
      days: days,
      interestId: new Types.ObjectId(interestId),
      interestCount,
      creatorId: creatorId ? new Types.ObjectId(creatorId) : null,
      creatorName,
      phone: user.phone,
      remark,
      weixinUrl: weixinUrl,
      sourceAppId: applicationId,
      openPlatformUserId,
      alipayUrl,
      customerId: new Types.ObjectId(customerId),
      channelCode: user?.channelCode ?? null,
      salesType: salesType,
      orderType: OrderType.Create,
      payType: isCorporateTransfer ? PayType.CorporateTransfer : PayType.Alipay
    })

    return orderNo
  }

  /**
   * 升级订单
   */
  async upgradeOrder({
    teamId,
    userId,
    interestCount,
    interestId,
    isCorporateTransfer,
    creatorId,
    creatorName,
    remark,
    payAmount,
    isPay,
    applicationId,
    openPlatformUserId
  }: PostUpgradeOrderRequestDTO) {
    const teamMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId),
      status: MemberStatusEnum.Joined
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }

    const interest = await this.interestModel.findById(new Types.ObjectId(interestId))

    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }

    const team = await this.teamModel.findById(new Types.ObjectId(teamId))

    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    if (
      !team.expiredAt ||
      (!team.isVip && team.expiredAt && team.expiredAt.getTime() < Date.now())
    ) {
      throw new ForbiddenException('您的VIP已过期')
    }

    if (team.interestCount >= interestCount) {
      throw new ForbiddenException('权益包数量必须大于升级前的数量')
    }

    const dayPrice = ((interestCount - team.interestCount) * interest.price) / 30
    const remainingDay = StatisticCommonService.remainingDay(team.expiredAt)
    // 升级需要的费用, 需要升级到的数量-当前VIP的权益包的数量 * 权益包单价 / 30天 * 剩余天数
    const needPayInterestCount = interestCount - team.interestCount
    const price = Math.trunc(
      Math.floor(remainingDay / 365) * this.yearOrderPrice * needPayInterestCount +
        (remainingDay % 365) * dayPrice
    )

    let payableAmount = price
    if (payAmount >= 0 && creatorId) {
      // 后台管理系统创建订单
      payableAmount = payAmount
    }

    const orderNo = this.generateNo()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 60 * 60 * 1000)

    const customerId = team.customerId ?? (await this.getTeamCustomerId(teamId))
    const user = await this.userModel
      .findById(new Types.ObjectId(userId))
      .select('phone channelCode')
    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    let weixinUrl = ''
    let alipayUrl = ''

    if (isPay && payableAmount > 0) {
      alipayUrl = await this.alipayTradePagePay(
        orderNo,
        '蚁小二lite权益升级',
        Math.trunc(payableAmount).toString()
      )
      weixinUrl = await this.wechatNativePay(
        Math.trunc(payableAmount),
        '蚁小二lite权益升级',
        orderNo,
        '127.0.0.1'
      )
    }

    await this.orderModel.create({
      orderNo,
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId),
      orderSource: creatorId ? OrderSource.System : OrderSource.Online,
      totalAmount: Math.trunc(price),
      payableAmount: Math.trunc(payableAmount),
      createdAt: currentDate,
      expiredAt: towHours,
      orderStatus: OrderStatus.Pending,
      vipMonth: 0,
      freeMonth: 0,
      interestId: new Types.ObjectId(interestId),
      interestCount,
      creatorId: creatorId ? new Types.ObjectId(creatorId) : null,
      creatorName,
      remark,
      phone: user.phone,
      weixinUrl,
      alipayUrl,
      sourceAppId: applicationId,
      openPlatformUserId,
      customerId: new Types.ObjectId(customerId),
      channelCode: user?.channelCode ?? null,
      salesType: salesType,
      payType: isCorporateTransfer ? PayType.CorporateTransfer : PayType.Other,
      orderType: OrderType.Upgrade
    })

    return orderNo
  }

  /**
   * 续费订单
   */
  async renewOrder({
    teamId,
    userId,
    month,
    days,
    interestId,
    isCorporateTransfer,
    creatorId,
    creatorName,
    remark,
    payAmount,
    isPay,
    applicationId,
    openPlatformUserId
  }: PostRenewOrderRequestDTO) {
    if (month <= 0 && days <= 0) {
      throw new ForbiddenException('参数不合法')
    }

    const teamMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId),
      status: MemberStatusEnum.Joined
    })
    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }
    if (
      !team.expiredAt ||
      (!team.isVip && team.expiredAt && team.expiredAt.getTime() < Date.now())
    ) {
      throw new ForbiddenException('您的VIP已过期')
    }

    let vipOften: {
      mount: number
      present: number
    } = { mount: 0, present: 0 }
    if (month > 0) {
      vipOften = VIPOften.find((item) => item.mount === month)

      if (!vipOften) {
        throw new NotFoundException('权益月份不存在')
      }
    }
    const interest = await this.interestModel.findById(new Types.ObjectId(interestId))
    if (!interest) {
      throw new NotFoundException('权益信息不存在')
    }
    const dayPrice = (team.interestCount * interest.price) / 30
    const price = month > 0 ? interest.price * vipOften.mount * team.interestCount : dayPrice * days
    let payableAmount = price
    switch (vipOften.mount) {
      case 1:
        if (team.salesType === SalesType.NotBuy) {
          //首月购买
          payableAmount = Math.trunc(this.firstMonthOrderPrice * team.interestCount)
        }
        break
      case 12:
        //年付优惠
        payableAmount = Math.trunc(this.yearOrderPrice * team.interestCount)
        break
    }
    if (payAmount >= 0 && creatorId) {
      // 后台管理系统创建订单
      payableAmount = payAmount
    }

    const orderNo = this.generateNo()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 60 * 60 * 1000)

    const customerId = team.customerId ?? (await this.getTeamCustomerId(teamId))
    const user = await this.userModel
      .findById(new Types.ObjectId(userId))
      .select('phone channelCode')
    const salesType = await this.getTeamOrderSalesTypeCheck(teamId, isPay)

    let weixinUrl = ''
    let alipayUrl = ''

    if (isPay && payableAmount > 0) {
      alipayUrl = await this.alipayTradePagePay(
        orderNo,
        '蚁小二lite权益续费',
        Math.trunc(payableAmount).toString()
      )
      weixinUrl = await this.wechatNativePay(
        Math.trunc(payableAmount),
        '蚁小二lite权益续费',
        orderNo,
        '127.0.0.1'
      )
    }

    await this.orderModel.create({
      orderNo,
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId),
      orderSource: creatorId ? OrderSource.System : OrderSource.Online,
      totalAmount: Math.trunc(price),
      payableAmount: Math.trunc(payableAmount),
      createdAt: currentDate,
      expiredAt: towHours,
      orderStatus: OrderStatus.Pending,
      vipMonth: vipOften.mount,
      freeMonth: vipOften.present,
      days: days,
      interestId: new Types.ObjectId(interestId),
      interestCount: team.interestCount,
      creatorId: creatorId ? new Types.ObjectId(creatorId) : null,
      creatorName,
      remark,
      weixinUrl,
      alipayUrl,
      sourceAppId: applicationId,
      openPlatformUserId,
      customerId: new Types.ObjectId(customerId),
      channelCode: user?.channelCode ?? null,
      salesType: salesType,
      phone: user.phone,
      orderType: OrderType.Renew,
      payType: isCorporateTransfer ? PayType.CorporateTransfer : PayType.Other
    })

    return orderNo
  }

  async handleCompletedOrder(orderNo: string) {
    try {
      const order = await this.orderModel.findOne({
        orderNo: orderNo
      })

      if (!order) {
        throw new NotFoundException('订单不存在')
      }
      if (order.orderStatus !== OrderStatus.Paid) {
        throw new ForbiddenException('订单未完成')
      }

      const insterest = await this.interestModel.findById(new Types.ObjectId(order.interestId))
      if (!insterest) {
        throw new NotFoundException('权益包信息不存在')
      }

      // 修改VIP相关信息
      const appPublish = insterest.appPublish
      const memberCountLimit = order.interestCount * insterest.memberCountLimit
      const accountCountLimit = order.interestCount * insterest.accountCountLimit // 变更为账号点数
      const accountCapacityLimit = order.interestCount * insterest.accountCapacityLimit

      const capacity = order.interestCount * insterest.capacityLimit
      const networkTraffic = order.interestCount * insterest.networkTrafficLimit

      const team = await this.teamModel.findById(new Types.ObjectId(order.teamId))
      if (!team) {
        throw new NotFoundException('团队信息不存在')
      }

      let startTime = order.payTime
      let expiredAt = new Date()
      let data: ContractEntity[] = []

      switch (order.orderType) {
        case OrderType.Create:
          if (order.freeMonth > 0) {
            expiredAt = dayjs(startTime).add(order.freeMonth, 'month').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime: startTime,
              endTime: expiredAt,
              payAmount: 0,
              isFree: true, //只有10+2的免费权益包
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount
            })
          }
          if (order.vipMonth > 0) {
            startTime = order.freeMonth > 0 ? expiredAt : startTime
            expiredAt = dayjs(startTime).add(order.vipMonth, 'month').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime,
              endTime: expiredAt,
              payAmount: order.payAmount,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount
            })
          }
          if (order.days > 0) {
            expiredAt = dayjs(startTime).add(order.days, 'day').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime: startTime,
              endTime: expiredAt,
              payAmount: 0,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount
            })
          }
          break
        case OrderType.Upgrade:
          expiredAt = team.expiredAt
          data.push({
            orderId: order.id,
            orderNo: order.orderNo,
            startTime,
            endTime: team.expiredAt,
            payAmount: order.payAmount,
            teamId: order.teamId,
            interestId: order.interestId,
            interestCount: order.interestCount,
            isFree: false
          })

          break
        case OrderType.Renew:
          startTime = team.expiredAt
          if (order.freeMonth > 0) {
            expiredAt = dayjs(startTime).add(order.freeMonth, 'month').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime: startTime,
              endTime: expiredAt,
              payAmount: 0,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount,
              isFree: true
            })
            startTime = expiredAt
          }
          if (order.vipMonth > 0) {
            expiredAt = dayjs(startTime).add(order.vipMonth, 'month').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime,
              endTime: expiredAt,
              payAmount: order.payAmount,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount
            })
          }
          if (order.days > 0) {
            expiredAt = dayjs(startTime).add(order.days, 'day').toDate()
            data.push({
              orderId: order.id,
              orderNo: order.orderNo,
              startTime,
              endTime: expiredAt,
              payAmount: order.payAmount,
              teamId: order.teamId,
              interestId: order.interestId,
              interestCount: order.interestCount
            })
          }
          break
        case OrderType.Gift:
          //绑定渠道，赠送时非vip也可以
          if (team.expiredAt) {
            startTime = team.expiredAt.getTime() > Date.now() ? team.expiredAt : new Date()
            expiredAt = dayjs(team.expiredAt).add(order.days, 'day').toDate()
          } else {
            expiredAt = dayjs(startTime).add(order.days, 'day').toDate()
          }
          data.push({
            orderId: order.id,
            orderNo: order.orderNo,
            startTime,
            endTime: expiredAt,
            payAmount: 0,
            teamId: order.teamId,
            interestId: order.interestId,
            interestCount: order.interestCount
          })
          break
      }

      const updateTeamData: any = {
        memberCountLimit: memberCountLimit,
        accountCountLimit: accountCountLimit, // 变更为账号点数
        accountCapacityLimit: accountCapacityLimit,
        appPublish: appPublish,
        capacity: capacity,
        isVip: true,
        expiredAt: expiredAt,
        interestCount: order.interestCount,
        networkTraffic: networkTraffic
      }
      if (team?.salesType === SalesType.FirstBuy && order.salesType === SalesType.ReBuy) {
        updateTeamData.salesType = SalesType.ReBuy
      } else if (team?.salesType === SalesType.NotBuy || !team?.salesType) {
        updateTeamData.salesType = order.salesType
      }
      await this.teamModel.updateOne(
        { _id: new Types.ObjectId(order.teamId) },
        {
          $set: updateTeamData
        }
      )

      const contract = await this.ContractModel.find({
        orderNo: orderNo
      })

      if (contract.length <= 0) {
        //没有订单合同才新增
        await this.ContractModel.create(data)
      }
    } catch (error) {
      throw new ForbiddenException('订单处理错误:' + error.message)
    }
  }

  async orderListByRefund(teamId: string) {
    // 已经使用过的合同
    const nowTime = new Date()
    const useContract = await this.ContractModel.find({
      teamId: new Types.ObjectId(teamId),
      isRefund: false,
      startTime: {
        $lt: nowTime
      },
      endTime: {
        $gt: nowTime
      }
    })
    const orderList = []

    useContract.forEach((item) => {
      const orderDays = dayjs(item.endTime).diff(item.startTime, 'day') + 1
      const days = dayjs(item.endTime).diff(dayjs(), 'day') + 1

      if (orderDays > 0 && days > 0 && orderDays >= days) {
        const amountByDay = item.payAmount / orderDays

        orderList.push({
          orderNo: item.orderNo,
          refundAmount: item.payAmount,
          actualRefundAmount: Math.trunc(amountByDay * days)
        })
      }
    })

    // 还未使用过的订单
    const unUserOrder = await this.ContractModel.find({
      teamId: new Types.ObjectId(teamId),
      isRefund: false,
      startTime: {
        $gt: new Date()
      }
    })

    unUserOrder.forEach((item) => {
      orderList.push({
        orderNo: item.orderNo,
        refundAmount: item.payAmount,
        actualRefundAmount: item.payAmount
      })
    })

    return orderList
  }

  /**
   * 订单退费
   */
  async refundOrder(
    username: string,
    { teamId, actualRefundAmount, remark }: PostRefundRequestDTO
  ) {
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))

    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    const refundOrderList = await this.orderListByRefund(teamId)

    if (refundOrderList && refundOrderList.length <= 0) {
      throw new ForbiddenException('当前团队没有订单可退')
    }

    const refundTotal = refundOrderList.reduce(
      (accumulator, current) => accumulator + current.actualRefundAmount,
      0
    )

    // 已经使用过的订单
    await this.ContractModel.updateMany(
      {
        teamId: new Types.ObjectId(teamId),
        startTime: {
          $lt: new Date()
        },
        endTime: {
          $gt: new Date()
        }
      },
      {
        isRefund: true
      }
    )

    // 还未使用过的订单
    await this.ContractModel.updateMany(
      {
        teamId: new Types.ObjectId(teamId),
        startTime: {
          $gt: new Date()
        }
      },
      {
        isRefund: true
      }
    )

    const refundNo = this.generateNo()

    //开启数据事务
    const dbTransaction = await this.connection.startSession()
    dbTransaction.startTransaction()

    try {
      await this.refundModel.create([
        {
          refundNo,
          teamId: new Types.ObjectId(teamId),
          refundableAmount: refundTotal,
          actualAmount: actualRefundAmount,
          username: username,
          remark,
          orderInfo: refundOrderList
        }
      ])

      await this.teamExpiredLogModel.create({
        teamId: new Types.ObjectId(teamId),
        expiredAt: team.expiredAt,
        customerId: new Types.ObjectId(team.customerId)
      })
      team.accountCountLimit = TeamFeatures.DefaultAccountCountLimit // 变更为账号点数
      team.accountCapacityLimit = TeamFeatures.DefaultAccountCapacityLimit
      team.memberCountLimit = TeamFeatures.DefaultMemberCountLimit
      team.isVip = false
      team.expiredAt = new Date()
      team.appPublish = false
      team.interestCount = 0
      team.capacity = 0
      team.accountCapacity = 0
      team.networkTraffic = 0
      team.useNetworkTraffic = 0

      await team.save()

      // 冻结账号, 清零账号点数
      await this.platformAccountModel.updateMany(
        { teamId: new Types.ObjectId(teamId) },
        { $set: { isFreeze: true, capacity: 0 } }
      )

      // 冻结成员
      await this.memberModel.updateMany(
        { teamId: new Types.ObjectId(teamId), roles: { $nin: [TeamRoleNames.MASTER] } },
        { $set: { isFreeze: true } }
      )

      await dbTransaction.commitTransaction()
    } catch (error) {
      await dbTransaction.abortTransaction()
      throw new ForbiddenException('赠送订单写入失败, 请稍后再试' + error.message)
    } finally {
      await dbTransaction.endSession()
    }
  }

  async alipayTradePagePay(orderNo: string, subject: string, totalAmount: string) {
    const { alipay } = this.configService.get<RootConfigMap['app']>('app')

    const alipaySdk = new AlipaySdk({
      // 设置应用 ID
      appId: alipay.appId,
      // 设置应用私钥
      privateKey: alipay.privateKey,
      // 设置支付宝公钥
      alipayPublicKey: alipay.alipayPublicKey,
      // 密钥类型，请与生成的密钥格式保持一致，参考平台配置一节
      keyType: 'PKCS1',

      gateway: 'https://openapi-sandbox.dl.alipaydev.com/gateway.do'
      // 设置网关地址，默认是 https://openapi.alipay.com
      // endpoint: 'https://openapi.alipay.com',
    })

    const result = await alipaySdk.curl('POST', '/v3/alipay/trade/precreate', {
      body: {
        out_trade_no: orderNo,
        subject,
        total_amount: totalAmount,
        notify_url: 'https://api-v2.yixiaoer.cn/api/payment-callback/ali-pay-back',
        passback_params: 'lite-' + process.env.NODE_ENV
      }
    })
    if (result.responseHttpStatus !== 200) {
      throw new ForbiddenException('支付宝生成二维码失败')
    }

    return result.data.qr_code
  }

  async wechatNativePay(
    totalAmount: number,
    subject: string,
    orderNo: string,
    spbillCreateIp: string
  ): Promise<string> {
    const { wechatPayInfo } = this.configService.get<RootConfigMap['app']>('app')

    try {
      const unifiedOrderData = {
        body: subject,
        out_trade_no: orderNo,
        total_fee: Math.round(totalAmount * 100), // 单位为分
        trade_type: 'NATIVE',
        spbill_create_ip: spbillCreateIp,
        nonce_str: this.generateNo(),
        attach: 'lite-' + process.env.NODE_ENV,
        notify_url: 'https://api-v2.yixiaoer.cn/api/payment-callback/wx-pay-back'
      }

      return new Promise((resolve, reject) => {
        WeixinPay.api.order.unified(
          {
            app: {
              id: wechatPayInfo.appId,
              secret: wechatPayInfo.appSecret
            },
            merchant: {
              id: wechatPayInfo.mchId,
              key: wechatPayInfo.key
            }
          },
          unifiedOrderData,
          function (error, data) {
            if (!error) {
              resolve(data.code_url) // 成功时将code_url通过resolve传递出去
            } else {
              this.logger.log('生成微信二维码失败', error)
              reject(new Error('生成微信二维码失败')) // 失败时通过reject抛出错误
            }
          }
        )
      })
    } catch (error) {
      this.logger.log(error)
      throw new ForbiddenException('生成微信二维码失败', error.message)
    }
  }

  /**
   * 绑定渠道赠送
   * @param teamId
   * @param userId
   * @param channelCode
   * @param giftDays
   * @param remark
   * @returns
   */
  async channelGiftOrder(
    teamId: string,
    userId: string,
    channelCode: string,
    giftDays: number = 0,
    remark: string
  ) {
    if (giftDays <= 0) {
      return
    }
    const teamMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId),
      status: MemberStatusEnum.Joined
    })

    if (!teamMember) {
      throw new NotFoundException('团队不存在')
    }
    const interest = await this.interestModel.findOne()
    if (!interest) {
      throw new NotFoundException('渠道兑换失败，请联系客服')
    }
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    const interestCount = team?.interestCount > 0 ? team?.interestCount : 1
    const price = ((interest.price * interestCount) / 30) * giftDays
    const orderNo = this.generateNo()
    const currentDate = new Date()
    const towHours = new Date(currentDate.getTime() + 60 * 60 * 1000)
    const customerId = team.customerId ?? (await this.getTeamCustomerId(teamId))
    const user = await this.userModel
      .findById(new Types.ObjectId(userId))
      .select('phone channelCode')
    await this.orderModel.create([
      {
        orderNo,
        userId: new Types.ObjectId(userId),
        teamId: new Types.ObjectId(teamId),
        orderSource: OrderSource.Online,
        totalAmount: Math.trunc(price),
        payableAmount: 0,
        createdAt: currentDate,
        expiredAt: towHours,
        orderStatus: OrderStatus.Paid,
        days: giftDays,
        interestId: new Types.ObjectId(interest._id),
        interestCount: interestCount,
        remark: remark,
        customerId: new Types.ObjectId(customerId),
        channelCode: channelCode,
        phone: user.phone,
        payType: PayType.Other,
        orderType: OrderType.Gift,
        salesType: SalesType.NotBuy,
        payTime: new Date()
      }
    ])

    return orderNo
  }

  /**
   * 校验团队的销售类型
   * @param teamId
   * @param payableAmount
   * @returns
   */
  async getTeamOrderSalesTypeCheck(teamId: string, isPay: boolean): Promise<number> {
    const orderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Paid,
      payAmount: { $gt: 0 }
    })

    if (isPay) {
      if (orderCount == 0) {
        return SalesType.FirstBuy
      }
      return SalesType.ReBuy
    }
    return SalesType.NotBuy
  }

  /**
   * 获取团队创建人客服id - 补偿方案team表没有customerId字段时使用
   * @param teamId
   * @returns
   */
  async getTeamCustomerId(teamId: string): Promise<string> {
    const masterMember = await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })
      .select('userId')

    const user = await this.userModel
      .findById(new Types.ObjectId(masterMember.userId))
      .select('customerId _id phone')

    return user?.customerId ? user?.customerId.toString() : null
  }
}
