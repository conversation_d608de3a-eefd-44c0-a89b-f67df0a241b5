import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { PublishChannel, TaskSetStatusEnum, UploadStatusEnum } from '@yxr/common'

/**
 * 任务集合
 */
@Schema({
  timestamps: true,
  optimisticConcurrency: true,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class TaskSetEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  //发布人手机号
  @Prop({
    type: String,
    required: false
  })
  phone: string

  //发布人昵称
  @Prop({
    type: String,
    required: false
  })
  nickName: string

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 平台名称
   */
  @Prop({
    type: [String],
    required: true
  })
  platforms: string[]

  @Prop({
    type: String,
    required: false
  })
  cover: string

  /**
   * 任务集身份ID，任务表绑定的父级ID
   */
  @Prop({
    type: String,
    required: true
  })
  taskIdentityId: string

  @Prop({
    type: String,
    index: true,
    default: null
  })
  publishType: string

  @Prop({
    type: String,
    enum: {
      values: Object.values(TaskSetStatusEnum),
      message: '{VALUE} is not a valid state'
    },
    default: TaskSetStatusEnum.Publishing,
    required: true
  })
  taskSetStatus: TaskSetStatusEnum

  @Prop({
    type: String,
    enum: {
      values: Object.values(UploadStatusEnum),
      message: '{VALUE} is not a valid state'
    },
    default: UploadStatusEnum.Waiting,
    required: true
  })
  uploadStatus: UploadStatusEnum

  @Prop({
    type: String
  })
  desc: string

  @Prop({
    type: Types.Map
  })
  descRich: unknown

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isAppContent: boolean

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isWeiXinContent: boolean

  @Prop({
    type: String,
    default: null
  })
  progressToken?: string

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isDeleted: boolean

  /**
   * 是否定时发布
   */
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  isTimed?: number

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDraft?: boolean

  @Prop({
    type: String,
    enum: PublishChannel,
    default: PublishChannel.local
  })
  publishChannel: PublishChannel

  @Prop({
    type: Types.Map
  })
  publishArgs: unknown

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: String,
    required: false,
    default: null
  })
  progress?: string
}

export const TaskSetSchema: ModelDefinition = {
  name: TaskSetEntity.name,
  schema: SchemaFactory.createForClass(TaskSetEntity)
}

export const TaskSetMongoose = MongooseModule.forFeature([TaskSetSchema])
