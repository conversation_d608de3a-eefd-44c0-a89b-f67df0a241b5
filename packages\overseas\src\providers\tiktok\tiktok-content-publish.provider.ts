import { Injectable, Logger } from '@nestjs/common'
import { ContentPublishProvider } from '../content-publish.provider'
import {
  OverseasContext,
  PublishTaskData,
  PublishResult,
  PublishContentData,
  PublishContentType,
  PublishTaskStatus
} from '../types'
import { Tik<PERSON><PERSON><PERSON> } from './tiktok-api'

@Injectable()
export class TiktokContentPublishProvider extends ContentPublishProvider {
  private readonly logger = new Logger(TiktokContentPublishProvider.name)

  constructor(private readonly tiktokApi: Tiktok<PERSON><PERSON>) {
    super()
  }

  /**
   * 验证发布内容是否符合TikTok要求
   */
  async validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    // TODO: 实现内容校验

    // // 检查内容类型支持
    // const supportedTypes = this.getSupportedContentTypes()
    // if (!supportedTypes.includes(content.type)) {
    //   errors.push(`不支持的内容类型: ${content.type}`)
    // }

    // // 检查文本长度
    // const limits = this.getContentLimits()
    // if (content.text && content.text.length > limits.maxTextLength) {
    //   errors.push(`文本内容超过最大长度限制 ${limits.maxTextLength} 字符`)
    // }

    // // TikTok主要是视频平台
    // if (content.type !== PublishContentType.Video && content.type !== PublishContentType.Mixed) {
    //   errors.push('TikTok主要支持视频内容发布')
    // }

    // if (!content.videoUrl) {
    //   errors.push('TikTok发布必须包含视频内容')
    // }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 发布内容到TikTok
   */
  async publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult> {
    this.logger.log(`开始发布到TikTok: 任务=${taskData.taskId}, 账号=${context.accountOpenId}`)

    try {
      const result = await this.publishVideoContent(context, taskData)

      console.log(result)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Success,
        platformContentId: result.data.video_id,
        platformContentUrl: result.data.share_url,
        rawResponse: result,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`TikTok发布失败: 任务=${taskData.taskId}`, error)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'TIKTOK_PUBLISH_ERROR',
        rawResponse: error.response?.data,
        completedAt: new Date()
      }
    }
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult> {
    try {
      const video = await this.tiktokApi.getVideoInfo(context, platformContentId)

      return {
        taskId: taskId,
        status: PublishTaskStatus.Success,
        platformContentId: platformContentId,
        platformContentUrl: video.data?.share_url || `https://www.tiktok.com/@user/video/${platformContentId}`,
        rawResponse: video,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`查询TikTok发布状态失败: 任务=${taskId}`, error)

      return {
        taskId: taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        completedAt: new Date()
      }
    }
  }

  /**
   * 删除已发布的内容
   */
  async deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      await this.tiktokApi.deleteVideo(context, platformContentId)
      return { success: true }
    } catch (error) {
      this.logger.error(`删除TikTok内容失败: ${platformContentId}`, error)
      return {
        success: false,
        errorMessage: error.message
      }
    }
  }

  /**
   * 获取支持的内容类型
   */
  getSupportedContentTypes(): string[] {
    return [
      PublishContentType.Video,
      PublishContentType.Mixed
    ]
  }

  /**
   * 获取内容限制
   */
  getContentLimits() {
    return {
      maxTextLength: 2200, // TikTok描述限制
      maxImageCount: 0,    // TikTok不支持纯图片
      maxVideoSize: 4 * 1024 * 1024 * 1024, // 4GB
      maxVideoDuration: 10 * 60, // 10分钟
      supportedImageFormats: [],
      supportedVideoFormats: ['mp4', 'mov', 'avi', 'flv', 'webm', '3gp']
    }
  }

  /**
   * 发布视频内容
   */
  private async publishVideoContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { videoUrl, text } = taskData.content

    // TODO: 这里需要将 taskData 中的表单信息传递给参数
    return await this.tiktokApi.uploadVideo(context, {
      video_url: videoUrl,
      caption: text || '',
      is_brand_organic: false,
      is_branded_content: false,
      disable_comment: false,
      disable_duet: false,
      disable_stitch: false,
      thumbnail_offset: 200,
      is_ai_generated: false,
      privacy_level: 'PUBLIC_TO_EVERYONE',
      upload_to_draft: false
    })
  }
}
