import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsString, Length } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { MemberStatusEnum } from '@yxr/common'
import { IsTeamProposalCode } from '../../common/validator/IsTeamProposalCode'

export class ProposalTeamDetailsDto {

  @ApiProperty({
    description: '团队Id',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: true
  })
  id: string

  @ApiProperty({
    description: '团队名称',
    example: 'XX的团队',
    required: true
  })
  name: string

  @ApiProperty({
    description: '团队LOGO图片地址',
    example: 'https://xxx.com/xxx.png',
    required: true
  })
  logoUrl: string

  @ApiProperty({
    description: '团队LOGO图片存储KEY',
    example: 'xxx.png',
    required: true
  })
  logoKey: string

  @ApiProperty({
    type: String,
    description: '加入状态',
    example: MemberStatusEnum.Joined,
    required: true,
    enum: MemberStatusEnum
  })
  status: MemberStatusEnum
}

export class ProposalTeamResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ProposalTeamDetailsDto
  })
  data: ProposalTeamDetailsDto
}

export class createProposalRequestBodyDTO {

  @ApiProperty({
    description: '团队邀请码',
    example: 'F64D98',
    required: true
  })
  @IsNotEmpty()
  @IsString()
  @IsTeamProposalCode()
  code: string
}

export class patchProposalRequestBodyDTO {
  @ApiProperty({
    type: Boolean,
    description: '状态, true: 同意, false: 拒绝',
    required: true
  })
  @IsBoolean()
  approved: boolean
}
