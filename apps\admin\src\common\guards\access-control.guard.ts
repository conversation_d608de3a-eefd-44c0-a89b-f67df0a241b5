import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { UserType } from '@yxr/common'
import type { FastifyRequest } from 'fastify'
import { ACCESS_CONTROL_KEY, AccessControlConfig } from '../decorators/access-control.decorator'

/**
 * 访问控制守卫
 * 基于装饰器配置进行权限验证
 */
@Injectable()
export class AccessControlGuard implements CanActivate {
  private readonly logger = new Logger(AccessControlGuard.name)

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    
    // 获取访问控制配置
    const accessConfig = this.reflector.getAllAndOverride<AccessControlConfig>(
      ACCESS_CONTROL_KEY,
      [context.getHandler(), context.getClass()]
    )

    // 如果没有配置访问控制，则允许通过（由其他守卫处理）
    if (!accessConfig) {
      return true
    }

    // 检查是否为匿名访问
    const anonymous = this.reflector.getAllAndOverride('anonymous', [
      context.getHandler(),
      context.getClass()
    ])

    if (anonymous) {
      return true
    }

    // 检查用户是否已认证
    if (!request.session) {
      throw new ForbiddenException('用户未认证')
    }

    const { userType, userId, appId, applicationId } = request.session

    // 检查用户类型是否被允许
    if (!accessConfig.allowedUserTypes.includes(userType as UserType)) {
      this.logger.warn(`用户类型 ${userType} 不被允许访问此接口`)
      throw new ForbiddenException('权限不足')
    }

    // 应用Token的特殊验证
    if (userType === UserType.APPLICATION) {
      if (!appId || !applicationId) {
        throw new ForbiddenException('应用Token信息不完整')
      }

      // 如果需要数据隔离，在request中标记
      if (accessConfig.requireDataIsolation) {
        request.dataIsolation = {
          sourceAppId: appId,
          applicationId: applicationId
        }
      }
    }

    // 自定义验证器
    if (accessConfig.customValidator) {
      if (!accessConfig.customValidator(request.session)) {
        throw new ForbiddenException('自定义权限验证失败')
      }
    }

    this.logger.debug(`用户 ${userId} (${userType}) 通过访问控制验证`)
    return true
  }
}
