import {
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { GroupEntity, MemberEntity, PlatformAccountEntity } from '@yxr/mongo'
import {
  GroupsDetailResponse,
  GroupsListResponse,
  PatchGroupsRequest,
  PostGroupsRequest
} from './group.dto'
import { TeamRoleNames } from '@yxr/common'

@Injectable()
export class GroupService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(GroupEntity.name) private groupModel: Model<GroupEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>
  ) {}

  /**
   * 添加分组
   * @param body
   * @returns
   */
  async postGroupsAsync(body: PostGroupsRequest): Promise<GroupsDetailResponse> {
    const { session } = this.request
    let group = await this.groupModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      name: body.name
    })

    if (group) {
      throw new ConflictException('分组已存在')
    }

    const data = await this.groupModel.create({
      name: body.name,
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })

    return {
      id: data._id.toString(),
      name: data.name,
      accounts: data.accounts,
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 分组列表
   * @param name
   * @param page
   * @param size
   * @returns
   */
  async getGroupsAsync(name: string, page: number, size: number): Promise<GroupsListResponse> {
    const skip = (page - 1) * size
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const query: any = {}

    query.teamId = new Types.ObjectId(currentTeamId)
    if (name) {
      query.name = { $regex: name, $options: 'i' }
    }

    const totalSize = await this.groupModel.countDocuments(query)
    const data = await this.groupModel
      .find(query)
      .sort({ createdAt: 'desc' })
      .skip(skip)
      .limit(size)

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data.map((item) => ({
        id: item._id.toString(),
        name: item.name,
        accounts: item.accounts,
        createdAt: item.createdAt.getTime()
      }))
    }
  }

  /**
   * 修改分组
   * @param groupsId
   * @param body
   */
  async patchGroupsAsync(
    groupsId: string,
    body: PatchGroupsRequest
  ): Promise<GroupsDetailResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    let group = await this.groupModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      _id: new Types.ObjectId(groupsId)
    })

    if (!group) {
      throw new NotFoundException('分组不存在')
    }
    const updateData: any = {}

    if (body.name) {
      updateData.name = body.name
    }

    if (body.accounts && body.accounts.length >= 0) {
      // 需要判断是否对修改账号有运营权限
      const member = await this.memberModel.findOne({
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(currentUserId)
      })

      // 获取分组中变动的账号ids
      const difference1 = group.accounts.filter((item) => !body.accounts.includes(item))
      const difference2 = body.accounts.filter((item) => !group.accounts.includes(item))
      const differenceIds = [...difference1, ...difference2]
      if (
        !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
      ) {
        if (!differenceIds.every((item) => member.accounts.includes(item))) {
          throw new ForbiddenException('修改失败，您没有该账号运营权限')
        }
      }

      const accountObjectIds = body.accounts.map((id) => new Types.ObjectId(id))
      // 确保所有账号存在
      const existingAccounts = await this.platformAccountModel.find({
        _id: { $in: accountObjectIds }
      })
      if (existingAccounts.length !== body.accounts.length) {
        throw new NotFoundException('一个或者多个账号不存在')
      }

      const accountsToAdd = body.accounts.filter((id) => !group.accounts.includes(id.toString()))
      if (accountsToAdd.length > 0) {
        // 对需要新增分组 ID 的账号进行更新
        const accountsToAddObjectIds = accountsToAdd.map((id) => new Types.ObjectId(id))
        await this.platformAccountModel.updateMany(
          { _id: { $in: accountsToAddObjectIds } },
          { $addToSet: { groups: groupsId } } // 使用 $addToSet 避免重复
        )
      }

      const accountsToRemove = group.accounts.filter((id) => !body.accounts.includes(id))
      if (accountsToRemove.length > 0) {
        // 对需要移除分组 ID 的账号进行更新
        const accountsToRemoveObjectIds = accountsToRemove.map((id) => new Types.ObjectId(id))
        await this.platformAccountModel.updateMany(
          { _id: { $in: accountsToRemoveObjectIds } },
          { $pull: { groups: groupsId } }
        )
      }

      updateData.accounts = body.accounts
    }

    const data = await this.groupModel.findOneAndUpdate(
      { _id: group._id },
      { $set: updateData }, // 使用 $set 只更新传入的字段
      { returnDocument: 'after', new: true } // 返回更新后的文档
    )

    return {
      id: data._id.toString(),
      name: data.name,
      accounts: data.accounts,
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 删除分组
   * @param groupsId
   */
  async deleteGroupsAsync(groupsId: string) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const group = await this.groupModel.findOne({
      _id: new Types.ObjectId(groupsId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    //运营人数据隔离,分组里面的账号不全有运营权限则不能删除
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      if (!group.accounts.every((item) => member.accounts.includes(item))) {
        throw new ForbiddenException('删除失败，没有分组内所有账号运营权限')
      }
    }

    if (group.accounts.length > 0) {
      const accountObjectIds = group.accounts.map((id) => new Types.ObjectId(id))
      await this.platformAccountModel.updateMany(
        { _id: { $in: accountObjectIds } },
        { $pull: { groups: group._id } }
      )
    }

    await this.groupModel.deleteOne({
      _id: new Types.ObjectId(group._id)
    })
  }
}
