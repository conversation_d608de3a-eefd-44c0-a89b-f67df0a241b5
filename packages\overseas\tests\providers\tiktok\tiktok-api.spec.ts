/**
 * TikTok API测试
 */

import { AxiosInstance } from 'axios'
import { TiktokApi } from '../../../src/providers/tiktok/tiktok-api'
import { 
  TikTokApiResponse,
  TikTokOAuth2TokenResponse,
  TikTokAccountOverviewResponse,
  TikTokVideoUploadResponse,
  TikTokVideoUploadParams,
  TikTokOAuth2Params
} from '../../../src/providers/tiktok/tiktok-api-types'
import { createOverseasContext } from '../../../src/providers/utils'
import { RemoteApiError, RemoteApiErrorCodes } from '../../../src/utils/error-handler'

// Mock axios和相关模块
jest.mock('../../../src/utils/axios-config')
jest.mock('../../../src/providers/tiktok/tiktok-error-handler')
jest.mock('../../../src/providers/utils')

describe('TikTok API', () => {
  let tiktokApi: TiktokApi
  let mockAxiosInstance: jest.Mocked<AxiosInstance>
  
  const mockContext = createOverseasContext('tiktok', {
    accountOpenId: 'test-open-id',
    teamId: 'test-team',
    userId: 'test-user',
    options: {
      credentials: {
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token'
      }
    }
  })

  beforeEach(() => {
    // Mock axios instance
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
      request: jest.fn(),
      interceptors: {
        request: { use: jest.fn(), eject: jest.fn() },
        response: { use: jest.fn(), eject: jest.fn() }
      }
    } as any

    tiktokApi = new TiktokApi()
    
    // Mock createTikTokAxiosInstance
    jest.spyOn(tiktokApi as any, 'createTikTokAxiosInstance').mockReturnValue(mockAxiosInstance)
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()

    // Mock environment variables
    process.env.TIKTOK_CLIENT_KEY = 'test-client-key'
    process.env.TIKTOK_CLIENT_SECRET = 'test-client-secret'
  })

  afterEach(() => {
    jest.restoreAllMocks()
    jest.clearAllMocks()
  })

  describe('oauth2_token', () => {
    it('应该成功获取OAuth2令牌', async () => {
      const mockParams: TikTokOAuth2Params = {
        auth_code: 'test-auth-code',
        redirect_uri: 'https://example.com/callback'
      }

      const mockResponse: TikTokApiResponse<TikTokOAuth2TokenResponse> = {
        code: 0,
        message: 'Success',
        data: {
          access_token: 'new-access-token',
          token_type: 'bearer',
          scope: 'user.info.basic,video.list',
          expires_in: 3600,
          refresh_token: 'new-refresh-token',
          refresh_token_expires_in: 86400,
          open_id: 'test-open-id'
        }
      }

      mockAxiosInstance.post.mockResolvedValue({ data: mockResponse })

      const result = await tiktokApi.oauth2_token(mockContext, mockParams)

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/open_api/v1.3/tt_user/oauth2/token/',
        {
          client_id: 'test-client-key',
          client_secret: 'test-client-secret',
          grant_type: 'authorization_code',
          auth_code: 'test-auth-code',
          redirect_uri: 'https://example.com/callback'
        },
        {
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          }
        }
      )

      expect(result).toEqual(mockResponse)
    })

    it('应该处理OAuth2令牌获取失败', async () => {
      const mockParams: TikTokOAuth2Params = {
        auth_code: 'invalid-auth-code',
        redirect_uri: 'https://example.com/callback'
      }

      const mockError = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.AccessTokenInvalid,
        { message: 'Invalid auth code' },
        { url: '/oauth2/token', method: 'POST' },
        { status: 200, statusText: 'OK' },
        mockContext
      )

      mockAxiosInstance.post.mockRejectedValue(mockError)

      await expect(
        tiktokApi.oauth2_token(mockContext, mockParams)
      ).rejects.toThrow(RemoteApiError)
    })
  })

  describe('getAccountOverview', () => {
    it('应该成功获取账号概览', async () => {
      const mockResponse: TikTokApiResponse<TikTokAccountOverviewResponse> = {
        code: 0,
        message: 'Success',
        data: {
          is_business_account: true,
          profile_image: 'https://example.com/avatar.jpg',
          username: 'test_user',
          profile_deep_link: 'https://tiktok.com/@test_user',
          display_name: 'Test User',
          bio_description: 'Test bio',
          is_verified: false,
          following_count: 100,
          followers_count: 1000,
          total_likes: 5000,
          videos_count: 50,
          metrics: []
        }
      }

      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse })

      const result = await tiktokApi.getAccountOverview(
        mockContext,
        'test-access-token',
        'test-open-id'
      )

      expect(mockAxiosInstance.get).toHaveBeenCalledWith(
        expect.stringContaining('business_id=test-open-id'),
        {
          headers: { 'Access-Token': 'test-access-token' }
        }
      )

      expect(result).toEqual(mockResponse)
    })

    it('应该支持可选参数', async () => {
      const mockResponse: TikTokApiResponse<TikTokAccountOverviewResponse> = {
        code: 0,
        message: 'Success',
        data: {
          is_business_account: true,
          profile_image: 'https://example.com/avatar.jpg',
          username: 'test_user',
          profile_deep_link: 'https://tiktok.com/@test_user',
          display_name: 'Test User',
          bio_description: 'Test bio',
          is_verified: false,
          following_count: 100,
          followers_count: 1000,
          total_likes: 5000,
          videos_count: 50,
          metrics: []
        }
      }

      mockAxiosInstance.get.mockResolvedValue({ data: mockResponse })

      const options = {
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        fields: ['username', 'followers_count', 'videos_count']
      }

      await tiktokApi.getAccountOverview(
        mockContext,
        'test-access-token',
        'test-open-id',
        options
      )

      const callUrl = mockAxiosInstance.get.mock.calls[0][0] as string
      expect(callUrl).toContain('start_date=2024-01-01')
      expect(callUrl).toContain('end_date=2024-01-31')
      expect(callUrl).toContain('fields=')
    })
  })

  describe('uploadVideo', () => {
    it('应该成功上传视频', async () => {
      const mockParams: TikTokVideoUploadParams = {
        video_url: 'https://example.com/video.mp4',
        caption: 'Test video caption',
        privacy_level: 'PUBLIC_TO_EVERYONE',
        disable_comment: false,
        disable_duet: false,
        disable_stitch: false
      }

      const mockResponse = {
        data: {
          code: 0,
          message: 'Success',
          data: {
            video_id: 'test-video-id',
            share_url: 'https://tiktok.com/@user/video/123',
            embed_link: 'https://tiktok.com/embed/123',
            unique_id: 'unique-123',
            title: 'Test Video',
            video_description: 'Test video caption',
            duration: 30,
            cover_image_url: 'https://example.com/cover.jpg',
            embed_html: '<iframe>...</iframe>',
            create_time: **********,
            is_top_video: false,
            reach: 1000,
            like_count: 50,
            comment_count: 10,
            share_count: 5,
            view_count: 1000
          }
        }
      }

      // Mock safeApiCall for legacy method
      jest.doMock('../../../src/providers/utils', () => ({
        safeApiCall: jest.fn().mockResolvedValue(mockResponse)
      }))

      // Since uploadVideo uses the legacy safeApiCall approach, we need to mock it
      const result = await tiktokApi.uploadVideo(mockContext, mockParams)

      expect(result).toBeDefined()
    })
  })

  describe('getVideoInfo', () => {
    it('应该成功获取视频信息', async () => {
      const mockResponse = {
        data: {
          code: 0,
          message: 'Success',
          data: {
            video_id: 'test-video-id',
            share_url: 'https://tiktok.com/@user/video/123',
            embed_link: 'https://tiktok.com/embed/123',
            unique_id: 'unique-123',
            title: 'Test Video',
            video_description: 'Test video description',
            duration: 30,
            cover_image_url: 'https://example.com/cover.jpg',
            create_time: **********,
            like_count: 50,
            comment_count: 10,
            share_count: 5,
            view_count: 1000
          }
        }
      }

      // Mock safeApiCall for legacy method
      jest.doMock('../../../src/providers/utils', () => ({
        safeApiCall: jest.fn().mockResolvedValue(mockResponse)
      }))

      const result = await tiktokApi.getVideoInfo(mockContext, 'test-video-id')

      expect(result).toBeDefined()
    })
  })

  describe('deleteVideo', () => {
    it('应该成功删除视频', async () => {
      const mockResponse = {
        data: {
          code: 0,
          message: 'Success',
          data: {
            video_id: 'test-video-id'
          }
        }
      }

      // Mock safeApiCall for legacy method
      jest.doMock('../../../src/providers/utils', () => ({
        safeApiCall: jest.fn().mockResolvedValue(mockResponse)
      }))

      const result = await tiktokApi.deleteVideo(mockContext, 'test-video-id')

      expect(result).toBeDefined()
    })
  })

  describe('私有方法', () => {
    describe('getAccessTokenFromContext', () => {
      it('应该从上下文中获取访问令牌', () => {
        const token = (tiktokApi as any).getAccessTokenFromContext(mockContext)
        expect(token).toBe('test-access-token')
      })

      it('应该在缺少访问令牌时抛出错误', () => {
        const contextWithoutToken = createOverseasContext('tiktok', {
          accountOpenId: 'test-open-id'
        })

        expect(() => {
          (tiktokApi as any).getAccessTokenFromContext(contextWithoutToken)
        }).toThrow('缺少TikTok访问令牌')
      })
    })

    describe('getOpenIdFromContext', () => {
      it('应该从上下文中获取OpenID', () => {
        const openId = (tiktokApi as any).getOpenIdFromContext(mockContext)
        expect(openId).toBe('test-open-id')
      })

      it('应该在缺少OpenID时抛出错误', () => {
        const contextWithoutOpenId = createOverseasContext('tiktok', {
          teamId: 'test-team'
        })

        expect(() => {
          (tiktokApi as any).getOpenIdFromContext(contextWithoutOpenId)
        }).toThrow('缺少TikTok账号OpenID')
      })
    })
  })

  describe('createTikTokAxiosInstance', () => {
    it('应该创建配置正确的axios实例', () => {
      const instance = (tiktokApi as any).createTikTokAxiosInstance(mockContext)
      expect(instance).toBeDefined()
    })
  })
})
