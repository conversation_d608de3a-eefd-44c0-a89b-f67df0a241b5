import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformUserEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformInvitationEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  InvitationStatus,
  UserType
} from '@yxr/common'
import {
  CreateInvitationRequestDto,
  HandleInvitationRequestDto,
  InvitationDto,
  InvitationListRequestDto,
  InvitationListResponseDto
} from '../dto/invitation.dto'

@Injectable()
export class InvitationService {
  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserEntity.name)
    private userModel: Model<OpenPlatformUserEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformInvitationEntity.name)
    private invitationModel: Model<OpenPlatformInvitationEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建邀请
   */
  async createInvitation(createDto: CreateInvitationRequestDto): Promise<InvitationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以发送邀请')
    }

    // 检查应用是否存在且用户是否为管理员
    const application = await this.applicationModel.findById(createDto.applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: new Types.ObjectId(createDto.applicationId),
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以发送邀请')
    }

    // 检查被邀请人是否存在
    const invitee = await this.userModel.findOne({ phone: createDto.phone })
    if (!invitee) {
      throw new NotFoundException('被邀请的用户不存在，请先让用户注册开放平台账号')
    }

    // 检查被邀请人是否已经是应用成员
    const existingRole = await this.userRoleModel.findOne({
      userId: invitee._id,
      applicationId: new Types.ObjectId(createDto.applicationId),
      status: OpenPlatformStatus.ACTIVE
    })

    if (existingRole) {
      throw new BadRequestException('该用户已经是应用成员')
    }

    // 检查是否已有待处理的邀请
    const existingInvitation = await this.invitationModel.findOne({
      applicationId: new Types.ObjectId(createDto.applicationId),
      inviteeId: invitee._id,
      status: InvitationStatus.PENDING
    })

    if (existingInvitation) {
      throw new BadRequestException('该用户已有待处理的邀请')
    }

    // 创建邀请
    const invitation = await this.invitationModel.create({
      applicationId: new Types.ObjectId(createDto.applicationId),
      inviterId: new Types.ObjectId(session.userId),
      inviteeId: invitee._id,
      inviteePhone: createDto.phone,
      invitedRole: createDto.invitedRole || OpenPlatformRoleNames.CHANNEL,
      status: InvitationStatus.PENDING,
      remark: createDto.remark || ''
    })

    return this.formatInvitationDto(invitation)
  }

  /**
   * 获取邀请列表
   */
  async getInvitationList(queryDto: InvitationListRequestDto): Promise<InvitationListResponseDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看邀请列表')
    }

    const { page = 1, size = 10, status, type = 'received' } = queryDto
    const skip = (page - 1) * size

    // 构建查询条件
    const query: any = {}

    if (type === 'sent') {
      // 查看发送的邀请
      query.inviterId = new Types.ObjectId(session.userId)
    } else {
      // 查看收到的邀请
      query.inviteeId = new Types.ObjectId(session.userId)
    }

    if (status !== undefined) {
      query.status = status
    }

    const [invitations, total] = await Promise.all([
      this.invitationModel
        .find(query)
        .skip(skip)
        .limit(size)
        .sort({ createdAt: -1 })
        .exec(),
      this.invitationModel.countDocuments(query)
    ])

    const invitationDtos = await Promise.all(
      invitations.map(invitation => this.formatInvitationDto(invitation))
    )

    return {
      data: invitationDtos,
      totalSize: total,
      totalPage: Math.ceil(total / size),
      page,
      size
    }
  }

  /**
   * 获取邀请详情
   */
  async getInvitationById(invitationId: string): Promise<InvitationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看邀请详情')
    }

    const invitation = await this.invitationModel.findById(invitationId)
    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    // 检查权限（只有邀请人或被邀请人可以查看）
    const userId = new Types.ObjectId(session.userId)
    if (!invitation.inviterId.equals(userId) && !invitation.inviteeId.equals(userId)) {
      throw new ForbiddenException('您没有权限查看此邀请')
    }

    return this.formatInvitationDto(invitation)
  }

  /**
   * 处理邀请（接受/拒绝）
   */
  async handleInvitation(
    invitationId: string,
    handleDto: HandleInvitationRequestDto
  ): Promise<{ message: string; status: InvitationStatus }> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以处理邀请')
    }

    const invitation = await this.invitationModel.findById(invitationId)
    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    // 检查权限（只有被邀请人可以处理）
    const userId = new Types.ObjectId(session.userId)
    if (!invitation.inviteeId.equals(userId)) {
      throw new ForbiddenException('只有被邀请人可以处理邀请')
    }

    // 检查邀请状态
    if (invitation.status !== InvitationStatus.PENDING) {
      throw new BadRequestException('邀请已被处理')
    }

    const newStatus = handleDto.action === 'accept' 
      ? InvitationStatus.ACCEPTED 
      : InvitationStatus.REJECTED

    // 更新邀请状态
    await this.invitationModel.findByIdAndUpdate(invitationId, {
      status: newStatus
    })

    // 如果接受邀请，创建用户角色关系
    if (handleDto.action === 'accept') {
      await this.userRoleModel.create({
        userId: invitation.inviteeId,
        applicationId: invitation.applicationId,
        role: invitation.invitedRole || OpenPlatformRoleNames.CHANNEL,
        invitedBy: invitation.inviterId,
        status: OpenPlatformStatus.ACTIVE
      })
    }

    const message = handleDto.action === 'accept' 
      ? '邀请已接受，您现在是该应用的渠道商' 
      : '邀请已拒绝'

    return {
      message,
      status: newStatus
    }
  }

  /**
   * 撤销邀请
   */
  async cancelInvitation(invitationId: string): Promise<void> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以撤销邀请')
    }

    const invitation = await this.invitationModel.findById(invitationId)
    if (!invitation) {
      throw new NotFoundException('邀请不存在')
    }

    // 检查权限（只有邀请人可以撤销）
    const userId = new Types.ObjectId(session.userId)
    if (!invitation.inviterId.equals(userId)) {
      throw new ForbiddenException('只有邀请人可以撤销邀请')
    }

    // 检查邀请状态
    if (invitation.status !== InvitationStatus.PENDING) {
      throw new BadRequestException('只能撤销待处理的邀请')
    }

    await this.invitationModel.findByIdAndDelete(invitationId)
  }

  /**
   * 格式化邀请DTO
   */
  private async formatInvitationDto(invitation: OpenPlatformInvitationEntity): Promise<InvitationDto> {
    // 获取应用信息
    const application = await this.applicationModel.findById(invitation.applicationId)
    
    // 获取邀请人信息
    const inviter = await this.userModel.findById(invitation.inviterId)
    
    // 获取被邀请人信息
    const invitee = await this.userModel.findById(invitation.inviteeId)

    return {
      id: (invitation as any)._id.toString(),
      applicationId: invitation.applicationId.toString(),
      applicationName: application?.name || '',
      inviterId: invitation.inviterId.toString(),
      inviterPhone: inviter?.phone || '',
      inviterNickname: inviter?.nickname || '',
      inviteeId: invitation.inviteeId.toString(),
      inviteePhone: invitation.inviteePhone,
      inviteeNickname: invitee?.nickname || '',
      status: invitation.status,
      invitedRole: invitation.invitedRole || OpenPlatformRoleNames.CHANNEL,
      remark: invitation.remark || '',
      createdAt: invitation.createdAt?.getTime() || 0,
      updatedAt: invitation.updatedAt?.getTime() || 0
    }
  }
}
