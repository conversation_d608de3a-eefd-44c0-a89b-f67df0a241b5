import {
  Injectable,
  Logger,
  OnModuleInit
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { HttpService } from '@nestjs/axios'
import { OnEvent } from '@nestjs/event-emitter'
import { ConfigService } from '@nestjs/config'
import { firstValueFrom, timeout, catchError } from 'rxjs'
import { AxiosError } from 'axios'
import {
  TeamEntity,
  OpenPlatformApplicationEntity,
  PlatformAccountEntity,
  TaskSetEntity,
  TaskEntity
} from '@yxr/mongo'
import { WebhookStatus } from '@yxr/common'
import {
  OpenPlatformWebhookEventType,
  WebhookEventData,
  WebhookPushConfig,
  WebhookPushResult,
  EventListenerParams,
  PlatformAccountCreatedEventData,
  PlatformAccountDeletedEventData,
  TaskSetCreatedEventData,
  TaskStatusUpdatedEventData
} from './webhook-event.types'

@Injectable()
export class WebhookEventService implements OnModuleInit {
  private readonly logger = new Logger(WebhookEventService.name)
  
  // Webhook推送配置
  private readonly config: WebhookPushConfig = {
    enabled: true, // 可通过环境变量控制
    maxRetries: 3,
    retryIntervals: [1000, 2000, 4000], // 1秒、2秒、4秒
    timeout: 5000 // 5秒超时
  }

  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TaskSetEntity.name)
    private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    // 从环境变量读取配置
    this.config.enabled = this.configService.get<boolean>('WEBHOOK_PUSH_ENABLED', true)
  }

  onModuleInit() {
    this.logger.log('WebhookEventService initialized')
    this.logger.log(`Webhook push enabled: ${this.config.enabled}`)
  }

  /**
   * 监听媒体账号创建事件
   */
  @OnEvent('platform.account.created')
  async handlePlatformAccountCreated(params: EventListenerParams) {
    if (!this.config.enabled) return

    try {
      const { teamId, data } = params
      const eventData: PlatformAccountCreatedEventData = {
        accountId: data.accountId,
        platformName: data.platformName,
        nickname: data.nickname,
        teamId: teamId,
        userId: data.userId,
        capacity: data.capacity || 1,
        createdAt: Date.now()
      }

      await this.pushWebhookEvent(
        teamId,
        OpenPlatformWebhookEventType.PLATFORM_ACCOUNT_CREATED,
        eventData
      )
    } catch (error) {
      this.logger.error(`处理媒体账号创建事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 监听媒体账号删除事件
   */
  @OnEvent('platform.account.deleted')
  async handlePlatformAccountDeleted(params: EventListenerParams) {
    if (!this.config.enabled) return

    try {
      const { teamId, data } = params
      const eventData: PlatformAccountDeletedEventData = {
        accountId: data.accountId,
        platformName: data.platformName,
        nickname: data.nickname,
        teamId: teamId,
        userId: data.userId,
        deletedAt: Date.now()
      }

      await this.pushWebhookEvent(
        teamId,
        OpenPlatformWebhookEventType.PLATFORM_ACCOUNT_DELETED,
        eventData
      )
    } catch (error) {
      this.logger.error(`处理媒体账号删除事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 监听任务集创建事件
   */
  @OnEvent('taskset.created')
  async handleTaskSetCreated(params: EventListenerParams) {
    if (!this.config.enabled) return

    try {
      const { teamId, data } = params
      const eventData: TaskSetCreatedEventData = {
        taskSetId: data.taskSetId,
        taskSetName: data.taskSetName,
        teamId: teamId,
        userId: data.userId,
        taskCount: data.taskCount || 0,
        createdAt: Date.now()
      }

      await this.pushWebhookEvent(
        teamId,
        OpenPlatformWebhookEventType.TASKSET_CREATED,
        eventData
      )
    } catch (error) {
      this.logger.error(`处理任务集创建事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 监听任务状态更新事件
   */
  @OnEvent('task.status.updated')
  async handleTaskStatusUpdated(params: EventListenerParams) {
    if (!this.config.enabled) return

    try {
      const { teamId, data } = params
      const eventData: TaskStatusUpdatedEventData = {
        taskId: data.taskId,
        taskSetId: data.taskSetId,
        taskSetName: data.taskSetName,
        teamId: teamId,
        userId: data.userId,
        oldStatus: data.oldStatus,
        newStatus: data.newStatus,
        platformName: data.platformName,
        accountNickname: data.accountNickname,
        updatedAt: Date.now()
      }

      await this.pushWebhookEvent(
        teamId,
        OpenPlatformWebhookEventType.TASK_STATUS_UPDATED,
        eventData
      )
    } catch (error) {
      this.logger.error(`处理任务状态更新事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 推送Webhook事件到开放平台应用
   */
  private async pushWebhookEvent(
    teamId: string,
    eventType: OpenPlatformWebhookEventType,
    eventData: any
  ): Promise<void> {
    try {
      // 查询团队信息，获取对应的开放平台应用
      const team = await this.teamModel.findOne({
        _id: new Types.ObjectId(teamId),
        source: 'open_platform_app',
        isDeleted: false
      }).lean()

      if (!team || !team.sourceAppId) {
        this.logger.debug(`团队 ${teamId} 不是开放平台团队，跳过Webhook推送`)
        return
      }

      // 查询应用的Webhook配置
      const application = await this.applicationModel.findOne({
        _id: new Types.ObjectId(team.sourceAppId),
        webhookStatus: WebhookStatus.VERIFIED
      }).lean()

      if (!application || !application.webhookUrl) {
        this.logger.debug(`应用 ${team.sourceAppId} 未配置有效的Webhook URL，跳过推送`)
        return
      }

      // 构造Webhook事件数据
      const webhookData: WebhookEventData = {
        type: eventType,
        timestamp: Date.now(),
        teamId: teamId,
        applicationId: team.sourceAppId,
        data: eventData
      }

      // 推送事件
      const result = await this.sendWebhookRequest(application.webhookUrl, webhookData)
      
      if (result.success) {
        this.logger.log(
          `Webhook事件推送成功: ${eventType}, 团队ID: ${teamId}, ` +
          `应用ID: ${team.sourceAppId}, 耗时: ${result.duration}ms`
        )
      } else {
        this.logger.warn(
          `Webhook事件推送失败: ${eventType}, 团队ID: ${teamId}, ` +
          `应用ID: ${team.sourceAppId}, 错误: ${result.message}, 重试次数: ${result.retryCount}`
        )
      }
    } catch (error) {
      this.logger.error(
        `推送Webhook事件失败: ${eventType}, 团队ID: ${teamId}, 错误: ${error.message}`,
        error.stack
      )
    }
  }

  /**
   * 发送Webhook请求（带重试机制）
   */
  private async sendWebhookRequest(
    webhookUrl: string,
    data: WebhookEventData
  ): Promise<WebhookPushResult> {
    const startTime = Date.now()
    let lastError: any

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        this.logger.debug(`发送Webhook请求 (第${attempt + 1}次尝试): ${webhookUrl}`)

        const response = await firstValueFrom(
          this.httpService.post(webhookUrl, data, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'YiXiaoEr-Webhook-Event/1.0'
            },
            timeout: this.config.timeout
          }).pipe(
            timeout(this.config.timeout),
            catchError((error: AxiosError) => {
              throw error
            })
          )
        )

        const duration = Date.now() - startTime
        return {
          success: true,
          statusCode: response.status,
          message: 'Webhook推送成功',
          retryCount: attempt,
          duration
        }

      } catch (error) {
        lastError = error
        this.logger.warn(
          `Webhook请求失败 (第${attempt + 1}次尝试): ${webhookUrl}, 错误: ${error.message}`
        )

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.config.maxRetries) {
          const retryDelay = this.config.retryIntervals[attempt] || this.config.retryIntervals[this.config.retryIntervals.length - 1]
          await this.sleep(retryDelay)
        }
      }
    }

    // 所有重试都失败了
    const duration = Date.now() - startTime
    return {
      success: false,
      statusCode: lastError?.response?.status || 0,
      message: this.getErrorMessage(lastError),
      retryCount: this.config.maxRetries,
      duration
    }
  }

  /**
   * 获取错误信息
   */
  private getErrorMessage(error: any): string {
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
      return `网络连接失败: ${error.message}`
    }

    if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
      return `请求超时，服务器未在${this.config.timeout / 1000}秒内响应`
    }

    if (error.response) {
      const status = error.response.status
      const statusText = error.response.statusText || '未知错误'
      return `HTTP错误: ${status} ${statusText}`
    }

    return `未知错误: ${error.message}`
  }

  /**
   * 等待指定毫秒数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 手动触发事件推送（用于测试）
   */
  async triggerTestEvent(teamId: string, eventType: OpenPlatformWebhookEventType, testData: any): Promise<void> {
    await this.pushWebhookEvent(teamId, eventType, testData)
  }
}
