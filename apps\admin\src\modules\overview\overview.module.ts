import { Module } from '@nestjs/common'
import { OverviewController } from './overview.controller'
import { OverviewService } from './overview.service'
import {
  BrowserMongoose,
  BrowserPublishStatisticMongoose,
  ContentMongoose,
  DataStatisticsMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  PlatformDataStatisticMongoose,
  TaskMongoose,
  TeamMongoose,
  TeamStatisticMongoose,
  UserMongoose
} from '@yxr/mongo'
import { CommonModule } from '@yxr/common'

@Module({
  imports: [
    UserMongoose,
    MemberMongoose,
    TeamMongoose,
    TaskMongoose,
    BrowserMongoose,
    ContentMongoose,
    PlatformAccountMongoose,
    TeamStatisticMongoose,
    DataStatisticsMongoose,
    BrowserPublishStatisticMongoose,
    PlatformDataStatisticMongoose,
    CommonModule
  ],
  controllers: [OverviewController],
  providers: [OverviewService],
  exports:[OverviewService]
})
export class OverviewModule {}
