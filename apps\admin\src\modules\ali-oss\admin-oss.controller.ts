import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { AdminOssService } from './admin-oss.service'
import { getOssSignatureResponseDTO } from './admin-oss.dto'

@ApiTags('OSS安装包上传')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@Controller('oss')
export class AdminOssController {
  constructor(private readonly adminOssService: AdminOssService) {}

  @Get('desktop/upload-url')
  @ApiOperation({ summary: '获取客户端直传地址' })
  @ApiOkResponse({ type: getOssSignatureResponseDTO, description: '操作成功' })
  async getOssUploadUrl(@Query('name') name: string) {
    console.log(name,'name')
    return await this.adminOssService.getUploadSignatureUrl(name)
  }
}
