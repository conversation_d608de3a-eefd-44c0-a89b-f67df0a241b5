import { Body, Controller, Delete, Get, Param, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  getMemberListOkResponseDTO,
  MaxAccountCountResponseDTO,
  MemberResponseDTO,
  PutMemberRemarkRequest
} from './member.dto'
import { MemberStatusEnum } from '@yxr/common'
import { MemberService } from './member.service'
import { isString } from 'class-validator'

@Controller('members')
@ApiTags('团队管理/成员')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class MemberController {
  constructor(private readonly memberService: MemberService) {}

  /**
   * 获取成员列表
   * @param size
   * @param page
   * @param statuses
   */
  @Get()
  @ApiOperation({ summary: '获取成员列表' })
  @ApiOkResponse({ type: getMemberListOkResponseDTO, description: '操作成功' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({
    name: 'statuses',
    required: false,
    type: Array<MemberStatusEnum>,
    description: '成员状态',
    example: [MemberStatusEnum.Joined, MemberStatusEnum.NotJoined, MemberStatusEnum.Pending],
    enum: MemberStatusEnum,
    isArray: true
  })
  getList(
    @Query('size', { transform: (value) => value || 10 }) size: number,
    @Query('page', { transform: (value) => value || 1 }) page: number,
    @Query('statuses', { transform: (value) => (isString(value) ? [value] : value) })
    statuses: MemberStatusEnum[]
  ) {
    return this.memberService.getPagedMembers({
      statuses,
      page,
      size
    })
  }

  /**
   * 获取成员详情
   * @param userId
   */
  @Get(':userId')
  @ApiOperation({ summary: '获取成员详情' })
  @ApiOkResponse({ type: MemberResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  getMember(@Param('userId') userId: string) {
    return this.memberService.getMember(userId)
  }

  /**
   * 设置成员角色
   * @param userId
   * @param roles
   */
  @Put(':userId/roles')
  @ApiOperation({ summary: '设置成员角色' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  putRoles(@Param('userId') userId: string, @Body() roles: string[]) {
    return this.memberService.setRoles({
      userId,
      roles: roles
    })
  }

  @Get('/max-account-count')
  @ApiOperation({ summary: '查询成员最大运营账号数' })
  @ApiOkResponse({ type: MaxAccountCountResponseDTO, description: '操作成功' })
  checkMaxAccountCount() {
    return this.memberService.getMaxAccountCount()
  }

  @Put(':userId/account-count')
  @ApiOperation({ summary: '设置成员最大运营账号数' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  setAccountCount(@Param('userId') userId: string, @Body() count: number) {
    return this.memberService.setAccountCount(userId, count)
  }

  /**
   * 设置成员运营账号
   * @param userId 成员用户Id
   * @param accounts
   */
  @Put(':userId/accounts')
  @ApiOperation({ summary: '设置成员运营账号' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  putAccounts(@Param('userId') userId: string, @Body() accounts: string[]) {
    return this.memberService.setAccounts({
      userId,
      accountIds: accounts
    })
  }

  @Put(':userId/remark')
  @ApiOperation({ summary: '修改当前用户团队昵称备注' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  async putMemberRemark(@Param('userId') userId: string, @Body() body: PutMemberRemarkRequest) {
    return await this.memberService.putMemberRemark(userId, body.remark)
  }

  /**
   * 设置成员运营网站空间
   * @param userId 成员用户Id
   * @param browsers
   */
  @Put(':userId/browsers')
  @ApiOperation({ summary: '设置成员运营网站空间', deprecated: true })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  putBrowsers(@Param('userId') userId: string, @Body() browsers: string[]) {
    return this.memberService.setBrowsers({
      userId,
      browserIds: browsers
    })
  }

  /**
   * 获取成员运营账号集合
   * @param userId 成员用户Id
   */
  @Get(':userId/accounts')
  @ApiOperation({ summary: '获取成员运营账号集合' })
  @ApiOkResponse({ type: [String], description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  getAccounts(@Param('userId') userId: string) {
    return this.memberService.getAccounts(userId)
  }

  /**
   * 获取成员运营网站空间集合
   * @param userId 成员用户Id
   */
  @Get(':userId/browsers')
  @ApiOperation({ summary: '获取成员运营网站空间集合' })
  @ApiOkResponse({ type: [String], description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: '成员用户Id' })
  getBrowsers(@Param('userId') userId: string) {
    return this.memberService.getBrowsers(userId)
  }

  /**
   * 移除成员/退出团队
   * @param userId 成员用户Id
   */
  @Delete(':userId')
  @ApiOperation({
    summary: '移除成员/退出团队',
    description:
      '创建人可以移除全部成员, 管理员用户可以移除普通成员, 普通成员只能移除自己(退出团队)'
  })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'userId', required: true, description: "成员用户Id, 退出团队时输入固定值'me'" })
  async delete(@Param('userId') userId: string) {
    if (userId.toLowerCase() === 'me') {
      return this.memberService.quit()
    } else {
      return this.memberService.remove(userId)
    }
  }

  /**
   * 更新成员冻结状态
   * @param userId
   * @param body
   * @returns
   */
  @Put(':userId/freeze')
  @ApiOperation({ summary: '更新成员冻结状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async putFreeze(
    @Param('userId') userId: string,
    @Body() body: { isFreeze: boolean } = { isFreeze: false }
  ) {
    return await this.memberService.putFreeze(userId, body.isFreeze)
  }
}
