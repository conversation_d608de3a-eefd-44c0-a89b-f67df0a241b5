import { Controller, Inject, Lo<PERSON>, Param, <PERSON>, Req, Res } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'
import { FastifyReply, FastifyRequest } from 'fastify'
import { Cryptography } from './Cryptography'
import { XMLParser } from 'fast-xml-parser'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { callbackBody, wxAppCallbackBody } from './wx-call-back.dto'
import { WxCacheKey } from './wx-cache-key'
import { WxPublishService } from './wx-publish.service'
import { WxThirdAuthService } from './wx-third-auth.service'
import { TlsService } from '@yxr/huoshan'

@Controller('wx/callback')
@ApiTags('媒体账号管理/微信第三方平台管理/回调管理')
export class WxCallBackController {
  constructor(
    private readonly wxPublishService: WxPublishService,
    private readonly wxThirdAuthService: WxThirdAuthService,
    private readonly loggerService: TlsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  /**
   * 获取微信票据验证推送消息 10分钟推送一次
   * 返回结构体
   * const body = {
   * xml: {
   *   AppId: 'wx9bc73e9a65c7b101',
   *   Encrypt:
   *     'l7MteHjfGDYu2ufyeLq10j1pSHUXe7Yq7rIimH9rgUK+wDxXR3w+h7JQYEqfZHTco+zj9KTm4DXCH1+H2Ur8cNoQsC+II4L9TLWXVfqhyEsXcXQffEFIRWqLA1nyAhBgszP8VtFPqlPz5YRgKqI86fFae/yWt6aFy5i/ZoSl+ujwja4WNs4CHy28SH3xdFYpLbXI17kSjOEgew11Jlw+YHXf+Slheag+SUdjC8NXkKOeMk2MuyRy2pPvdZTGuCDhA4rmnunpN+Yuk/QaitBVZXe5QCIil3t8n0AWDttnmIimBTE+UzpCQtAvKE8KWrrpZQZ+tXtJS7dYWIdQmodFdigRoWPI9y4gDskmrhca7XMf6eT2FXhP9tJDgO0RCmiczn1iMNsGh/g209BzTXX5YL6NwCuIp+CCmtbzxdjc34w8wIQT8ndFMPfcVGLY0G+4MeO04/Q/P35g2OQ6yL6z0A=='
   *  }
   * }
   * @returns
   */
  @Post()
  @ApiOperation({ summary: '获取微信第三方平台推送的消息与事件' })
  @ApiOkResponse()
  async PostWxThirdCallBack(@Req() req: FastifyRequest, @Res() res: FastifyReply) {
    const body = req.body as callbackBody
    const wx_encrypt_decrypt_key = process.env.WX_ENCRYPT_DECRYPT_KEY
    // 微信消息体解密
    let appidExtracted = ''
    const decrypted = Cryptography.aesDecrypt(body.xml.Encrypt, wx_encrypt_decrypt_key, (appid) => {
      appidExtracted = appid
    })

    // 创建 XML 解析器实例
    const xmlParser = new XMLParser()
    const result = xmlParser.parse(decrypted)

    //票据推送
    if (result.xml.InfoType == 'component_verify_ticket') {
      const wxComponentVerifyTicketTime = WxCacheKey.ComponentVerifyTicketTime
      //设置ticket票据
      await this.cacheManager.set(
        WxCacheKey.ComponentVerifyTicket,
        result.xml.ComponentVerifyTicket,
        wxComponentVerifyTicketTime
      )
    }

    if (result.xml.InfoType == 'authorized') {
      //todo:: 暂时不处理，走前端接口上报
      console.log('授权通知结果：' + JSON.stringify(result))
    }
    if (result.xml.InfoType == 'unauthorized') {
      await this.loggerService.info(null, '取消授权通知结果：', { data: JSON.stringify(result) })
      await this.wxThirdAuthService.cancelAuth(result.xml.AuthorizerAppid)
    }
    if (result.xml.InfoType == 'updateauthorized') {
      await this.loggerService.info(null, '更新授权通知结果：', { data: JSON.stringify(result) })
      await this.wxThirdAuthService.updateAuthInfomation(result.xml.AuthorizerAppid)
    }
    res.status(200).send('success')
  }

  /**
   * 微信公众号和小程序平台推送的消息与事件
   * 返回结构体
   * const body = {
   *  xml: {
   *    ToUserName: 'gh_c6e61f562741',
   *    Encrypt:
   *      'WhzAYKGwWdP11b/QR8ZdFX8qH5ngjODx6rEfVM87Qbk71++SaKz6HDhdPJzK0fh9yThwnuiq5CbdJqabHwov3bkmOMbKREBcm4Jnk4Cu1WBa9junBsQeH1jllEsEO1t4EXjYHif9RZ1EVaOro60c6ijZJ2qfdJE+fsOUn8hT26coVJ/nbPPRgjUphxcu7ZHCWVBDoMqrb37kFINyJjVeBolYhVG4kofja7FfXZ8ULFXzMEkZQW18bb5J1e/dIwWL54wMwtEkXYrIzj9TyVU3F30EONE4dkoGj5LKdTXEwTFnHuVGBo7Hc1vPUEbHHp25hYWeuuhVRs5uzBIH4JAPTvlIMYTrp0kakfZdK5cFbSEkvMfpPOVUwJ+FuWXfCX+2r4Xm0vcH3gJmCnheXOhwd4yUSd/EvwWzp+191bMsuvtMxTg0C6L5adZX3ea4SJ4uMooXKf9EEnNFBQjH4wCz6LhrnUBK2p0+Ih7mdh5TPBY6ONp7XqpQiojsXoobixSVwSEMgSpqSncjK6rIkl5QpDhW9Nmv9TNM6Lh7oTjsDA5kPKtjhrftmLj6KvoYPFsodIsS2Z/afG728WL3K+wY1Q=='
   *  }
   * }
   * @param appId 应用ID
   */
  @Post(':APPID')
  @ApiOperation({ summary: '获取微信公众号和小程序平台推送的消息与事件' })
  @ApiOkResponse()
  async PostWxAppCallBack(
    @Param('APPID') appId: string,
    @Req() req: FastifyRequest,
    @Res() res: FastifyReply
  ) {
    const body = req.body as wxAppCallbackBody
    const wx_encrypt_decrypt_key = process.env.WX_ENCRYPT_DECRYPT_KEY

    // 微信消息体解密
    let appidExtracted = ''
    const decrypted = Cryptography.aesDecrypt(body.xml.Encrypt, wx_encrypt_decrypt_key, (appid) => {
      appidExtracted = appid
    })
    // 创建 XML 解析器实例
    const xmlParser = new XMLParser()
    const result = xmlParser.parse(decrypted)

    if (result.xml.Event == 'PUBLISHJOBFINISH') {
      await this.loggerService.info(null, '微信公众号发布数据结果：', {
        data: JSON.stringify(result)
      })
      await this.wxPublishService.updateWxPublishStatus(
        result.xml.PublishEventInfo.publish_id,
        result.xml.PublishEventInfo.publish_status,
        result.xml.PublishEventInfo?.article_id ?? null,
        result.xml.PublishEventInfo?.article_detail?.item
          ? JSON.stringify(result.article_detail?.item)
          : null
      )
    }

    if (result.xml.Event == 'MASSSENDJOBFINISH') {
      await this.loggerService.info(null, '微信公众号群发数据结果：', {
        data: JSON.stringify(result)
      })
      await this.wxPublishService.updateWxSendAllStatus(result.xml.MsgID, result.xml.Status)
    }
    res.status(200).send('success')
  }
}
