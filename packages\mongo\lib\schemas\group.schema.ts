import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from "@nestjs/mongoose"
import { extend } from "dayjs"
import mongoose, { Types } from "mongoose"
import { PlatformAccountEntity, PlatformAccountSchema } from "./platform_accounts.schema"

@Schema({
    timestamps: true,
    versionKey: false,
    toJSON: {
        transform(_, ret) {
        ret.id = ret._id
        delete ret._id
        }
    }
})

export class GroupEntity {

    @Prop({
        type: String,
        required: true
    })
    name: string

    @Prop({
        type: Types.ObjectId,
        required: true
    })
    userId:Types.ObjectId

    @Prop({
        type: Types.ObjectId,
        index: true,
        required: true
    })
    teamId: Types.ObjectId

    @Prop({
        type: [String]
    })
    accounts: string[]

    @Prop({
        type: Date
    })
    createdAt?: Date

    @Prop({
        type: Date
    })
    updatedAt?: Date
}

export const GroupSchema: ModelDefinition = {
    name: GroupEntity.name,
    schema: SchemaFactory.createForClass(GroupEntity)
}

export const GroupMongoose = MongooseModule.forFeature([GroupSchema])
