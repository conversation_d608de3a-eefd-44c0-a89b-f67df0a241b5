export function genSocketRedisKey(key: string | number) {
  return `${key}:socket`
}

//获取团队用户ID在线key
export function getTeamOnlineUsersRedisKey(key: string | number) {
  return `${key}:online_users`
}

//客服二维码缓存key
export function getCustomerQrRedisKey(key: string) {
  return `customers:${key}`
}

//设备连接房间
export function socketDeviceRoomName(deviceType: string) {
  return `device:${deviceType}`
}

export const RedisTTLNoExpiration = 0
