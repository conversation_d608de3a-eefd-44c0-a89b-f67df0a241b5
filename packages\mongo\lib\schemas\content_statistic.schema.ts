import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { ContentType } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
/**
 * 作品统计数据
 */
export class ContentStatisticEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  //发布人ID
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: false
  })
  publishUserId?: Types.ObjectId

  //账号ID
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: String,
    required: true
  })
  platformName: string

  @Prop({
    type: String,
    index: true,
    default: null
  })
  title?: string

  @Prop({
    type: String,
    index: true,
    default: null
  })
  desc?: string

  @Prop({
    type: String,
    index: true,
    enum: ContentType,
    default: ContentType.article
  })
  contentType: ContentType

  @Prop({
    type: Types.Map
  })
  contentData: unknown

  //推荐数
  @Prop({
    type: Number
  })
  reCommand: number

  //播放数
  @Prop({
    type: Number
  })
  play: number

  //阅读数
  @Prop({
    type: Number
  })
  read: number

  //点赞
  @Prop({
    type: Number
  })
  great: number

  //评论
  @Prop({
    type: Number
  })
  comment: number

  //分享
  @Prop({
    type: Number
  })
  share: number

  //收藏
  @Prop({
    type: Number
  })
  collect: number

  //新增粉丝
  @Prop({
    type: Number
  })
  addFans: number

  //完播率
  @Prop({
    type: Number
  })
  finishPlay: number

  //爱心
  @Prop({
    type: Number
  })
  love: number

  //弹幕
  @Prop({
    type: Number
  })
  danmu: number

  //硬币
  @Prop({
    type: Number
  })
  coin: number

  /**
   * 喜欢
   */
  @Prop({
    type: Number
  })
  like?: number

  /**
   * 投票数
   */
  @Prop({
    type: Number
  })
  vote?: number

  /**
   * 播放时长
   */
  @Prop({
    type: Number
  })
  playTime?: number

  @Prop({
    type: String
  })
  publishId?: string

  //发布时间
  @Prop({
    type: Date
  })
  publishTime?: Date

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const ContentStatisticSchema: ModelDefinition = {
  name: ContentStatisticEntity.name,
  schema: SchemaFactory.createForClass(ContentStatisticEntity)
}

export const ContentStatisticMongoose = MongooseModule.forFeature([ContentStatisticSchema])
