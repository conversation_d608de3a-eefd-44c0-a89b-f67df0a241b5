import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class FavoritesGroupItemEntity {
  /**
   * 来源主键ID originalId = browserId 说明来自browser表 不等于则来自browser_favorites表
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  originalId: Types.ObjectId

  /**
   * 空间ID
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  browserId: Types.ObjectId

  /**
   * 空间ID
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: false
  })
  accountId?: Types.ObjectId

  /**
   * 网站地址
   */
  @Prop({
    type: String,
    required: false
  })
  websiteUrl?: string

  /**
   * 收藏分组Id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  groupId: Types.ObjectId

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const FavoritesGroupItemSchema: ModelDefinition = {
  name: FavoritesGroupItemEntity.name,
  schema: SchemaFactory.createForClass(FavoritesGroupItemEntity)
}

export const FavoritesGroupItemMongoose = MongooseModule.forFeature([FavoritesGroupItemSchema])
