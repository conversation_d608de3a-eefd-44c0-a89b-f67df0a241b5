import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'

export class TodayPushesCountDTO {
  @ApiProperty({
    type: Number,
    description: '发布次数',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  count: Number

  @ApiProperty({
    type: Boolean,
    description: '是否可以发布',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  pushable: Boolean
}

export class TodayPushesCountResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TodayPushesCountDTO
  })
  data: TodayPushesCountDTO
}
