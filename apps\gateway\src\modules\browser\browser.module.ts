import { Module } from '@nestjs/common'
import {
  BrowserFavoritesMongoose,
  BrowserGroupMongoose,
  BrowserMongoose,
  FavoritesGroupItemMongoose,
  FavoritesGroupMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  TeamMongoose
} from '@yxr/mongo'
import { WebhookModule } from '../webhook/webhook.module'
import { BrowserCollectService } from './browser-collect.service'
import { CollectGroupController } from './collect-group.controller'
import { CollectGroupService } from './collect-group.service'
import { BrowsersDeleteChangedListener } from './browsers-delete.changed.listeners'
import { TeamModule } from '../team/team.module'

@Module({
  imports: [
    BrowserMongoose,
    BrowserGroupMongoose,
    BrowserFavoritesMongoose,
    FavoritesGroupMongoose,
    FavoritesGroupItemMongoose,
    PlatformAccountMongoose,
    TeamMongoose,
    MemberMongoose,
    WebhookModule,
    TeamModule
  ],
  controllers: [CollectGroupController],
  providers: [
    BrowserCollectService,
    CollectGroupService,
    BrowsersDeleteChangedListener
  ],
  exports: [BrowserCollectService]
})
export class BrowserModule {}
