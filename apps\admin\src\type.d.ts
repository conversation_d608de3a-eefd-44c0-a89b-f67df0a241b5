import { AdminEntity, OpenPlatformUserEntity } from "@yxr/mongo";

declare module 'fastify' {
  interface FastifyRequest {
    user: AdminEntity | OpenPlatformUserEntity
    authorization: string

    /**
     * 统一的用户会话信息
     */
    session?: {
      /**
       * 用户ID
       */
      userId: string

      /**
       * 用户类型：admin | open-platform | application
       */
      userType: 'admin' | 'open-platform' | 'application'

      /**
       * 团队ID（开放平台用户可能为空）
       */
      teamId?: string

      /**
       * 应用ID（开放平台用户特有）
       */
      applicationId?: string

      /**
       * 应用appId（应用Token特有）
       */
      appId?: string
    }

    /**
     * 数据隔离信息（应用Token访问时使用）
     */
    dataIsolation?: {
      /**
       * 应用的appId
       */
      sourceAppId: string

      /**
       * 应用的数据库ID
       */
      applicationId: string
    }
  }
}