/**
 * 海外平台连接器工具函数测试
 */

import { 
  createOverseasContext,
  batchApiCalls
} from '../../src/providers/utils'

describe('海外平台连接器工具函数', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('createOverseasContext', () => {
    it('应该创建基础的海外平台上下文', () => {
      const context = createOverseasContext('tiktok')

      expect(context.platform).toBe('tiktok')
      expect(context.accountOpenId).toBeUndefined()
      expect(context.teamId).toBeUndefined()
      expect(context.userId).toBeUndefined()
      expect(context.options).toBeUndefined()
    })

    it('应该创建包含完整信息的上下文', () => {
      const context = createOverseasContext('tiktok', {
        accountOpenId: 'account-123',
        teamId: 'team-456',
        userId: 'user-789',
        options: {
          credentials: {
            access_token: 'token-123',
            refresh_token: 'refresh-456'
          },
          customOption: 'value'
        }
      })

      expect(context.platform).toBe('tiktok')
      expect(context.accountOpenId).toBe('account-123')
      expect(context.teamId).toBe('team-456')
      expect(context.userId).toBe('user-789')
      expect(context.options?.credentials?.access_token).toBe('token-123')
      expect(context.options?.credentials?.refresh_token).toBe('refresh-456')
      expect(context.options?.customOption).toBe('value')
    })

    it('应该支持不同的平台', () => {
      const platforms = ['tiktok', 'facebook', 'instagram', 'twitter', 'youtube']

      platforms.forEach(platform => {
        const context = createOverseasContext(platform, {
          accountOpenId: `${platform}-account`
        })

        expect(context.platform).toBe(platform)
        expect(context.accountOpenId).toBe(`${platform}-account`)
      })
    })

    it('应该正确处理空的选项对象', () => {
      const context = createOverseasContext('tiktok', {})

      expect(context.platform).toBe('tiktok')
      expect(context.accountOpenId).toBeUndefined()
      expect(context.teamId).toBeUndefined()
      expect(context.userId).toBeUndefined()
      expect(context.options).toBeUndefined()
    })
  })

  describe('batchApiCalls', () => {
    it('应该成功执行批量API调用', async () => {
      const mockCalls = [
        jest.fn().mockResolvedValue({ data: 'result1' }),
        jest.fn().mockResolvedValue({ data: 'result2' }),
        jest.fn().mockResolvedValue({ data: 'result3' })
      ]

      const results = await batchApiCalls(mockCalls, 2)

      expect(results).toHaveLength(3)
      expect(results[0].success).toBe(true)
      expect(results[0].data).toEqual({ data: 'result1' })
      expect(results[1].success).toBe(true)
      expect(results[1].data).toEqual({ data: 'result2' })
      expect(results[2].success).toBe(true)
      expect(results[2].data).toEqual({ data: 'result3' })

      // 验证所有调用都被执行
      mockCalls.forEach(call => {
        expect(call).toHaveBeenCalledTimes(1)
      })
    })

    it('应该正确处理失败的API调用', async () => {
      const error1 = new Error('API调用1失败')
      const error2 = new Error('API调用3失败')

      const mockCalls = [
        jest.fn().mockRejectedValue(error1),
        jest.fn().mockResolvedValue({ data: 'result2' }),
        jest.fn().mockRejectedValue(error2)
      ]

      const results = await batchApiCalls(mockCalls, 2)

      expect(results).toHaveLength(3)
      
      expect(results[0].success).toBe(false)
      expect(results[0].error).toBe(error1)
      expect(results[0].data).toBeUndefined()

      expect(results[1].success).toBe(true)
      expect(results[1].data).toEqual({ data: 'result2' })
      expect(results[1].error).toBeUndefined()

      expect(results[2].success).toBe(false)
      expect(results[2].error).toBe(error2)
      expect(results[2].data).toBeUndefined()
    })

    it('应该支持自定义并发数', async () => {
      const mockCalls = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockResolvedValue('result2'),
        jest.fn().mockResolvedValue('result3'),
        jest.fn().mockResolvedValue('result4'),
        jest.fn().mockResolvedValue('result5')
      ]

      // 使用并发数为1，确保串行执行
      const startTime = Date.now()
      const results = await batchApiCalls(mockCalls, 1)
      const endTime = Date.now()

      expect(results).toHaveLength(5)
      results.forEach((result, index) => {
        expect(result.success).toBe(true)
        expect(result.data).toBe(`result${index + 1}`)
      })

      // 验证所有调用都被执行
      mockCalls.forEach(call => {
        expect(call).toHaveBeenCalledTimes(1)
      })
    })

    it('应该正确处理空的调用数组', async () => {
      const results = await batchApiCalls([], 3)

      expect(results).toHaveLength(0)
    })

    it('应该正确处理单个API调用', async () => {
      const mockCall = jest.fn().mockResolvedValue({ data: 'single-result' })

      const results = await batchApiCalls([mockCall], 3)

      expect(results).toHaveLength(1)
      expect(results[0].success).toBe(true)
      expect(results[0].data).toEqual({ data: 'single-result' })
      expect(mockCall).toHaveBeenCalledTimes(1)
    })

    it('应该使用默认并发数', async () => {
      const mockCalls = [
        jest.fn().mockResolvedValue('result1'),
        jest.fn().mockResolvedValue('result2'),
        jest.fn().mockResolvedValue('result3'),
        jest.fn().mockResolvedValue('result4')
      ]

      // 不指定并发数，应该使用默认值3
      const results = await batchApiCalls(mockCalls)

      expect(results).toHaveLength(4)
      results.forEach((result, index) => {
        expect(result.success).toBe(true)
        expect(result.data).toBe(`result${index + 1}`)
      })
    })

    it('应该正确处理混合成功和失败的情况', async () => {
      const mockCalls = [
        jest.fn().mockResolvedValue('success1'),
        jest.fn().mockRejectedValue(new Error('error1')),
        jest.fn().mockResolvedValue('success2'),
        jest.fn().mockRejectedValue(new Error('error2')),
        jest.fn().mockResolvedValue('success3')
      ]

      const results = await batchApiCalls(mockCalls, 2)

      expect(results).toHaveLength(5)
      
      expect(results[0].success).toBe(true)
      expect(results[0].data).toBe('success1')
      
      expect(results[1].success).toBe(false)
      expect(results[1].error?.message).toBe('error1')
      
      expect(results[2].success).toBe(true)
      expect(results[2].data).toBe('success2')
      
      expect(results[3].success).toBe(false)
      expect(results[3].error?.message).toBe('error2')
      
      expect(results[4].success).toBe(true)
      expect(results[4].data).toBe('success3')
    })

    it('应该正确处理异步调用的时序', async () => {
      const delays = [100, 50, 200, 30, 150]
      const mockCalls = delays.map((delay, index) => 
        jest.fn().mockImplementation(() => 
          new Promise(resolve => 
            setTimeout(() => resolve(`result${index + 1}`), delay)
          )
        )
      )

      const startTime = Date.now()
      const results = await batchApiCalls(mockCalls, 3)
      const endTime = Date.now()

      expect(results).toHaveLength(5)
      results.forEach((result, index) => {
        expect(result.success).toBe(true)
        expect(result.data).toBe(`result${index + 1}`)
      })

      // 验证并发执行确实节省了时间
      // 总延迟如果串行执行应该是 100+50+200+30+150 = 530ms
      // 并发执行应该明显少于这个时间
      expect(endTime - startTime).toBeLessThan(500)
    })
  })
})
