import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { AdminEntity, OrderEntity, TeamEntity, TeamStatisticEntity } from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import { <PERSON>ron } from '@nestjs/schedule'
import dayjs from 'dayjs'
import { AdminRole, OpenPlatformOrderResourceType, OrderStatus, SalesType } from '@yxr/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { TeamService } from '../team/team.service'
import { TlsService } from '@yxr/huoshan'
import { OpenPlatformOrderManagerService } from '@yxr/open-platform-order'

@Injectable()
export class TeamStatisticCornService implements OnModuleInit {
  private readonly lockPrefix = 'lock:'
  private lockValue: string = 'handleTeamSalesStatistic'
  private teamExpiredLockValue: string = 'handleTeamExpired'
  private teamNetworkTrafficLockValue: string = 'handleTeamNetworkTraffic'
  private syncTeamAccountCapacityLockValue: string = 'syncTeamAccountCapacity'

  constructor(
    @InjectModel(TeamStatisticEntity.name)
    private teamStatisticModel: Model<TeamStatisticEntity>,
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly loggerService: TlsService,
    private readonly teamService: TeamService,
    private readonly orderManagerService: OpenPlatformOrderManagerService
  ) {}

  async onModuleInit() {
    // const currentDate = new Date()
    // for (let i = 30; i >= 1; i--) {
    //   const prevDate = new Date(currentDate)
    //   prevDate.setDate(currentDate.getDate() - i)
    //   await this.TeamStatisticTask(dayjs(prevDate).format('YYYY-MM-DD'))
    // }
    // console.log('teamStatistic-corn-service init')
    // await this.ResetTeamNetworkTrafficCronTask()
  }

  /**
   * 每分钟处理一次过期团队
   */
  @Cron('0 * * * * *', {
    name: 'handleExpired'
  })
  async handleExpired() {
    const expiredTeams = await this.teamModel.find({
      isVip: true,
      expiredAt: { $lt: new Date() }
    })

    if (expiredTeams.length === 0) return

    if (await this.acquireLock(this.teamExpiredLockValue, 60)) {
      try {
        for (const team of expiredTeams) {
          await this.teamService.downgradeTeam(team.id)
        }
      } catch (e) {
        await this.loggerService.error(null, '团队过期定时任务处理失败', { error: e.message })
      } finally {
        await this.releaseLock(this.teamExpiredLockValue)
      }
    }
  }

  /**
   * 每日团队数据归档
   * 每天2:00执行
   */
  @Cron('0 0 2 * * *', {
    name: 'teamStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async UpdateTeamStatisticCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        const yesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
        await this.TeamStatisticTask(yesterday)
      } catch (e) {
        await this.loggerService.error(null, '每日团队销售数据定时处理失败', { error: e })
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  /**
   * 每月1号零点重置vip团队流量容量
   * 每月1号00:00 执行
   */
  @Cron('0 0 0 1 * *', {
    name: 'resetTeamNetworkTraffic',
    timeZone: 'Asia/Shanghai'
  })
  async ResetTeamNetworkTrafficCronTask() {
    if (await this.acquireLock(this.teamNetworkTrafficLockValue, 60)) {
      try {
        await this.ResertTeamNetworkTrafficTask()
      } catch (e) {
        await this.loggerService.error(null, '每月重置团队流量定时处理失败', { error: e })
      } finally {
        await this.releaseLock(this.teamNetworkTrafficLockValue)
      }
    }
  }

  async ResertTeamNetworkTrafficTask() {
    const batchSize = 500 // 每批次查询的数量
    const totalTeams = await this.teamModel.countDocuments({
      isVip: true
    })
    const totalBatches = Math.ceil(totalTeams / batchSize)
    for (let i = 0; i < totalBatches; i++) {
      const teams = await this.teamModel
        .find({
          isVip: true
        })
        .skip(i * batchSize)
        .limit(batchSize)
        .select('_id interestCount')

      for (const team of teams) {
        await this.teamModel.updateOne(
          {
            _id: new Types.ObjectId(team._id)
          },
          {
            $set: {
              networkTraffic: team.interestCount * ***********,
              useNetworkTraffic: 0
            }
          }
        )
        console.log(`已重置团队流量团队ID:${team._id} 流量${team.interestCount * ***********}`)
      }
      console.log(`已插入批次: ${i + 1}/${totalBatches}`)
    }
  }

  /**
   * 每天凌晨1点同步团队账号点数
   * 定时任务：计算所有团队的当前有效账号点数并更新到团队表
   */
  @Cron('0 1 * * *', {
    name: 'sync-team-account-capacity',
    timeZone: 'Asia/Shanghai'
  })
  async syncTeamAccountCapacity(): Promise<void> {
    if (await this.acquireLock(this.syncTeamAccountCapacityLockValue, 60)) {
      try {
        const startTime = new Date()
        this.loggerService.info(null, '开始执行团队账号点数同步任务', {})

        try {
          let processedTeams = 0
          let totalUpdatedCapacity = 0
          let errorCount = 0

          // 使用聚合查询获取所有有账号点数订单的团队及其有效订单
          const teamAccountData = await this.orderManagerService.getTeamAccountCapacityData()

          this.loggerService.info(null, '团队账号点数同步任务', {
            message: `找到${teamAccountData.length}个团队有账号点数订单，开始同步处理`
          })
          // this.logger.log(`找到${teamAccountData.length}个团队有账号点数订单，开始同步处理`)

          // 分批处理团队数据，避免一次性处理过多数据
          const batchSize = 50
          for (let i = 0; i < teamAccountData.length; i += batchSize) {
            const batch = teamAccountData.slice(i, i + batchSize)

            try {
              const batchResult =
                await this.orderManagerService.processBatchTeamAccountCapacity(batch)
              processedTeams += batchResult.processedCount
              totalUpdatedCapacity += batchResult.totalCapacity
            } catch (error) {
              errorCount++
              this.loggerService.error(
                null,
                `批次处理失败 (${i}-${i + batch.length - 1}): ${error.message}`,
                error.stack
              )
            }
          }

          const endTime = new Date()
          const duration = endTime.getTime() - startTime.getTime()

          this.loggerService.info(null, '团队账号点数同步任务', {
            message:
              `团队账号点数同步任务完成: ` +
              `处理团队=${processedTeams}个, ` +
              `更新总账号点数=${totalUpdatedCapacity}, ` +
              `错误数量=${errorCount}, ` +
              `耗时=${duration}ms`
          })
        } catch (error) {
          this.loggerService.error(null, `团队账号点数同步任务失败: ${error.message}`, {
            error: error.stack
          })
        }
      } catch (e) {
        await this.loggerService.error(null, '团队账号点数同步任务失败', { error: e })
      } finally {
        await this.releaseLock(this.syncTeamAccountCapacityLockValue)
      }
    }
  }

  /**
   * 团队业绩数据归档统计
   * @param yesterday
   */
  async TeamStatisticTask(yesterday: string) {
    const beijingStart = dayjs(yesterday).tz('Asia/Shanghai').startOf('day')
    const beijingEnd = dayjs(yesterday).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfYesterday = beijingStart.utc().toDate()
    const endOfYesterday = beijingEnd.utc().toDate()
    // 注册有效团队数
    const registerTeamCount = await this.teamModel.countDocuments({
      createdAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      isDeleted: false
    })
    // 首次付费团队数
    const paidTeamCount = await this.orderModel.countDocuments({
      createdAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      orderStatus: OrderStatus.Paid,
      salesType: SalesType.FirstBuy,
      sourceAppId: { $exists: false }
    })
    // 续费订单团队数
    const renewTeam = await this.orderModel.aggregate([
      {
        $match: {
          createdAt: {
            $gt: startOfYesterday,
            $lte: endOfYesterday
          },
          orderStatus: OrderStatus.Paid,
          salesType: SalesType.ReBuy,
          sourceAppId: { $exists: false }
        }
      },
      {
        $group: {
          _id: '$teamId' // 按 teamId 分组
        }
      }
    ])
    //过期未续费团队数
    const unRenewTeamCount = await this.teamModel.countDocuments({
      expiredAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      isDeleted: false,
      salesType: { $in: [SalesType.FirstBuy, SalesType.ReBuy] },
      sourceAppId: { $exists: false }
    })
    const renewTeamCount = renewTeam.length

    const conversionRate =
      registerTeamCount > 0 && paidTeamCount > 0
        ? parseFloat(((paidTeamCount * 100) / registerTeamCount).toFixed(2))
        : 0
    const renewRate =
      renewTeamCount > 0
        ? parseFloat(((renewTeamCount * 100) / (unRenewTeamCount + renewTeamCount)).toFixed(2))
        : 0
    const teamStatistic = await this.teamStatisticModel.findOne({
      createTime: yesterday,
      type: 0
    })
    if (teamStatistic) {
      teamStatistic.paidTeamCount = paidTeamCount
      teamStatistic.renewTeamCount = renewTeamCount
      teamStatistic.expiredTeamCount = unRenewTeamCount
      teamStatistic.registerTeamCount = registerTeamCount
      teamStatistic.renewRate = renewRate
      teamStatistic.conversionRate = conversionRate
      await teamStatistic.save()
    } else {
      await this.teamStatisticModel.create({
        createTime: yesterday,
        paidTeamCount: paidTeamCount,
        renewTeamCount: renewTeamCount,
        expiredTeamCount: unRenewTeamCount,
        registerTeamCount: registerTeamCount,
        renewRate: renewRate,
        conversionRate: conversionRate,
        type: 0
      })
    }

    // 更新三天前有效团队归档数据
    const threeDayAgo = dayjs(yesterday).tz('Asia/Shanghai').subtract(3, 'day').format('YYYY-MM-DD')
    const threeDayRegister = await this.teamStatisticModel.findOne({
      createTime: threeDayAgo
    })
    const beijingThreeStart = dayjs(threeDayAgo).tz('Asia/Shanghai').startOf('day')
    const beijingThreeEnd = dayjs(threeDayAgo).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfthreeAgo = beijingThreeStart.utc().toDate()
    const endOfthreeAgo = beijingThreeEnd.utc().toDate()
    if (threeDayRegister) {
      // 三天前的注册有效团队数
      const effectiveTeamCount = await this.teamModel.countDocuments({
        createdAt: {
          $gt: startOfthreeAgo,
          $lte: endOfthreeAgo
        },
        isDeleted: false,
        $or: [{ memberCount: { $gt: 1 } }, { accountCount: { $gt: 0 } }],
        sourceAppId: { $exists: false }
      })

      threeDayRegister.registerTeamCount = effectiveTeamCount
      threeDayRegister.conversionRate =
        effectiveTeamCount > 0 && threeDayRegister.paidTeamCount > 0
          ? parseFloat(((threeDayRegister.paidTeamCount * 100) / effectiveTeamCount).toFixed(2))
          : 0
      await threeDayRegister.save()
    }

    // 归属客服统计
    await this.CustomerTeamStatisticList(
      yesterday,
      startOfYesterday,
      endOfYesterday,
      startOfthreeAgo,
      endOfthreeAgo
    )
  }

  /**
   * 归属客服团队统计归档
   * @param yesterday
   * @param startOfYesterday
   * @param endOfYesterday
   * @param startOfthreeAgo
   * @param endOfthreeAgo
   */
  async CustomerTeamStatisticList(
    yesterday: string,
    startOfYesterday: Date,
    endOfYesterday: Date,
    startOfthreeAgo: Date,
    endOfthreeAgo: Date
  ) {
    const customers = await this.adminModel
      .find({
        role: AdminRole.Customer
      })
      .select('_id name role')
      .lean()

    for (let index = 0; index < customers.length; index++) {
      const customer = customers[index]
      await this.CustomerTeamStatistic(
        customer._id.toString(),
        yesterday,
        startOfYesterday,
        endOfYesterday,
        startOfthreeAgo,
        endOfthreeAgo
      )
    }
  }

  /**
   * 单个客服团队统计归档
   * @param customerId
   * @param yesterday
   */
  async CustomerTeamStatistic(
    customerId: string,
    yesterday: string,
    startOfYesterday: Date,
    endOfYesterday: Date,
    startOfthreeAgo: Date,
    endOfthreeAgo: Date
  ) {
    // 注册有效团队数
    const registerTeamCount = await this.teamModel.countDocuments({
      createdAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      isDeleted: false,
      customerId: new Types.ObjectId(customerId),
      sourceAppId: { $exists: false }
    })

    // 首次付费团队数
    const paidTeamCount = await this.orderModel.countDocuments({
      createdAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      salesType: SalesType.FirstBuy,
      orderStatus: OrderStatus.Paid,
      customerId: new Types.ObjectId(customerId),
      sourceAppId: { $exists: false }
    })
    // 续费订单团队数
    const renewTeam = await this.orderModel.aggregate([
      {
        $match: {
          createdAt: {
            $gt: startOfYesterday,
            $lte: endOfYesterday
          },
          salesType: SalesType.ReBuy,
          orderStatus: OrderStatus.Paid,
          customerId: new Types.ObjectId(customerId),
          sourceAppId: { $exists: false }
        }
      },
      {
        $group: {
          _id: '$teamId' // 按 teamId 分组
        }
      }
    ])
    //过期未续费团队数
    const unRenewTeamCount = await this.teamModel.countDocuments({
      expiredAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      },
      isDeleted: false,
      customerId: new Types.ObjectId(customerId),
      salesType: { $in: [SalesType.FirstBuy, SalesType.ReBuy] },
      sourceAppId: { $exists: false }
    })
    const renewTeamCount = renewTeam.length

    const conversionRate =
      registerTeamCount > 0 && paidTeamCount > 0
        ? parseFloat(((paidTeamCount * 100) / registerTeamCount).toFixed(2))
        : 0
    const renewRate =
      renewTeamCount > 0
        ? parseFloat(((renewTeamCount * 100) / (unRenewTeamCount + renewTeamCount)).toFixed(2))
        : 0
    const teamStatistic = await this.teamStatisticModel.findOne({
      createTime: yesterday,
      customerId: new Types.ObjectId(customerId),
      type: 1
    })
    if (teamStatistic) {
      teamStatistic.paidTeamCount = paidTeamCount
      teamStatistic.renewTeamCount = renewTeamCount
      teamStatistic.expiredTeamCount = unRenewTeamCount
      teamStatistic.registerTeamCount = registerTeamCount
      teamStatistic.renewRate = renewRate
      teamStatistic.conversionRate = conversionRate
      await teamStatistic.save()
    } else {
      await this.teamStatisticModel.create({
        createTime: yesterday,
        paidTeamCount: paidTeamCount,
        renewTeamCount: renewTeamCount,
        expiredTeamCount: unRenewTeamCount,
        registerTeamCount: registerTeamCount,
        renewRate: renewRate,
        conversionRate: conversionRate,
        customerId: new Types.ObjectId(customerId),
        type: 1
      })
    }

    // 更新三天前有效团队归档数据
    const threeDayAgo = dayjs(yesterday).tz('Asia/Shanghai').subtract(3, 'day').format('YYYY-MM-DD')
    const threeDayRegister = await this.teamStatisticModel.findOne({
      createTime: threeDayAgo,
      type: 1,
      customerId: new Types.ObjectId(customerId)
    })
    if (threeDayRegister) {
      // 三天前的注册有效团队数
      const effectiveTeamCount = await this.teamModel.countDocuments({
        createdAt: {
          $gt: startOfthreeAgo,
          $lte: endOfthreeAgo
        },
        isDeleted: false,
        $or: [{ memberCount: { $gt: 1 } }, { accountCount: { $gt: 0 } }],
        customerId: new Types.ObjectId(customerId),
        sourceAppId: { $exists: false }
      })

      threeDayRegister.registerTeamCount = effectiveTeamCount
      threeDayRegister.conversionRate =
        effectiveTeamCount > 0 && threeDayRegister.paidTeamCount > 0
          ? parseFloat(((threeDayRegister.paidTeamCount * 100) / effectiveTeamCount).toFixed(2))
          : 0
      await threeDayRegister.save()
    }
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
