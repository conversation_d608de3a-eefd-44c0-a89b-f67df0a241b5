import { Body, Controller, Get, Inject, Param, Put } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { WxThirdAuthService } from './wx-third-auth.service'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { PreAuthCodeResponseDTO, WxPlatformAccountRequest } from './wx-third-auth.dto'
import { PlatformAccountDetailResponseDTO } from '../platform/platform-account.dto'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'

@Controller('wx/third')
@ApiTags('媒体账号管理/微信第三方平台管理/授权管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class WxThirdAuthController {
  constructor(
    private readonly wxThirdAuthService: WxThirdAuthService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Get('pre_auth_code')
  @ApiOperation({ summary: '获取微信第三方预授权码' })
  @ApiOkResponse({ type: PreAuthCodeResponseDTO })
  async getPreAuthCode() {
    const { userId: currentUserId } = this.request.session
    return await this.wxThirdAuthService.getPreAuthCode(currentUserId)
  }

  @Put('auth')
  @ApiOperation({ summary: '更新授权账号详情信息' })
  @ApiOkResponse({ type: PlatformAccountDetailResponseDTO })
  async getAuthInformation(@Body() body: WxPlatformAccountRequest) {
    const { teamId: currentTeamId } = this.request.session
    return await this.wxThirdAuthService.getAuthInformation(currentTeamId, body.auth_code)
  }
}
