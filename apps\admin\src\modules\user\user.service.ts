import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import {
  createAdminUserRequestDto,
  AdminUserDto,
  getAdminUsersQueryDto,
  GetAdminUsersResponse,
  patchAdminUserRequestDto,
  UserLoginRequestDto,
  UserLoginResphoneDTO,
  resetPasswordRequestDto,
  putStatusRequestDto,
  CustomerResponse,
  BindMfaRequestDTO
} from './user.dto'
import { InjectModel } from '@nestjs/mongoose'
import { AdminEntity } from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import crypto from 'crypto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { nanoid } from 'nanoid'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { AdminOssService } from '../ali-oss/admin-oss.service'
import { AdminRole } from '@yxr/common'
import { getCustomerQrRedisKey, RedisTTLNoExpiration } from '@yxr/utils'
import QRCode from 'qrcode'
import speakeasy from 'speakeasy'
import { UnifiedAuthService } from '../../common/services/unified-auth.service'

@Injectable()
export class UserService {
  constructor(
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly adminOssService: AdminOssService,
    private readonly unifiedAuthService: UnifiedAuthService
  ) {}

  verifyPassword(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  /**
   * 用户登录
   * @param userLoginRequestDto
   * @returns
   */
  async putLoginUser(userLoginRequestDto: UserLoginRequestDto): Promise<UserLoginResphoneDTO> {
    const adminInfo = await this.adminModel.findOne({ username: userLoginRequestDto.username })

    if (
      !adminInfo ||
      !this.verifyPassword(userLoginRequestDto.password, adminInfo.salt, adminInfo.password)
    ) {
      throw new NotFoundException('账号或密码错误')
    }
    if (process.env.NODE_ENV === 'prod') {
      if (!adminInfo.mfaSecret) {
        const secret = speakeasy.generateSecret({ name: '蚁小二lite后台管理' })
        const encryptSecret = this.encrypt(secret.base32, adminInfo.salt)
        const url = await QRCode.toDataURL(secret.otpauth_url)

        return {
          verifyMfa: true,
          initMfa: true,
          qrcode: url,
          secret: encryptSecret,
          authorization: '',
          name: '',
          role: -1
        }
      }

      return {
        verifyMfa: true,
        initMfa: false,
        qrcode: '',
        secret: '',
        name: '',
        role: -1,
        authorization: ''
      }
    }

    const authorization = await this.unifiedAuthService.generateAdminToken(adminInfo)

    return {
      verifyMfa: false,
      initMfa: false,
      qrcode: '',
      secret: '',
      name: adminInfo.name,
      authorization: authorization,
      role: adminInfo.role
    }
  }

  async verifyMfa({ username, password, token, secret }: BindMfaRequestDTO) {
    const userInfo = await this.adminModel.findOne({
      username: username
    })

    if (!userInfo || !this.verifyPassword(password, userInfo.salt, userInfo.password)) {
      throw new NotFoundException('账号或密码错误')
    }

    if (userInfo.mfaSecret) {
      const decryptSecret = this.decrypt(userInfo.mfaSecret, userInfo.secretIv)
      const verified = speakeasy.totp.verify({
        secret: decryptSecret,
        encoding: 'base32',
        token
      })

      if (verified) {
        const authorization = await this.unifiedAuthService.generateAdminToken(userInfo)

        return {
          name: userInfo.name,
          authorization: authorization,
          role: userInfo.role
        }
      }

      throw new ForbiddenException('无效的验证码')
    } else {
      const decryptSecret = this.decrypt(secret, userInfo.salt)
      const verified = speakeasy.totp.verify({
        secret: decryptSecret,
        encoding: 'base32',
        token,
        window: 6 //允许前后各一个时间步长的偏差 6 * 30s = 3min
      })
      if (verified) {
        await this.adminModel.updateOne(
          { _id: userInfo._id },
          {
            mfaSecret: secret,
            secretIv: userInfo.salt
          }
        )
        const authorization = await this.unifiedAuthService.generateAdminToken(userInfo)

        return {
          name: userInfo.name,
          authorization: authorization,
          role: userInfo.role
        }
      }

      throw new ForbiddenException('无效的验证码')
    }
  }

  /**
   * 生成 token 并缓存
   * @deprecated 推荐使用 UnifiedAuthService.generateAdminToken，此方法保留用于向后兼容
   * @param userInfo
   */
  async generateAuthorization(userInfo: AdminEntity) {
    // 使用 UnifiedAuthService 来生成统一格式的 token，确保与 UnifiedTokenGuard 兼容
    return await this.unifiedAuthService.generateAdminToken(userInfo)
  }

  async deleteAuthorization() {
    const { user, authorization } = this.request

    if (!user) {
      throw new ForbiddenException('登录失效, 请重新登录')
    }

    if (user) {
      await Promise.all([
        // this.cacheManager.del(user.username),
        this.cacheManager.del(authorization)
      ])
    }
  }

  /**
   * 创建用户
   * @param data
   * @returns
   */
  async createAdminUser(data: createAdminUserRequestDto): Promise<AdminUserDto> {
    const admin = await this.adminModel.findOne({ username: data.username })

    if (admin) {
      throw new ForbiddenException('该管理员账号已存在')
    }

    const { salt, hash } = this.hashPassword(data.password)
    const createAdmin: AdminEntity = {
      username: data.username,
      name: data.name,
      password: hash,
      salt: salt,
      role: data.role,
      qrCode: '',
      status: data.status ?? 0
    }

    if (data.qrCode) {
      const buffer = Buffer.from(data.qrCode, 'base64')
      const envPrefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}`
      const name = `customer/qr/${envPrefix}/${Date.now()}.png`
      const url = await this.adminOssService.uploadFile(buffer, name)
      createAdmin.qrCode = url
    }

    const adminInfo = await this.adminModel.create(createAdmin)

    if (adminInfo) {
      await this.cacheManager.set(
        getCustomerQrRedisKey(adminInfo.id.toString()),
        adminInfo.qrCode,
        RedisTTLNoExpiration
      )
    }

    return {
      id: adminInfo.id,
      username: adminInfo.username,
      name: adminInfo.name,
      role: adminInfo.role,
      createdAt: adminInfo.createdAt.getTime(),
      qrCode: adminInfo.qrCode ? `${process.env.OSS_DOWNLOAD_URL}/${adminInfo.qrCode}` : '',
      status: adminInfo.status
    }
  }

  /**
   * 获取后台用户列表
   * @param query
   * @returns
   */
  async getAdminUsers(query: getAdminUsersQueryDto): Promise<GetAdminUsersResponse> {
    const items = await this.adminModel
      .find()
      .skip((query.page - 1) * query.size)
      .limit(query.size)
      .sort({ createdAt: 'desc' })
    const totalSize = await this.adminModel.countDocuments()

    return {
      totalSize,
      page: query.page,
      size: query.size,
      totalPage: Math.ceil(totalSize / query.size),
      data: items.map((item) => ({
        id: item.id,
        username: item.username,
        name: item.name,
        role: item.role,
        qrCode: item.qrCode ? `${process.env.OSS_DOWNLOAD_URL}/${item.qrCode}` : '',
        createdAt: item.createdAt.getTime(),
        status: item.status
      }))
    }
  }

  /**
   * 删除后台用户
   * @param id
   */
  async deleteAdminUser(id: string): Promise<void> {
    const result = await this.adminModel.findById(new Types.ObjectId(id))
    if (!result) {
      throw new NotFoundException('数据不存在')
    }
    if (result.role === AdminRole.SuperAdmin) {
      throw new ForbiddenException('超管账号不能删除')
    }
    await this.adminModel.deleteOne({
      _id: result._id
    })
    if (result.qrCode) {
      await this.adminOssService.deleteOssObject(result.qrCode)
    }
  }

  /**
   * 获取后台用户详情
   * @param id
   * @returns
   */
  async getAdminUser(id: string): Promise<AdminUserDto> {
    const adminInfo = await this.adminModel.findById(new Types.ObjectId(id))

    if (!adminInfo) {
      throw new NotFoundException('数据不存在')
    }

    return {
      id: adminInfo.id,
      username: adminInfo.username,
      name: adminInfo.name,
      role: adminInfo.role,
      createdAt: adminInfo.createdAt.getTime(),
      qrCode: adminInfo.qrCode ? `${process.env.OSS_DOWNLOAD_URL}/${adminInfo.qrCode}` : '',
      status: adminInfo.status
    }
  }

  /**
   * 修改后台用户信息
   * @param id
   * @param data
   */
  async patchAdminUser(id: string, data: patchAdminUserRequestDto) {
    const adminInfo = await this.adminModel.findById(new Types.ObjectId(id))

    if (!adminInfo) {
      throw new NotFoundException('数据不存在')
    }
    if (data.name) {
      adminInfo.name = data.name
    }
    if (data.qrCode) {
      if (adminInfo.qrCode) {
        await this.adminOssService.deleteOssObject(adminInfo.qrCode)
      }
      const buffer = Buffer.from(data.qrCode, 'base64')
      const envPrefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}`
      const name = `customer/qr/${envPrefix}/${Date.now()}.png`
      const url = await this.adminOssService.uploadFile(buffer, name)
      adminInfo.qrCode = url

      await this.cacheManager.set(
        getCustomerQrRedisKey(adminInfo.id.toString()),
        adminInfo.qrCode,
        RedisTTLNoExpiration
      )
    }
    if ((data.status === 0 || data.status === 1) && adminInfo.role === AdminRole.Customer) {
      adminInfo.status = data.status
    }
    await adminInfo.save()
  }

  /**
   * 重置密码
   * @param id
   * @param data
   * @returns
   */
  async resetPassword(id: string, data: resetPasswordRequestDto) {
    const adminInfo = await this.adminModel.findById(new Types.ObjectId(id))
    if (!adminInfo) {
      throw new NotFoundException('数据不存在')
    }
    if (data.password) {
      const { salt, hash } = this.hashPassword(data.password)
      adminInfo.password = hash
      adminInfo.salt = salt
    }
    await adminInfo.save()
  }

  /**
   * 更新状态
   * @param id
   * @param data
   */
  async putStatus(id: string, data: putStatusRequestDto) {
    const adminInfo = await this.adminModel.findById(new Types.ObjectId(id))
    if (!adminInfo) {
      throw new NotFoundException('数据不存在')
    }
    if (adminInfo.role !== AdminRole.Customer) {
      throw new ForbiddenException('客服角色才能修改分配状态')
    }
    if (data.status === 0 || data.status === 1) {
      adminInfo.status = data.status
    }
    await adminInfo.save()
  }

  /**
   * 客服列表
   * @returns
   */
  async getCustomers(): Promise<CustomerResponse[]> {
    const result = await this.adminModel.find({
      role: AdminRole.Customer
    })
    return result.map((item) => ({
      id: item.id,
      name: item.name
    }))
  }

  private decrypt(encryptedDataStr: string, secret: string) {
    try {
      const encryptDatatoArray = encryptedDataStr.split(':')

      // 将 Base64 编码的加密数据转为 Buffer
      const encryptedData = encryptDatatoArray[1]

      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        secret,
        Buffer.from(encryptDatatoArray[0], 'base64')
      )
      // 解密数据
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      // 返回解密后的字符串
      return decrypted
    } catch (err) {
      throw new BadRequestException(`[解密数据失败]:${err.message}`)
    }
  }

  private encrypt(plaintext: string, key: crypto.CipherKey) {
    try {
      const iv = crypto.randomBytes(16)

      // 创建 AES 加密器（使用 AES-128-CBC）
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
      let encrypted = cipher.update(plaintext, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      // 返回加密后的数据和 IV（Base64 编码）
      return iv.toString('base64') + ':' + encrypted
    } catch (err) {
      throw new BadRequestException(`[加密数据失败]:${err.message}`)
    }
  }
}
