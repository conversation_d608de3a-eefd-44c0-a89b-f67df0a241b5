/**
 * Facebook平台特定错误处理器
 */

import { AxiosResponse } from 'axios'
import { BusinessErrorChecker, OverseasContext } from '../../utils/axios-config'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  extractRequestInfo,
  extractResponseInfo
} from '../../utils/error-handler'

/**
 * Facebook API响应接口
 */
export interface FacebookApiResponse {
  error?: {
    message: string
    type: string
    code: number
    error_subcode?: number
    fbtrace_id?: string
  }
}

/**
 * Facebook业务错误检查器
 */
export class FacebookBusinessErrorChecker implements BusinessErrorChecker {
  /**
   * 检查Facebook响应是否包含业务错误
   * Facebook API在出现业务错误时会在响应中包含error字段
   */
  hasBusinessError(response: AxiosResponse<FacebookApiResponse>): boolean {
    return response.data && response.data.error !== undefined
  }

  /**
   * 处理Facebook业务错误
   * 根据Facebook错误码映射到标准错误类型
   */
  async handleBusinessError(response: AxiosResponse<FacebookApiResponse>, context: OverseasContext): Promise<never> {
    const data = response.data

    if (!data || !data.error) {
      // 不应该调用这个方法，因为没有错误
      throw new RemoteApiError(
        context.platform,
        RemoteApiErrorCodes.UnknownRemoteApiError,
        { message: '意外调用了错误处理方法，但API返回成功' },
        extractRequestInfo(response),
        extractResponseInfo(response),
        context
      )
    }

    const error = data.error
    let errorCode: RemoteApiErrorCodes

    // 根据Facebook错误码映射到标准错误类型
    switch (error.code) {
      // 访问令牌相关错误
      case 190: // Invalid access token
        errorCode = RemoteApiErrorCodes.AccessTokenInvalid
        break
      case 102: // Session key invalid
        errorCode = RemoteApiErrorCodes.AccessTokenExpired
        break

      // 权限相关错误
      case 200: // Permissions error
        errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
        break

      // 限流相关错误
      case 4: // Application request limit reached
      case 17: // User request limit reached
        errorCode = RemoteApiErrorCodes.RateLimitExceeded
        break

      // 参数错误
      case 100: // Invalid parameter
        errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
        break

      // 内容相关错误
      case 368: // Content violates policy
        errorCode = RemoteApiErrorCodes.ContentViolation
        break

      // 账号相关错误
      case 341: // Account temporarily unavailable
        errorCode = RemoteApiErrorCodes.AccountSuspended
        break

      // 服务器错误
      case 1: // API Unknown
      case 2: // API Service
        errorCode = RemoteApiErrorCodes.ServerError
        break

      // 未知错误
      default:
        errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      {
        message: error.message,
        facebookErrorCode: error.code,
        facebookErrorType: error.type,
        facebookErrorSubcode: error.error_subcode,
        facebookTraceId: error.fbtrace_id
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    console.warn(`[Facebook] API业务错误`, {
      errorCode: apiError.errorCode,
      facebookCode: error.code,
      facebookType: error.type,
      message: error.message,
      traceId: error.fbtrace_id,
      context: context,
      requestInfo: apiError.requestInfo,
      responseInfo: apiError.responseInfo
    })

    throw apiError
  }
}

/**
 * 创建Facebook业务错误检查器实例
 */
export function createFacebookBusinessErrorChecker(): BusinessErrorChecker {
  return new FacebookBusinessErrorChecker()
}

/**
 * Facebook错误码到错误类型的映射表（用于文档和调试）
 */
export const FACEBOOK_ERROR_CODE_MAPPING = {
  // 令牌错误
  190: 'ACCESS_TOKEN_INVALID',
  102: 'ACCESS_TOKEN_EXPIRED',

  // 权限错误
  200: 'SCOPE_NOT_AUTHORIZED',

  // 限流错误
  4: 'RATE_LIMIT_EXCEEDED',
  17: 'RATE_LIMIT_EXCEEDED',

  // 参数错误
  100: 'REQUEST_PARAMETERS_INCORRECT',

  // 内容错误
  368: 'CONTENT_VIOLATION',

  // 账号错误
  341: 'ACCOUNT_SUSPENDED',

  // 服务器错误
  1: 'SERVER_ERROR',
  2: 'SERVER_ERROR'
} as const

/**
 * 获取Facebook错误码的描述信息
 */
export function getFacebookErrorDescription(code: number): string {
  const mapping = FACEBOOK_ERROR_CODE_MAPPING as Record<number, string>
  return mapping[code] || 'UNKNOWN_ERROR'
}
