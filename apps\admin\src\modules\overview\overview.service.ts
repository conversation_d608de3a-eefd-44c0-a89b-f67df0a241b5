import { Inject, Injectable } from '@nestjs/common'
import {
  AccountScaleDTO,
  AccountStatistic,
  DataScaleDTO,
  DataStatistic,
  DataStatisticDTO,
  OnlineScaleDTO,
  PlatformPublishScaleDTO,
  RegisterScaleDTO,
  TeamStatisticDTO,
  TeamStatisticListRequest
} from './overview.dto'
import { InjectModel } from '@nestjs/mongoose'
import {
  BrowserEntity,
  BrowserPublishStatisticEntity,
  ContentEntity,
  DataStatisticEntity,
  LoginStatus,
  MemberEntity,
  PlatformAccountEntity,
  PlatformDataStatisticEntity,
  TaskEntity,
  TeamEntity,
  TeamStatisticEntity,
  UserEntity
} from '@yxr/mongo'
import { FilterQuery, Model, Types } from 'mongoose'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import dayjs from 'dayjs'
import {
  AllPlatforms,
  OverviewDateTypeEnum,
  PlatformNameEnum,
  SalesType,
  StatisticCommonService,
  TeamRoleNames
} from '@yxr/common'

@Injectable()
export class OverviewService {
  constructor(
    @InjectModel(TaskEntity.name) private temporaryTaskModel: Model<TaskEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(DataStatisticEntity.name) private dataStatisticModel: Model<DataStatisticEntity>,
    @InjectModel(BrowserPublishStatisticEntity.name)
    private browserPublishStatisticModel: Model<BrowserPublishStatisticEntity>,
    @InjectModel(TeamStatisticEntity.name)
    private teamStatisticModel: Model<TeamStatisticEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(BrowserEntity.name)
    private browserModel: Model<BrowserEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformDataStatisticEntity.name)
    private platformDataStatisticModel: Model<PlatformDataStatisticEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly statisticCommonService: StatisticCommonService
  ) {}

  async getTasksByDate(type: OverviewDateTypeEnum) {
    const { startDate, endDate, days } = this.getDateRange(type)

    const cacheKey = 'push-tasks-' + days

    const cacheData = await this.cacheManager.get(cacheKey)

    if (cacheData) {
      return { data: cacheData }
    }

    const res = await this.temporaryTaskModel.aggregate([
      {
        $match: {
          createdAt: {
            $gte: startDate,
            $lte: endDate
          }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      }
    ])

    let initData = this.statisticCommonService.generateDates(days)

    const data = initData.map((item) => ({
      date: item.date,
      count: res.find((i) => i._id === item.date)?.count ?? 0
    }))

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    await this.cacheManager.set(cacheKey, data, taskOverdueToken)

    return { data }
  }

  async getUsersByDate(type: OverviewDateTypeEnum) {
    const { startDate, endDate, days } = this.getDateRange(type)

    const cacheKey = 'activite-users-' + days

    const cacheData = await this.cacheManager.get(cacheKey)

    if (cacheData) {
      return { data: cacheData }
    }

    const res = await this.temporaryTaskModel.aggregate([
      {
        $match: {
          createdAt: {
            $gte: startDate,
            $lte: endDate
          }
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
          },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          _id: 0,
          date: '$_id.date',
          count: { $size: '$uniqueUsers' }
        }
      }
    ])

    const resDateMap = res.reduce((acc, item) => {
      acc[item.date] = item?.count ?? 0
      return acc
    }, {})
    let initData = this.statisticCommonService.generateDates(days)

    const data = initData.map((item) => ({
      date: item.date,
      count: resDateMap[item.date] ?? item.count
    }))

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    await this.cacheManager.set(cacheKey, data, taskOverdueToken)

    return { data }
  }

  /**
   * 根据天数返回统计数据
   * @param type
   * @returns
   */
  async getDataStatistic(type: OverviewDateTypeEnum): Promise<DataStatisticDTO> {
    const { days } = this.getDateRange(type)

    const cacheKey = 'data-statistic-' + days
    const cacheData = await this.cacheManager.get<DataStatistic[]>(cacheKey)
    if (cacheData) {
      return {
        data: cacheData
      }
    }

    const date = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
    const res = await this.dataStatisticModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: date
          }
        }
      },
      {
        $project: {
          createTime: 1,
          registerCount: 1,
          teamPublishCount: 1,
          appUserPublishCount: 1,
          publishCount: 1
        }
      }
    ])

    const resDateMap = res.reduce((acc, item) => {
      acc[item.createTime] = item
      return acc
    }, {})
    let initData = this.statisticCommonService.generateDates(days)

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(
        cacheKey,
        initData.map((item) => ({
          date: item.date,
          registerCount: resDateMap[item.date]?.registerCount ?? 0,
          teamPublishCount: resDateMap[item.date]?.teamPublishCount ?? 0,
          appUserPublishCount: resDateMap[item.date]?.appUserPublishCount ?? 0,
          publishCount: resDateMap[item.date]?.publishCount ?? 0
        })),
        taskOverdueToken
      )
    }

    return {
      data: initData.map((item) => ({
        date: item.date,
        registerCount: resDateMap[item.date]?.registerCount ?? 0,
        publishCount: resDateMap[item.date]?.publishCount ?? 0,
        teamPublishCount: resDateMap[item.date]?.teamPublishCount ?? 0,
        appUserPublishCount: resDateMap[item.date]?.appUserPublishCount ?? 0
      }))
    }
  }

  async getAccountStatistic(): Promise<AccountStatistic[]> {
    const { days } = this.getDateRange(OverviewDateTypeEnum.Thirty)

    const platforms = AllPlatforms
    const cacheKey = 'account-statistic-' + days
    const cacheData = await this.cacheManager.get<AccountStatistic[]>(cacheKey)
    if (cacheData) {
      return cacheData
    }
    const accountStatistic: AccountStatistic[] = []
    const initData = this.statisticCommonService.generateDates(days)
    const date = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
    const res = await this.platformDataStatisticModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: date
          }
        }
      },
      {
        $project: {
          createTime: 1,
          platformName: 1,
          incrementAccountTotal: 1
        }
      }
    ])

    for (const platform of platforms) {
      const platformData = res.filter((pd) => pd.platformName === platform)
      const resDateMap = platformData.reduce((acc, item) => {
        acc[item.createTime] = item
        return acc
      }, {})
      accountStatistic.push({
        platformName: platform,
        trendData: initData.map((item) => ({
          date: item.date,
          incrementAccountTotal: resDateMap[item.date]?.incrementAccountTotal ?? 0
        }))
      })
    }

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(cacheKey, accountStatistic, taskOverdueToken)
    }

    return accountStatistic
  }

  async getDataScale(): Promise<DataScaleDTO> {
    const cacheKey = 'data-scale-statistic'
    const cacheData = await this.cacheManager.get<DataScaleDTO>(cacheKey)
    if (cacheData) {
      return cacheData
    }

    const result = await this.contentModel.aggregate([
      {
        $group: {
          _id: '$publishType',
          count: { $sum: 1 }
        }
      }
    ])
    const resDateMap = result.reduce((acc, item) => {
      acc[item._id] = item.count
      return acc
    }, {})

    const browserPublish = await this.browserPublishStatisticModel.aggregate([
      {
        // 分组并求和
        $group: {
          _id: null,
          totalSum: { $sum: '$publishCount' }
        }
      }
    ])

    const appPublish = await this.contentModel.aggregate([
      {
        $match: {
          isAppContent: true
        }
      },
      {
        $group: {
          _id: null,
          totalSum: { $sum: 1 }
        }
      }
    ])
    const oneClickPublish = await this.contentModel.aggregate([
      {
        $match: {
          isAppContent: false
        }
      },
      {
        $group: {
          _id: null,
          totalSum: { $sum: 1 }
        }
      }
    ])

    const platformAccountNum = await this.platformAccountModel.countDocuments()
    const browserNum = await this.browserModel.countDocuments({
      accountId: { $exists: false }
    })
    const browserPublishNum = browserPublish[0]?.totalSum ?? 0 //浏览器发布数
    const appPublishNum = appPublish[0]?.totalSum ?? 0 //app发布数
    const oneClickPublishNum = oneClickPublish[0]?.totalSum ?? 0 //客户端一键发布数
    const verticalVideoNum = resDateMap?.verticalVideo ?? 0 //竖版视频
    const horizonVideoNum = resDateMap?.horizonVideo ?? 0 //横版视频
    const videoNum = resDateMap?.video ?? 0 //视频
    const textPublishNum = resDateMap?.imageText ?? 0
    const articlePublishNum = resDateMap?.article ?? 0

    const videoPublishNum = verticalVideoNum + horizonVideoNum + videoNum
    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(
        cacheKey,
        {
          videoPublishNum: videoPublishNum,
          textPublishNum: textPublishNum,
          articlePublishNum: articlePublishNum,
          oneClickPublishNum: oneClickPublishNum,
          appPublishNum: appPublishNum,
          browserPublishNum: browserPublishNum,
          browserNum: browserNum,
          platformAccountNum: platformAccountNum
        },
        taskOverdueToken
      )
    }

    return {
      videoPublishNum: videoPublishNum,
      textPublishNum: textPublishNum,
      articlePublishNum: articlePublishNum,
      oneClickPublishNum: oneClickPublishNum,
      appPublishNum: appPublishNum,
      browserPublishNum: browserPublishNum,
      browserNum: browserNum,
      platformAccountNum: platformAccountNum
    }
  }

  async getRegisterScale(type?: OverviewDateTypeEnum): Promise<RegisterScaleDTO> {
    let days = 0
    if (type) {
      days = 30
    }

    const cacheKey = 'register-scale-statistic' + days
    const cacheData = await this.cacheManager.get<RegisterScaleDTO>(cacheKey)
    if (cacheData) {
      return cacheData
    }
    const where: any = { $match: {} }
    if (days > 0) {
      const now = dayjs().tz('Asia/Shanghai')
      const beijingStart = now.subtract(30, 'day').startOf('day').valueOf()
      const beijingEnd = now.subtract(1, 'day').endOf('day').valueOf()
      where.$match.createdAt = {
        $gt: new Date(beijingStart),
        $lte: new Date(beijingEnd)
      }
    }

    const userRegister = await this.userModel.aggregate([
      where,
      {
        // 分组并求和
        $group: {
          _id: '$registrationSource',
          count: { $sum: 1 }
        }
      }
    ])
    console.log('userRegister', userRegister)
    const userRegisterMap = userRegister.reduce((us, user) => {
      // 处理用户注册来源
      const key = user._id === null || user._id === 'other' ? 'other' : user._id
      us[key] = (us[key] || 0) + user.count
      return us
    }, {})

    const iosRegisterNum = userRegisterMap?.ios ?? 0
    const windowsRegisterNum = userRegisterMap?.windows ?? 0
    const macRegisterNum = userRegisterMap?.mac ?? 0
    const androidRegisterNum = userRegisterMap?.android ?? 0
    const otherRegisterNum = userRegisterMap?.other ?? 0

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(
        cacheKey,
        {
          iosRegisterNum: iosRegisterNum,
          windowsRegisterNum: windowsRegisterNum,
          macRegisterNum: macRegisterNum,
          androidRegisterNum: androidRegisterNum,
          otherRegisterNum: otherRegisterNum
        },
        taskOverdueToken
      )
    }

    return {
      iosRegisterNum: iosRegisterNum,
      windowsRegisterNum: windowsRegisterNum,
      macRegisterNum: macRegisterNum,
      androidRegisterNum: androidRegisterNum,
      otherRegisterNum: otherRegisterNum
    }
  }

  async getAccountScale(): Promise<AccountScaleDTO[]> {
    const cacheKey = 'account-scale-statistic'
    const cacheData = await this.cacheManager.get<AccountScaleDTO[]>(cacheKey)
    if (cacheData) {
      return cacheData
    }

    const accountArr = await this.platformAccountModel.aggregate([
      {
        $match: {
          platformName: { $ne: PlatformNameEnum.视频号 }
        }
      },
      {
        // 分组并求和
        $group: {
          _id: '$platformName',
          count: { $sum: 1 }
        }
      }
    ])
    const accountMap = accountArr.map((item) => {
      return {
        platformName: item._id,
        count: item.count
      }
    })

    //视频号单独统计
    const shipinghaoCount = await this.platformAccountModel.countDocuments({
      platformName: PlatformNameEnum.视频号
    })
    //普通视频号
    const putongShipinghaoCount = await this.platformAccountModel.countDocuments({
      platformName: PlatformNameEnum.视频号,
      parentId: null
    })

    accountMap.push(
      {
        platformName: PlatformNameEnum.视频号,
        count: putongShipinghaoCount
      },
      {
        platformName: `不掉线${PlatformNameEnum.视频号}`,
        count: shipinghaoCount - putongShipinghaoCount
      }
    )

    const sortedDesc = [...accountMap].sort((a, b) => b.count - a.count)
    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(cacheKey, sortedDesc, taskOverdueToken)
    }

    return sortedDesc
  }

  async getPublishScale(): Promise<PlatformPublishScaleDTO[]> {
    const cacheKey = 'publish-scale-statistic'
    const cacheData = await this.cacheManager.get<PlatformPublishScaleDTO[]>(cacheKey)
    if (cacheData) {
      return cacheData
    }

    // 计算7天时间范围
    const time = dayjs().tz('Asia/Shanghai')
    const endTime = time.subtract(1, 'day').endOf('day').valueOf()
    const startTime = time.subtract(7, 'day').startOf('day').valueOf()
    const beijinStartTime = dayjs(Number(startTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')
    const beijinEndTime = dayjs(Number(endTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')
    const platformDataArr = await this.platformDataStatisticModel.aggregate([
      {
        $match: {
          createTime: {
            $gte: beijinStartTime,
            $lte: beijinEndTime
          }
        }
      },
      {
        // 分组并求和
        $group: {
          _id: '$platformName',
          successPublishTotal: { $sum: '$successPublishTotal' },
          failPublishTotal: { $sum: '$failPublishTotal' }
        }
      }
    ])
    const platformDataMap = platformDataArr.map((item) => {
      return {
        platformName: item._id,
        successPublishTotal: item.successPublishTotal,
        failPublishTotal: item.failPublishTotal
      }
    })

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()

    if (taskOverdueToken > 0) {
      await this.cacheManager.set(cacheKey, platformDataMap, taskOverdueToken)
    }

    return platformDataMap
  }

  /**
   * 根据日期类型返回开始日期与结束日期，以及天数
   * @param dateType 日期类型
   * @returns
   */
  getDateRange(dateType: OverviewDateTypeEnum): { startDate: Date; endDate: Date; days: number } {
    let startDateLocal = dayjs().tz('Asia/Shanghai')
    let endDateLocal = dayjs().tz('Asia/Shanghai').endOf('day')
    let days = 30

    switch (dateType) {
      case OverviewDateTypeEnum.Day:
        startDateLocal = startDateLocal.subtract(1, 'day')
        days = 1
        break
      case OverviewDateTypeEnum.Seven:
        startDateLocal = startDateLocal.subtract(7, 'day')
        days = 7
        break
      case OverviewDateTypeEnum.Thirty:
        startDateLocal = startDateLocal.subtract(30, 'day')
        days = 30
        break
      default:
        startDateLocal = startDateLocal.subtract(30, 'day')
        days = 30
        break
    }

    // 转换为 UTC 时间
    const startDate = startDateLocal.startOf('day').utc().toDate()
    const endDate = endDateLocal.utc().toDate()
    return { startDate, endDate, days }
  }

  async getTeamRate(query: TeamStatisticListRequest): Promise<TeamStatisticDTO> {
    const page = query.page
    const size = query.size
    const where: any = {
      $match: {
        type: 0
      }
    }

    if (!query.startTime || !query.endTime) {
      // 计算默认的 7 天时间范围
      const now = dayjs().tz('Asia/Shanghai')
      query.endTime = now.subtract(1, 'day').endOf('day').valueOf()
      query.startTime = now.subtract(7, 'day').startOf('day').valueOf()
    }
    const startTime = dayjs(Number(query.startTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')
    const endTime = dayjs(Number(query.endTime)).tz('Asia/Shanghai').format('YYYY-MM-DD')

    where.$match.createTime = {
      $gte: startTime,
      $lte: endTime
    }

    if (query.customerId) {
      where.$match.customerId = new Types.ObjectId(query.customerId)
      where.$match.type = 1
    }
    const result = await this.teamStatisticModel.aggregate([
      where,
      {
        $facet: {
          counts: [{ $count: 'total' }],
          items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }],
          totals: [
            {
              $group: {
                _id: null,
                registerTeamTotal: { $sum: '$registerTeamCount' },
                paidTeamTotal: { $sum: '$paidTeamCount' },
                renewTeamTotal: { $sum: '$renewTeamCount' }
              }
            }
          ]
        }
      }
    ])

    const data = result[0]?.items.map((item) => {
      return {
        createTime: item.createTime,
        registerTeamCount: item.registerTeamCount,
        paidTeamCount: item.paidTeamCount,
        conversionRate: item.conversionRate,
        expiredTeamCount: item.expiredTeamCount,
        renewTeamCount: item.renewTeamCount,
        renewRate: item.renewRate
      }
    })
    const totalSize = result[0]?.counts[0]?.total ?? 0
    const registerTeamTotal = result[0]?.totals[0]?.registerTeamTotal ?? 0
    const paidTeamTotal = result[0]?.totals[0]?.paidTeamTotal ?? 0
    const renewTeamTotal = result[0]?.totals[0]?.renewTeamTotal ?? 0

    const expiredTeams = await this.teamModel.find({
      expiredAt: {
        $gt: new Date(Number(query.startTime)),
        $lt: new Date(Number(query.endTime))
      },
      isVip: false,
      salesType: { $in: [SalesType.FirstBuy, SalesType.ReBuy] }
    })
    let expiredTeamTotal = expiredTeams.length
    if (query.customerId) {
      //归属人查询
      const ids = expiredTeams.map((item) => item._id)
      const members = await this.memberModel
        .find({
          roles: { $in: [TeamRoleNames.MASTER] },
          teamId: { $in: ids }
        })
        .select('userId')
      const userIds = members.map((item) => item.userId)
      expiredTeamTotal = await this.userModel.countDocuments({
        _id: { $in: userIds },
        customerId: new Types.ObjectId(query.customerId)
      })
    }

    const conversionRateTotal =
      paidTeamTotal > 0 && registerTeamTotal > 0
        ? parseFloat(((paidTeamTotal * 100) / registerTeamTotal).toFixed(2))
        : 0
    const renewRateTotal =
      renewTeamTotal > 0
        ? parseFloat(((renewTeamTotal * 100) / (renewTeamTotal + expiredTeamTotal)).toFixed(2))
        : 0

    return {
      totalSize,
      page,
      size,
      registerTeamTotal: registerTeamTotal,
      paidTeamTotal: paidTeamTotal,
      conversionRateTotal: conversionRateTotal,
      expiredTeamTotal: expiredTeamTotal,
      renewTeamTotal: renewTeamTotal,
      renewRateTotal: renewRateTotal,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  async getOnlineScale(): Promise<OnlineScaleDTO[]> {
    const cacheKey = 'online-scale'
    const cacheData = await this.cacheManager.get<OnlineScaleDTO[]>(cacheKey)
    if (cacheData) {
      return cacheData
    }

    const platforms = AllPlatforms

    const beijinNow = dayjs().tz('Asia/Shanghai')
    const oneDayEnd = beijinNow.subtract(1, 'day').endOf('day').valueOf()
    const oneDayAgo = beijinNow.subtract(1, 'day').startOf('day').valueOf()
    const threeDayAgo = beijinNow.subtract(3, 'day').startOf('day').valueOf()
    const servenDayAgo = beijinNow.subtract(7, 'day').startOf('day').valueOf()
    const fourteenDayAgo = beijinNow.subtract(14, 'day').startOf('day').valueOf()
    const thirtyDayAgo = beijinNow.subtract(30, 'day').startOf('day').valueOf()

    const result: OnlineScaleDTO[] = []

    const ranges = [
      { label: '1 天内在线', start: oneDayAgo, end: oneDayEnd, loginStatus: 1 },
      { label: '1-3 天在线', start: threeDayAgo, end: oneDayAgo, loginStatus: 1 },
      { label: '3-7 天在线', start: servenDayAgo, end: threeDayAgo, loginStatus: 1 },
      { label: '7-14 天在线', start: fourteenDayAgo, end: servenDayAgo, loginStatus: 1 },
      { label: '14-30 天在线', start: thirtyDayAgo, end: fourteenDayAgo, loginStatus: 1 },
      { label: '30 天以上在线', end: thirtyDayAgo, loginStatus: 1 },
      { label: '已失效', loginStatus: 2 }
    ]

    for (const platform of platforms) {
      let stats = {}

      for (const range of ranges) {
        let query: FilterQuery<PlatformAccountEntity> = {}
        query.platformName = platform
        if (range?.start || range?.end) {
          query.loginStatusUpdatedAt = {}
          if (range.start) {
            query.loginStatusUpdatedAt.$gte = new Date(range.start)
          }
          if (range.end) {
            query.loginStatusUpdatedAt.$lt = new Date(range.end)
          }
        }

        if (range.loginStatus === 1) {
          //在线
          query.status = LoginStatus.Succesed
        } else {
          //掉线
          query.status = { $in: [LoginStatus.Expired, LoginStatus.CancelAuth] }
        }
        // 统计每个平台在不同时间区间的用户数
        stats[range.label] = await this.platformAccountModel.countDocuments(query)
      }

      result.push({ platformName: platform, stats })
    }

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()
    await this.cacheManager.set(cacheKey, result, taskOverdueToken)

    return result
  }
}
