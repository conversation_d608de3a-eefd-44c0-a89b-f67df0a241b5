import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'

export class LatestResponseDTO {}

export class GetLatestResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: LatestResponseDTO
  })
  data: LatestResponseDTO
}

export enum DesktopTypeEnum {
  'windows' = 'windows',
  'macos' = 'macos',
  'ios' = 'ios',
  'android' = 'android'
}

export class DesktopLatestRequest {
  @ApiProperty({
    title: '系统类型',
    description: '系统类型: windows, macos, ios, android',
    type: String,
    enum: DesktopTypeEnum,
    example: DesktopTypeEnum.windows
  })
  @IsString()
  @IsNotEmpty({ message: '版本类型不能为空' })
  desktopType: DesktopTypeEnum

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty({ message: '版本号不能为空' })
  version: string
}

export class DesktopLatestResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '1.0.0'
  })
  version: string

  @ApiResponseProperty({
    type: String,
    example: 'https://static-lite.yixiaoer.cn/development/1.0.0/'
  })
  url: string

  @ApiResponseProperty({
    type: Boolean,
    example: false
  })
  isForce: boolean

  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  isUpdate: boolean

  @ApiResponseProperty({
    type: String,
    example: '发版公告'
  })
  notice: string
}

export class GetDesktopLatestResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: DesktopLatestResponseDTO
  })
  data: DesktopLatestResponseDTO
}

export class BaiduReportRequest{
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty({ message: 'bd不能为空' })
  bd:string

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty({ message: '类型不能为空' })
  newType:number = 6
}
