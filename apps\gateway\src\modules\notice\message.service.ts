import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { MessageEntity } from '@yxr/mongo'
import { MessageDetail, MessageListRequestDTO, MessagePageResponse } from './message.dto'
import { NoticeTypesEnum } from '@yxr/common'

@Injectable()
export class MessageService {
  constructor(@InjectModel(MessageEntity.name) private messageModel: Model<MessageEntity>) {}

  /**
   * 获取最新系统消息
   * @returns
   */
  async getNewest(): Promise<MessageDetail> {
    const message = await this.messageModel
      .findOne({ type: NoticeTypesEnum.System })
      .sort({ createdAt: -1 })

    return {
      id: message.id,
      title: message.title,
      content: message.content,
      type: message.type,
      isPopUp: message.isPopUp,
      createdAt: message.createdAt ? message.createdAt.getTime() : 0
    }
  }

  /**
   * 分页获取用户消息列表
   * @param size
   * @param page
   */
  async getPagedMessages(query: MessageListRequestDTO): Promise<MessagePageResponse> {
    const messages = await this.messageModel
      .find({ type: NoticeTypesEnum.System })
      .sort({ updatedAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.messageModel.find().countDocuments()

    return {
      data: messages.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        type: item.type,
        isPopUp: item.isPopUp,
        createdAt: item.createdAt ? item.createdAt.getTime() : 0
      })),
      page: query.page,
      size: query.size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / query.size)
    }
  }
}
