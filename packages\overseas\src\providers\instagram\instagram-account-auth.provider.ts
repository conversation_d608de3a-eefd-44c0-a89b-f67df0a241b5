import { Injectable, Logger, Scope } from '@nestjs/common'
import { Account<PERSON>uthProvider } from '../account-auth.provider'
import { OverseasContext, PlatformAccountInfo } from '../types'
import { InstagramApi } from './instagram-api'

@Injectable({ scope: Scope.TRANSIENT })
export class InstagramAccountAuthProvider implements AccountAuthProvider {
  logger = new Logger(InstagramAccountAuthProvider.name)
  private readonly clientId = process.env.INSTAGRAM_CLIENT_ID || ''

  private readonly oauthCallbackUri = `${process.env.OVERSEAS_BASE_ADDRESS}auth/callback`

  constructor(private readonly api: InstagramApi) {
  }

  async generateAuthorizationUrl(state: string): Promise<string> {
    // https://www.instagram.com/accounts/login/?force_authentication&platform_app_id=***************&enable_fb_login&next=https://www.instagram.com/oauth/authorize/third_party/?enable_fb_login=0&client_id=***************&redirect_uri=https://www.upmee.cc/user/authorize&response_type=code&scope=instagram_business_basic,instagram_business_content_publish,instagram_business_manage_messages,instagram_business_manage_comments,instagram_business_manage_insights&state=********************************************************************************************&upmeeId=**********&teamId=**********&logger_id=5ad3cce5-c229-4a11-9c67-963b6fcb5cfd
    // https://www.instagram.com/oauth/authorize?enable_fb_login=0&force_authentication=1&client_id=****************&redirect_uri=https://www-dev.anter.cc/api/accounts/auth/callback&response_type=code&scope=instagram_business_basic%2Cinstagram_business_manage_messages%2Cinstagram_business_manage_comments%2Cinstagram_business_content_publish%2Cinstagram_business_manage_insights

    const scope = [
      'instagram_business_basic',
      'instagram_business_content_publish',
      'instagram_business_manage_messages',
      'instagram_business_manage_comments',
      'instagram_business_manage_insights'
    ].join(',')

    const url = new URL('https://api.instagram.com/oauth/authorize')
    url.searchParams.set('client_id', this.clientId)
    url.searchParams.set('response_type', 'code')
    url.searchParams.set('scope', scope)
    url.searchParams.set('redirect_uri', this.oauthCallbackUri)
    url.searchParams.set('state', state)

    return url.toString()
  }

  async exchangeAuthCodeForAccounts(context: OverseasContext, code: string, state: string): Promise<PlatformAccountInfo[]> {

    // 获取短效令牌
    const short = await this.api.get_short_live_oauth_access_token(context, {
      code: code,
      redirect_uri: this.oauthCallbackUri
    })

    // 换取长效令牌
    const long = await this.api.get_long_live_oauth_access_token(context, { access_token: short.access_token })

    // 获取公共主页清单
    const info = await this.api.get_me(context, long.access_token)

    // 获取公共主页详情
    return [
      {
        openId: info.user_id,
        credentials: {
          access_token: long.access_token,
          expireAt: new Date(long.expires_in * 1000 + new Date().getTime())
        },
        avatar: info.profile_picture_url,
        name: info.name,
        username: info.username
      } as PlatformAccountInfo
    ]
  }
}
