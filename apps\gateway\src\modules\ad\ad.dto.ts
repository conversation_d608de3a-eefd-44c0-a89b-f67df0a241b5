import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class AdDetailDTO {
  @ApiProperty({
    type: String,
    description: '广告id',
    default: 1
  })
  id?: string

  @ApiProperty({
    type: Number,
    description: '排序',
    default: 1
  })
  sort: number

  @ApiProperty({
    type: String,
    description: '名称',
    default: 'banner图片名称'
  })
  name: string

  @ApiProperty({
    type: String,
    description: '广告图访问地址'
  })
  adPath?: string

  @ApiProperty({
    type: String,
    description: '跳转地址'
  })
  jumpToUrl: string

  @ApiProperty({
    type: String,
    description: '弹框方案everyday每天一次,threeday三天一次'
  })
  popupType?: string
}

export class AdsListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AdDetailDTO
  })
  data: AdDetailDTO
}
