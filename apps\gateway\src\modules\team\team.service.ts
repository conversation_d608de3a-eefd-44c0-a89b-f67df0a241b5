import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types, UpdateQuery } from 'mongoose'
import {
  BrowserEntity,
  BrowserFavoritesEntity,
  ContentEntity,
  FavoritesGroupEntity,
  GroupEntity,
  MemberEntity,
  PlatformAccountEntity,
  TaskEntity,
  TeamComponentEntity,
  TeamEntity,
  UserEntity
} from '@yxr/mongo'
import {
  patchTeamRequestBodyDTO,
  putTeamComponentRequestBodyDTO,
  TeamDetailsDto,
  TeamPagedListResponse
} from './team.dto'
import { type FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import { customAlphabet, nanoid } from 'nanoid'
import {
  CacheKeyService,
  MemberStatusEnum,
  StatisticCommonService,
  TeamFeatures,
  TeamRoleNames
} from '@yxr/common'
import { RootConfigMap } from '@yxr/config'
import { ConfigService } from '@nestjs/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { AuthorizationService } from '../../common/security/authorization.service'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { CustomerService } from './customer.service'
import { TeamAreaResponse } from '../user/user.dto'
import { TlsService, TosService } from '@yxr/huoshan'

@Injectable()
export class TeamService {
  constructor(
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(GroupEntity.name) private groupModel: Model<GroupEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(BrowserEntity.name) private browserModel: Model<BrowserEntity>,
    @InjectModel(BrowserFavoritesEntity.name)
    private browserFavoritesModel: Model<BrowserFavoritesEntity>,
    @InjectModel(FavoritesGroupEntity.name)
    private favoritesGroupModel: Model<FavoritesGroupEntity>,
    @InjectModel(TeamComponentEntity.name)
    private teamComponentModel: Model<TeamComponentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    private readonly ossService: TosService,
    private authorizationService: AuthorizationService,
    private readonly webhookService: WebhookService,
    private readonly customerService: CustomerService,
    private readonly loggerService: TlsService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 分页获取团队列表
   */
  async getPagedTeams({
    page,
    size
  }: {
    size: number
    page: number
  }): Promise<TeamPagedListResponse> {
    const { userId: currentUserId } = this.request.session

    const result = await this.memberModel
      .aggregate([
        {
          $match: {
            userId: new Types.ObjectId(currentUserId),
            status: MemberStatusEnum.Joined,
            isFreeze: false
          }
        },
        {
          $lookup: {
            from: 'teamentities',
            localField: 'teamId',
            foreignField: '_id',
            pipeline: [
              {
                $match: {
                  isDeleted: false // 团队未解散
                }
              }
            ],
            as: 'teams'
          }
        },
        {
          $unwind: {
            path: '$teams' // 展开 teams 数组
          }
        },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
          }
        }
      ])
      .exec()

    const totalSize = result[0]?.counts[0]?.total ?? 0
    return {
      data: await Promise.all(
        result[0]?.items.map(async (item) => ({
          id: item?.teams._id,
          name: item?.teams.name,
          logoUrl: `${process.env.OSS_DOWNLOAD_URL}/${item?.teams.logo}`,
          logoKey: item?.teams.logo,
          isVip: item?.teams.isVip ?? false,
          expiredAt: item?.teams.expiredAt?.getTime()
        }))
      ),
      page,
      size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / size)
    }
  }

  /**
   * 创建团队
   */
  async createTeam({ name, logoKey }: { name: string; logoKey: string }): Promise<TeamDetailsDto> {
    const { userId: currentUserId } = this.request.session

    // 校验用户加入的团队(包含自己创建的)数量是否超限
    const teamCount = await this.teamCount(currentUserId)
    if (teamCount >= TeamFeatures.MaximumTeamCount) {
      throw new ForbiddenException('团队数量已达上限')
    }
    const currentUser = await this.userModel.findById(new Types.ObjectId(currentUserId))

    const session = await this.teamModel.db.startSession()
    session.startTransaction()

    // TODO: 当团队数量越来越多时, 会有比较大的概览出现CODE冲突, 需要更好的技术机制来确保 code 唯一
    const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 6)
    const code = nanoid()

    if (logoKey === undefined || logoKey == '') {
      logoKey = 'avatars/team-default.png'
    }

    try {
      // 创建团队
      const teams = await this.teamModel.create<TeamEntity>(
        [
          {
            name: name,
            logo: logoKey,
            code: code,
            accountCountLimit: TeamFeatures.DefaultAccountCountLimit, // 变更为账号点数
            accountCapacityLimit: TeamFeatures.DefaultAccountCapacityLimit,
            accountCount: 0, // 变更为账号点数
            accountCapacity: 0,
            memberCountLimit: TeamFeatures.DefaultMemberCountLimit,
            memberCount: 1,
            enabled: true,
            isVip: false,
            isDeleted: false,
            interestCount: 0,
            customerId: new Types.ObjectId(currentUser.customerId),
            appPublish: false
          }
        ],
        { session }
      )

      // 创建默认成员关系
      await this.memberModel.create<MemberEntity>(
        [
          {
            userId: new Types.ObjectId(currentUserId),
            teamId: new Types.ObjectId(teams[0]._id),
            roles: [TeamRoleNames.MASTER],
            accounts: [],
            remark: currentUser.nickName,
            status: MemberStatusEnum.Joined
          }
        ],
        { session }
      )

      await session.commitTransaction()

      return {
        id: teams[0].id,
        name: teams[0].name,
        logoUrl: `${process.env.OSS_DOWNLOAD_URL}/${teams[0].logo}`,
        logoKey: teams[0].logo,
        code: teams[0].code,
        accountCountLimit: teams[0].accountCountLimit, // 变更为账号点数
        accountCapacityLimit: teams[0].accountCapacityLimit,
        accountCount: teams[0].accountCount, // 变更为账号点数
        accountCapacity: teams[0].accountCapacity,
        memberCountLimit: teams[0].memberCountLimit,
        memberCount: teams[0].memberCount,
        interestCount: teams[0].interestCount ?? 0,
        appPublish: teams[0].appPublish ?? false,
        createdAt: teams[0].createdAt.getTime(),
        isVip: teams[0].isVip ?? false,
        expiredAt: teams[0].expiredAt?.getTime(),
        salesType: teams[0].salesType,
        remainingDay: StatisticCommonService.remainingDay(teams[0].expiredAt)
      }
    } catch (error) {
      await session.abortTransaction()
      await this.loggerService.error(null, '创建团队失败', { error: error })
      // 这里最后可能出现异常的目前就是团队编码的重复冲突, 团队数量不大时, 概率较小, 暂时简单处理
      throw new HttpException('网络异常, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }
  }

  /**
   * 获取团队详情
   * @param teamId
   */
  async getTeam(teamId: string): Promise<TeamDetailsDto> {
    const { userId: currentUserId } = this.request.session

    await this.authorizationService.checkRoleNames(teamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN,
      TeamRoleNames.MEMBER
    ])

    const team = await this.teamModel
      .findOne({
        _id: new Types.ObjectId(teamId),
        isDeleted: false
      })
      .exec()

    // 当前用户需要在这个团队中, 否则抛出异常
    if (team == null) {
      throw new NotFoundException('团队不存在')
    }

    const masterMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })
    const { serviceCodeUrl } = this.configService.get<RootConfigMap['app']>('app')
    const qrCode = await this.customerService.assignAsync(masterMember.userId.toString())

    // 获取组件信息
    const components = await this.teamComponentModel
      .find({
        teamId: new Types.ObjectId(team.id)
      })
      .select('_id name enabled componentArgs')
      .lean()

    // 如果不存在 superdir 组件，则构造一条默认数据
    if (!components.some(component => component.name === 'superdir')) {
      // 创建默认的 superdir 组件数据
      components.push ({
        _id: new Types.ObjectId(),
        teamId: new Types.ObjectId(teamId),
        name: 'superdir',
        enabled: true,
        componentArgs: {},
        __v: 0
      })
    }
    return {
      id: team.id,
      name: team.name,
      logoUrl: `${process.env.OSS_DOWNLOAD_URL}/${team.logo}`,
      logoKey: team.logo,
      code: team.code,
      publishAccountLimit: 20,
      accountCountLimit: team.accountCountLimit, // 变更为账号点数
      accountCapacityLimit: team.accountCapacityLimit,
      capacity: team.capacity === 0 ? 500 * 1024 * 1024 : team.capacity,
      usedCapacity: team.usedCapacity,
      networkTraffic: team.networkTraffic,
      useNetworkTraffic: team.useNetworkTraffic,
      accountCount: team.accountCount, // 变更为账号点数
      accountCapacity: team.accountCapacity,
      memberCountLimit: team.memberCountLimit,
      memberCount: team.memberCount,
      createdAt: team.createdAt?.getTime(),
      isVip: team.isVip ?? false,
      interestCount: team?.interestCount ?? 0,
      appPublish: team?.appPublish ?? false,
      expiredAt: team.expiredAt?.getTime(),
      remainingDay: StatisticCommonService.remainingDay(team.expiredAt),
      salesType: team.salesType,
      components: components?.map((item) => ({
        id: item._id.toString(),
        name: item.name,
        enabled: item.enabled,
        componentArgs: item.componentArgs ?? {}
      })),
      corporateTransfer: qrCode ?? serviceCodeUrl
    }
  }

  /**
   * 登录到团队(切换团队)
   */
  async signin(teamId: string): Promise<{ authorization: string }> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const { authorization: oldAuthorization } = this.request.headers
    // 未发生团队切换, 为了避免token过期, 直接返回旧的 token
    if (currentTeamId === teamId) {
      return { authorization: oldAuthorization }
    }

    const member = await this.memberModel.findOne({
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(teamId),
      status: MemberStatusEnum.Joined
    })

    if (member === null) {
      throw new ForbiddenException('您无法登录到该团队')
    }

    // 缓存新的 token
    const token = nanoid() // 产生一个新的 token
    const tokenKey = `session:au-${token}`
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    const userIdKey = this.request.client?.name
      ? `session:ui-${currentUserId}:${this.request.client?.name}`
      : `session:ui-${currentUserId}`

    await Promise.all([
      this.cacheManager.store.client.expire(`session:au-${oldAuthorization}`, 10), // 设置旧的 token 10s 后过期
      this.cacheManager.set(
        tokenKey,
        {
          user: this.request.user, // 保留和旧设计不合理的兼容, 当 request 中的 user 可以安全删除时, 可以删除
          userId: currentUserId,
          teamId: teamId
        },
        overdueToken
      ),
      this.cacheManager.set(userIdKey, tokenKey, overdueToken) // 更新通过 userid 找到 token 的缓存值, 便于在用户重复登录时能快速定位到需要过期的 token
    ])

    // 更新用户级别的上一次登录团队设置
    await this.userModel
      .updateOne(
        { _id: new Types.ObjectId(currentUserId) },
        { latestTeamId: new Types.ObjectId(teamId) }
      )
      .exec()

    return {
      authorization: token
    }
  }

  async update(teamId: string, body: patchTeamRequestBodyDTO): Promise<TeamDetailsDto> {
    const { userId: currentUserId } = this.request.session

    await this.authorizationService.checkRoleNames(teamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN
    ])

    const update: UpdateQuery<TeamEntity> = {}
    if (body.name && body.name.length > 0) update.name = body.name

    if (body.logoKey && body.logoKey.length > 0) update.logo = body.logoKey

    // 修改
    const team = await this.teamModel.findByIdAndUpdate(new Types.ObjectId(teamId), update).exec()

    return {
      id: team.id,
      name: body.name && body.name.length > 0 ? body.name : team.name,
      logoUrl: await this.ossService.getAccessSignatureUrl(body.logoKey ?? team.logo),
      logoKey: body.logoKey ?? team.logo,
      code: team.code,
      accountCountLimit: team.accountCountLimit, // 变更为账号点数
      accountCapacityLimit: team.accountCapacityLimit,
      accountCount: team.accountCount, // 变更为账号点数
      accountCapacity: team.accountCapacity,
      memberCountLimit: team.memberCountLimit,
      memberCount: team.memberCount,
      salesType: team.salesType,
      createdAt: team.createdAt.getTime(),
      interestCount: team.interestCount,
      appPublish: team.appPublish,
      isVip: team.isVip ?? false,
      expiredAt: team.expiredAt?.getTime(),
      remainingDay: StatisticCommonService.remainingDay(team.expiredAt)
    }
  }

  async delete(teamId: string) {
    const { userId: currentUserId } = this.request.session

    await this.authorizationService.checkRoleNames(teamId, currentUserId, [TeamRoleNames.MASTER])

    // 校验用户创建的团队数量是否保留最低限度
    const teamCount = await this.teamCount(currentUserId, true)

    if (teamCount <= 1) {
      throw new ForbiddenException('团队无法解散, 您至少需要保留一个由您创建的团队')
    }

    await this.userModel.updateMany(
      { latestTeamId: new Types.ObjectId(teamId) },
      { latestTeamId: null }
    )

    const members = await this.memberModel.find(
      {
        teamId: new Types.ObjectId(teamId),
        status: MemberStatusEnum.Joined
      },
      { userId: 1 }
    )

    await this.teamModel
      .updateOne<TeamEntity>({ _id: new Types.ObjectId(teamId) }, { isDeleted: true })
      .exec()

    // 向团队所有成员发送通知
    await this.webhookService.grpchook(
      members.map((u) => u.userId.toString()),
      teamId,
      { event: WebhookEvents.TeamExit }
    )

    //清理团队授权账号、账号分组、发布记录、网站、网站收藏、网站分组数据
    await this.platformAccountModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.groupModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.contentModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.taskModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.browserModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.browserFavoritesModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
    await this.favoritesGroupModel.deleteMany({
      teamId: new Types.ObjectId(teamId)
    })
  }

  /**
   * 用户已占用的团队数量
   * @param currentUserId
   * @param isMasterRole 是否由自己创建的
   * @returns
   */
  async teamCount(currentUserId: string, isMasterRole: boolean = false): Promise<number> {
    const where: any = {}

    if (currentUserId) {
      where.userId = new Types.ObjectId(currentUserId)
    }

    if (isMasterRole) {
      where.roles = { $in: [TeamRoleNames.MASTER] }
    }

    const validMemberIds = await this.memberModel.find(where, { teamId: 1 }).exec()

    const teamIdObjectIds = validMemberIds.map((doc) => new Types.ObjectId(doc.teamId))

    const teamCount = await this.teamModel
      .countDocuments({
        _id: { $in: teamIdObjectIds },
        isDeleted: false
      })
      .exec()

    return teamCount
  }

  /**
   * 检查团队账号数量
   * @param teamId
   * @param isFreeze
   */
  async checkTeamAccountLimit(teamId: string, isFreeze?: boolean) {
    const whereAccount: { teamId: Types.ObjectId; isFreeze?: boolean } = {
      teamId: new Types.ObjectId(teamId)
    }
    if (typeof isFreeze !== 'undefined') {
      whereAccount.isFreeze = isFreeze
    }
    const accounts = await this.platformAccountModel.find(whereAccount)

    const accountTotalCapacity = accounts.reduce((acc, curr) => acc + curr.capacity, 0)

    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (accountTotalCapacity >= team.accountCapacityLimit) {
      throw new ForbiddenException(`当前团队账号点数已达上限！`)
    }
  }

  /**
   * 检查团队是否可以解冻
   * @param teamId
   * @param isFreeze
   */
  async getTeamVip(teamId: string): Promise<Boolean> {
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    return team.isVip
  }

  /**
   * 检查团队账号数量
   * @param teamId
   * @param isFreeze
   */
  async updateTeamAccount(teamId: string) {
    const accounts = await this.platformAccountModel.find({
      teamId: new Types.ObjectId(teamId)
    })
    const browserCount = await this.browserModel.countDocuments({
      teamId: new Types.ObjectId(teamId),
      accountId: { $exists: false }
    })

    await this.teamModel.updateOne(
      {
        _id: new Types.ObjectId(teamId)
      },
      {
        accountCount: accounts.length + browserCount,
        accountCapacity: accounts.filter(acc => acc.isFreeze == false).reduce((acc, curr) => acc + curr.capacity, 0)
      }
    )
  }

  async getTeamAreas(teamId: string): Promise<TeamAreaResponse[]> {
    const team = await this.teamModel.findOne({ _id: new Types.ObjectId(teamId) })

    if (!team) {
      throw new NotFoundException('团队未找到')
    }
    const areas = team.kuaidailiAreas
    if (Array.isArray(areas) && areas.length > 0) {
      return areas
        .filter((area) => area && typeof area === 'object') // 过滤掉无效值
        .map((area) => ({
          name: area?.city ? `${area.province.name}/${area?.city.name}` : area.province.name,
          code: area?.city ? area?.city.code : area.province.code
        }))
    } else {
      return []
    }
  }

  /**
   * 新增和更新团队组件
   * @param teamId
   * @param body
   */
  async putTeamComponents(teamId: string, body: putTeamComponentRequestBodyDTO) {
    const teamComponent = await this.teamComponentModel.findOne({
      teamId: new Types.ObjectId(teamId),
      name: body.name
    })
      console.log(body.componentArgs ?? null, 'body.componentArgs')

    if (teamComponent) {
      // 动态构建更新对象
      const updateFields: any = { name: body.name }
      if (body.enable !== undefined) updateFields.enabled = body.enable // 检查 enable 是否传入
      if (body.componentArgs !== undefined) updateFields.componentArgs = body.componentArgs // 检查 componentArgs 是否传入
      await this.teamComponentModel.updateOne(
        {
          _id: new Types.ObjectId(teamComponent.id)
        },
        {
          $set: updateFields
        }
      )
    } else {
      await this.teamComponentModel.insertOne({
        teamId: new Types.ObjectId(teamId),
        name: body.name,
        enabled: body.enable ?? false,
        componentArgs: body.componentArgs ?? null
      })
    }
  }

  /**
   * 检测发布权益
   */
  async checkPush(teamId: string): Promise<Boolean> {
    const currentTeamId = teamId
    const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))
    if (team.isVip) {
      return true
    }
    const counterKey = CacheKeyService.genCounterKey(currentTeamId)
    const cacheValue = (await this.cacheManager.store.client.get(counterKey)) || 0
    const currentValue = cacheValue ? parseInt(cacheValue, 10) : 0
    // 如果已超过 3 次，直接返回 false
    if (currentValue >= 3) {
      return false
    }
    return true
  }

  /**
   * 设置团队发布次数
   * @param teamId
   */
  async setPush(teamId: string) {
    const counterKey = CacheKeyService.genCounterKey(teamId)
    await this.cacheManager.store.client.incr(counterKey)
    // 设置每日0点重置的过期时间
    const now = new Date()
    const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
    const expireTime = midnight.getTime() - now.getTime()
    await this.cacheManager.store.client.expire(counterKey, Math.floor(expireTime / 1000))
  }
}
