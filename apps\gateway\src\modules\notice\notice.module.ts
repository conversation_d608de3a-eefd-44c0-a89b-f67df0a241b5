import { Module } from '@nestjs/common'
import { NoticeController } from './notice.controller'
import { NoticeService } from './notice.service'
import { MessageMongoose, NoticeMongoose, OnlineScriptMongoose } from '@yxr/mongo'
import { MessageController } from './message.controller'
import { MessageService } from './message.service'

@Module({
  imports: [MessageMongoose, NoticeMongoose, OnlineScriptMongoose],
  controllers: [NoticeController, MessageController],
  providers: [NoticeService, MessageService]
})
export class NoticeModule {}
