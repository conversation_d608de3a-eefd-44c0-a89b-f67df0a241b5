# 灵活用户标识系统实现

## 概述

在 `UserEntity` 中实现了灵活的用户标识系统，支持手机号和账号两种登录方式，提供了完整的验证机制、唯一性保证和向后兼容性。

## 功能特性

### 1. **数据库字段修改**
- ✅ 在 `UserEntity` 中添加了 `account` 字段（字符串类型，可选）
- ✅ 将现有的 `phone` 字段修改为可选（nullable）
- ✅ 移除了 `phone` 字段的数据库唯一索引约束
- ✅ 添加了业务层面的唯一性验证逻辑

### 2. **字段约束规则**
- ✅ `phone` 和 `account` 字段不能同时为空（至少有一个必须有值）
- ✅ `account` 字段不能是手机号码格式（添加了格式验证）
- ✅ `account` 字段长度限制：8-20个字符
- ✅ `account` 字段支持字母、数字、下划线、中文等字符，但不能是纯数字的手机号格式

### 3. **唯一性验证逻辑**
- ✅ 在业务逻辑层面确保 `phone` 字段在非空时的唯一性
- ✅ 在业务逻辑层面确保 `account` 字段在非空时的唯一性
- ✅ 创建用户时验证输入的账号或手机号是否已被使用

### 4. **登录方式支持**
- ✅ **手机验证码登录**：保持现有逻辑不变（手机号+验证码）
- ✅ **账号密码登录**：支持两种方式
  - 手机号 + 密码（现有方式）
  - 账号名称（account字段）+ 密码（新增方式）

### 5. **用户注册场景**
- ✅ **手机号注册**：phone字段必填，account字段可选
- ✅ **账号注册**：account字段必填，phone字段可选（为空）
- ✅ 注册时验证所填写的标识符（手机号或账号）的唯一性

## 技术实现

### 1. 数据库实体修改

**文件**: `packages/mongo/lib/schemas/user.schema.ts`

```typescript
@Prop({
  type: String,
  required: false,
  index: true,
  minlength: [11, '手机号长度不能小于11'],
  maxlength: [11, '手机号长度不能大于11']
})
phone?: string

@Prop({
  type: String,
  required: false,
  index: true,
  minlength: [8, '账号长度不能小于8个字符'],
  maxlength: [20, '账号长度不能大于20个字符']
})
account?: string
```

### 2. 用户验证工具类

**文件**: `packages/common/user-validation.utils.ts`

#### 2.1 核心验证方法
```typescript
/**
 * 验证用户标识符（phone和account不能同时为空）
 */
static validateUserIdentifiers(phone?: string, account?: string): UserValidationResult {
  // 检查是否同时为空
  if (!phone && !account) {
    return {
      isValid: false,
      error: UserValidationError.BOTH_IDENTIFIERS_EMPTY,
      message: '手机号和账号不能同时为空，至少需要提供一个'
    }
  }

  // 验证手机号格式
  if (phone && !this.validatePhoneFormat(phone)) {
    return {
      isValid: false,
      error: UserValidationError.PHONE_INVALID_FORMAT,
      message: '手机号格式不正确，请输入11位有效手机号'
    }
  }

  // 验证账号格式
  if (account && !this.validateAccountFormat(account)) {
    if (PHONE_REGEX.test(account)) {
      return {
        isValid: false,
        error: UserValidationError.ACCOUNT_CANNOT_BE_PHONE,
        message: '账号不能是手机号格式，请使用其他字符组合'
      }
    }
    
    return {
      isValid: false,
      error: UserValidationError.ACCOUNT_INVALID_FORMAT,
      message: '账号格式不正确，支持8-20位字母、数字、下划线、中文，不能为纯数字'
    }
  }

  return {
    isValid: true
  }
}
```

#### 2.2 格式验证
```typescript
/**
 * 手机号正则表达式
 * 支持中国大陆手机号格式：1开头的11位数字
 */
export const PHONE_REGEX = /^1[3-9]\d{9}$/

/**
 * 账号名称正则表达式
 * 支持：字母、数字、下划线、中文字符
 * 不允许：纯数字（避免与手机号混淆）
 * 长度：8-20个字符
 */
export const ACCOUNT_REGEX = /^(?![0-9]+$)[a-zA-Z0-9_\u4e00-\u9fa5]{8,20}$/
```

#### 2.3 自动识别输入类型
```typescript
/**
 * 识别用户输入的标识符类型（手机号或账号）
 */
static identifyUserInput(input: string): UserIdentifierInfo {
  if (!input) {
    return {
      type: UserIdentifierType.ACCOUNT,
      value: input,
      isValid: false
    }
  }

  // 检查是否为手机号格式
  if (PHONE_REGEX.test(input)) {
    return {
      type: UserIdentifierType.PHONE,
      value: input,
      isValid: true
    }
  }

  // 检查是否为有效账号格式
  const isValidAccount = ACCOUNT_REGEX.test(input) && !PHONE_REGEX.test(input)
  
  return {
    type: UserIdentifierType.ACCOUNT,
    value: input,
    isValid: isValidAccount
  }
}
```

### 3. 用户标识符管理服务

**文件**: `apps/gateway/src/modules/user/user-identifier.service.ts`

#### 3.1 唯一性验证
```typescript
/**
 * 验证手机号是否已被使用
 */
async validatePhoneUniqueness(phone: string, excludeUserId?: string): Promise<void> {
  if (!phone) return

  const query: any = { phone }
  if (excludeUserId) {
    query._id = { $ne: excludeUserId }
  }

  const existingUser = await this.userModel.findOne(query).lean()
  if (existingUser) {
    throw new BadRequestException(
      UserValidationUtils.getErrorMessage(UserValidationError.PHONE_ALREADY_EXISTS)
    )
  }
}

/**
 * 验证账号是否已被使用
 */
async validateAccountUniqueness(account: string, excludeUserId?: string): Promise<void> {
  if (!account) return

  const query: any = { account }
  if (excludeUserId) {
    query._id = { $ne: excludeUserId }
  }

  const existingUser = await this.userModel.findOne(query).lean()
  if (existingUser) {
    throw new BadRequestException(
      UserValidationUtils.getErrorMessage(UserValidationError.ACCOUNT_ALREADY_EXISTS)
    )
  }
}
```

#### 3.2 用户查找
```typescript
/**
 * 根据手机号或账号查找用户
 */
async findUserByIdentifier(phone?: string, account?: string): Promise<UserEntity | null> {
  if (phone) {
    return await this.userModel.findOne({ phone }).lean()
  }
  
  if (account) {
    return await this.userModel.findOne({ account }).lean()
  }

  return null
}
```

### 4. 登录逻辑修改

**文件**: `apps/gateway/src/modules/user/user.service.ts`

#### 4.1 统一登录入口
```typescript
async putLoginUser(body: UserLoginRegisterRequestBodyDTO): Promise<UserLoginResphoneDTO> {
  let result: UserLoginResphoneDTO
  
  // 验证用户标识符
  const validation = UserValidationUtils.validateUserIdentifiers(body.phone, body.account)
  if (!validation.isValid) {
    throw new BadRequestException(validation.message)
  }

  if (body.code) {
    // 验证码登录（仅支持手机号）
    if (!body.phone) {
      throw new BadRequestException('验证码登录需要提供手机号')
    }
    result = await this.phoneLogin(body)
  } else {
    // 密码登录（支持手机号或账号）
    result = await this.passwordLogin(body)
  }

  // 记录设备信息（如果有手机号）
  if (body.phone) {
    await this.userDevicesService.putUserDevices(body.phone, body.deviceId)
  }
  
  return result
}
```

#### 4.2 密码登录支持账号
```typescript
/**
 * 用户密码登录
 * @param data
 * @returns
 */
async passwordLogin(data: UserLoginRegisterRequestBodyDTO) {
  // 根据手机号或账号查找用户
  let user: any = null
  
  if (data.phone) {
    user = await this.userModel.findOne({ phone: data.phone })
  } else if (data.account) {
    user = await this.userModel.findOne({ account: data.account })
  }
  
  if (!user) {
    const identifier = data.phone ? '手机号' : '账号'
    throw new NotFoundException(`${identifier}未注册`)
  }
  
  // 后续密码验证逻辑保持不变...
}
```

#### 4.3 注册时支持账号
```typescript
// 验证用户标识符（格式和唯一性）
await this.userIdentifierService.validateUserIdentifiers(phone, body.account)

// 创建用户时包含账号字段
const users = await this.userModel.create<UserEntity>(
  [
    {
      phone: phone,
      account: body.account, // 支持在注册时设置账号
      nickName: nickname,
      avatar: avatar,
      latestTeamId: new Types.ObjectId(latestTeamId),
      registrationSource: platform,
      channelCode: channelInfo ? channelInfo.channelCode : null,
      source: 'gateway'
    }
  ],
  { session }
)
```

### 5. DTO更新

**文件**: `apps/gateway/src/modules/user/user.dto.ts`

#### 5.1 登录请求DTO
```typescript
@Validate(UserIdentifierValidator)
export class UserLoginRegisterRequestBodyDTO {
  @ApiProperty({
    description: '手机号或账号（二选一）',
    example: '***********',
    required: false
  })
  @IsOptional()
  @IsString({ message: '手机号码格式不正确' })
  phone?: string

  @ApiProperty({
    description: '账号名称（二选一）',
    example: 'user_account_123',
    required: false
  })
  @IsOptional()
  @IsString({ message: '账号格式不正确' })
  account?: string

  // 其他字段保持不变...
}
```

#### 5.2 用户信息响应DTO
```typescript
export class UserInfoResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '***********',
    required: false
  })
  phone?: string

  @ApiResponseProperty({
    type: String,
    example: 'user_account_123',
    required: false
  })
  account?: string

  // 其他字段保持不变...
}
```

#### 5.3 自定义验证器
```typescript
/**
 * 用户标识符验证器
 * 验证phone和account字段不能同时为空，且格式正确
 */
@ValidatorConstraint({ name: 'userIdentifier', async: false })
export class UserIdentifierValidator implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const object = args.object as any
    const phone = object.phone
    const account = object.account
    
    const validation = UserValidationUtils.validateUserIdentifiers(phone, account)
    return validation.isValid
  }

  defaultMessage(args: ValidationArguments) {
    const object = args.object as any
    const phone = object.phone
    const account = object.account
    
    const validation = UserValidationUtils.validateUserIdentifiers(phone, account)
    return validation.message || '用户标识符验证失败'
  }
}
```

## 使用场景

### 1. **手机号注册和登录**
```bash
# 手机号验证码登录
curl -X POST "/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "***********",
    "code": "123456"
  }'

# 手机号密码登录
curl -X POST "/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "***********",
    "password": "password123"
  }'
```

### 2. **账号注册和登录**
```bash
# 账号密码登录
curl -X POST "/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "account": "user_account_123",
    "password": "password123"
  }'
```

### 3. **混合注册**
```bash
# 手机号+账号注册（验证码方式）
curl -X POST "/user/login" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "***********",
    "account": "my_custom_account",
    "code": "123456"
  }'
```

## 错误处理

### 1. **格式验证错误**
```json
{
  "statusCode": 400,
  "message": "账号格式不正确，支持8-20位字母、数字、下划线、中文，不能为纯数字",
  "error": "Bad Request"
}
```

### 2. **唯一性验证错误**
```json
{
  "statusCode": 400,
  "message": "该手机号已被注册，请使用其他手机号或直接登录",
  "error": "Bad Request"
}
```

### 3. **标识符为空错误**
```json
{
  "statusCode": 400,
  "message": "手机号和账号不能同时为空，至少需要提供一个",
  "error": "Bad Request"
}
```

## 向后兼容性

### 1. **现有用户数据**
- ✅ 现有用户数据保持不变（phone字段有值，account字段为空）
- ✅ 现有用户的登录方式不受影响
- ✅ 现有API接口保持兼容

### 2. **数据迁移**
- ✅ 无需数据迁移，现有数据自动兼容
- ✅ 新用户可以选择使用手机号或账号进行注册
- ✅ 确保迁移过程中不影响现有用户的正常使用

### 3. **API兼容性**
- ✅ 现有的手机号登录API保持不变
- ✅ 新增的账号登录通过相同的API端点实现
- ✅ 响应格式保持一致，只是新增了account字段

## 安全特性

### 1. **输入验证**
- ✅ 严格的格式验证防止恶意输入
- ✅ 账号不能是手机号格式，避免混淆
- ✅ 长度限制防止过长输入

### 2. **唯一性保证**
- ✅ 业务层面的唯一性验证
- ✅ 防止重复注册
- ✅ 数据一致性保证

### 3. **错误信息**
- ✅ 友好的错误提示
- ✅ 不泄露敏感信息
- ✅ 明确的操作指导

这个灵活的用户标识系统为用户提供了更多的登录选择，同时保持了系统的安全性和数据一致性，确保了向后兼容性和良好的用户体验。
