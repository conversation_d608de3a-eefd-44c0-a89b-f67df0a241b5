import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { OrderSource, OrderStatus, OrderType, PayType, SalesType, OpenPlatformOrderMainType, OpenPlatformOrderResourceType } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class OrderEntity {
  /**
   * 用户id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  /**
   * 团队id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 权益包id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  interestId: Types.ObjectId

  /**
   * 创建者id
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    default: null
  })
  creatorId?: Types.ObjectId

  /**
   * 手机号
   */
  @Prop({
    type: String,
    required: false
  })
  phone: string

  /**
   * 创建者username
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  creatorName?: string

  /**
   * 订单编号
   */
  @Prop({
    type: String,
    required: true
  })
  orderNo: string

  /**
   * 订单状态
   */
  @Prop({
    type: String,
    enum: {
      values: [OrderStatus.Pending, OrderStatus.Paid, OrderStatus.Cancelled, OrderStatus.Refunded],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: OrderStatus.Pending
  })
  orderStatus: OrderStatus

  /**
   * 订单类型
   */
  @Prop({
    type: String,
    enum: {
      values: [
        OrderType.Create,
        OrderType.Upgrade,
        OrderType.Renew,
        OrderType.Gift,
        OrderType.OpenPlatformCreate,
        OrderType.OpenPlatformRenew,
        OrderType.OpenPlatformAddon,
        OrderType.OpenPlatformAccountPoints,
        OrderType.OpenPlatformTraffic
      ],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: OrderType.Create
  })
  orderType: OrderType

  /**
   * 订单来源
   */
  @Prop({
    type: String,
    enum: {
      values: [OrderSource.Online, OrderSource.System, OrderSource.OpenPlatform],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: OrderSource.Online
  })
  orderSource: OrderSource

  /**
   * 支付类型
   */
  @Prop({
    type: String,
    enum: {
      values: [PayType.WechatPay, PayType.Alipay, PayType.CorporateTransfer, PayType.Other],
      message: '{VALUE} is not a valid state'
    },
    required: false,
    default: PayType.WechatPay
  })
  payType?: PayType

  /**
   * 支付时间
   */
  @Prop({
    type: Date,
    required: false
  })
  payTime?: Date

  /**
   * 订单金额
   */
  @Prop({
    type: Number,
    required: true
  })
  totalAmount: number

  /**
   * 应付金额
   */
  @Prop({
    type: Number,
    required: true
  })
  payableAmount: number

  /**
   * 实付金额
   */
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  payAmount: number

  /**
   * 购买月份
   */
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  vipMonth: number

  /**
   * 赠送月份
   */
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  freeMonth: number

  /**
   * 赠送天数
   */
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  days: number

  /**
   * 权益包数量
   */
  @Prop({
    type: Number,
    required: true
  })
  interestCount: number

  /**
   * 是否是免费
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isFree: boolean

  /**
   * 订单过期
   */
  @Prop({
    type: Date
  })
  expiredAt?: Date

  /**
   * 创建时间
   */
  @Prop({
    type: Date
  })
  createdAt?: Date

  /**
   * 更新时间
   */
  @Prop({
    type: Date
  })
  updatedAt?: Date

  /**
   * 微信支付二维码url
   */
  @Prop({
    type: String
  })
  weixinUrl?: string

  /**
   * 阿里云支付二维码url
   */
  @Prop({
    type: String
  })
  alipayUrl?: string

  @Prop({
    type: String
  })
  remark?: string

  @Prop({
    type: String,
    required: false,
    default: null
  })
  channelCode?: string

  @Prop({
    type: Types.ObjectId,
    required: false,
    default: null
  })
  customerId?: Types.ObjectId

  /**
   * 销售类型
   */
  @Prop({
    type: Number,
    enum: SalesType,
    required: false,
    default: SalesType.NotBuy
  })
  salesType?: SalesType

  /**
   * 开放平台订单主类型（仅开放平台订单使用）
   */
  @Prop({
    type: String,
    enum: OpenPlatformOrderMainType,
    required: false
  })
  openPlatformMainType?: OpenPlatformOrderMainType

  /**
   * 主订单ID（仅增购订单使用，指向关联的主订单）
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  parentOrderId?: Types.ObjectId

  /**
   * 订单开始时间（开放平台订单专用）
   */
  @Prop({
    type: Date,
    required: false
  })
  startTime?: Date

  /**
   * 订单结束时间（开放平台订单专用）
   */
  @Prop({
    type: Date,
    required: false
  })
  endTime?: Date

  /**
   * 账号数量（开放平台订单专用）
   */
  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0
  })
  accountCapacity?: number

  /**
   * 流量数量（开放平台订单专用，单位：GB）
   */
  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0
  })
  trafficCount?: number

  /**
   * 时长（开放平台订单专用，单位：月）
   */
  @Prop({
    type: Number,
    required: false,
    min: 0,
    default: 0
  })
  duration?: number

  /**
   * 创建订单的应用appId（仅开放平台订单使用）
   */
  @Prop({
    type: String,
    required: false,
    index: true
  })
  sourceAppId?: string

  /**
   * 创建者的开放平台用户ID（仅开放平台订单使用）
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  openPlatformUserId?: Types.ObjectId

  /**
   * 开放平台订单资源类型（新订单系统专用）
   */
  @Prop({
    type: String,
    enum: OpenPlatformOrderResourceType,
    required: false
  })
  resourceType?: OpenPlatformOrderResourceType

  /**
   * 流量过期时间（仅流量订单使用，流量有效期1年）
   */
  @Prop({
    type: Date,
    required: false
  })
  trafficExpiredAt?: Date
}

export const OrderSchema: ModelDefinition = {
  name: OrderEntity.name,
  schema: SchemaFactory.createForClass(OrderEntity)
}

export const OrderMongoose = MongooseModule.forFeature([OrderSchema])
