import {
  Body,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Res
} from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  PatchPlatformAccountRequest,
  PlatformAccountDetailResponseDTO,
  PlatformAccountRequest,
  PlatformAccountListResponseDTO,
  PlatformAccountListRequest,
  PutPlatformAccountOverviewRequest,
  PutPlatformAccountOverviewResponse,
  WechatKeysCreateRequest,
  PlatformAccountPutPrincipal,
  PlatformAccountPutOperators,
  PlatformAccountCookieRequest,
  AccountOverviewReportRequest
} from './platform-account.dto'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { PlatformAccountService } from './platform-account.service'
import { isString } from 'class-validator'
import {
  UpdateVideoAccountResponseDTO,
  WxAssistantCreateRequest,
  WxAssistantResponseDTO,
  AccountLockListResponseDTO
} from '../wx-third-platform/wx-ipad/wx-ipad.dto'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { PlatformAccountCloudService } from './platform-account-cloud.service'
import {
  AccountCloudBaseRequst,
  AccountLocationResponseDTO,
  AccountMusicBodyRequest,
  AccountMusicCategoryResponseDTO,
  AccountMusicResponseDTO,
  AccountValidationResponseDTO
} from './platform-account-cloud.dto'
import { AccountOverviewSummaryResponseDTO } from './platform-account-statistic.dto'
import { PlatformAccountStatisticService } from './platform-account-statistic.service'
import { BrowserCollectService } from '../browser/browser-collect.service'
import { BrowserFavoriteDTO, PostBrowserCollectRequest } from '../browser/browser.dto'
import { PlatformAccountReportService } from './platform-account-report.service'
import { FastifyReply } from 'fastify'
import dayjs from 'dayjs'
import { StatisticCommonService } from '@yxr/common'

@Controller('platform-accounts')
@ApiTags('媒体账号管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class PlatformAccountController {
  constructor(
    private readonly platformAccountService: PlatformAccountService,
    private readonly statisticCommonService: StatisticCommonService,
    private readonly platformAccountReportService: PlatformAccountReportService,
    private readonly platformAccountStatisticService: PlatformAccountStatisticService,
    private readonly platformAccountCloudService: PlatformAccountCloudService,
    private readonly browserCollectService: BrowserCollectService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建账号
   * @param body
   * @returns
   */
  @Put()
  @ApiOperation({ summary: '创建账号' })
  @ApiOkResponse({ type: PlatformAccountDetailResponseDTO })
  async putPlatformAccounts(@Body() body: PlatformAccountRequest) {
    return this.platformAccountService.platformAccountCreateAsync(body)
  }

  /**
   * 账号列表
   * @param size
   * @param page
   * @param time
   * @returns
   */
  @Get()
  @ApiOperation({ summary: '账号列表' })
  @ApiOkResponse({ type: PlatformAccountListResponseDTO })
  @ApiQuery({ type: PlatformAccountListRequest })
  async getPlatformAccounts(
    @Query() query: PlatformAccountListRequest,
    @Query('platforms[]', { transform: (value) => (isString(value) ? [value] : value) })
    platforms: string[]
  ) {
    return await this.platformAccountService.getPlatformAccountListAsync(query, platforms)
  }

  /**
   * 账号详情
   * @param platformAccountId
   * @returns
   */
  @Get(':platformAccountId')
  @ApiOperation({ summary: '获取账号详情' })
  @ApiOkResponse({ type: PlatformAccountDetailResponseDTO })
  async getPlatformAccountById(@Param('platformAccountId') platformAccountId: string) {
    return await this.platformAccountService.getPlatformAccountDetailAsync(platformAccountId)
  }

  /**
   * 账号音乐
   * @param platformAccountId
   * @returns
   */
  @Get(':platformAccountId/music')
  @ApiOperation({ summary: '获取账号音乐' })
  @ApiOkResponse({ type: AccountMusicResponseDTO })
  async getPlatformAccountMusic(
    @Param('platformAccountId') platformAccountId: string,
    @Query() query: AccountMusicBodyRequest
  ) {
    return await this.platformAccountCloudService.getPlatformAccountMusic(platformAccountId, query)
  }

  /**
   * 账号音乐分类
   * @param platformAccountId
   * @returns
   */
  @Get(':platformAccountId/music/category')
  @ApiOperation({ summary: '获取账号音乐分类' })
  @ApiOkResponse({ type: AccountMusicCategoryResponseDTO })
  async getPlatformAccountMusicCategory(@Param('platformAccountId') platformAccountId: string) {
    return await this.platformAccountCloudService.getPlatformAccountMusicCategory(platformAccountId)
  }

  @Get(':platformAccountId/location')
  @ApiOperation({ summary: '获取账号地理位置' })
  @ApiOkResponse({ type: AccountLocationResponseDTO })
  async getPlatformAccountLocation(
    @Param('platformAccountId') platformAccountId: string,
    @Query() query: AccountCloudBaseRequst
  ) {
    return await this.platformAccountCloudService.getPlatformAccountLocation(
      platformAccountId,
      query
    )
  }

  /**
   * 更新媒体账号信息
   * @param platformAccountId
   * @returns
   */
  @Patch(':platformAccountId')
  @ApiOperation({ summary: '更新账号' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async PatchPlatformAccount(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: PatchPlatformAccountRequest
  ) {
    return await this.platformAccountService.patchPlatformAccountsAsync(platformAccountId, body)
  }

  /**
   * 更新账号冻结状态
   * @param platformAccountId
   * @param body
   * @returns
   */
  @Put(':platformAccountId/freeze')
  @ApiOperation({ summary: '更新账号冻结状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async putPlatformAccountFreeze(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: { isFreeze: boolean } = { isFreeze: false }
  ) {
    return await this.platformAccountService.putPlatformAccountFreeze(
      platformAccountId,
      body.isFreeze
    )
  }

  /**
   * 账号删除
   * @param platformAccountId
   * @returns
   */
  @Delete(':platformAccountId')
  @ApiOperation({ summary: '账号删除' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deletePlatformAccounts(@Param('platformAccountId') platformAccountId: string) {
    return await this.platformAccountService.deletePlatformAccountAsync(platformAccountId)
  }

  /**
   * 账号概览数据上报
   * @param platformAccountId
   * @returns
   */
  @Put(':platformAccountId/overview')
  @ApiOperation({ summary: '账号数据上报' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async putPlatformAccountOverviews(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body?: PutPlatformAccountOverviewRequest
  ) {
    return await this.platformAccountService.platformAccountDataSync(platformAccountId)
    // return await this.platformAccountService.putPlatformAccountOverviews(platformAccountId, body)
  }

  @Get(':platformAccountId/videoAccounts')
  @ApiOperation({ summary: '微信账号下视频号列表' })
  @ApiOkResponse({ type: WxAssistantResponseDTO })
  async getAssistantMembers(@Param('platformAccountId') platformAccountId: string) {
    return await this.platformAccountService.getAssistantMembers(platformAccountId)
  }

  @Post(':platformAccountId/videoAccounts/auths')
  @ApiOperation({ summary: '批量添加微信账号下的视频号' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiBody({ type: [WxAssistantCreateRequest] })
  async assistantMembersAuthorize(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: WxAssistantCreateRequest[]
  ) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    return await this.platformAccountService.assistantMembersAuthorize(
      platformAccountId,
      currentTeamId,
      currentUserId,
      body
    )
  }

  @Get(':platformAccountId/cookie')
  @ApiOperation({ summary: '获取账号cookie, 微信号下视频号cookie令牌（需要微信账号锁）' })
  @ApiOkResponse({ type: UpdateVideoAccountResponseDTO })
  @ApiHeader({ name: 'wxkey', required: false })
  async getToken(@Param('platformAccountId') platformAccountId: string) {
    const { teamId: currentTeamId } = this.request.session
    const wxkey = this.request.headers.wxkey as string
    return await this.platformAccountService.updateAssistantMember(
      platformAccountId,
      currentTeamId,
      wxkey
    )
  }

  @Post(':platformAccountId/cookie-refresh')
  @ApiOperation({ summary: '刷新账号cookie, 刷新微信号下视频号cookie令牌（需要微信账号锁）' })
  @ApiOkResponse({ type: UpdateVideoAccountResponseDTO })
  @ApiHeader({ name: 'wxkey', required: false })
  async getRefreshToken(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: PlatformAccountCookieRequest
  ) {
    const { teamId: currentTeamId } = this.request.session
    const wxkey = this.request.headers.wxkey as string
    return await this.platformAccountService.getRefreshToken(
      platformAccountId,
      currentTeamId,
      wxkey,
      body
    )
  }

  @Post('wechat/keys')
  @ApiOperation({ summary: '生成锁定微信账号密钥' })
  @ApiOkResponse({ type: AccountLockListResponseDTO })
  async postAccountLock(@Body() body: WechatKeysCreateRequest) {
    const { teamId: currentTeamId } = this.request.session
    return await this.platformAccountService.postAccountLock(body.platformAccountIds, currentTeamId)
  }

  @Delete(':platformAccountId/wechat/keys')
  @ApiOperation({ summary: '解锁微信账号（需要微信账号锁）' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiHeader({ name: 'wxkey', required: true })
  async deleteAccountLock(@Param('platformAccountId') platformAccountId: string) {
    const { teamId: currentTeamId } = this.request.session
    const wxkey = this.request.headers.wxkey as string
    return await this.platformAccountService.deleteAccountLock(
      platformAccountId,
      currentTeamId,
      wxkey
    )
  }

  @Put(':platformAccountId/wechat/keys/alive')
  @ApiOperation({ summary: '更新账号锁定有效时间（需要微信账号锁）' })
  @ApiOkResponse({ type: UpdateVideoAccountResponseDTO })
  @ApiHeader({ name: 'wxkey', required: true })
  async putAccountLock(@Param('platformAccountId') platformAccountId: string) {
    const { teamId: currentTeamId } = this.request.session
    const wxkey = this.request.headers.wxkey as string
    return await this.platformAccountService.putAccountLock(platformAccountId, currentTeamId, wxkey)
  }

  /**
   * 账号概览数据列表
   * @param platformAccountIds
   * @returns
   */
  @Get('overviews')
  @ApiOperation({ summary: '账号概览' })
  @ApiOkResponse({ type: [PutPlatformAccountOverviewResponse] })
  async getPlatformAccountOverviews(
    @Query('platformAccountIds[]', { transform: (value) => (isString(value) ? [value] : value) })
    platformAccountIds: string[]
  ) {
    return await this.platformAccountService.getPlatformAccountOverviews(platformAccountIds)
  }

  /**
   * 账号概览数据列表
   * @param platformAccountIds
   * @returns
   */
  @Get('overviews/summary')
  @ApiOperation({ summary: '账号概览汇总', deprecated: true })
  @ApiOkResponse({ type: AccountOverviewSummaryResponseDTO })
  async getAccountOverviewSummary(@Query('platform') platform: string) {
    return await this.platformAccountStatisticService.getAccountOverviewSummary(platform)
  }

  @Get(':platformAccountId/members')
  @ApiOperation({ summary: '根据账号获取运营人' })
  @ApiOkResponse({ type: AccountOverviewSummaryResponseDTO })
  async getPlatformAccountMembers(@Param('platformAccountId') platformAccountId: string) {
    return this.platformAccountService.getPlatformAccountMembers(platformAccountId)
  }

  @Put(':platformAccountId/principal')
  @ApiOperation({ summary: '设置负责人' })
  async setPlatformAccountPrincipal(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: PlatformAccountPutPrincipal
  ) {
    return this.platformAccountService.setPlatformAccountPrincipal(
      platformAccountId,
      this.request.session,
      body.memberId
    )
  }

  @Delete(':platformAccountId/principal')
  @ApiOperation({ summary: '删除负责人' })
  async deletePlatformAccountPrincipal(@Param('platformAccountId') platformAccountId: string) {
    return this.platformAccountService.setPlatformAccountPrincipal(
      platformAccountId,
      this.request.session
    )
  }

  @Get('principal/overview')
  @ApiOperation({ summary: '负责人数据' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async getPrincipalOverviews() {
    return this.platformAccountService.getPrincipalOverview()
  }

  @Get(':memberId/platformAccounts')
  @ApiOperation({ summary: '根据成员获取运营账号' })
  async getPlatformAccountsByMember(@Param('memberId') memberId: string) {
    return this.platformAccountService.getPlatformAccountsByMember(memberId)
  }

  @Put(':platformAccountId/operator')
  @ApiOperation({ summary: '设置账号运营人' })
  async putPlatformAccount(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: PlatformAccountPutOperators
  ) {
    return this.platformAccountService.putPlatformAccount(platformAccountId, body.memberIds)
  }

  @Put(':platformAccountId/collect')
  @ApiOperation({ summary: '收藏分组新增/修改' })
  @ApiOkResponse({ type: BrowserFavoriteDTO })
  async browserFavorites(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: PostBrowserCollectRequest
  ) {
    return await this.browserCollectService.PostBrowserCollect(platformAccountId, body)
  }

  @Delete(':platformAccountId/collect/:collectId')
  @ApiOperation({ summary: '删除收藏地址' })
  @ApiOkResponse()
  async deleteBrowserFavorites(
    @Param('platformAccountId') platformAccountId: string,
    @Param('collectId') collectId: string
  ) {
    return await this.browserCollectService.DelectBrowserCollect(platformAccountId, collectId)
  }

  /**
   * 账号概览数据导出
   * @param body
   * @returns
   */
  @Get('overviews/export')
  @ApiOperation({ summary: '账号数据导出' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async platformAccountOverviewsReport(
    @Query() body: AccountOverviewReportRequest,
    @Res() res: FastifyReply
  ) {
    const exportData = await this.platformAccountReportService.exportAccountOverviews(body)
    if (exportData.length <= 0) {
      res.status(200).send({
        statusCode: 0
      })
      return
    }
    const fields = await this.statisticCommonService.getAccountOverviewField(body.platform)
    // 定义一个映射，将字段名映射为中文
    const titleMap = {
      platformAccountName: '账号名字',
      platformName: '所属平台',
      loginStatus: '账号状态',
      updatedAt: '数据更新时间'
    }
    fields.forEach((item) => {
      titleMap[item.key] = item.value
    })

    // 动态生成 CSV 头部，并将字段名转换为中文
    const headers = Object.keys(exportData[0]).map((key) => ({
      id: key,
      title: titleMap[key] || key.charAt(0).toUpperCase() + key.slice(1) // 默认为字段名首字母大写
    }))

    // 生成 CSV 内容
    const csvContent = await this.platformAccountReportService.generateCsv(exportData, headers)
    const time = dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD')
    const exportFileName = exportData[0].platformName + '账号数据' + time + '.csv'

    // 设置响应头
    res.header('Content-Type', 'text/csv')
    res.header('Content-Disposition', 'attachment;filename=' + encodeURIComponent(exportFileName))

    // 返回 CSV 内容
    res.send(csvContent)
  }

  /**
   * 检测账号有效性
   * @param platformAccountId 平台账号ID
   * @returns 账号有效性检测结果
   */
  @Get(':platformAccountId/validate')
  @ApiOperation({ summary: '检测账号有效性' })
  @ApiOkResponse({ type: AccountValidationResponseDTO })
  async validateAccount(@Param('platformAccountId') platformAccountId: string) {
    const { teamId: currentTeamId } = this.request.session
    return await this.platformAccountCloudService.validateAccount(platformAccountId, currentTeamId)
  }
}
