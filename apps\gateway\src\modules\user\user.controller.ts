import { Body, Controller, Delete, Post, Get, Patch, Put, Param } from '@nestjs/common'
import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader
} from '@nestjs/swagger'

import { UserService } from './user.service'
import {
  changePhoneRequestDto,
  patchUserRequestBodyDTO,
  resetPasswordRequestDto,
  TokenValidateRequestBodyDTO,
  UserLoginOkResponseDTO,
  UserLoginRegisterRequestBodyDTO,
  UserOkUserInfoResponseDTO,
  UserSendCodeRequestBodyDTO,
  UserSendCodeResponseDTO
} from './user.dto'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'

@Controller('users')
@ApiTags('用户管理')
export class UserController {
  constructor(private readonly userService: UserService) {}

  /**
   * token有效性检测
   * @param data
   */
  @Post('token/validate')
  @ApiOperation({ summary: '用户token有效性检测' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  tokenValidate(@Body() body: TokenValidateRequestBodyDTO) {
    return this.userService.tokenValidate(body)
  }

  /**
   * 用户注册
   * @param data
   */
  @Post('auth')
  @ApiOperation({ summary: '用户注册/登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestResponseDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  registerUser(@Body() data: UserLoginRegisterRequestBodyDTO) {
    return this.userService.putLoginUser(data)
  }

  /**
   * 用户退出登陆
   * @param data
   */
  @Delete('auth')
  @ApiOperation({ summary: '退出登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiHeader({ name: 'authorization', required: true })
  logout() {
    return this.userService.logout()
  }

  /**
   *  发送短信验证码
   * @returns
   * @param data
   */
  @ApiOperation({
    summary: '发送验证码',
    description: '注意: 为了方便测试, 本接口在测试环境会将验证码返回给调用方'
  })
  @ApiOkResponse({ type: UserSendCodeResponseDTO, description: '操作成功' })
  @ApiBadRequestResponse({ description: '验证码发送失败', type: BaseBadRequestResponseDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @Post('sms-code')
  sendVerificationCode(@Body() data: UserSendCodeRequestBodyDTO) {
    return this.userService.sendVerificationCode(data)
  }

  @Get('info')
  @ApiOperation({ summary: '获取当前登录用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getUserInfo() {
    return await this.userService.getUserInfo()
  }

  @Patch('info')
  @ApiOperation({ summary: '修改当前用户信息' })
  @ApiOkResponse({ type: UserOkUserInfoResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async updateUserInfo(@Body() data: patchUserRequestBodyDTO) {
    return await this.userService.updateUserInfo(data)
  }

  @Put('password')
  @ApiOperation({ summary: '重置或设置密码' })
  @ApiOkResponse({ description: '操作成功', type: UserOkUserInfoResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async resetPassword(@Body() data: resetPasswordRequestDto) {
    await this.userService.resetPassword(data)
  }

  @Get('phone/token/:code')
  @ApiOperation({ summary: '更换手机第一步，校验用户已绑定手机获取更换手机token' })
  @ApiOkResponse({ description: '操作成功', type: UserLoginOkResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getPhoneToken(@Param('code') code: string) {
    return await this.userService.getPhoneToken(code)
  }

  @Put('phone')
  @ApiOperation({ summary: '更换手机号' })
  @ApiOkResponse({ description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async putPhone(@Body() data: changePhoneRequestDto) {
    await this.userService.changePhone(data)
  }
}
