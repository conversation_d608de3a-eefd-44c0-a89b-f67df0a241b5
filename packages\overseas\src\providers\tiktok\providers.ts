import { Provider } from '@nestjs/common'
import { OverseasPlatformNames } from '../../constants'
import { AccountAuthProvider } from '../account-auth.provider'
import { ContentPublishProvider } from '../content-publish.provider'
import { TiktokAccountAuthProvider } from './tiktok-account-auth.provider'
import { TiktokContentPublishProvider } from './tiktok-content-publish.provider'
import { TiktokApi } from './tiktok-api'

export const providers = [
  TiktokApi,
  {
    provide: AccountAuthProvider.register_token(OverseasPlatformNames.Tiktok),
    useClass: TiktokAccountAuthProvider
  },
  {
    provide: ContentPublishProvider.register_token(OverseasPlatformNames.Tiktok),
    useClass: TiktokContentPublishProvider
  },
  // {
  //   provide: UserinfoService.token(Tiktok),
  //   useClass: TiktokBusinessUserinfoService
  // },
  // {
  //   provide: ContentService.token(Tiktok),
  //   useClass: TiktokBusinessContentService
  // },
  // {
  //   provide: CommentService.token(Tiktok),
  //   useClass: TiktokBusinessCommentService
  // },
  // {
  //   provide: MessageService.token(Tiktok),
  //   useClass: TiktokBusinessMessageService
  // },
  // {
  //   provide: DataRetrievalService.token(Tiktok),
  //   useClass: TiktokBusinessDataRetrievalService
  // },
  // {
  //   provide: PermissionProvider.token(Tiktok),
  //   useClass: TiktokBusinessPermissionProvider
  // },
  // {
  //   provide: WebhookProvider.token(Tiktok),
  //   useClass: TiktokBusinessWebhookProvider
  // }
] as Provider[]
