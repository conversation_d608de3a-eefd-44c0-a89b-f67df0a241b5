import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class CommonTopicsGroupEntity {
  @Prop({
    type: String,
    required: true
  })
  name: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const CommonTopicsGroupSchema: ModelDefinition = {
  name: CommonTopicsGroupEntity.name,
  schema: SchemaFactory.createForClass(CommonTopicsGroupEntity)
}

export const CommonTopicsGroupMongoose = MongooseModule.forFeature([CommonTopicsGroupSchema])
