import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common'
import axios from 'axios'
import {
  PostRobotRequest,
  RobotOverviewResponse,
  RobotResponse,
  SendMessageTestRequest
} from './robots.dto'
import { InjectModel } from '@nestjs/mongoose'
import { RobotEntity, TeamEntity } from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import { ReportTypeEnum, RobotTypeEnum, SendingTimeTypeEnum } from '@yxr/common'
import dayjs from 'dayjs'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class RobotsService {
  constructor(
    @InjectModel(RobotEntity.name) private robotModel: Model<RobotEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    private readonly loggerService: TlsService
  ) {}

  /**
   * 修改数据推送设置
   * @param id
   */
  async putRobot(currentTeamId: string, body: PostRobotRequest): Promise<RobotResponse> {
    const robot = await this.robotModel.findOne({
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!RobotsService.checkWebhookUrl(body.webhookUrl, body.robotType)) {
      throw new ForbiddenException('webhook地址格式不正确')
    }
    let data: RobotEntity
    if (robot) {
      data = await this.robotModel.findOneAndUpdate(
        {
          _id: new Types.ObjectId(robot.id)
        },
        {
          robotType: body.robotType,
          reportType: body.reportType,
          webhookUrl: body.webhookUrl,
          sendTimeType: body.sendTimeType
        },
        {
          new: true // 这个选项确保返回更新后的文档
        }
      )
    } else {
      data = await this.robotModel.create({
        teamId: new Types.ObjectId(currentTeamId),
        robotType: body.robotType,
        reportType: body.reportType,
        webhookUrl: body.webhookUrl,
        sendTimeType: body.sendTimeType
      })
    }

    return {
      teamId: data.teamId.toString(),
      robotType: data.robotType,
      reportType: data.reportType,
      webhookUrl: data.webhookUrl,
      sendTimeType: data.sendTimeType,
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 获取数据推送设置
   * @param id
   */
  async getRobot(currentTeamId: string): Promise<RobotResponse> {
    const data = await this.robotModel.findOne({
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!data) {
      throw new NotFoundException('数据推送设置不存在')
    }

    return {
      teamId: data.teamId.toString(),
      robotType: data.robotType,
      reportType: data.reportType,
      webhookUrl: data.webhookUrl,
      sendTimeType: data.sendTimeType,
      createdAt: data.createdAt.getTime()
    }
  }

  async sendRobotMessage(
    teamId: string,
    robotType: RobotTypeEnum,
    reportType: string,
    sendTimeType: string[],
    webhookUrl: string,
    robotOverview: RobotOverviewResponse
  ) {
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (!team) {
      return
    }
    await this.sendMessage(
      team.name,
      robotType,
      reportType,
      webhookUrl,
      robotOverview,
      false,
      sendTimeType
    )
  }

  async sendTestMessage(currentTeamId: string, body: SendMessageTestRequest) {
    const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))
    if (!team) {
      throw new ForbiddenException('团队不存在')
    }
    if (!RobotsService.checkWebhookUrl(body.webhookUrl, body.robotType)) {
      throw new ForbiddenException('webhook地址格式不正确')
    }
    const reportTypes = Array.isArray(body.reportType) ? body.reportType : [body.reportType] // 如果是字符串，包装成数组
    for (const reportType of reportTypes) {
      const robotData = {
        current: {
          fansTotal: 100,
          playTotal: 200,
          readTotal: 300,
          commentsTotal: 400,
          likesTotal: 500,
          favoritesTotal: 600
        },
        increments: {
          fansTotal: 10,
          playTotal: 20,
          readTotal: 30,
          commentsTotal: -40,
          likesTotal: -50,
          favoritesTotal: -60
        }
      } as RobotOverviewResponse

      await this.sendMessage(
        team.name,
        body.robotType,
        reportType,
        body.webhookUrl,
        robotData,
        true
      )
    }
  }

  private async sendMessage(
    teamName: string,
    robotType: RobotTypeEnum,
    reportType: string,
    webhookUrl: string,
    robotOverview: RobotOverviewResponse,
    isTest: boolean = false,
    sendTimeType?: string[]
  ) {
    let reportName = '日报'
    let date = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY年MM月DD日')
    if (reportType == ReportTypeEnum.Weekly) {
      reportName = '周报'
      // 计算上周一的日期
      const lastWeekMonday = dayjs()
        .tz('Asia/Shanghai')
        .subtract(1, 'week')
        .startOf('week')
        .add(1, 'day') // 因为 startOf('week') 默认是周日，+1天调整为周一
      // 计算上周日的日期
      let lastWeekSunday = lastWeekMonday.add(6, 'day')
      if (sendTimeType && sendTimeType.includes(SendingTimeTypeEnum.Friday)) {
        const lastWeekStart = lastWeekMonday.add(5, 'day') //上周六
        const lastWeekEnd = lastWeekMonday.add(11, 'day') //本周五
        date = `${lastWeekStart.format('YYYY年MM月DD日')} ~ ${lastWeekEnd.format('MM月DD日')}`
      } else {
        date = `${lastWeekMonday.format('YYYY年MM月DD日')} ~ ${lastWeekSunday.format('MM月DD日')}`
      }
    }
    const title = `【${teamName}】${date}【数据统计】${reportName}`

    switch (robotType) {
      case RobotTypeEnum.FeiShu:
        await this.sendFeishuMessageTemplate(webhookUrl, title, robotOverview, isTest)
        break
      case RobotTypeEnum.DingDing:
        await this.sendDingDingMessageTemplate(webhookUrl, title, robotOverview, isTest)
        break
      case RobotTypeEnum.QiyeWx:
        await this.sendQiWeiMessageTemplate(webhookUrl, title, robotOverview, isTest)
        break
    }
  }

  /**
   * 飞书数据推送模型
   * https://open.feishu.cn/open-apis/bot/v2/hook/f9431eae-1309-4411-a9ac-8496cb06c852
   * @param webhookUrl
   */
  private async sendFeishuMessageTemplate(
    webhookUrl: string,
    title: string,
    robotData: RobotOverviewResponse,
    isTest = false
  ) {
    try {
      const WEBHOOK_URL = webhookUrl
      const data = {
        msg_type: 'post',
        content: {
          post: {
            zh_cn: {
              title: title,
              content: [
                [{ tag: 'text', text: '────────────────' }],
                [
                  { tag: 'text', text: `粉丝：${robotData.current.fansTotal}    ` },
                  {
                    tag: 'text',
                    text: `${robotData.increments.fansTotal > 0 ? '📈' : '📉'}${robotData.increments.fansTotal}`,
                    un_escape: true
                  }
                ],
                [
                  {
                    tag: 'text',
                    text: `播放：${robotData.current.playTotal + robotData.current.readTotal}    `
                  },
                  {
                    tag: 'text',
                    text: `${robotData.increments.playTotal + robotData.increments.readTotal > 0 ? '📈' : '📉'}${robotData.increments.playTotal + robotData.increments.readTotal}`,
                    un_escape: true
                  }
                ],
                [
                  { tag: 'text', text: `评论：${robotData.current.commentsTotal}    ` },
                  {
                    tag: 'text',
                    text: `${robotData.increments.commentsTotal > 0 ? '📈' : '📉'}${robotData.increments.commentsTotal}`,
                    un_escape: true
                  }
                ],
                [
                  { tag: 'text', text: `点赞：${robotData.current.likesTotal}    ` },
                  {
                    tag: 'text',
                    text: `${robotData.increments.likesTotal > 0 ? '📈' : '📉'}${robotData.increments.likesTotal}`,
                    un_escape: true
                  }
                ],
                [
                  { tag: 'text', text: `收藏：${robotData.current.favoritesTotal}    ` },
                  {
                    tag: 'text',
                    text: `${robotData.increments.favoritesTotal > 0 ? '📈' : '📉'}${robotData.increments.favoritesTotal}`,
                    un_escape: true
                  }
                ]
              ]
            }
          }
        }
      }
      if (isTest) {
        data.content.post.zh_cn.content.push([{ tag: 'text', text: '<此条为测试内容>' }])
      }
      const result = await axios.post(WEBHOOK_URL, data)
      if (result.data.code !== 0) {
        await this.loggerService.info(null, `机器人消息发送失败:webhookUrl=${webhookUrl}`, {
          robotType: RobotTypeEnum.FeiShu,
          title,
          error: JSON.stringify(result.data)
        })
      }
    } catch (error) {
      await this.loggerService.info(null, `机器人消息发送异常:webhookUrl=${webhookUrl}`, {
        robotType: RobotTypeEnum.FeiShu,
        title,
        error: error.message
      })
      // throw new ForbiddenException('消息发送失败')
    }
  }

  /**
   * 钉钉数据推送模型
   * https://oapi.dingtalk.com/robot/send?access_token=e7e712a7ede8d65a97e7ccd08eb291a500b6900390d4d44811adf5f7a078dffd
   * @param webhookUrl
   */
  private async sendDingDingMessageTemplate(
    webhookUrl: string,
    title: string,
    robotData: RobotOverviewResponse,
    isTest = false
  ) {
    try {
      const WEBHOOK_URL = webhookUrl
      const data = {
        msgtype: 'markdown',
        markdown: {
          title: title,
          text: `
          ### ${title}  
          ---  
          **粉丝**: ${robotData.current.fansTotal}    ${robotData.increments.fansTotal > 0 ? '📈' : '📉'}${robotData.increments.fansTotal}  
          **播放**: ${robotData.current.playTotal + robotData.current.readTotal}    ${robotData.increments.playTotal + robotData.increments.readTotal > 0 ? '📈' : '📉'}${robotData.increments.playTotal + robotData.increments.readTotal}  
          **评论**: ${robotData.current.commentsTotal}    ${robotData.increments.commentsTotal > 0 ? '📈' : '📉'}${robotData.increments.commentsTotal}  
          **点赞**: ${robotData.current.likesTotal}    ${robotData.increments.likesTotal > 0 ? '📈' : '📉'}${robotData.increments.likesTotal}  
          **收藏**: ${robotData.current.favoritesTotal}    ${robotData.increments.favoritesTotal > 0 ? '📈' : '📉'}${robotData.increments.favoritesTotal}  
          ${isTest ? '<此条为测试内容>' : ''}  
          `
        }
      }
      const result = await axios.post(WEBHOOK_URL, data)
      if (result.data.errcode !== 0) {
        await this.loggerService.info(null, `机器人消息发送失败:webhookUrl=${webhookUrl}`, {
          robotType: RobotTypeEnum.DingDing,
          title,
          error: JSON.stringify(result.data)
        })
      }
    } catch (error) {
      await this.loggerService.info(null, `机器人消息发送异常:webhookUrl=${webhookUrl}`, {
        robotType: RobotTypeEnum.DingDing,
        title,
        error: error.message
      })
    }
  }

  /**
   * 企业微信数据推送模型
   * https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e679a4e8-078b-411f-af26-daedf2668ed6
   * @param webhookUrl
   */
  private async sendQiWeiMessageTemplate(
    webhookUrl: string,
    title: string,
    robotData: RobotOverviewResponse,
    isTest = false
  ) {
    try {
      const WEBHOOK_URL = webhookUrl
      const data = {
        msgtype: 'markdown',
        markdown: {
          content: `
          ### ${title}  
          ────────────────  
          **粉丝**: ${robotData.current.fansTotal}    ${robotData.increments.fansTotal > 0 ? '📈' : '📉'}${robotData.increments.fansTotal}  
          **播放**: ${robotData.current.playTotal + robotData.current.readTotal}    ${robotData.increments.playTotal + robotData.increments.readTotal > 0 ? '📈' : '📉'}${robotData.increments.playTotal + robotData.increments.readTotal}  
          **评论**: ${robotData.current.commentsTotal}    ${robotData.increments.commentsTotal > 0 ? '📈' : '📉'}${robotData.increments.commentsTotal}  
          **点赞**: ${robotData.current.likesTotal}    ${robotData.increments.likesTotal > 0 ? '📈' : '📉'}${robotData.increments.likesTotal}  
          **收藏**: ${robotData.current.favoritesTotal}    ${robotData.increments.favoritesTotal > 0 ? '📈' : '📉'}${robotData.increments.favoritesTotal}
          ${isTest ? '<此条为测试内容>' : ''}  
          `
        }
      }
      const result = await axios.post(WEBHOOK_URL, data)
      if (result.data.errcode !== 0) {
        await this.loggerService.info(null, `机器人消息发送失败:webhookUrl=${webhookUrl}`, {
          robotType: RobotTypeEnum.QiyeWx,
          webhookUrl,
          title,
          error: JSON.stringify(result.data)
        })
      }
    } catch (error) {
      await this.loggerService.info(null, `机器人消息发送异常:webhookUrl=${webhookUrl}`, {
        robotType: RobotTypeEnum.QiyeWx,
        title,
        error: error.message
      })
    }
  }

  /**
   * 校验webhook格式
   * @param url
   * @param platform
   * @returns
   */
  static checkWebhookUrl(url: string, platform: RobotTypeEnum) {
    const patterns = {
      feishu: /^https:\/\/open\.feishu\.cn\/open-apis\/bot\/v2\/hook\/[a-zA-Z0-9_-]{32,64}$/,
      dingding: /^https:\/\/oapi\.dingtalk\.com\/robot\/send\?access_token=[a-z0-9]{32,64}.*$/,
      qiyewx: /^https:\/\/qyapi\.weixin\.qq\.com\/cgi-bin\/webhook\/send\?key=[a-zA-Z0-9_-]{32,64}$/
    }

    const regex = patterns[platform]
    if (!regex) {
      return false
    }
    return regex.test(url)
  }
}
