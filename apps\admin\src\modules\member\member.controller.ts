import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Request } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { MemberQueryDTO, MemberResponseDTO, PatchCustomerRequest } from './member.dto'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { MemberService } from './member.service'
import { MemberDevicesService } from './member-devices.service'
import {
  DeviceLogsQueryDTO,
  MemberDeviceLogsResponseDTO,
  MemberDevicesResponseDTO
} from './member-devices.dto'
import { AdminAndOpenPlatformAccess } from '../../common/decorators/access-control.decorator'
import type { FastifyRequest } from 'fastify'
import { UserType } from '@yxr/common'

@Controller('members')
@AdminAndOpenPlatformAccess()
@ApiTags('会员')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class MemberController {
  constructor(
    private readonly memberService: MemberService,
    private readonly memberDevicesService: MemberDevicesService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取会员列表' })
  @ApiOkResponse({ description: '操作成功', type: MemberResponseDTO })
  @ApiQuery({ type: MemberQueryDTO, required: false })
  async getMembers(@Query() query: MemberQueryDTO, @Request() request: FastifyRequest) {
    if (request.session?.userType === UserType.OPEN_PLATFORM) {
      if (!query.applicationId) {
        throw new Error('开放平台查询必须传应用ID')
      }
    }

    return this.memberService.getMembers(query)
  }

  @Patch(':id/customer')
  @ApiOperation({ summary: '更换客服归属人' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async updateCustomer(@Param('id') id: string, @Body() body: PatchCustomerRequest) {
    return await this.memberService.updateCustomer(id, body)
  }

  @Get(':id/devices')
  @ApiOperation({ summary: '获取会员登录设备列表' })
  @ApiOkResponse({ description: '操作成功', type: MemberDevicesResponseDTO })
  async getMemberDevices(@Param('id') id: string) {
    return this.memberDevicesService.getMemberDevices(id)
  }

  @Post(':id/devices/logs')
  @ApiOperation({ summary: '生成会员在线设备日志' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  async postDeviceLog(@Param('id') id: string) {
    return this.memberDevicesService.postDeviceLog(id)
  }

  @Get('devices/logs')
  @ApiOperation({ summary: '获取日志下载列表' })
  @ApiOkResponse({ description: '操作成功', type: MemberDeviceLogsResponseDTO })
  @ApiQuery({ type: DeviceLogsQueryDTO, required: false })
  async getDeviceLogs(@Query() query: DeviceLogsQueryDTO) {
    return this.memberDevicesService.getDeviceLogs(query)
  }

  @Delete('devices/logs/:id')
  @ApiOperation({ summary: '删除下载日志' })
  @ApiOkResponse({ description: '操作成功', type: MemberDeviceLogsResponseDTO })
  async deleteDeviceLogs(@Param('id') id: string) {
    return this.memberDevicesService.deleteDeviceLogs(id)
  }
}
