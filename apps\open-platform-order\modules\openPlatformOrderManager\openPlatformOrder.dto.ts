import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsDateString, IsNumber, IsOptional, IsString, Min } from 'class-validator'
import { Transform } from 'class-transformer'
import { OpenPlatformOrderResourceType } from '@yxr/common'

/**
 * 创建账号点数订单请求DTO
 */
export class CreateAccountPointsOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: '账号点数数量',
    example: 10,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  accountCount: number

  @ApiProperty({
    description: '时长（月）',
    example: 3,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  duration: number

  @ApiProperty({
    description: '订单开始时间（ISO 8601格式）',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsDateString()
  startTime: string

  @ApiPropertyOptional({
    description: '来源应用ID',
    example: 'app_123456'
  })
  @IsOptional()
  @IsString()
  sourceAppId?: string

  @ApiPropertyOptional({
    description: '备注',
    example: '账号点数订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 创建流量订单请求DTO
 */
export class CreateTrafficOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  userId: string

  @ApiProperty({
    description: '流量数量（GB）',
    example: 100,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  trafficCount: number

  @ApiPropertyOptional({
    description: '来源应用ID',
    example: 'app_123456'
  })
  @IsOptional()
  @IsString()
  sourceAppId?: string

  @ApiPropertyOptional({
    description: '备注',
    example: '流量订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 新订单响应DTO
 */
export class NewOrderResponseDto {
  @ApiProperty({
    description: '订单ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '订单编号',
    example: 'OP*************123'
  })
  orderNo: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiProperty({
    description: '订单状态',
    example: 'paid'
  })
  orderStatus: string

  @ApiProperty({
    description: '订单类型',
    example: 'open_platform_account_points'
  })
  orderType: string

  @ApiProperty({
    description: '资源类型',
    enum: OpenPlatformOrderResourceType,
    example: OpenPlatformOrderResourceType.AccountPoints
  })
  resourceType: OpenPlatformOrderResourceType

  @ApiProperty({
    description: '账号点数数量（仅账号点数订单）',
    example: 10
  })
  accountCount: number

  @ApiProperty({
    description: '流量数量（GB，仅流量订单）',
    example: 100
  })
  trafficCount: number

  @ApiProperty({
    description: '时长（月）',
    example: 3
  })
  duration: number

  @ApiProperty({
    description: '开始时间（时间戳）',
    example: *************
  })
  startTime: number

  @ApiProperty({
    description: '结束时间（时间戳）',
    example: *************
  })
  endTime: number

  @ApiPropertyOptional({
    description: '流量过期时间（时间戳，仅流量订单）',
    example: *************
  })
  trafficExpiredAt?: number

  @ApiPropertyOptional({
    description: '备注',
    example: '订单备注'
  })
  remark?: string

  @ApiProperty({
    description: '创建时间（时间戳）',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间（时间戳）',
    example: *************
  })
  updatedAt: number
}

/**
 * 团队账号点数查询响应DTO
 */
export class TeamAccountPointsResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '当前有效账号点数',
    example: 25
  })
  currentAccountPoints: number

  @ApiProperty({
    description: '有效订单数量',
    example: 3
  })
  activeOrderCount: number

  @ApiProperty({
    description: 'VIP过期时间（时间戳）',
    example: *************
  })
  vipExpiredAt: number

  @ApiProperty({
    description: '查询时间（时间戳）',
    example: *************
  })
  queryTime: number
}
