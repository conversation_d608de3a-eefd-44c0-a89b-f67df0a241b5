/**
 * Twitter平台特定错误处理器
 */

import { AxiosResponse } from 'axios'
import { BusinessErrorChecker, OverseasContext } from '../../utils/axios-config'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  extractRequestInfo,
  extractResponseInfo
} from '../../utils/error-handler'

/**
 * Twitter API错误接口
 */
export interface TwitterApiError {
  code: number
  message: string
}

/**
 * Twitter API响应接口
 */
export interface TwitterApiResponse {
  errors?: TwitterApiError[]
  detail?: string
  title?: string
  type?: string
}

/**
 * Twitter业务错误检查器
 */
export class TwitterBusinessErrorChecker implements BusinessErrorChecker {
  /**
   * 检查Twitter响应是否包含业务错误
   * Twitter API在出现业务错误时会在响应中包含errors字段
   */
  hasBusinessError(response: AxiosResponse<TwitterApiResponse>): boolean {
    return response.data && (
      (response.data.errors && response.data.errors.length > 0) ||
      response.data.detail !== undefined
    )
  }

  /**
   * 处理Twitter业务错误
   * 根据Twitter错误码映射到标准错误类型
   */
  async handleBusinessError(response: AxiosResponse<TwitterApiResponse>, context: OverseasContext): Promise<never> {
    const data = response.data

    if (!data || (!data.errors && !data.detail)) {
      // 不应该调用这个方法，因为没有错误
      throw new RemoteApiError(
        context.platform,
        RemoteApiErrorCodes.UnknownRemoteApiError,
        { message: '意外调用了错误处理方法，但API返回成功' },
        extractRequestInfo(response),
        extractResponseInfo(response),
        context
      )
    }

    let errorCode: RemoteApiErrorCodes
    let errorMessage: string
    let twitterErrorCode: number | undefined

    // 处理v1.1 API的错误格式
    if (data.errors && data.errors.length > 0) {
      const error = data.errors[0]
      errorMessage = error.message
      twitterErrorCode = error.code

      // 根据Twitter错误码映射到标准错误类型
      switch (error.code) {
        // 认证相关错误
        case 89: // Invalid or expired token
          errorCode = RemoteApiErrorCodes.AccessTokenInvalid
          break
        case 135: // Could not authenticate you
          errorCode = RemoteApiErrorCodes.AccessTokenInvalid
          break

        // 限流相关错误
        case 88: // Rate limit exceeded
          errorCode = RemoteApiErrorCodes.RateLimitExceeded
          break

        // 内容相关错误
        case 187: // Status is a duplicate
          errorCode = RemoteApiErrorCodes.DuplicateContent
          break
        case 186: // Tweet needs to be a bit shorter
          errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
          break

        // 账号相关错误
        case 326: // Account temporarily locked
          errorCode = RemoteApiErrorCodes.AccountSuspended
          break
        case 64: // Your account is suspended
          errorCode = RemoteApiErrorCodes.AccountSuspended
          break

        // 权限相关错误
        case 220: // Your credentials do not allow access to this resource
          errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
          break

        // 参数错误
        case 44: // Attachment URL parameter is invalid
        case 34: // Sorry, that page does not exist
          errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
          break

        // 未知错误
        default:
          errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
      }
    } 
    // 处理v2 API的错误格式
    else if (data.detail) {
      errorMessage = data.detail
      
      // v2 API通常通过HTTP状态码和detail信息来判断错误类型
      if (data.title === 'Unauthorized') {
        errorCode = RemoteApiErrorCodes.AccessTokenInvalid
      } else if (data.title === 'Too Many Requests') {
        errorCode = RemoteApiErrorCodes.RateLimitExceeded
      } else if (data.title === 'Forbidden') {
        errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
      } else {
        errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
      }
    } else {
      errorMessage = '未知Twitter API错误'
      errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      {
        message: errorMessage,
        twitterErrorCode: twitterErrorCode,
        twitterErrorTitle: data.title,
        twitterErrorType: data.type
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    console.warn(`[Twitter] API业务错误`, {
      errorCode: apiError.errorCode,
      twitterCode: twitterErrorCode,
      twitterTitle: data.title,
      message: errorMessage,
      context: context,
      requestInfo: apiError.requestInfo,
      responseInfo: apiError.responseInfo
    })

    throw apiError
  }
}

/**
 * 创建Twitter业务错误检查器实例
 */
export function createTwitterBusinessErrorChecker(): BusinessErrorChecker {
  return new TwitterBusinessErrorChecker()
}

/**
 * Twitter错误码到错误类型的映射表（用于文档和调试）
 */
export const TWITTER_ERROR_CODE_MAPPING = {
  // 认证错误
  89: 'ACCESS_TOKEN_INVALID',
  135: 'ACCESS_TOKEN_INVALID',

  // 限流错误
  88: 'RATE_LIMIT_EXCEEDED',

  // 内容错误
  187: 'DUPLICATE_CONTENT',
  186: 'REQUEST_PARAMETERS_INCORRECT',

  // 账号错误
  326: 'ACCOUNT_SUSPENDED',
  64: 'ACCOUNT_SUSPENDED',

  // 权限错误
  220: 'SCOPE_NOT_AUTHORIZED',

  // 参数错误
  44: 'REQUEST_PARAMETERS_INCORRECT',
  34: 'REQUEST_PARAMETERS_INCORRECT'
} as const

/**
 * 获取Twitter错误码的描述信息
 */
export function getTwitterErrorDescription(code: number): string {
  const mapping = TWITTER_ERROR_CODE_MAPPING as Record<number, string>
  return mapping[code] || 'UNKNOWN_ERROR'
}
