import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { UserType } from '@yxr/common'
import type { FastifyRequest } from 'fastify'
import { OptimizedUnifiedAuthService } from '../services/unified-auth.service'
import { ApplicationTokenService } from '../services/application-token.service'

/**
 * 优化后的统一认证守卫
 *
 * 优化点：
 * 1. 通过服务抽象减少直接依赖
 * 2. 将认证逻辑委托给专门的认证服务
 * 3. 保持向后兼容性
 * 4. 减少模块间的耦合
 */
@Injectable()
export class OptimizedUnifiedTokenGuard implements CanActivate {
  private readonly logger = new Logger(OptimizedUnifiedTokenGuard.name)

  constructor(
    private reflector: Reflector,
    private unifiedAuthService: OptimizedUnifiedAuthService,
    private applicationTokenService: ApplicationTokenService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { authorization } = request.headers

    // 检查是否为匿名访问
    const anonymous = this.reflector.getAllAndOverride('anonymous', [
      context.getHandler(),
      context.getClass()
    ])

    if (anonymous) {
      return true
    }

    if (!authorization) {
      throw new UnauthorizedException('缺少认证信息')
    }

    try {
      // 尝试各种认证方式
      const authResult = await this.tryAuthenticate(authorization)

      if (authResult) {
        // 设置请求上下文
        request.user = authResult.user
        request.authorization = authorization
        request.session = authResult.session
        return true
      }

      throw new UnauthorizedException('认证失败')
    } catch (error) {
      this.logger.warn(`认证失败: ${error.message}`)
      throw new UnauthorizedException('登录失效, 请重新登录')
    }
  }

  /**
   * 尝试各种认证方式
   */
  private async tryAuthenticate(authorization: string): Promise<{
    user: any
    session: any
  } | null> {
    // 1. 尝试管理员认证
    const adminResult = await this.unifiedAuthService.validateAdminToken(authorization)
    if (adminResult.isValid && adminResult.user && adminResult.session) {
      return {
        user: adminResult.user,
        session: adminResult.session
      }
    }

    // 2. 尝试开放平台用户认证
    const openPlatformResult =
      await this.unifiedAuthService.validateOpenPlatformToken(authorization)
    if (openPlatformResult.isValid && openPlatformResult.user && openPlatformResult.session) {
      return {
        user: openPlatformResult.user,
        session: openPlatformResult.session
      }
    }

    // 3. 尝试应用Token认证
    const applicationResult = await this.tryApplicationTokenAuth(authorization)
    if (applicationResult) {
      return applicationResult
    }

    // 4. 开发环境模拟认证
    if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev') {
      const devResult = await this.unifiedAuthService.validateDevMockToken(authorization)
      if (devResult.isValid && devResult.user && devResult.session) {
        return {
          user: devResult.user,
          session: devResult.session
        }
      }
    }

    return null
  }

  /**
   * 应用Token认证
   */
  private async tryApplicationTokenAuth(authorization: string): Promise<{
    user: any
    session: any
  } | null> {
    try {
      // 检查Bearer Token格式
      if (!authorization.startsWith('Bearer ')) {
        return null
      }

      const token = authorization.substring(7)
      const payload = await this.applicationTokenService.verifyToken(token)

      if (!payload) {
        return null
      }

      return {
        user: null, // 应用Token不对应具体用户
        session: {
          userId: payload.userId,
          userType: UserType.APPLICATION,
          teamId: undefined,
          applicationId: payload.applicationId,
          appId: payload.appId
        }
      }
    } catch {
      return null
    }
  }
}
