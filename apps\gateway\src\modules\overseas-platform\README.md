# 海外平台集成服务 - 账号点数监控功能

## 功能概述

在用户授权成功后，系统会自动检查团队账号点数使用情况。当添加新账号会导致点数超限时，暂停写入数据库，通过WebSocket发送超限通知，让用户选择部分账号进行添加。

## 技术设计

### 1. 点数规则

- **海外平台账号点数**: 每个海外平台账号占用 **4个点数**
- **团队点数限制**: 存储在 `TeamEntity.accountCapacityLimit` 字段
- **当前使用点数**: 所有 `PlatformAccountEntity.capacity` 字段的总和
- **点数检查**: 在写入数据库前预先检查是否会超限

### 2. 服务架构

#### AccountCapacityService (账号容量检查服务)
- **职责**: 专门处理团队账号点数的检查和计算
- **核心方法**:
  - `checkAccountCapacityLimit()`: 检查新增账号是否会超出点数限制
  - `getTeamCapacityUsage()`: 获取团队当前点数使用情况

#### OverseasAccountService (海外账号管理服务)
- **职责**: 专门处理海外平台账号的创建和管理
- **核心方法**:
  - `createOverseasAccounts()`: 批量创建海外平台账号
  - `formatAccountsForWebSocket()`: 格式化账号信息用于WebSocket事件
  - `getOverseasAccountCapacity()`: 获取海外账号占用的点数

#### OverseasIntegrationService (海外平台集成服务)
- **职责**: 处理授权回调和点数检查流程
- **核心方法**: `authorizationCallback()`

#### OverseasPlatformAuthService (海外平台授权服务)
- **职责**: 处理用户选择账号的逻辑
- **核心方法**: `selectAccounts()`

### 3. 核心流程

#### 授权回调处理 (`authorizationCallback`)

1. **接收授权数据**: 获取用户授权成功的账号列表
2. **点数预检查**: 计算添加所有账号后是否会超出团队点数限制
3. **超限处理**:
   - 如果超限：暂停写入数据库，将账号数据临时存储到缓存
   - 发送 `capacity_exceed` 状态的WebSocket消息
4. **正常处理**:
   - 如果未超限：正常创建账号并设置4个点数
   - 发送 `authorized` 状态的WebSocket消息

#### 用户选择账号 (`selectAccounts`)

1. **获取临时数据**: 从缓存中获取之前存储的账号信息
2. **筛选账号**: 根据用户选择的账号ID筛选要创建的账号
3. **再次检查**: 防止并发问题，再次验证点数限制
4. **创建账号**: 为选择的账号创建数据库记录并设置点数
5. **清理缓存**: 删除临时存储的数据
6. **发送通知**: 发送 `authorized` 状态的WebSocket消息

### 3. WebSocket消息格式

使用现有的 `overseas_account_authorized` 事件：

```typescript
{
  event: 'overseas_account_authorized',
  body: {
    state: string,                    // 授权操作识别符
    status: 'authorized' | 'capacity_exceed' | 'expired' | 'error',
    accounts: Array,                  // 授权成功的账号信息
    allowable: number,                // 允许选择的账号个数
    message: 'OK' | string           // 错误信息
  }
}
```

#### 状态说明

- **authorized**: 授权成功，账号已添加到账号中心
- **capacity_exceed**: 授权成功但账号超出点数，需要用户选择
- **expired**: 用户操作超时，授权动作已失效
- **error**: 授权过程中出现错误

#### 字段说明

- **authorized**: 只包含 `state`、`status`、`message` 字段
- **capacity_exceed**: 包含 `accounts` 和 `allowable` 字段，其中 `allowable < accounts.length`
- **error**: 只有此状态时，`message !== 'OK'`

## API接口

### 1. 授权回调接口

```
POST /overseas-integration/auth-callback
```

**实现位置**: `OverseasIntegrationService.authorizationCallback()`

**请求体**:
```typescript
{
  state: string,                    // 授权状态码
  accounts: Array<{                 // 授权成功的账号
    openId: string,                 // 账号openId
    nick_name: string,              // 账号昵称
    avatar: string,                 // 账号头像
    credentials: unknown            // 授权凭证
  }>
}
```

### 2. 用户选择账号接口

```
POST /overseas/select-accounts
```

**实现位置**: `OverseasPlatformAuthService.selectAccounts()`
**依赖服务**: `AccountCapacityService`, `OverseasAccountService`

**请求体**: `SelectAccountsInput` (定义在 `overseas-platform-auth.dto.ts`)
```typescript
{
  state: string,                    // 授权状态码
  selectedAccountIds: string[]      // 用户选择的账号openId列表
}
```

## 缓存机制

### 临时账号存储

- **缓存键**: `temp_accounts_${state}`
- **存储内容**:
  ```typescript
  {
    accounts: Array,              // 授权成功的账号数据
    cache: AuthorizationStateCache, // 原始授权缓存数据
    timestamp: number             // 存储时间戳
  }
  ```
- **过期时间**: 5分钟（300秒）

## 错误处理

### 1. 点数检查失败
- 记录错误日志
- 返回超限状态，阻止账号创建

### 2. 临时数据过期
- 抛出 `ForbiddenException`: "授权数据已过期，请重新授权"

### 3. 并发点数检查
- 在用户选择账号时再次检查点数限制
- 防止多用户同时操作导致的点数超限

### 4. 账号已存在处理
- 更新现有账号的授权凭据
- 记录警告日志，继续处理其他账号

## 前端集成示例

### 1. 监听WebSocket消息

```javascript
socket.on('overseas_account_authorized', (data) => {
  const { state, status, accounts, allowable, message } = data.body

  switch (status) {
    case 'authorized':
      showSuccess('账号授权成功')
      closeAuthWindow(state)
      break

    case 'capacity_exceed':
      showAccountSelector({
        state,
        accounts,
        allowable,
        message: `账号点数不足，最多可选择 ${allowable} 个账号`
      })
      break

    case 'expired':
      showError('授权已过期，请重新授权')
      closeAuthWindow(state)
      break

    case 'error':
      showError(`授权失败: ${message}`)
      closeAuthWindow(state)
      break
  }
})
```

### 2. 用户选择账号

```javascript
async function submitSelectedAccounts(state, selectedAccountIds) {
  try {
    const response = await fetch('/overseas/select-accounts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ state, selectedAccountIds })
    })

    if (response.ok) {
      showSuccess('账号添加请求已提交')
      // WebSocket会收到后续的授权成功消息
    } else {
      const error = await response.json()
      showError(`提交失败: ${error.message}`)
    }
  } catch (error) {
    showError(`网络错误: ${error.message}`)
  }
}
```

## 配置说明

- **海外平台账号点数**: 4个点数/账号（在 `OverseasAccountService` 中定义）
- **默认团队点数限制**: 5个点数（`TeamFeatures.DefaultAccountCapacityLimit`）
- **临时缓存过期时间**: 5分钟
- **点数检查范围**: 排除冻结账号（`isFreeze: { $ne: true }`）

## 日志记录

系统会记录以下关键信息：

- 点数检查的详细信息（DEBUG级别）
- 账号创建成功的统计（LOG级别）
- 账号已绑定其他团队的警告（WARN级别）
- 点数检查失败等错误（ERROR级别）
