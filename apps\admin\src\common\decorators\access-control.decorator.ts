import { SetMetadata } from '@nestjs/common'
import { UserType } from '@yxr/common'

/**
 * 权限控制装饰器的元数据键
 */
export const ACCESS_CONTROL_KEY = 'access_control'

/**
 * 访问控制配置
 */
export interface AccessControlConfig {
  /**
   * 允许的用户类型
   */
  allowedUserTypes: UserType[]
  
  /**
   * 是否需要数据隔离（仅对APPLICATION类型有效）
   */
  requireDataIsolation?: boolean
  
  /**
   * 自定义权限验证函数
   */
  customValidator?: (session: any) => boolean
}

/**
 * 应用Token访问装饰器
 * 标识接口允许应用Token访问
 */
export const ApplicationAccess = (requireDataIsolation: boolean = true) =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [UserType.APPLICATION],
    requireDataIsolation
  } as AccessControlConfig)

/**
 * 管理员专用装饰器
 * 标识接口仅允许admin用户访问
 */
export const AdminOnly = () =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [UserType.ADMIN],
    requireDataIsolation: false
  } as AccessControlConfig)

/**
 * Gateway原生用户专用装饰器
 * 标识接口仅允许gateway原生用户访问（通过gateway自己的认证系统）
 */
export const GatewayOnly = () =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [], // Gateway有自己的认证系统，这里不处理
    requireDataIsolation: false
  } as AccessControlConfig)

/**
 * 开放平台用户访问装饰器
 * 标识接口允许开放平台用户访问
 */
export const OpenPlatformAccess = () =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [UserType.OPEN_PLATFORM],
    requireDataIsolation: false
  } as AccessControlConfig)

/**
 * 混合访问装饰器
 * 允许多种用户类型访问
 */
export const MixedAccess = (
  allowedUserTypes: UserType[],
  requireDataIsolation: boolean = false
) =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes,
    requireDataIsolation
  } as AccessControlConfig)

/**
 * 管理员和开放平台用户访问装饰器
 * 常用的组合装饰器
 */
export const AdminAndOpenPlatformAccess = () =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [UserType.ADMIN, UserType.OPEN_PLATFORM],
    requireDataIsolation: false
  } as AccessControlConfig)

/**
 * 所有认证用户访问装饰器
 * 允许所有已认证的用户类型访问
 */
export const AllAuthenticatedAccess = (requireDataIsolation: boolean = false) =>
  SetMetadata(ACCESS_CONTROL_KEY, {
    allowedUserTypes: [UserType.ADMIN, UserType.OPEN_PLATFORM, UserType.APPLICATION],
    requireDataIsolation
  } as AccessControlConfig)
