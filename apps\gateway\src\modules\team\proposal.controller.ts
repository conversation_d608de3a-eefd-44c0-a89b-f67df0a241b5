import { Body, Controller, Get, Param, Patch, Post } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader, ApiOkResponse, ApiOperation, ApiParam,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO, BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { createProposalRequestBodyDTO, patchProposalRequestBodyDTO, ProposalTeamResponseDTO } from './proposal.dto'
import { ProposalService } from './proposal.service'

@Controller('proposals')
@ApiTags('团队管理/申请单')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class ProposalController {

  constructor(private readonly proposalService: ProposalService) {
  }

  /**
   * 获取邀请码状态
   */
  @Get(':code')
  @ApiOperation({ summary: '查询邀请码状态', description: '用户通过邀请码申请加入团队, 可以通过此接口查询邀请码的加入状态' })
  @ApiOkResponse({ type: ProposalTeamResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'code', required: true, description: '团队邀请码', example: 'F64D98', type: String })
  getTeamInfo(@Param('code') code: string){
    return this.proposalService.getTeamInfo(code)
  }

  /**
   * 创建申请单
   * @param data
   */
  @Post()
  @ApiOperation({ summary: '创建申请单' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  create(
    @Body() data: createProposalRequestBodyDTO
  ) {
    return this.proposalService.create(data)
  }

  /**
   * 通过/拒绝申请单
   * @param proposalId
   * @param data
   */
  @Patch(':proposalId')
  @ApiOperation({ summary: '通过/拒绝申请单' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'proposalId', required: true, description: '申请单ID' })
  patch(
    @Param('proposalId') proposalId: string,
    @Body() data: patchProposalRequestBodyDTO
  ) {
    return this.proposalService.handle({proposalId, approved: data.approved})
  }
}
