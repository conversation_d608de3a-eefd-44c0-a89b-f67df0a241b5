import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class CommonCategorysGroupEntity {
  @Prop({
    type: String,
    required: true
  })
  name: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const CommonCategorysGroupSchema: ModelDefinition = {
  name: CommonCategorysGroupEntity.name,
  schema: SchemaFactory.createForClass(CommonCategorysGroupEntity)
}

export const CommonCategorysGroupMongoose = MongooseModule.forFeature([CommonCategorysGroupSchema])
