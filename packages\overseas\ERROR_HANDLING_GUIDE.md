# 海外平台连接器异常处理机制指南

## 概述

海外平台连接器的异常处理机制为各个海外媒体平台API调用提供了统一、完善的错误处理解决方案，覆盖了网络异常、HTTP状态码异常、API限流、认证授权异常以及平台特定异常等各种错误类型。

## 主要特性

### 1. 统一的错误格式化和分类
- **错误代码标准化**: 使用`RemoteApiErrorCodes`枚举统一所有错误类型
- **错误分类**: 按网络、认证、授权、限流、业务、平台等维度分类
- **严重程度评估**: 自动评估错误的严重程度（低、中、高、严重）
- **本地化错误信息**: 提供中文错误提示信息

### 2. 智能重试机制
- **指数退避算法**: 避免频繁重试造成的服务压力
- **可配置重试策略**: 支持自定义重试次数、延迟时间等参数
- **智能重试判断**: 根据错误类型自动判断是否应该重试

### 3. 平台特定错误处理
- **TikTok**: 处理业务错误码映射（code !== 0）
- **Facebook**: 处理访问令牌、权限、限流等特定错误
- **Instagram**: 处理内容违规、媒体上传等特定错误
- **Twitter**: 处理重复内容、账号锁定等特定错误
- **YouTube**: 处理配额限制、权限范围等特定错误

### 4. 完善的日志记录
- **结构化日志**: 包含完整的请求/响应信息和上下文
- **分级日志**: 根据错误严重程度自动选择日志级别
- **调试信息**: 提供详细的错误堆栈和调试信息

## 核心组件

### 1. RemoteApiError 类
统一的API错误类，包含以下信息：
- 平台名称
- 错误代码
- 错误分类和严重程度
- 本地化错误信息
- 请求/响应详情
- 上下文信息
- 是否可重试标识

### 2. 错误处理器接口
- `OverseasApiErrorHandler`: 定义错误处理的标准接口
- `DefaultOverseasApiErrorHandler`: 默认错误处理实现
- `BusinessErrorChecker`: 平台特定业务错误检查器

### 3. 统一拦截器
- `createOverseasAxiosInstance`: 创建带统一拦截器的axios实例
- 自动错误处理和转换
- 自动重试机制
- 自动代理配置

## 使用方法

### 1. 基础使用

```typescript
import { createOverseasContext } from './providers/utils'
import { createOverseasAxiosInstance, InterceptorConfig } from './utils/axios-config'
import { RemoteApiError, RemoteApiErrorCodes } from './utils/error-handler'

// 创建上下文
const context = createOverseasContext('tiktok', {
  accountOpenId: 'account-123',
  teamId: 'team-456',
  userId: 'user-789'
})

// 创建拦截器配置
const interceptorConfig: InterceptorConfig = {
  context,
  enableRetry: true,
  enableBusinessErrorCheck: true
}

// 创建axios实例
const api = createOverseasAxiosInstance(
  'https://business-api.tiktok.com',
  interceptorConfig
)

try {
  const response = await api.post('/open_api/v1.3/tt_user/oauth2/token/', data)
  console.log('成功:', response.data)
} catch (error) {
  if (error instanceof RemoteApiError) {
    console.error('API错误:', error.localizedMessage)
    console.log('错误代码:', error.errorCode)
    console.log('是否可重试:', error.isRetryable)
  }
}
```

### 2. 启用重试机制

```typescript
const interceptorConfig: InterceptorConfig = {
  context,
  enableRetry: true,
  retryConfig: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2
  }
}

const api = createOverseasAxiosInstance(baseURL, interceptorConfig)

// API调用失败时会自动重试
const response = await api.post('/api/endpoint', data)
```

### 3. 平台特定错误处理

```typescript
import { createTikTokBusinessErrorChecker } from './providers/tiktok/tiktok-error-handler'

// 使用TikTok特定的错误检查器
const interceptorConfig: InterceptorConfig = {
  context,
  businessErrorChecker: createTikTokBusinessErrorChecker(),
  enableBusinessErrorCheck: true
}

const api = createOverseasAxiosInstance(
  'https://business-api.tiktok.com',
  interceptorConfig
)
```

### 4. 批量API调用

```typescript
import { batchApiCalls } from './providers/utils'

const calls = [
  () => api.get('/endpoint1'),
  () => api.get('/endpoint2'),
  () => api.get('/endpoint3')
]

const results = await batchApiCalls(calls, 2) // 并发数为2

results.forEach((result, index) => {
  if (result.success) {
    console.log(`调用${index}成功:`, result.data)
  } else {
    console.error(`调用${index}失败:`, result.error?.message)
  }
})
```

## 错误类型说明

### 网络相关错误
- `NETWORK_ERROR`: 一般网络连接异常
- `TIMEOUT`: 请求超时
- `CONNECTION_REFUSED`: 连接被拒绝
- `PROXY_ERROR`: 代理服务器连接失败

### HTTP状态码错误
- `REQUEST_PARAMETERS_INCORRECT`: 400 请求参数无效
- `UNAUTHORIZED`: 401 认证失败
- `FORBIDDEN`: 403 权限不足
- `NOT_FOUND`: 404 资源不存在
- `SERVER_ERROR`: 5xx 服务器内部错误

### API限流错误
- `RATE_LIMIT_EXCEEDED`: 请求频率过高
- `QUOTA_EXCEEDED`: 配额已用完

### 认证授权错误
- `ACCESS_TOKEN_INVALID`: 访问令牌无效
- `ACCESS_TOKEN_EXPIRED`: 访问令牌已过期
- `SCOPE_NOT_AUTHORIZED`: 权限范围不足
- `REFRESH_TOKEN_INVALID`: 刷新令牌无效

### 平台特定错误
- `CONTENT_VIOLATION`: 内容违反平台规定
- `MEDIA_UPLOAD_FAILED`: 媒体文件上传失败
- `ACCOUNT_SUSPENDED`: 账号已被暂停
- `FEATURE_NOT_AVAILABLE`: 功能暂不可用
- `DUPLICATE_CONTENT`: 重复内容

## 配置选项

### 重试配置
```typescript
interface RetryConfig {
  maxRetries: number        // 最大重试次数
  baseDelay: number         // 基础延迟时间(ms)
  maxDelay: number          // 最大延迟时间(ms)
  backoffMultiplier: number // 退避倍数
}
```

### 默认重试配置
```typescript
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2
}
```

### 拦截器配置
```typescript
interface InterceptorConfig {
  context: OverseasContext
  retryConfig?: Partial<RetryConfig>
  businessErrorChecker?: BusinessErrorChecker
  enableRetry?: boolean
  enableBusinessErrorCheck?: boolean
}
```

## 最佳实践

### 1. 错误处理
- 始终检查错误类型并进行相应处理
- 对于可重试的错误，考虑使用重试机制
- 记录详细的错误信息用于调试和监控

### 2. 重试策略
- 对于网络错误和临时性错误启用重试
- 对于认证错误和业务逻辑错误不要重试
- 合理设置重试次数和延迟时间

### 3. 日志记录
- 利用内置的结构化日志记录功能
- 根据错误严重程度调整日志级别
- 避免在日志中记录敏感信息

### 4. 性能优化
- 使用批量API调用减少网络开销
- 合理设置并发数量避免过载
- 利用axios实例复用减少连接开销

## TikTok平台特定处理

### 错误码映射
TikTok API的特点是即使出现业务错误，HTTP状态码仍然是200，需要通过响应数据中的code字段来判断：

```typescript
// TikTok错误码映射示例
switch (data.code) {
  case 40001:
    errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
    break
  case 40102:
  case 40104:
  case 40105:
    errorCode = RemoteApiErrorCodes.AccessTokenInvalid
    break
  case 40103:
    errorCode = RemoteApiErrorCodes.AccessTokenExpired
    break
  // ... 更多映射
}
```

### 使用示例
```typescript
import { TiktokApi } from './providers/tiktok/tiktok-api'

const tiktokApi = new TiktokApi()

try {
  const result = await tiktokApi.uploadVideo(context, {
    video_url: 'https://example.com/video.mp4',
    caption: '测试视频',
    privacy_level: 'PUBLIC_TO_EVERYONE'
  })
  console.log('上传成功:', result.data.video_id)
} catch (error) {
  if (error instanceof RemoteApiError) {
    switch (error.errorCode) {
      case RemoteApiErrorCodes.AccessTokenExpired:
        // 处理令牌过期
        break
      case RemoteApiErrorCodes.ContentViolation:
        // 处理内容违规
        break
      default:
        console.error('其他错误:', error.localizedMessage)
    }
  }
}
```

## 集成现有代码

现有的海外平台API调用代码可以通过以下方式逐步迁移：

1. **替换axios实例创建方式**
   ```typescript
   // 旧方式
   const api = axios.create({ baseURL: 'https://api.example.com' })
   
   // 新方式
   const api = createOverseasAxiosInstance(baseURL, interceptorConfig)
   ```

2. **更新错误处理逻辑**
   ```typescript
   // 旧方式
   try {
     const response = await api.get('/endpoint')
   } catch (error) {
     console.error('请求失败:', error.message)
   }
   
   // 新方式
   try {
     const response = await api.get('/endpoint')
   } catch (error) {
     if (error instanceof RemoteApiError) {
       console.error('API错误:', error.localizedMessage)
       console.log('错误代码:', error.errorCode)
     }
   }
   ```

3. **添加重试机制（可选）**
4. **更新日志记录方式**

详细的迁移示例请参考项目中的示例文件。

---

**相关文档**:
- [主文档](./README.md) - 海外平台连接器完整指南
- [代理配置指南](./PROXY_CONFIG_GUIDE.md) - 代理配置详细说明
- [项目目录](./DIRECTORY.md) - 项目结构说明
