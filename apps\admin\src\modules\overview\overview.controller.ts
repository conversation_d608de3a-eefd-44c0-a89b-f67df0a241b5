import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  AccountScaleResponse,
  AccountStatisticResponse,
  DataScaleResponse,
  DataStatisticResponse,
  OnlineScaleResponse,
  OverviewDto,
  OverviewQueryDTO,
  PlatformPublishScaleResponse,
  RegisterScaleDTO,
  TeamStatisticListRequest,
  TeamStatisticResponse
} from './overview.dto'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OverviewService } from './overview.service'
import { AdminAndOpenPlatformAccess, AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('overviews')
@AdminAndOpenPlatformAccess()
@ApiTags('数据概览')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({
  name: 'authorization',
  required: true
})
@AdminOnly()
export class OverviewController {
  constructor(private readonly overviewService: OverviewService) {}

  @Get('push-tasks')
  @ApiOperation({ summary: '作品发布量' })
  @ApiOkResponse({ description: '操作成功', type: OverviewDto })
  @ApiQuery({
    type: OverviewQueryDTO
  })
  async getTasks(@Query() params: OverviewQueryDTO) {
    return await this.overviewService.getTasksByDate(params.dateType)
  }

  @Get('active-users')
  @ApiOperation({ summary: '活跃用户量' })
  @ApiOkResponse({ description: '操作成功', type: OverviewDto })
  @ApiQuery({
    type: OverviewQueryDTO
  })
  async getActiveUsers(@Query() params: OverviewQueryDTO) {
    return await this.overviewService.getUsersByDate(params.dateType)
  }

  @Get('data-statistic')
  @ApiOperation({ summary: '获取归档数据' })
  @ApiOkResponse({ description: '操作成功', type: DataStatisticResponse })
  @ApiQuery({
    type: OverviewQueryDTO
  })
  async getDataStatistic(@Query() params: OverviewQueryDTO) {
    return await this.overviewService.getDataStatistic(params.dateType)
  }

  @Get('data-scale')
  @ApiOperation({ summary: '获取数据比例数据' })
  @ApiOkResponse({ description: '操作成功', type: DataScaleResponse })
  async getDataScale() {
    return await this.overviewService.getDataScale()
  }

  @Get('register-scale')
  @ApiOperation({ summary: '用户注册设备数据' })
  @ApiOkResponse({ description: '操作成功', type: RegisterScaleDTO })
  async getRegisterScale(@Query() params: OverviewQueryDTO) {
    return await this.overviewService.getRegisterScale(params.dateType)
  }

  @Get('account-scale')
  @ApiOperation({ summary: '媒体账号占比数据' })
  @ApiOkResponse({ description: '操作成功', type: AccountScaleResponse })
  async getPlatformAccountScale() {
    return await this.overviewService.getAccountScale()
  }

  @Get('team/rate')
  @ApiOperation({ summary: '团队转化率/续费率' })
  @ApiOkResponse({ description: '操作成功', type: TeamStatisticResponse })
  @ApiQuery({ type: TeamStatisticListRequest })
  async getTeamConverRate(@Query() params: TeamStatisticListRequest) {
    return await this.overviewService.getTeamRate(params)
  }

  @Get('online-scale')
  @ApiOperation({ summary: '账号登录保持统计' })
  @ApiOkResponse({ description: '操作成功', type: OnlineScaleResponse })
  async getOnlineScale() {
    return await this.overviewService.getOnlineScale()
  }

  @Get('publish-scale')
  @ApiOperation({ summary: '平台发布占比数据' })
  @ApiOkResponse({ description: '操作成功', type: PlatformPublishScaleResponse })
  async getPublishScale() {
    return await this.overviewService.getPublishScale()
  }

  @Get('account-statistic')
  @ApiOperation({ summary: '获取账号数量趋势' })
  @ApiOkResponse({ description: '操作成功', type: AccountStatisticResponse })
  async getAccountStatistic() {
    return await this.overviewService.getAccountStatistic()
  }

}
