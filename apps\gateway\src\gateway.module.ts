import { Module } from '@nestjs/common'
import { UserModule } from './modules/user/user.module'
import { baseModule } from './common/baseModule'
import { PlatformAccountModule } from './modules/platform/platform-account.model'
import { TaskModule } from './modules/publish/task.model'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { TokenGuard } from './common/guards'
import { ResponseTransformInterceptor } from './common/interceptors'
import { GlobalExceptionFilter } from './common/filters'
import { TeamModule } from './modules/team/team.module'
import { NoticeModule } from './modules/notice/notice.module'
import { WebhookModule } from './modules/webhook/webhook.module'
import { AuthorizationService } from './common/security/authorization.service'
import { StorageModule } from './modules/storage/storage.module'
import { EventEmitterModule } from '@nestjs/event-emitter'
import { BrowserModule } from './modules/browser/browser.module'
import { OnlineScriptModule } from './modules/online-script/online-script.module'
import { SocketModule } from './modules/socket/socket.module'
import { MaterialModule } from './modules/material-library/material.module'
import { TopicsModule } from './modules/topics/topics.module'
import { WxThirdPlatformModule } from './modules/wx-third-platform/wx-third-platform.module'
import { OrderModule } from './modules/order/order.module'
import { QuickModule } from './modules/quick/quick.model'
import { AdModule } from './modules/ad/ad.module'
import { OverviewModule } from './modules/overview/overview.model'
import { KuaidailiModule } from './modules/kuaidaili/kuaidaiali.module'
import { DownloadModule } from './modules/download/download.model'
import { WechatIpadModule } from './modules/wechat-ipad/wechat-ipad.module'
import { OverseasPlatformModule } from './modules/overseas-platform/overseas-platform.module'
import { RobotsModule } from './modules/robot/robots.module'
import { HuoshanModule } from '@yxr/huoshan'


@Module({
  imports: [
    EventEmitterModule.forRoot(),
    ...baseModule,
    UserModule,
    PlatformAccountModule,
    WxThirdPlatformModule,
    TaskModule,
    TeamModule,
    NoticeModule,
    WebhookModule,
    StorageModule,
    BrowserModule,
    OnlineScriptModule,
    SocketModule,
    MaterialModule,
    TopicsModule,
    OrderModule,
    QuickModule,
    AdModule,
    OverviewModule,
    KuaidailiModule,
    HuoshanModule,
    DownloadModule,
    WechatIpadModule,
    OverseasPlatformModule,
    RobotsModule
  ],
  providers: [
    { provide: APP_FILTER, useClass: GlobalExceptionFilter },
    { provide: APP_INTERCEPTOR, useClass: ResponseTransformInterceptor },
    { provide: APP_GUARD, useClass: TokenGuard },
    AuthorizationService
  ]
})
export class GatewayModule {}
