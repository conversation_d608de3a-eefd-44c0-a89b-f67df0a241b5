export enum WebhookEvents {
  /**
   * 验证 webhook
   */
  VerifyWebhook = 'verify_webhook',
  /**
   * 新增消息通知事件
   * 推送给相关用户有新通知产生 - 红点显示
   */
  NoticeCreate = 'notice_create',
  /**
   * 消息通知列表数据更新事件
   */
  // NoticeUpdate = 'notice_update',
  /**
   * 媒体账号列表数据更新事件
   */
  AccountCreate = 'account_create',
  AccountUpdate = 'account_update',
  AccountDelete = 'account_delete',

  /**
   * 成员运营账号已变更
   */
  MemberAccountsChangedEvent = 'member_accounts_changed',

  /**
   * 取消/设置管理管
   */
  TeamRoleChange = 'team_role_change',

  /**
   *  退出团队/移除团队事件
   */
  TeamExit = 'team_exit',

  /**
   * 用户申请加入团队被管理员通过时
   */
  TeamJoined = 'team_joined',

  /**
   * 发布记录列表数据更新事件
   */
  // PublishUpdate = 'publish_update',

  /**
   * 解散团队
   */
  // TeamDelete = 'team_delete',

  /**
   * 取消/设置运营人
   */
  // AccountManagerChange = 'account_manager_change',

  /**
   * APP 发布任务推送
   */
  AppTaskPushing = 'app_task_pushing',

  /**
   * 账号空间数据变更事件
   */
  AccountSpaceDumpUpdated = 'account_space_dump_updated',

  /**
   * 常规空间数据变更事件
   */
  WebSpaceDumpUpdated = 'web_space_dump_updated',

  /**
   * 团队版本升级
   */
  TeamVersionUpgrade = 'team_version_upgrade',
  /**
   * 团队版本降级
   */
  TeamVersionDowngrade = 'team_version_downgrade',

  /**
   * 海外平台账号授权事件
   */
  OverseasAccountAuthorized = 'overseas_account_authorized',

  /**
   * 账号在其地方登录事件
   */
  LoginOnAnotherDevice = 'login_on_another_device'

}
