import { BadRequestException, Body, Controller, Post } from '@nestjs/common'
import { ApiBadRequestResponse, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'
import { BaseBadRequestResponseDTO } from '../../common/dto/BaseResponseDTO'
import { DownloadService } from './download.service'
import { captchaVerifyRequestDTO,OssResourceUrlResponseDTO } from './download.dto'

@Controller('downloads')
@ApiTags('下载管理')
@ApiBadRequestResponse({ type: BaseBadRequestResponseDTO, description: '参数错误' })
export class DownloadController {
  constructor(private readonly downloadService: DownloadService) {}

  @Post('desktop')
  @ApiOperation({ summary: '生成桌面应用下载地址' })
  @ApiOkResponse({ type: OssResourceUrlResponseDTO })
  async postDesktopUrl(@Body() body: captchaVerifyRequestDTO) {
    const s = body.type
    if (
      s !== 'windows-x86' &&
      s !== 'windows-x64' &&
      s !== 'macos-x64' &&
      s !== 'macos-arm64' &&
      s !== 'android'
    ) {
      throw new BadRequestException('只支持windows-x86、windows-x64、macos-x64、macos-arm64、android类型')
    }
    return await this.downloadService.postDesktopUrl(body)
  }
}
