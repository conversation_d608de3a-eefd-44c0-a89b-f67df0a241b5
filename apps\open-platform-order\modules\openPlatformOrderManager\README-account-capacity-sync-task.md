# 团队账号点数同步定时任务

## 功能概述

新增了一个定时任务功能，用于每天自动同步所有团队的账号点数限制，确保团队表中的 `accountCapacityLimit` 字段与当前有效的账号点数订单保持一致。

## 定时任务配置

### 执行时间
```typescript
@Cron('0 1 * * *', {
  name: 'sync-team-account-capacity',
  timeZone: 'Asia/Shanghai'
})
```

- **执行时间**: 每天凌晨 1:00
- **时区**: Asia/Shanghai (中国标准时间)
- **任务名称**: sync-team-account-capacity
- **避免冲突**: 与现有的流量清理任务（凌晨2:00）错开执行

## 核心功能

### 1. 主要定时任务方法

```typescript
async syncTeamAccountCapacity(): Promise<void>
```

**功能描述:**
- 查询所有有账号点数订单的团队
- 计算每个团队当前有效的账号点数总和
- 批量更新团队表的 `accountCapacityLimit` 字段
- 记录详细的执行日志和统计信息

### 2. 数据查询优化

```typescript
private async getTeamAccountCapacityData(): Promise<Array<{
  teamId: string
  currentCapacity: number
  calculatedCapacity: number
}>>
```

**使用聚合查询优化性能:**
- 一次性查询所有有效的账号点数订单
- 按团队分组计算总账号点数
- 关联团队表获取当前的账号点数限制
- 避免多次数据库查询，提高执行效率

### 3. 批量处理机制

```typescript
private async processBatchTeamAccountCapacity(teamData): Promise<{
  processedCount: number
  totalCapacity: number
}>
```

**批量处理特点:**
- 分批处理团队数据，每批50个团队
- 使用 `bulkWrite` 进行批量数据库更新
- 只更新需要变更的团队，避免不必要的写操作
- 单个团队处理失败不影响其他团队

## 业务逻辑

### 统计范围
查询条件：
```typescript
{
  orderStatus: OrderStatus.Paid,           // 已支付订单
  resourceType: OpenPlatformOrderResourceType.AccountPoints, // 账号点数订单
  startTime: { $lte: now },               // 开始时间 <= 当前时间
  endTime: { $gt: now }                   // 结束时间 > 当前时间
}
```

### 计算逻辑
1. **按团队分组**: 将所有有效订单按 `teamId` 分组
2. **累加账号点数**: 对每个团队的有效订单的 `accountCapacity` 字段求和
3. **比较差异**: 将计算结果与团队当前的 `accountCapacityLimit` 比较
4. **条件更新**: 只有当计算值与当前值不同时才执行更新

### 聚合查询示例
```typescript
await this.orderModel.aggregate([
  {
    $match: {
      orderStatus: OrderStatus.Paid,
      resourceType: OpenPlatformOrderResourceType.AccountPoints,
      startTime: { $lte: now },
      endTime: { $gt: now }
    }
  },
  {
    $group: {
      _id: '$teamId',
      totalAccountCapacity: { $sum: '$accountCapacity' },
      orderCount: { $sum: 1 }
    }
  },
  {
    $lookup: {
      from: 'teams',
      localField: '_id',
      foreignField: '_id',
      as: 'team'
    }
  }
])
```

## 手动执行接口

### 管理员手动触发
```typescript
async manualSyncTeamAccountCapacity(): Promise<{
  success: boolean
  processedTeams: number
  totalUpdatedCapacity: number
  message: string
  duration: number
}>
```

**功能特点:**
- 提供给管理员手动执行同步的接口
- 返回详细的执行结果和统计信息
- 记录执行时长便于性能监控
- 可用于测试和紧急同步场景

## 日志记录

### 任务开始日志
```typescript
this.logger.log('开始执行团队账号点数同步任务')
```

### 进度日志
```typescript
this.logger.log(
  `找到${teamAccountData.length}个团队有账号点数订单，开始同步处理`
)
```

### 详细调试日志
```typescript
this.logger.debug(
  `团队账号点数需要更新: teamId=${data.teamId}, ` +
  `当前=${data.currentCapacity}, 计算=${data.calculatedCapacity}`
)
```

### 完成统计日志
```typescript
this.logger.log(
  `团队账号点数同步任务完成: ` +
  `处理团队=${processedTeams}个, ` +
  `更新总账号点数=${totalUpdatedCapacity}, ` +
  `错误数量=${errorCount}, ` +
  `耗时=${duration}ms`
)
```

### 错误日志
```typescript
this.logger.error(
  `批次处理失败 (${i}-${i + batch.length - 1}): ${error.message}`,
  error.stack
)
```

## 性能优化

### 1. 聚合查询优化
- 使用MongoDB聚合管道一次性完成复杂查询
- 避免多次往返数据库的N+1查询问题
- 在数据库层面完成分组和计算操作

### 2. 批量处理
- 分批处理大量团队数据，避免内存溢出
- 使用 `bulkWrite` 进行批量数据库更新
- 减少数据库连接和事务开销

### 3. 条件更新
- 只更新需要变更的团队记录
- 避免不必要的数据库写操作
- 减少数据库锁定时间

### 4. 错误隔离
- 单个团队处理失败不影响其他团队
- 批次处理失败不影响其他批次
- 详细的错误日志便于问题排查

## 业务场景

### 场景1: 订单过期自动调整
```
团队A: 当前accountCapacityLimit=15
订单1: 10个账号点数, 2024-01-01 到 2024-02-01 (已过期)
订单2: 5个账号点数, 2024-01-15 到 2024-03-15 (有效)

同步结果: accountCapacityLimit=5 (只计算有效订单)
```

### 场景2: 新订单生效
```
团队B: 当前accountCapacityLimit=5
订单1: 5个账号点数, 2024-01-01 到 2024-03-01 (有效)
订单2: 8个账号点数, 2024-02-01 到 2024-04-01 (新生效)

同步结果: accountCapacityLimit=13 (5+8)
```

### 场景3: 无变化情况
```
团队C: 当前accountCapacityLimit=10
订单1: 10个账号点数, 2024-01-01 到 2024-03-01 (有效)

同步结果: 无需更新 (计算值与当前值相同)
```

## 监控和维护

### 关键指标
- 任务执行时长
- 处理的团队数量
- 更新的账号点数总量
- 错误发生次数

### 性能监控
- 聚合查询执行时间
- 批量更新操作耗时
- 内存使用情况

### 数据一致性检查
- 定期验证团队账号点数的准确性
- 监控订单状态变化对同步的影响
- 检查异常数据和边界情况

## 注意事项

1. **时区设置**: 确保定时任务在正确的时区执行
2. **数据库性能**: 大量团队时注意聚合查询的性能影响
3. **错误处理**: 确保单个失败不影响整体任务执行
4. **日志管理**: 定期清理过多的调试日志
5. **监控告警**: 设置任务执行失败的告警机制

## 管理员接口

### 手动同步接口
```http
POST /admin/open-platform/orders/sync-account-capacity
Authorization: Bearer {admin_token}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "账号点数同步成功",
  "data": {
    "success": true,
    "processedTeams": 150,
    "totalUpdatedCapacity": 2500,
    "message": "同步完成: 处理150个团队, 总账号点数2500",
    "duration": 1250
  }
}
```

**接口特点:**
- 管理员专用接口（需要 `@AdminOnly` 权限）
- 返回详细的执行统计信息
- 可用于测试和紧急同步场景
- 记录执行时长便于性能监控

## 测试验证

### 功能测试步骤

1. **创建测试数据**
```typescript
// 创建团队和账号点数订单
const team = await createTestTeam()
const order1 = await createAccountPointsOrder({
  teamId: team.id,
  accountCapacityLimit: 10,
  startTime: new Date('2024-01-01'),
  endTime: new Date('2024-03-01')
})
const order2 = await createAccountPointsOrder({
  teamId: team.id,
  accountCapacityLimit: 5,
  startTime: new Date('2024-02-01'),
  endTime: new Date('2024-04-01')
})
```

2. **执行同步任务**
```typescript
// 手动触发同步
const result = await orderManagerService.manualSyncTeamAccountCapacity()
```

3. **验证结果**
```typescript
// 检查团队账号点数是否正确更新
const updatedTeam = await teamModel.findById(team.id)
expect(updatedTeam.accountCapacityLimit).toBe(15) // 10 + 5
```

### 性能测试

**测试场景:**
- 1000个团队，每个团队平均3个有效订单
- 预期处理时间: < 30秒
- 内存使用: < 500MB

**测试命令:**
```bash
# 使用管理员接口测试
curl -X POST "http://localhost:3000/admin/open-platform/orders/sync-account-capacity" \
  -H "Authorization: Bearer {admin_token}"
```

## 扩展功能

### 未来可能的增强
1. **增量同步**: 只处理有变化的团队
2. **并行处理**: 使用多线程或集群处理大量数据
3. **实时同步**: 在订单状态变化时立即更新
4. **数据校验**: 增加数据一致性校验机制
5. **性能分析**: 提供详细的性能分析报告

## 总结

这个定时任务功能提供了：

✅ **自动化同步**: 每天凌晨1点自动执行
✅ **性能优化**: 聚合查询 + 批量处理
✅ **错误处理**: 完善的错误隔离和日志记录
✅ **手动触发**: 管理员可手动执行同步
✅ **监控友好**: 详细的执行统计和日志
✅ **数据一致性**: 确保团队账号点数的准确性

通过这个定时任务，系统能够自动维护团队账号点数的准确性，减少了手动维护的工作量，提高了数据的一致性和可靠性。
