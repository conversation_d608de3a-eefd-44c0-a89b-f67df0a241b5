import { Controller, Post } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiUnauthorizedResponse,
  ApiHeader
} from '@nestjs/swagger'
import { AdminOnly } from '../../../common/decorators/access-control.decorator'
import { BaseUnauthorizedResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformOrderService } from '../services/open-platform-order.service'

@Controller('admin/open-platform/orders')
@AdminOnly()
@ApiTags('开放平台订单管理（管理员）')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class OpenPlatformOrderAdminController {
  constructor(private readonly openPlatformOrderService: OpenPlatformOrderService) {}

  @Post('sync-account-capacity')
  @ApiOperation({
    summary: '手动同步团队账号点数',
    description: '手动触发团队账号点数同步任务，重新计算所有团队的账号点数限制'
  })
  @ApiResponse({
    status: 200,
    description: '同步完成',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: '是否成功' },
        processedTeams: { type: 'number', description: '处理的团队数量' },
        totalUpdatedCapacity: { type: 'number', description: '更新的总账号点数' },
        message: { type: 'string', description: '结果消息' },
        duration: { type: 'number', description: '执行时长（毫秒）' }
      }
    }
  })
  async syncAccountCapacity() {
    const result = await this.openPlatformOrderService.manualSyncTeamAccountCapacity()
    return {
      code: result.success ? 200 : 500,
      message: result.success ? '账号点数同步成功' : '账号点数同步失败',
      data: result
    }
  }
}
