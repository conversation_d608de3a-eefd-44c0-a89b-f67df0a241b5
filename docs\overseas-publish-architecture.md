# 海外平台内容发布分布式技术架构设计

## 架构概述

本系统采用分布式架构，将海外平台内容发布功能分为国内服务端和香港服务端两部分，通过HTTP API进行通信，实现跨地域的内容发布能力。

### 核心组件

1. **国内服务端 (/apps/gateway)**
   - 接收客户端发布请求
   - 保存发布任务和内容数据
   - 调用香港服务执行发布
   - 处理发布结果回调

2. **香港服务端 (/apps/overseavice)**
   - 执行具体的海外平台发布任务
   - 与海外平台API直接交互
   - 回调发布结果到国内服务

3. **海外平台提供者 (/packages/overseas)**
   - 抽象化各平台的发布接口
   - 统一的内容发布和账号授权能力

## 技术架构图

```
┌─────────────────┐    HTTP API    ┌─────────────────┐    Platform API    ┌─────────────────┐
│                 │ ──────────────> │                 │ ─────────────────> │                 │
│  国内服务端      │                │  香港服务端      │                   │  海外平台        │
│  (Gateway)      │ <────────────── │  (Overseavice)  │ <───────────────── │  (Facebook等)   │
│                 │    Callback     │                 │    Response       │                 │
└─────────────────┘                └─────────────────┘                   └─────────────────┘
        │                                   │
        │                                   │
        v                                   v
┌─────────────────┐                ┌─────────────────┐
│                 │                │                 │
│  国内数据库      │                │  Redis缓存       │
│  (MongoDB)      │                │  (临时状态)      │
│                 │                │                 │
└─────────────────┘                └─────────────────┘
```

## 数据流设计

### 1. 发布流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 国内服务端
    participant DB as 数据库
    participant Overseas as 香港服务端
    participant Platform as 海外平台

    Client->>Gateway: 提交发布请求
    Gateway->>DB: 保存任务和内容
    Gateway->>Overseas: 发送发布任务
    Gateway->>Client: 返回任务ID
    
    Overseas->>Platform: 调用平台API发布
    Platform->>Overseas: 返回发布结果
    Overseas->>Gateway: 回调发布结果
    Gateway->>DB: 更新任务状态
    Gateway->>Client: WebSocket通知状态变更
```

### 2. 状态管理

发布任务状态流转：
- `pending` → `publishing` → `success`/`failed`
- `pending` → `publishing` → `reviewing` → `approved`/`rejected`

注意：海外平台任务不使用独立的发布渠道，而是通过平台账号的 `platformType` 字段识别，保持原有的 `local`/`cloud` 发布渠道。

## 核心接口设计

### 国内服务端接口

#### 1. 创建发布任务
```http
POST /overseas-platform/publish
Content-Type: application/json

{
  "platformAccountIds": ["account1", "account2"],
  "content": {
    "type": "text",
    "text": "发布内容",
    "title": "标题",
    "images": ["url1", "url2"],
    "tags": ["tag1", "tag2"]
  },
  "publishAt": "2024-01-01T10:00:00Z",
  "maxRetries": 3
}
```

#### 2. 查询发布状态
```http
GET /overseas-platform/publish/status/{taskSetId}
```

#### 3. 发布结果回调
```http
POST /overseas-platform/publish/callback
Content-Type: application/json

{
  "taskId": "task123",
  "status": "success",
  "platformContentId": "fb_post_123",
  "platformContentUrl": "https://facebook.com/post/123",
  "completedAt": "2024-01-01T10:05:00Z"
}
```

### 香港服务端接口

#### 1. 执行发布任务
```http
POST /content-publish/tasks
Content-Type: application/json

{
  "taskId": "task123",
  "taskSetId": "taskset123",
  "teamId": "team123",
  "userId": "user123",
  "accountOpenId": "fb_page_123",
  "platform": "facebook",
  "content": { ... },
  "callbackUrl": "https://gateway.com/callback"
}
```

#### 2. 批量发布任务
```http
POST /content-publish/tasks/batch
Content-Type: application/json

{
  "tasks": [...]
}
```

## 数据模型设计

### 发布内容数据结构
```typescript
interface PublishContentData {
  type: 'text' | 'image' | 'video' | 'mixed'
  text?: string
  title?: string
  description?: string
  images?: string[]
  videoUrl?: string
  videoCover?: string
  tags?: string[]
  location?: {
    name: string
    latitude?: number
    longitude?: number
  }
  platformSpecific?: Record<string, any>
}
```

### 发布任务数据结构
```typescript
interface PublishTaskData {
  taskId: string
  taskSetId: string
  teamId: string
  userId: string
  accountOpenId: string
  platform: string
  content: PublishContentData
  publishAt?: Date
  callbackUrl: string
  retryCount?: number
  maxRetries?: number
  createdAt: Date
}
```

## 错误处理和重试机制

### 1. 错误分类
- **网络错误**: 自动重试，最大重试次数3次
- **认证错误**: 不重试，直接失败
- **内容违规**: 不重试，标记为拒绝
- **平台限制**: 延迟重试

### 2. 重试策略
- 指数退避算法
- 最大重试间隔: 30分钟
- 重试次数限制: 3次

### 3. 监控和告警
- 发布成功率监控
- 平台API响应时间监控
- 错误率告警
- 回调失败告警

## 安全机制

### 1. 跨服务认证
- 使用API密钥进行服务间认证
- 请求签名验证
- IP白名单限制

### 2. 数据安全
- 敏感数据加密存储
- 传输过程HTTPS加密
- 访问令牌定期刷新

## 部署和运维

### 1. 服务部署
- 国内服务: 阿里云ECS
- 香港服务: 香港云服务器
- 数据库: MongoDB集群
- 缓存: Redis集群

### 2. 监控指标
- 发布任务处理量
- 平台API调用成功率
- 服务响应时间
- 错误率统计

### 3. 日志管理
- 结构化日志输出
- 集中式日志收集
- 日志分级和过滤
- 敏感信息脱敏

## 扩展性设计

### 1. 新平台接入
- 实现`ContentPublishProvider`接口
- 注册到提供者工厂
- 配置平台特定参数

### 2. 功能扩展
- 定时发布功能
- 内容审核集成
- 数据统计分析
- 批量操作优化

## 性能优化

### 1. 并发处理
- 异步任务处理
- 批量API调用
- 连接池复用

### 2. 缓存策略
- 平台配置缓存
- 用户认证信息缓存
- API响应缓存

### 3. 资源优化
- 图片压缩和CDN
- 视频转码优化
- 带宽限制控制
