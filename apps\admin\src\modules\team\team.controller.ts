import { Body, Controller, Get, Inject, Param, Post, Put, Query, Request } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  RefundOrderLogsResponseDTO,
  RefundOrderRequestBodyDTO,
  RefundOrdersResponseDTO,
  TeamAccountsResponseDTO,
  TeamListRequest,
  TeamListResponseDTO,
  TeamMembersResponseDTO
} from './team.dto'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { TeamService } from './team.service'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { AdminAndOpenPlatformAccess } from '../../common/decorators/access-control.decorator'
import { UserType } from '@yxr/common'

@Controller('teams')
@AdminAndOpenPlatformAccess()
@ApiTags('团队')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class TeamController {
  constructor(
    private readonly teamService: TeamService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Get()
  @ApiOperation({ summary: '获取团队列表' })
  @ApiOkResponse({ description: '操作成功', type: TeamListResponseDTO })
  @ApiQuery({ type: TeamListRequest })
  async getMembers(@Query() query: TeamListRequest, @Request() request: FastifyRequest) {
    if (request.session?.userType === UserType.OPEN_PLATFORM) {
      if (!query.applicationId) {
        throw new Error('开放平台查询必须传应用ID')
      }
    }
    return this.teamService.getTeams(query)
  }

  @Get('/:teamId/refund')
  @ApiOperation({ summary: '获取团队退款记录' })
  @ApiOkResponse({ description: '操作成功', type: RefundOrderLogsResponseDTO })
  async getRefund(@Param('teamId') teamId: string) {
    return await this.teamService.getTeamRefund(teamId)
  }

  @Get(':teamId/refund/orders')
  @ApiOperation({ summary: '退款对应订单' })
  @ApiOkResponse({ type: RefundOrdersResponseDTO })
  async getOrderListByRefund(@Param('teamId') teamId: string) {
    return await this.teamService.getOrderListByRefund(teamId)
  }

  @Post(':teamId/refund')
  @ApiOperation({ summary: '退款' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async refund(@Param('teamId') teamId: string, @Body() body: RefundOrderRequestBodyDTO) {
    const { user, session } = this.request
    // 根据用户类型获取用户名
    const username = (user as any)?.username || (user as any)?.phone || session?.userId || 'unknown'
    return await this.teamService.refund(username, teamId, body)
  }

  @Get(':teamId/members')
  @ApiOperation({ summary: '获取团队成员列表数据' })
  @ApiOkResponse({ type: TeamMembersResponseDTO })
  async getTeamMembers(@Param('teamId') teamId: string) {
    return await this.teamService.getTeamMembers(teamId)
  }

  @Get(':teamId/accounts')
  @ApiOperation({ summary: '获取团队媒体账号列表数据' })
  @ApiOkResponse({ type: TeamAccountsResponseDTO })
  async getTeamAccounts(@Param('teamId') teamId: string) {
    return await this.teamService.getTeamAccounts(teamId)
  }
}
