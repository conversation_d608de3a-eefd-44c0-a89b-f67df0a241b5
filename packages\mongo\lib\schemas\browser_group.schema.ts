import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
/**
 * 浏览器分组
 */
export class BrowserGroupEntity {
  @Prop({
    type: String,
    required: true
  })
  name: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: [String]
  })
  browsers: string[]

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const BrowserGroupSchema: ModelDefinition = {
  name: BrowserGroupEntity.name,
  schema: SchemaFactory.createForClass(BrowserGroupEntity)
}

export const BrowserGroupMongoose = MongooseModule.forFeature([BrowserGroupSchema])
