import { Inject, Injectable, Logger, Scope } from '@nestjs/common'
import { AccountAuthProvider } from '../account-auth.provider'
import { auth } from 'twitter-api-sdk'
import { OAuth2Scopes } from 'twitter-api-sdk/dist/auth'
import crypto from 'crypto'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { OverseasContext, PlatformAccountInfo } from '../types'
import { TwitterApi } from './twitter-api'

@Injectable({ scope: Scope.TRANSIENT })
export class TwitterAccountAuthProvider implements AccountAuthProvider {
  logger = new Logger(TwitterAccountAuthProvider.name)

  private readonly client_id = process.env.TWITTER_CLIENT_ID || ''

  private readonly client_secret = process.env.TWITTER_CLIENT_SECRET || ''

  private readonly oauthCallbackUri = `${process.env.OVERSEAS_BASE_ADDRESS}auth/callback`

  // @ts-ignore
  private readonly scopes: OAuth2Scopes[] = ['tweet.read', 'users.read', 'tweet.write', 'tweet.moderate.write', 'media.write', 'offline.access']

  constructor(
    private readonly api: TwitterApi,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>) {
  }

  async generateAuthorizationUrl(state: string): Promise<string> {
    const authClient = new auth.OAuth2User({
      client_id: this.client_id,
      client_secret: this.client_secret,
      callback: this.oauthCallbackUri,
      scopes: this.scopes
    })

    // 1. 生成 code_verifier
    const codeVerifier = crypto.randomBytes(32).toString('base64url').slice(0, 43)

    // this.logger.debug(`code_verifier: ${codeVerifier}`)
    await this.cacheManager.set(`twitter:${state}:code_verifier`, codeVerifier, 60 * 1000)

    // 2. 生成 code_challenge（S256）
    // const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url')

    const url = authClient.generateAuthURL({
      state: state,
      code_challenge: codeVerifier,
      code_challenge_method: 'plain'
    })

    return url
  }

  async exchangeAuthCodeForAccounts(context: OverseasContext, code: string, state: string): Promise<PlatformAccountInfo[]> {

    const codeVerifier = await this.cacheManager.get<string>(`twitter:${state}:code_verifier`)
    if (!codeVerifier) {
      throw new Error('code_verifier not found') // 操作超时导致缓存失效, 需要重新授权
    }

    const token = await this.api.oauth2_token(context, {
      code: code,
      redirect_uri: this.oauthCallbackUri,
      code_verifier: codeVerifier
    })

    this.logger.debug(`retrieveAccessToken: ${JSON.stringify(token, null, 2)}`)

    const user = await this.api.get_users_me(context, token.access_token, {
      'user.fields': ['username', 'profile_image_url', 'name', 'verified']
    })

    this.logger.debug(`getUserInfo: ${JSON.stringify(user, null, 2)}`)

    return [
      {
        openId: user.data.id,
        credentials: {
          token_type: token.token_type,
          scope: token.scope,
          access_token: token.access_token,
          expire_at: new Date(token.expires_in * 1000 + new Date().getTime()),
          refresh_token: token.refresh_token
        },
        avatar: user.data.profile_image_url,
        name: user.data.name!,
        username: user.data.username
      } as PlatformAccountInfo,
    ]
  }
}
