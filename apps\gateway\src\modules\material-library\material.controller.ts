import { Controller, Get, Param, Query, Post, Put, Delete, Body } from '@nestjs/common'
import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiParam
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'

import {
  DeleteMultiMaterialResponseDTO,
  MaterialGroupListResponseDTO,
  MaterialGroupResponseDTO,
  MaterialListResponseDTO,
  PostMaterialGroupsRequest,
  PostMaterialRequest,
  SetMaterialGroupRequest
} from './material.dto'
import { MaterialGroupService } from './material-group.service'
import { MaterialService } from './material.service'
import { isString } from 'class-validator'

@Controller('material')
@ApiTags('素材库管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class MaterialController {
  constructor(
    private readonly materialGroupService: MaterialGroupService,
    private readonly materialService: MaterialService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取素材列表' })
  @ApiParam({ name: 'type', required: false, description: '素材类型 全部则不传' })
  @ApiParam({ name: 'groupId', required: false, description: '分组ID 全部分组则不传' })
  @ApiParam({ name: 'size', required: false, description: '每页显示数量 默认10' })
  @ApiParam({ name: 'page', required: false, description: '分页页码 默认1' })
  @ApiOkResponse({ type: MaterialListResponseDTO, description: '操作成功' })
  async getMaterial(
    @Query('type') type: string,
    @Query('groupId') groupId: string,
    @Query('size', { transform: (value) => value || 10 }) size: number,
    @Query('page', { transform: (value) => value || 1 }) page: number
  ) {
    return await this.materialService.getMaterial(type, groupId, page, size)
  }

  @Post()
  @ApiOperation({ summary: '上传素材' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postMaterial(@Body() body: PostMaterialRequest) {
    return await this.materialService.postMaterial(body)
  }

  @Post(':materialId/set-group')
  @ApiOperation({ summary: '设置素材分组' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async patchMaterial(
    @Param('materialId') materialId: string,
    @Body() body: SetMaterialGroupRequest
  ) {
    return await this.materialService.setMaterialGroup(materialId, body)
  }

  @Delete(':materialId')
  @ApiOperation({ summary: '删除素材' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deleteMaterial(@Param('materialId') materialId: string) {
    return await this.materialService.deleteMaterial(materialId)
  }

  @Delete('batch')
  @ApiOperation({ summary: '批量删除素材库' })
  @ApiOkResponse({ type: DeleteMultiMaterialResponseDTO })
  async deleteBatchMaterials(
    @Query('materialIds[]', { transform: (value) => (isString(value) ? [value] : value) })
    materialIds: string[]
  ) {
    return await this.materialService.deleteBatchMaterials(materialIds)
  }

  @Get('groups')
  @ApiOperation({ summary: '获取分组列表' })
  @ApiOkResponse({ type: MaterialGroupListResponseDTO, description: '操作成功' })
  async getGroups() {
    return await this.materialGroupService.getGroupsAsync()
  }

  @Post('groups')
  @ApiOperation({ summary: '创建分组' })
  @ApiOkResponse({ type: MaterialGroupResponseDTO, description: '操作成功' })
  async postGroups(@Body() body: PostMaterialGroupsRequest) {
    return await this.materialGroupService.postGroups(body)
  }

  @Put('groups/:groupId')
  @ApiOperation({ summary: '修改分组' })
  @ApiOkResponse({ type: MaterialGroupResponseDTO, description: '操作成功' })
  async putGroups(@Param('groupId') groupId: string, @Body() body: PostMaterialGroupsRequest) {
    return await this.materialGroupService.putGroupsAsync(groupId, body)
  }

  @Delete('groups/:groupId')
  @ApiOperation({ summary: '删除分组' })
  @ApiOkResponse()
  async deleteGroups(@Param('groupId') groupId: string) {
    return await this.materialGroupService.deleteGroupsAsync(groupId)
  }
}
