import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { InvitationStatus, OpenPlatformRoleNames } from '@yxr/common'

@Schema({
  collection: 'open_platform_invitations',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformInvitationEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  inviterId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  inviteeId: Types.ObjectId

  @Prop({
    type: String,
    required: true
  })
  inviteePhone: string

  @Prop({
    type: Number,
    enum: InvitationStatus,
    required: true,
    default: InvitationStatus.PENDING
  })
  status: InvitationStatus

  @Prop({
    type: String,
    enum: OpenPlatformRoleNames.All,
    required: true,
    default: OpenPlatformRoleNames.CHANNEL
  })
  invitedRole: string

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  remark?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

// 创建复合索引确保同一应用对同一用户只能有一个待处理的邀请
const schema = SchemaFactory.createForClass(OpenPlatformInvitationEntity);
schema.index(
  { applicationId: 1, inviteeId: 1, status: 1 }, 
  { 
    unique: true,
    partialFilterExpression: { status: InvitationStatus.PENDING }
  }
);

export const OpenPlatformInvitationSchema: ModelDefinition = {
  name: OpenPlatformInvitationEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformInvitationEntity)
}

export const OpenPlatformInvitationMongoose = MongooseModule.forFeature([OpenPlatformInvitationSchema])
