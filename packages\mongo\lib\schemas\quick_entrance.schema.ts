import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class QuickEntranceEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 快捷参数包 前端需要的快捷入口数据
   */
  @Prop({
    type: Types.Map,
    required: true
  })
  quickArgs: unknown
}

export const QuickEntranceSchema: ModelDefinition = {
  name: QuickEntranceEntity.name,
  schema: SchemaFactory.createForClass(QuickEntranceEntity)
}

export const QuickEntranceMongoose = MongooseModule.forFeature([QuickEntranceSchema])
