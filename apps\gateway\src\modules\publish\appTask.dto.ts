import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Type } from 'class-transformer'
import { TaskSetStatusEnum } from '@yxr/common'

export class TaskPlatformAccountDTO {
  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    example: 'http://xxxxxx.png'
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    example: '葫芦娃七兄弟'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    example: '抖音'
  })
  platformName: string
}

export class AppTaskCreateDTO {
  @ApiProperty({
    description: '账号媒体ID',
    example: '3a031a79-e785-7bef-a665-2bf310ea0212',
    required: true
  })
  @IsString({ message: '账号ID格式不正确' })
  platformAccountId: string

  // 视频地址??
  @ApiProperty({
    description: '视频存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsOptional()
  videoKey?: string

  @ApiProperty({
    description: '封面存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsOptional()
  coverKey?: string

  @ApiProperty({
    description: '描述',
    example: '视频描述',
    required: false
  })
  @IsOptional()
  desc?: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  @IsOptional()
  descRich?: unknown

  @ApiProperty({
    description: '发布参数(动态结构, 服务端不做任何处理, 会原封不动的下发给客户端用于发布)',
    example: {
      title: '测试标题',
      content: '测试内容',
      cover: 'https://xxxxx.png',
      tags: ['测试标签'],
      category: '测试分类',
      video: {
        url: 'https://xxxxx.mp4',
        cover: 'https://xxxxx.png'
      }
    },
    required: false
  })
  publishArgs: unknown
}

export class AppTaskSetCreateRequest {
  @ApiProperty({
    description: '任务集发布类型',
    example: 'verticalVideo',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '发布类型不能为空' })
  publishType: string

  @ApiProperty({
    description: '任务集封面存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsString()
  @IsNotEmpty({ message: '任务集封面不能为空' })
  coverKey: string

  @ApiProperty({
    description: '描述',
    example: '任务集描述',
    required: false
  })
  @IsOptional()
  desc: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  @IsOptional()
  descRich: unknown

  @ApiProperty({
    type: [String],
    description: '账号媒体ID数组',
    example: "['抖音号,视频号']",
    required: true
  })
  @IsArray({ message: '发布平台格式不匹配' })
  @IsNotEmpty({ message: '发布平台不能为空' })
  platforms: string[]

  @ApiProperty({
    type: [AppTaskCreateDTO],
    description: '子任务数组',
    required: true
  })
  @IsArray({ message: '发布任务格式不匹配' })
  @IsNotEmpty({ message: '发布任务不能为空' })
  @Type(() => AppTaskCreateDTO)
  appTask: AppTaskCreateDTO[]
}

export class AppTaskDetailsDto {
  @ApiProperty({
    description: '任务ID',
    required: true
  })
  id: string

  @ApiProperty({
    description: '任务集ID',
    required: true
  })
  taskSetId: string

  @ApiProperty({
    description: '账号媒体',
    required: true
  })
  platformAccount: TaskPlatformAccountDTO
}

export class AppTaskSetDetailsDto {
  @ApiProperty({
    description: '任务集的taskIdentityId',
    required: true
  })
  id: string

  @ApiProperty({
    type: [String],
    example: "['抖音号,视频号']",
    description: '账号媒体',
    required: true
  })
  platforms: string[]

  @ApiProperty({
    description: '封面',
    example: 'https://xxxxxx.png',
    required: false
  })
  @IsOptional()
  coverKey: string

  @ApiProperty({
    description: '封面',
    example: 'https://xxxxxx.png',
    required: false
  })
  @IsOptional()
  coverUrl: string

  @ApiProperty({
    description: '描述',
    example: '视频描述',
    required: false
  })
  desc: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  descRich: unknown

  @ApiProperty({
    description: '任务分配状态(0:未分配, 1:已分配)',
    required: true
  })
  distributionStatus: string

  @ApiProperty({
    title: '任务集状态',
    description:
      '任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功), allfailed(全部发布失败)',
    type: String,
    enum: TaskSetStatusEnum,
    example: TaskSetStatusEnum.Publishing
  })
  taskSetStatus: TaskSetStatusEnum
}

export class TaskStatusDto {
  @ApiProperty({
    description: '任务ID',
    required: true
  })
  id: string

  @ApiProperty({
    description: '任务分配状态(0:未分配, 1:已分配)',
    required: true
  })
  distributionStatus: string
}

export class TaskStatusListResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TaskStatusDto]
  })
  data: TaskStatusDto[]
}

export class AppTaskListResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AppTaskDetailsDto]
  })
  data: AppTaskDetailsDto[]
}

export class AppTaskSetPagedListResponse {
  @ApiResponseProperty({
    type: [AppTaskSetDetailsDto]
  })
  data: AppTaskSetDetailsDto[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

// TODO:
export class getAppTaskPagedListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '查询此时间戳之后是否有新消息 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  time: number = 0
}

export class AppTaskPagedListResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AppTaskDetailsDto]
  })
  data: AppTaskDetailsDto[]
}

export class getAppTaskSetPagedListOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AppTaskSetPagedListResponse
  })
  data: AppTaskSetPagedListResponse
}

export class PublishAttachmentsDTO {
  @ApiProperty({
    type: String,
    description: '视频地址',
    example: 'https://xxx.com/xxx.mp4',
    required: true
  })
  videoUrl: string

  @ApiProperty({
    type: String,
    description: '封面地址',
    example: 'https://xxx.com/xxx.jpg',
    required: true
  })
  coverUrl: string
}

export class PublishPlatformAccountDTO {
  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    example: '葫芦娃七兄弟'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    example: '头像地址'
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    example: '1e222223'
  })
  platformAuthorId: string

  @ApiProperty({
    type: String,
    example: '抖音'
  })
  platformName: string

  @ApiProperty({
    type: String,
    example: '备注'
  })
  remark: string
}

// TODO:
export class AppPublishTaskDetailsDto {
  @ApiProperty({
    description: '任务ID',
    required: true
  })
  taskId: string

  @ApiProperty({
    description: '任务集ID',
    required: true
  })
  taskSetId: string

  @ApiProperty({
    type: String,
    description: '任务状态更新令牌',
    example: 'nonaid()',
    required: true
  })
  token: string

  @ApiProperty({
    description: '发布账号',
    required: true
  })
  platformAccount: PublishPlatformAccountDTO

  @ApiProperty({
    description: '发布账号',
    required: true
  })
  attachments: PublishAttachmentsDTO

  @ApiProperty({
    description: '发布参数(动态结构, 服务端不做任何处理, 会原封不动的下发给客户端用于发布)',
    example: {
      title: '测试标题',
      content: '测试内容',
      cover: 'https://xxxxx.png',
      tags: ['测试标签'],
      category: '测试分类',
      video: {
        url: 'https://xxxxx.mp4',
        cover: 'https://xxxxx.png'
      }
    },
    required: false
  })
  publishArgs: unknown
}

export class getAppPublishTaskDetailsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AppPublishTaskDetailsDto]
  })
  data: AppPublishTaskDetailsDto[]
}
