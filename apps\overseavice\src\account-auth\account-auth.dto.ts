import { ApiProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'

export enum AuthorizationStatus {
  /**
   * 用户操作超时
   */
  Expired = 'expired',

  /**
   * 已授权成功
   */
  Authorized = 'authorized',

  /**
   * 等待用户操作
   */
  Waiting = 'waiting',

  /**
   * 未知错误
   */
  Error = 'error'
}

// =======================================================================================================================
// Input
// =======================================================================================================================
export class OverseasAuthorizationCallbackInputDto {
  @IsString()
  @IsNotEmpty()
  code: string

  @IsString()
  @IsNotEmpty()
  state: string
}
