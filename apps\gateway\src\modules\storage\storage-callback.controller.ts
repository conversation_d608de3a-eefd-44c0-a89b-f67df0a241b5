import { Controller, Inject, Injectable, Logger, Post, Res } from '@nestjs/common'
import { ApiOkResponse, ApiOperation } from '@nestjs/swagger'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest, FastifyReply } from 'fastify'

@Controller('storages-callback')
export class StorageCallbackController {
  logger = new Logger('StorageCallbackController')

  constructor(@Inject(REQUEST) private request: FastifyRequest) {}

  @Post()
  @ApiOperation({ summary: '接收阿里云上传回调' })
  @ApiOkResponse()
  async postOssCallBack(@Res() reply: FastifyReply) {
    const body = this.request.body
    this.logger.debug(`获取返回oss回调结果：${JSON.stringify(body)}`)

    reply.code(200).send({
      Status: 'Ok'
    })
  }
}
