import { Provider } from '@nestjs/common'
import { OverseasPlatformNames } from '../../constants'
import { AccountAuthProvider } from '../account-auth.provider'
import { ContentPublishProvider } from '../content-publish.provider'
import { InstagramAccountAuthProvider } from './instagram-account-auth.provider'
import { InstagramContentPublishProvider } from './instagram-content-publish.provider'
import { InstagramApi } from './instagram-api'

export const providers = [
  InstagramApi,
  {
    provide: AccountAuthProvider.register_token(OverseasPlatformNames.Instagram),
    useClass: InstagramAccountAuthProvider
  },
  {
    provide: ContentPublishProvider.register_token(OverseasPlatformNames.Instagram),
    useClass: InstagramContentPublishProvider
  },
  // {
  //   provide: UserinfoService.token(Tiktok),
  //   useClass: TiktokBusinessUserinfoService
  // },
  // {
  //   provide: ContentService.token(Tiktok),
  //   useClass: TiktokBusinessContentService
  // },
  // {
  //   provide: CommentService.token(Tiktok),
  //   useClass: TiktokBusinessCommentService
  // },
  // {
  //   provide: MessageService.token(Tiktok),
  //   useClass: TiktokBusinessMessageService
  // },
  // {
  //   provide: DataRetrievalService.token(Tiktok),
  //   useClass: TiktokBusinessDataRetrievalService
  // },
  // {
  //   provide: PermissionProvider.token(Tiktok),
  //   useClass: TiktokBusinessPermissionProvider
  // },
  // {
  //   provide: WebhookProvider.token(Tiktok),
  //   useClass: TiktokBusinessWebhookProvider
  // }
] as Provider[]
