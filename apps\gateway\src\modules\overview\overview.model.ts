import { Module } from '@nestjs/common'
import {
  HistoryStatisticMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  PlatformAccountSummaryMongoose,
  PlatformAccountTrendMongoose,
  TaskMongoose
} from '@yxr/mongo'
import { OverviewService } from './overview.service'
import { OverviewController } from './overview.controller'
import { CommonModule } from '@yxr/common'

@Module({
  imports: [
    PlatformAccountMongoose,
    TaskMongoose,
    MemberMongoose,
    PlatformAccountSummaryMongoose,
    HistoryStatisticMongoose,
    PlatformAccountTrendMongoose,
    CommonModule
  ],
  controllers: [OverviewController],
  providers: [OverviewService]
})
export class OverviewModule {}
