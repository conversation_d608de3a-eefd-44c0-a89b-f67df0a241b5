# 海外平台内容发布系统部署指南

## 环境要求

### 国内服务端 (Gateway)
- **操作系统**: Linux (Ubuntu 20.04+ 或 CentOS 7+)
- **Node.js**: 18.x 或更高版本
- **数据库**: MongoDB 5.0+
- **缓存**: Redis 6.0+
- **内存**: 最低 4GB，推荐 8GB+
- **存储**: 最低 50GB，推荐 100GB+

### 香港服务端 (Overseavice)
- **操作系统**: Linux (Ubuntu 20.04+ 或 CentOS 7+)
- **Node.js**: 18.x 或更高版本
- **缓存**: Redis 6.0+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+

## 环境变量配置

### 国内服务端环境变量

创建 `.env` 文件：

```bash
# 基础配置
NODE_ENV=production
PORT=3000

# 数据库配置
MONGODB_URI=****************************************************
MONGODB_DB_NAME=yixiaoer

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_NORMAL_DB=0

# 海外服务配置
OVERSEAS_BASE_ADDRESS=https://overseas.yixiaoer.com/
LITE_BASE_ADDRESS=https://api.yixiaoer.com/

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 文件存储配置
OSS_ACCESS_KEY_ID=your_oss_access_key
OSS_ACCESS_KEY_SECRET=your_oss_secret_key
OSS_BUCKET=your_bucket_name
OSS_REGION=oss-cn-hangzhou

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/yixiaoer/gateway.log
```

### 香港服务端环境变量

创建 `.env` 文件：

```bash
# 基础配置
NODE_ENV=production
PORT=3000

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_NORMAL_DB=0

# 国内服务配置
LITE_BASE_ADDRESS=https://api.yixiaoer.com/

# 海外平台配置
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret

INSTAGRAM_CLIENT_ID=your_instagram_app_id
INSTAGRAM_CLIENT_SECRET=your_instagram_app_secret

TWITTER_CLIENT_ID=your_twitter_app_id
TWITTER_CLIENT_SECRET=your_twitter_app_secret

TIKTOK_CLIENT_ID=your_tiktok_app_id
TIKTOK_CLIENT_SECRET=your_tiktok_app_secret

YOUTUBE_CLIENT_ID=your_youtube_app_id
YOUTUBE_CLIENT_SECRET=your_youtube_app_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/yixiaoer/overseavice.log
```

## 部署步骤

### 1. 国内服务端部署

#### 1.1 安装依赖
```bash
# 克隆代码
git clone https://github.com/your-org/yixiaoer-service.git
cd yixiaoer-service

# 安装依赖
pnpm install

# 构建项目
pnpm build:gateway
```

#### 1.2 配置数据库
```bash
# 启动MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# 创建数据库用户
mongo
> use yixiaoer
> db.createUser({
    user: "yixiaoer_user",
    pwd: "your_password",
    roles: [{ role: "readWrite", db: "yixiaoer" }]
  })
```

#### 1.3 配置Redis
```bash
# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis

# 配置Redis密码
sudo vim /etc/redis/redis.conf
# 取消注释并设置: requirepass your_redis_password

# 重启Redis
sudo systemctl restart redis
```

#### 1.4 启动服务
```bash
# 使用PM2管理进程
npm install -g pm2

# 启动服务
pm2 start ecosystem.config.js --only gateway

# 设置开机自启
pm2 startup
pm2 save
```

### 2. 香港服务端部署

#### 2.1 安装依赖
```bash
# 克隆代码
git clone https://github.com/your-org/yixiaoer-service.git
cd yixiaoer-service

# 安装依赖
pnpm install

# 构建项目
pnpm build:overseavice
```

#### 2.2 配置Redis
```bash
# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis

# 配置Redis密码
sudo vim /etc/redis/redis.conf
# 取消注释并设置: requirepass your_redis_password

# 重启Redis
sudo systemctl restart redis
```

#### 2.3 启动服务
```bash
# 启动服务
pm2 start ecosystem.config.js --only overseavice

# 设置开机自启
pm2 startup
pm2 save
```

## Nginx配置

### 国内服务端Nginx配置

```nginx
server {
    listen 80;
    server_name api.yixiaoer.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yixiaoer.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 文件上传大小限制
    client_max_body_size 100M;

    # 日志配置
    access_log /var/log/nginx/api.yixiaoer.com.access.log;
    error_log /var/log/nginx/api.yixiaoer.com.error.log;
}
```

### 香港服务端Nginx配置

```nginx
server {
    listen 80;
    server_name overseas.yixiaoer.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name overseas.yixiaoer.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 限制访问来源（可选）
    allow 国内服务器IP;
    deny all;

    # 日志配置
    access_log /var/log/nginx/overseas.yixiaoer.com.access.log;
    error_log /var/log/nginx/overseas.yixiaoer.com.error.log;
}
```

## PM2配置文件

创建 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [
    {
      name: 'gateway',
      script: './dist/apps/gateway/main.js',
      cwd: '/path/to/yixiaoer-service',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/yixiaoer/gateway-error.log',
      out_file: '/var/log/yixiaoer/gateway-out.log',
      log_file: '/var/log/yixiaoer/gateway.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    },
    {
      name: 'overseavice',
      script: './dist/apps/overseavice/main.js',
      cwd: '/path/to/yixiaoer-service',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      error_file: '/var/log/yixiaoer/overseavice-error.log',
      out_file: '/var/log/yixiaoer/overseavice-out.log',
      log_file: '/var/log/yixiaoer/overseavice.log',
      time: true,
      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512'
    }
  ]
}
```

## 监控和日志

### 1. 系统监控

安装监控工具：
```bash
# 安装htop
sudo apt install htop

# 安装iotop
sudo apt install iotop

# 安装netstat
sudo apt install net-tools
```

### 2. 应用监控

使用PM2监控：
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs

# 查看监控面板
pm2 monit

# 重启应用
pm2 restart all

# 重载应用（零停机）
pm2 reload all
```

### 3. 日志轮转

配置logrotate：
```bash
sudo vim /etc/logrotate.d/yixiaoer
```

```
/var/log/yixiaoer/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        pm2 reloadLogs
    endscript
}
```

## 安全配置

### 1. 防火墙配置

```bash
# 启用UFW
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 拒绝其他端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
```

### 2. SSL证书

使用Let's Encrypt：
```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d api.yixiaoer.com
sudo certbot --nginx -d overseas.yixiaoer.com

# 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 备份策略

### 1. 数据库备份

```bash
#!/bin/bash
# backup-mongodb.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mongodb"
DB_NAME="yixiaoer"

mkdir -p $BACKUP_DIR

mongodump --host localhost --port 27017 \
  --username yixiaoer_user --password your_password \
  --db $DB_NAME --out $BACKUP_DIR/$DATE

# 压缩备份
tar -czf $BACKUP_DIR/mongodb_$DATE.tar.gz $BACKUP_DIR/$DATE
rm -rf $BACKUP_DIR/$DATE

# 删除7天前的备份
find $BACKUP_DIR -name "mongodb_*.tar.gz" -mtime +7 -delete
```

### 2. 代码备份

```bash
#!/bin/bash
# backup-code.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/code"
SOURCE_DIR="/path/to/yixiaoer-service"

mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/code_$DATE.tar.gz \
  --exclude=node_modules \
  --exclude=dist \
  --exclude=.git \
  $SOURCE_DIR

# 删除30天前的备份
find $BACKUP_DIR -name "code_*.tar.gz" -mtime +30 -delete
```

## 故障排查

### 常见问题

1. **服务无法启动**
   - 检查端口是否被占用：`netstat -tlnp | grep :3000`
   - 检查环境变量配置
   - 查看错误日志：`pm2 logs`

2. **数据库连接失败**
   - 检查MongoDB服务状态：`sudo systemctl status mongod`
   - 验证连接字符串和认证信息
   - 检查防火墙设置

3. **Redis连接失败**
   - 检查Redis服务状态：`sudo systemctl status redis`
   - 验证密码配置
   - 检查网络连接

4. **海外平台API调用失败**
   - 检查网络连接
   - 验证API密钥和权限
   - 查看平台API状态页面

### 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 定期清理过期数据
   - 监控慢查询

2. **缓存优化**
   - 合理设置缓存过期时间
   - 监控缓存命中率
   - 定期清理无效缓存

3. **应用优化**
   - 启用gzip压缩
   - 优化图片和视频处理
   - 实现连接池复用
