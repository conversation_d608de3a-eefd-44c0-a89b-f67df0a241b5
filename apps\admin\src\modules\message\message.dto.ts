import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Type } from 'class-transformer'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { NoticeTypesEnum } from '@yxr/common'

export class MessageCreateRequestDTO {
  @ApiProperty({
    type: String,
    description: '标题',
    default: '通知标题'
  })
  @IsString()
  @IsNotEmpty({ message: '标题不能为空' })
  title: string

  @ApiProperty({
    type: Boolean,
    description: '是否弹框',
    default: false
  })
  @IsBoolean()
  isPopUp: boolean

  @ApiProperty({
    type: String,
    description: '内容'
  })
  @IsString()
  @IsNotEmpty({ message: '内容不能为空' })
  content: string
}

export class PutAdEnabledRequestDTO {
  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  @IsBoolean()
  enabled: boolean
}

export class MessageListRequestDTO {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class MessageDetail {
  @ApiProperty({
    type: String,
    description: '消息Id',
    default: '123232132'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '标题',
    default: '消息标题'
  })
  title: string

  @ApiProperty({
    type: String,
    description: '内容',
    default: '消息内容'
  })
  content: string

  @ApiProperty({
    type: Number,
    description: '创建时间'
  })
  createdAt: number

  @ApiProperty({
    type: Boolean,
    description: '是否弹窗'
  })
  isPopUp: boolean

  @ApiProperty({
    type: String,
    enum: [NoticeTypesEnum.System],
    description: '消息类型 system系统'
  })
  type: NoticeTypesEnum
}

export class MessageDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MessageDetail
  })
  data: MessageDetail
}

export class MessageListResponse {
  @ApiResponseProperty({
    type: [MessageDetail]
  })
  data: MessageDetail[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class MessageListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MessageListResponse
  })
  data: MessageListResponse
}
