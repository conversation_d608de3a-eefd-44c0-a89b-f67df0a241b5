import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsNumber, IsOptional, IsEnum, IsDateString, Min, IsMongoId } from 'class-validator'
import { Transform } from 'class-transformer'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { OrderStatus, OrderType, OpenPlatformOrderMainType } from '@yxr/common'

/**
 * 创建开通订单请求DTO
 */
export class CreateOpenPlatformOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsMongoId()
  teamId: string

  @ApiProperty({
    description: '账号数量',
    example: 10,
    minimum: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: '账号数量必须大于0' })
  accountCount: number

  @ApiProperty({
    description: '流量数量（GB）',
    example: 100,
    minimum: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: '流量数量必须大于0' })
  trafficCount: number

  @ApiProperty({
    description: '时长（月）',
    example: 3,
    minimum: 1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1, { message: '时长必须大于0' })
  duration: number

  @ApiPropertyOptional({
    description: '订单开始时间（可选，默认为当前时间）',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDateString()
  startTime?: string

  @ApiPropertyOptional({
    description: '订单备注',
    example: '开通VIP服务'
  })
  @IsOptional()
  @IsString()
  remark?: string
}



/**
 * 创建增购订单请求DTO
 */
export class CreateAddonOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsMongoId()
  teamId: string

  @ApiProperty({
    description: '账号数量',
    example: 5,
    minimum: 0
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: '账号数量不能小于0' })
  accountCount: number

  @ApiProperty({
    description: '流量数量（GB）',
    example: 50,
    minimum: 0
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0, { message: '流量数量不能小于0' })
  trafficCount: number

  @ApiProperty({
    description: '时长（可自定义数字）',
    example: 1.5,
    minimum: 0.1
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0.1, { message: '时长必须大于0' })
  duration: number

  @ApiProperty({
    description: '订单开始时间',
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsNotEmpty()
  @IsDateString()
  startTime: string

  @ApiPropertyOptional({
    description: '订单备注',
    example: '增购账号和流量'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 订单列表查询请求DTO
 */
export class OrderListRequestDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value) || 10)
  limit?: number = 10

  @ApiPropertyOptional({
    description: '团队ID筛选',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsMongoId()
  teamId?: string

  @ApiPropertyOptional({
    description: '订单状态筛选',
    enum: OrderStatus
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  orderStatus?: OrderStatus

  @ApiPropertyOptional({
    description: '订单类型筛选',
    enum: OrderType
  })
  @IsOptional()
  @IsEnum(OrderType)
  orderType?: OrderType

  @ApiPropertyOptional({
    description: '主订单类型筛选',
    enum: OpenPlatformOrderMainType
  })
  @IsOptional()
  @IsEnum(OpenPlatformOrderMainType)
  mainType?: OpenPlatformOrderMainType
}

/**
 * 订单响应DTO
 */
export class OrderResponseDto {
  @ApiProperty({
    description: '订单ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '订单编号',
    example: 'OP202401010001'
  })
  orderNo: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiProperty({
    description: '订单状态',
    enum: OrderStatus
  })
  orderStatus: OrderStatus

  @ApiProperty({
    description: '订单类型',
    enum: OrderType
  })
  orderType: OrderType

  @ApiProperty({
    description: '主订单类型',
    enum: OpenPlatformOrderMainType
  })
  mainType: OpenPlatformOrderMainType

  @ApiProperty({
    description: '账号数量',
    example: 10
  })
  accountCount: number

  @ApiProperty({
    description: '流量数量（GB）',
    example: 100
  })
  trafficCount: number

  @ApiProperty({
    description: '时长（月）',
    example: 3
  })
  duration: number

  @ApiProperty({
    description: '订单开始时间',
    example: *************
  })
  startTime: number

  @ApiProperty({
    description: '订单结束时间',
    example: *************
  })
  endTime: number

  @ApiProperty({
    description: '订单总金额（分）',
    example: 15000
  })
  totalAmount: number

  @ApiProperty({
    description: '应付金额（分）',
    example: 15000
  })
  payableAmount: number

  @ApiProperty({
    description: '订单总金额（元）',
    example: 150.00
  })
  totalAmountYuan: number

  @ApiProperty({
    description: '应付金额（元）',
    example: 150.00
  })
  payableAmountYuan: number

  @ApiPropertyOptional({
    description: '主订单ID（仅增购订单）',
    example: '507f1f77bcf86cd799439011'
  })
  parentOrderId?: string

  @ApiPropertyOptional({
    description: '订单备注',
    example: '开通VIP服务'
  })
  remark?: string

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 订单列表响应DTO
 */
export class OrderListResponseDto {
  @ApiProperty({
    type: [OrderResponseDto]
  })
  list: OrderResponseDto[]

  @ApiProperty({
    example: 100
  })
  total: number

  @ApiProperty({
    example: 1
  })
  page: number

  @ApiProperty({
    example: 10
  })
  limit: number
}

/**
 * 创建订单响应DTO
 */
export class CreateOrderResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: OrderResponseDto
  })
  data: OrderResponseDto
}

/**
 * 获取订单列表响应DTO
 */
export class GetOrderListResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: OrderListResponseDto
  })
  data: OrderListResponseDto
}

/**
 * 获取订单详情响应DTO
 */
export class GetOrderDetailResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: OrderResponseDto
  })
  data: OrderResponseDto
}

/**
 * 团队VIP状态DTO
 */
export class TeamVipStatusDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '是否为VIP',
    example: true
  })
  isVip: boolean

  @ApiProperty({
    description: 'VIP过期时间',
    example: *************
  })
  expiredAt: number

  @ApiProperty({
    description: '当前账号数量限制',
    example: 10
  })
  accountCountLimit: number

  @ApiProperty({
    description: '当前流量限制（GB）',
    example: 100
  })
  networkTraffic: number

  @ApiProperty({
    description: '当前有效的主订单ID',
    example: '507f1f77bcf86cd799439011'
  })
  currentMainOrderId?: string

  @ApiProperty({
    description: '增购订单列表',
    type: [OrderResponseDto]
  })
  addonOrders: OrderResponseDto[]
}

/**
 * 获取团队VIP状态响应DTO
 */
export class GetTeamVipStatusResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: TeamVipStatusDto
  })
  data: TeamVipStatusDto
}
