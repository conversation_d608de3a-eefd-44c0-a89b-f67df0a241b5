import { Modu<PERSON> } from '@nestjs/common'
import {
  AdminMongoose,
  BrowserFavoritesMongoose,
  BrowserMongoose,
  ContentMongoose,
  CustomerAssignRecordMongoose,
  FavoritesGroupItemMongoose,
  FavoritesGroupMongoose,
  GroupMongoose,
  InvitationMongoose,
  MemberMongoose,
  NoticeMongoose, PlatformAccountCookieMongoose,
  PlatformAccountMongoose,
  PlatformAccountOverviewMongoose,
  PlatformAccountSummaryMongoose,
  ProposalMongoose,
  TaskMongoose,
  TeamComponentMongoose,
  TeamMongoose,
  UserMongoose
} from '@yxr/mongo'
import { InvitationController } from './invitation.controller'
import { ProposalController } from './proposal.controller'
import { MemberController } from './member.controller'
import { TeamController } from './team.controller'
import { TeamService } from './team.service'
import { ProposalService } from './proposal.service'
import { InvitationService } from './invitation.service'
import { MemberService } from './member.service'
import { AuthorizationService } from '../../common/security/authorization.service'
import { MemberRolesChangedListener } from './member-roles.changed.listeners'
import { WebhookModule } from '../webhook/webhook.module'
import { CommonModule } from '@yxr/common'
import { PlatformMembersChangedListener } from './platform-members.changed.listener'
import { DataSyncCornService } from '../sync/data-sync.service'
import { CustomerService } from './customer.service'
import { ScheduleModule } from '@nestjs/schedule'
import { HuoshanModule } from '@yxr/huoshan'

@Module({
  imports: [
    TeamMongoose,
    MemberMongoose,
    ProposalMongoose,
    InvitationMongoose,
    PlatformAccountMongoose,
    UserMongoose,
    NoticeMongoose,
    BrowserMongoose,
    GroupMongoose,
    ContentMongoose,
    TaskMongoose,
    BrowserFavoritesMongoose,
    FavoritesGroupMongoose,
    FavoritesGroupItemMongoose,
    PlatformAccountOverviewMongoose,
    PlatformAccountSummaryMongoose,
    AdminMongoose,
    CustomerAssignRecordMongoose,
    TeamComponentMongoose,
    HuoshanModule,
    WebhookModule,
    CommonModule,
    ScheduleModule.forRoot(),
    PlatformAccountCookieMongoose
  ],
  controllers: [InvitationController, ProposalController, MemberController, TeamController],
  providers: [
    TeamService,
    ProposalService,
    InvitationService,
    MemberService,
    AuthorizationService,
    MemberRolesChangedListener,
    PlatformMembersChangedListener,
    DataSyncCornService,
    CustomerService
  ],
  exports: [MemberService, TeamService]
})
export class TeamModule {}
