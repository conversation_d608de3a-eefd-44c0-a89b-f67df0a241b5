import { Controller, Get, Query, UseGuards } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  NoticesPageResponseDTO,
  VersionQueryDTO,
  VersionsPageResponseDTO,
  getNoticesNewestResponseDTO
} from './notice.dto'
import { NoticeService } from './notice.service'

@Controller('notices')
@ApiTags('消息通知管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
export class NoticeController {
  constructor(private readonly noticeService: NoticeService) {}

  /**
   * 获取最新通知状态
   * @param time
   */
  @Get('latest')
  @ApiOkResponse({ type: getNoticesNewestResponseDTO })
  @ApiOperation({ summary: '获取用户最新通知状态' })
  @ApiQuery({
    name: 'time',
    required: true,
    type: Number,
    description: '查询此时间戳之后是否有新消息 <默认 0> '
  })
  @ApiHeader({ name: 'authorization', required: true })
  getNewest(@Query('time', { transform: (value) => value || 0 }) time: number) {
    return this.noticeService.getNewest(new Date(time))
  }

  @Get()
  @ApiOkResponse({ type: NoticesPageResponseDTO })
  @ApiOperation({ summary: '获取用户消息列表' })
  @ApiHeader({ name: 'authorization', required: true })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  getList(
    @Query('size', { transform: (value) => value || 10 }) size: number,
    @Query('page', { transform: (value) => value || 1 }) page: number
  ) {
    return this.noticeService.getPagedNotices({ size, page })
  }

  @Get('version')
  @ApiOkResponse({ type: VersionsPageResponseDTO })
  @ApiOperation({ summary: '获取版本公告列表' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  getVersionList(@Query() query: VersionQueryDTO) {
    return this.noticeService.getPagedVersions(query)
  }
}
