import { HttpStatus, ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { AdminModule } from './admin.module'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
import helmet from '@fastify/helmet'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'

import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import dayjs from 'dayjs'

dayjs.extend(utc)
dayjs.extend(timezone)

async function bootstrap() {
  const fastify = new FastifyAdapter({
    bodyLimit: 1024 * 1024 * 500 // 请求体大小限制为 500MB
  })

  fastify.register(helmet)

  const app = await NestFactory.create<NestFastifyApplication>(AdminModule, fastify)
  const configService = app.get(ConfigService) as ConfigService<RootConfigMap, true>
  const appConfig = configService.get<RootConfigMap['app']>('app')

  app.enableCors({
    origin: appConfig.cors.allowOrigin,
    methods: appConfig.cors.allowMethod,
    allowedHeaders: appConfig.cors.allowHeader,
    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: HttpStatus.NO_CONTENT
  })

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true
    })
  )

  if (process.env.NODE_ENV !== 'test') {
    const ClientDocument = SwaggerModule.createDocument(
      app,
      new DocumentBuilder().setTitle('蚁小二 Lite API 文档').setVersion('1.0').build()
    )

    SwaggerModule.setup('admin-api', app, ClientDocument)
  }

  await app.listen(3001, appConfig.http.host)
}

bootstrap()
