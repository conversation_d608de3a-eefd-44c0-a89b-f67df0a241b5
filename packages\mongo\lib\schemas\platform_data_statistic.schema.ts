import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})

/**
 * 平台相关数据统计
 */
export class PlatformDataStatisticEntity {
  @Prop({
    type: String,
    index: true,
    required: true
  })
  platformName: string

  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  //每日新增账号数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  incrementAccountTotal: number

  //成功发布数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  successPublishTotal: number

  //失败发布数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  failPublishTotal: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const PlatformDataStatisticSchema: ModelDefinition = {
  name: PlatformDataStatisticEntity.name,
  schema: SchemaFactory.createForClass(PlatformDataStatisticEntity)
}

export const PlatformDataStatisticMongoose = MongooseModule.forFeature([
  PlatformDataStatisticSchema
])
