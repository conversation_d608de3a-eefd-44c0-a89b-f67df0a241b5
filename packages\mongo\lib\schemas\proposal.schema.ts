import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { ProposalStatusEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class ProposalEntity {

  /**
   * 申请人Id
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: [String],
    required: true
  })
  roles: string[]

  // 状态: 未批准, 已批准, 已拒绝
  @Prop({
    type: String,
    enum: {
      values: [ProposalStatusEnum.Pending, ProposalStatusEnum.Approved, ProposalStatusEnum.Rejected],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: ProposalStatusEnum.Pending
  })
  status: ProposalStatusEnum

  /**
   * 处理时间
   */
  @Prop({
    type: Date
  })
  handledAt?: Date

  /**
   * 处理人
   */
  @Prop({
    type: Types.ObjectId,
    index: true
  })
  handledBy?: Types.ObjectId

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const ProposalSchema: ModelDefinition = {
  name: ProposalEntity.name,
  schema: SchemaFactory.createForClass(ProposalEntity)
}

export const ProposalMongoose = MongooseModule.forFeature([ProposalSchema])
