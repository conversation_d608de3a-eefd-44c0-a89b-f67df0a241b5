import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  TaskCloudPushResponseDTO,
  TaskDetailResponseDTO,
  TaskSetCreateRequest,
  TaskSetCreateResponseDTO,
  TaskSetDetailResponseDTO,
  TaskSetListRequest,
  TaskSetListResponseDTO,
  TaskSetUpdateRequest
} from './taskSet.dto'
import { TaskSetService } from './taskSet.service'
import { isString } from 'class-validator'

@Controller('taskSets')
@ApiTags('任务管理/任务集管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class TaskSetController {
  constructor(private readonly taskSetService: TaskSetService) {}

  @Get(':taskSetId/tasks')
  @ApiOperation({ summary: '任务集子任务列表' })
  @ApiOkResponse({ type: TaskDetailResponseDTO })
  async getSetTaskList(@Param('taskSetId') taskSetId: string) {
    return await this.taskSetService.getSetTaskList(taskSetId)
  }

  @Get(':taskSetId')
  @ApiOperation({ summary: '任务集详情' })
  @ApiOkResponse({ type: TaskSetDetailResponseDTO })
  async getPublishTaskSetDetail(@Param('taskSetId') taskSetId: string) {
    return await this.taskSetService.getTaskSetDetailAsync(taskSetId)
  }

  @Get(':taskSetId/publishForm')
  @ApiOperation({ summary: '获取可发布的任务集对象, 云发布版本' })
  @ApiOkResponse({ type: TaskCloudPushResponseDTO })
  getPublishTaskSet(@Param('taskSetId') taskSetId: string) {
    return this.taskSetService.getPublishForm(taskSetId)
  }

  @Get()
  @ApiOperation({ summary: '任务集列表' })
  @ApiOkResponse({ type: TaskSetListResponseDTO })
  @ApiQuery({ type: TaskSetListRequest })
  async getPublishTaskSets(
    @Query() query: TaskSetListRequest,
    @Query('userIds[]', { transform: (value) => (isString(value) ? [value] : value) })
    userIds: string[]
  ) {
    return await this.taskSetService.getTaskSetsAsync(query, userIds)
  }

  @Post()
  @ApiOperation({ summary: '创建任务集(app发布)' })
  @ApiOkResponse({ type: TaskSetCreateResponseDTO })
  async postPublishTasks(@Body() body: TaskSetCreateRequest) {
    return await this.taskSetService.postTaskSetAsync(body)
  }

  @Post('v1')
  @ApiOperation({ summary: '创建任务集v1(不包含app发布)' })
  @ApiOkResponse({ type: TaskSetCreateResponseDTO })
  async postPublishTasks1(@Body() body: TaskSetCreateRequest) {
    return await this.taskSetService.postTaskSetAsync1(body)
  }

  @Patch(':taskSetId')
  @ApiOperation({ summary: '更新任务集信息' })
  @ApiOkResponse({ type: TaskSetCreateResponseDTO })
  async patchPublishTaskSets(
    @Param('taskSetId') taskSetId: string,
    @Body() body: TaskSetUpdateRequest
  ) {
    return await this.taskSetService.patchPublishTaskSets(taskSetId, body)
  }

  @Delete('batch')
  @ApiOperation({ summary: '批量删除任务集' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deletePublishTasks(
    @Query('taskSetIds[]', { transform: (value) => (isString(value) ? [value] : value) })
    taskSetIds: string[]
  ) {
    return await this.taskSetService.deleteTaskSetAsync(taskSetIds)
  }
}
