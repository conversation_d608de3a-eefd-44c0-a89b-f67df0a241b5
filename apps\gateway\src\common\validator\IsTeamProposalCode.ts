import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface
} from 'class-validator'

@ValidatorConstraint({ async: false })
export class IsTeamProposalCodeConstraint implements ValidatorConstraintInterface {
  validate(text: string) {
    return /^[a-zA-Z0-9]{6}$/g.test(text)
  }

  defaultMessage() {
    return '无效的邀请码'
  }
}

export function IsTeamProposalCode(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsTeamProposalCodeConstraint
    })
  }
}
