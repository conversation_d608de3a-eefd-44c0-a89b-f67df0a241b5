/**
 * 用户验证工具类
 * 提供用户标识符验证、格式检查等功能
 */

/**
 * 手机号正则表达式
 * 支持中国大陆手机号格式：1开头的11位数字
 */
export const PHONE_REGEX = /^1[3-9]\d{9}$/

/**
 * 账号名称正则表达式
 * 支持：字母、数字、下划线、中文字符
 * 不允许：纯数字（避免与手机号混淆）
 * 长度：8-20个字符
 */
export const ACCOUNT_REGEX = /^(?![0-9]+$)[a-zA-Z0-9_\u4e00-\u9fa5]{8,20}$/

/**
 * 用户标识符类型枚举
 */
export enum UserIdentifierType {
  PHONE = 'phone',
  ACCOUNT = 'account'
}

/**
 * 用户验证错误类型
 */
export enum UserValidationError {
  PHONE_INVALID_FORMAT = 'PHONE_INVALID_FORMAT',
  ACCOUNT_INVALID_FORMAT = 'ACCOUNT_INVALID_FORMAT',
  ACCOUNT_CANNOT_BE_PHONE = 'ACCOUNT_CANNOT_BE_PHONE',
  BOTH_IDENTIFIERS_EMPTY = 'BOTH_IDENTIFIERS_EMPTY',
  PHONE_ALREADY_EXISTS = 'PHONE_ALREADY_EXISTS',
  ACCOUNT_ALREADY_EXISTS = 'ACCOUNT_ALREADY_EXISTS',
  IDENTIFIER_NOT_FOUND = 'IDENTIFIER_NOT_FOUND'
}

/**
 * 用户验证结果接口
 */
export interface UserValidationResult {
  isValid: boolean
  error?: UserValidationError
  message?: string
}

/**
 * 用户标识符信息接口
 */
export interface UserIdentifierInfo {
  type: UserIdentifierType
  value: string
  isValid: boolean
}

/**
 * 用户验证工具类
 */
export class UserValidationUtils {
  /**
   * 验证手机号格式
   */
  static validatePhoneFormat(phone: string): boolean {
    if (!phone) return true // 可选字段，空值有效
    return PHONE_REGEX.test(phone)
  }

  /**
   * 验证账号格式
   */
  static validateAccountFormat(account: string): boolean {
    if (!account) return true // 可选字段，空值有效
    
    // 检查基本格式
    if (!ACCOUNT_REGEX.test(account)) {
      return false
    }
    
    // 确保不是手机号格式
    if (PHONE_REGEX.test(account)) {
      return false
    }
    
    return true
  }

  /**
   * 验证用户标识符（phone和account不能同时为空）
   */
  static validateUserIdentifiers(phone?: string, account?: string): UserValidationResult {
    // 检查是否同时为空
    if (!phone && !account) {
      return {
        isValid: false,
        error: UserValidationError.BOTH_IDENTIFIERS_EMPTY,
        message: '手机号和账号不能同时为空，至少需要提供一个'
      }
    }

    // 验证手机号格式
    if (phone && !this.validatePhoneFormat(phone)) {
      return {
        isValid: false,
        error: UserValidationError.PHONE_INVALID_FORMAT,
        message: '手机号格式不正确，请输入11位有效手机号'
      }
    }

    // 验证账号格式
    if (account && !this.validateAccountFormat(account)) {
      if (PHONE_REGEX.test(account)) {
        return {
          isValid: false,
          error: UserValidationError.ACCOUNT_CANNOT_BE_PHONE,
          message: '账号不能是手机号格式，请使用其他字符组合'
        }
      }
      
      return {
        isValid: false,
        error: UserValidationError.ACCOUNT_INVALID_FORMAT,
        message: '账号格式不正确，支持8-20位字母、数字、下划线、中文，不能为纯数字'
      }
    }

    return {
      isValid: true
    }
  }

  /**
   * 识别用户输入的标识符类型（手机号或账号）
   */
  static identifyUserInput(input: string): UserIdentifierInfo {
    if (!input) {
      return {
        type: UserIdentifierType.ACCOUNT,
        value: input,
        isValid: false
      }
    }

    // 检查是否为手机号格式
    if (PHONE_REGEX.test(input)) {
      return {
        type: UserIdentifierType.PHONE,
        value: input,
        isValid: true
      }
    }

    // 检查是否为有效账号格式
    const isValidAccount = ACCOUNT_REGEX.test(input) && !PHONE_REGEX.test(input)
    
    return {
      type: UserIdentifierType.ACCOUNT,
      value: input,
      isValid: isValidAccount
    }
  }

  /**
   * 生成用户友好的错误消息
   */
  static getErrorMessage(error: UserValidationError): string {
    const errorMessages = {
      [UserValidationError.PHONE_INVALID_FORMAT]: '手机号格式不正确，请输入11位有效手机号',
      [UserValidationError.ACCOUNT_INVALID_FORMAT]: '账号格式不正确，支持8-20位字母、数字、下划线、中文，不能为纯数字',
      [UserValidationError.ACCOUNT_CANNOT_BE_PHONE]: '账号不能是手机号格式，请使用其他字符组合',
      [UserValidationError.BOTH_IDENTIFIERS_EMPTY]: '手机号和账号不能同时为空，至少需要提供一个',
      [UserValidationError.PHONE_ALREADY_EXISTS]: '该手机号已被注册，请使用其他手机号或直接登录',
      [UserValidationError.ACCOUNT_ALREADY_EXISTS]: '该账号已被注册，请使用其他账号或直接登录',
      [UserValidationError.IDENTIFIER_NOT_FOUND]: '用户不存在，请检查手机号或账号是否正确'
    }

    return errorMessages[error] || '未知错误'
  }

  /**
   * 检查字符串是否为手机号格式
   */
  static isPhoneNumber(input: string): boolean {
    return PHONE_REGEX.test(input)
  }

  /**
   * 检查字符串是否为有效账号格式
   */
  static isValidAccount(input: string): boolean {
    return ACCOUNT_REGEX.test(input) && !PHONE_REGEX.test(input)
  }

  /**
   * 标准化手机号（移除空格、特殊字符等）
   */
  static normalizePhone(phone: string): string {
    if (!phone) return phone
    return phone.replace(/\s+/g, '').replace(/[^\d]/g, '')
  }

  /**
   * 标准化账号（移除首尾空格）
   */
  static normalizeAccount(account: string): string {
    if (!account) return account
    return account.trim()
  }
}

/**
 * 用户标识符验证装饰器工厂
 */
export function ValidateUserIdentifiers() {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = function (...args: any[]) {
      const [data] = args
      
      if (data && (data.phone !== undefined || data.account !== undefined)) {
        const validation = UserValidationUtils.validateUserIdentifiers(data.phone, data.account)
        
        if (!validation.isValid) {
          throw new Error(validation.message)
        }
      }

      return method.apply(this, args)
    }

    return descriptor
  }
}
