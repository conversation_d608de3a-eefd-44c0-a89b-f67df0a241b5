import { Injectable, Scope } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import { AccountAuthProvider } from './providers/account-auth.provider'
import { ContentPublishProvider } from './providers/content-publish.provider'

@Injectable({ scope: Scope.DEFAULT })
export class OverseasProviderFactory {
  constructor(private readonly moduleRef: ModuleRef) {}

  accountAuthProvider(platformName: string): Promise<AccountAuthProvider> {
    return this.moduleRef.resolve<AccountAuthProvider>(AccountAuthProvider.register_token(platformName))
  }

  contentPublishProvider(platformName: string): Promise<ContentPublishProvider> {
    return this.moduleRef.resolve<ContentPublishProvider>(ContentPublishProvider.register_token(platformName))
  }
}
