import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { AdTypeEmun, PopupTypeEmun } from '@yxr/common'

@Schema({
  timestamps: true,
  optimisticConcurrency: true,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class AdEntity {
  /**
   * 操作人名称
   */
  @Prop({
    type: String,
    required: true
  })
  adminName: string

  @Prop({
    type: String,
    enum: AdTypeEmun,
    index: true,
    required: true,
    default: AdTypeEmun.Banner
  })
  adType: AdTypeEmun

  /**
   * 名称
   */
  @Prop({
    type: String,
    required: true
  })
  name: string

  /**
   * 排序
   */
  @Prop({
    type: Number,
    required: true,
    default: 1
  })
  sort: number

  /**
   * 广告图片地址
   */
  @Prop({
    type: String,
    required: true
  })
  adUrl?: string

  /**
   * 是否有效
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  enabled: boolean

  /**
   * 是否时间永久有效
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isTimed: boolean

  /**
   * 有效开始时间
   */
  @Prop({
    type: Date,
    required: false
  })
  expiredStartAt?: Date

  /**
   * 有效结束时间
   */
  @Prop({
    type: Date,
    required: false
  })
  expiredEndAt?: Date

  /**
   * 是否跳转
   */
  @Prop({
    type: Boolean,
    required: true
  })
  isJumpTo: boolean

  /**
   * 跳转地址
   */
  @Prop({
    type: String,
    required: false
  })
  jumpToUrl?: string

  @Prop({
    type: String,
    enum: PopupTypeEmun,
    required: false
  })
  popupType?: PopupTypeEmun
}

export const AdSchema: ModelDefinition = {
  name: AdEntity.name,
  schema: SchemaFactory.createForClass(AdEntity)
}

export const AdMongoose = MongooseModule.forFeature([AdSchema])
