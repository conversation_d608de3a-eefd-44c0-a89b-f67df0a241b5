import { BadRequestException, ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import {
  CreateDesktopAppRequest,
  CreateOnlineScriptRequest,
  DesktopAppDetailsDto,
  DesktopAppListResponseDTO,
  GetDesktopAppLatestRequest,
  GetDesktopAppListRequest,
  GetOnlineScriptListRequest,
  OnlineScriptDetailsDto,
  OnlineScriptListResponseDTO,
  PutDesktopAppRequest
} from './online-script.dto'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types } from 'mongoose'
import { AdminEntity, OnlineScriptEntity } from '@yxr/mongo'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { AdminOssService } from '../ali-oss/admin-oss.service'
import { VersionService } from '@yxr/common'

@Injectable()
export class OnlineScriptService {
  logger = new Logger('OnlineScriptService')

  constructor(
    @InjectModel(OnlineScriptEntity.name) private model: Model<OnlineScriptEntity>,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    private readonly webhookService: WebhookService,
    private readonly adminOssService: AdminOssService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  normalizeType(str: string) {
    const s = str.toLowerCase()
    if (s !== 'rpa' && s !== 'spider') {
      throw new BadRequestException('只支持 rpa、spider类型')
    }
    return s
  }

  desktopType(str: string) {
    const s = str.toLowerCase()
    if (s !== 'windows' && s !== 'macos' && s !== 'ios' && s !== 'android') {
      throw new BadRequestException('只支持windows、macos、ios、android类型')
    }
    return s
  }

  async getOnlineScriptList(
    query: GetOnlineScriptListRequest
  ): Promise<OnlineScriptListResponseDTO> {
    const filter: FilterQuery<OnlineScriptEntity> = {}
    if (query.type) {
      filter.type = this.normalizeType(query.type)
    }

    const items = await this.model
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.model.find(filter).countDocuments()

    return {
      totalSize,
      page: query.page,
      size: query.size,
      totalPage: Math.ceil(totalSize / query.size),
      data: items.map((item) => ({
        type: item.type,
        version: item.version,
        createdAt: item.createdAt?.getTime()
      }))
    }
  }

  async createOnlineScript(request: CreateOnlineScriptRequest): Promise<OnlineScriptDetailsDto> {
    const type = this.normalizeType(request.type)

    // 先查询数据库中最新版本
    const [latest] = await this.model
      .find<OnlineScriptEntity>({ type: type })
      .sort({ createdAt: -1 })
      .limit(1)

    if (latest) {
      // 版本号只能递增
      if (VersionService.compareVersions(latest.version, request.version) <= 0) {
        throw new ForbiddenException(
          `版本号 (${request.version}) 必须高于当前最新的版本号 (${latest.version})`
        )
      }
    }

    // 上传到 oss
    const envPrefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}/`
    const name = `scripts/${envPrefix}${request.type}-${request.version}.js`
    const buffer = Buffer.from(request.scripts, 'base64')
    await this.adminOssService.uploadFile(buffer, name)

    const newest = await this.model.create<OnlineScriptEntity>({
      version: request.version,
      numberVersion: VersionService.versionToNumber(
        ...(VersionService.parseVersion(request.version) as [number, number, number])
      ),
      type: type,
      storage: name
    })

    await this.webhookService.sendToAll({
      event: WebhookEvents.OnlineScriptsUpdated,
      body: {
        [type]: {
          version: newest.version,
          url: `${process.env.OSS_DOWNLOAD_URL}/${name}`
        }
      }
    })

    return {
      type: newest.type,
      version: newest.version,
      createdAt: newest.createdAt?.getTime()
    }
  }

  /**
   * 获取版本管理列表
   * @param query
   * @returns
   */
  async getDesktopAppList(query: GetDesktopAppListRequest): Promise<DesktopAppListResponseDTO> {
    const filter: FilterQuery<OnlineScriptEntity> = {}
    if (query.type) {
      const type = this.desktopType(query.type)
      filter.type = type
    } else {
      filter.type = { $in: ['android', 'ios', 'macos', 'windows'] }
    }

    if (query.publishStartTime && query.publishEndTime) {
      filter.createdAt = {
        $gte: new Date(query.publishStartTime),
        $lte: new Date(query.publishEndTime)
      }
    }

    if (query.version) {
      filter.version = { $regex: query.version, $options: 'i' }
    }

    const items = await this.model
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.model.find(filter).countDocuments()

    const userObjectIds = items.map((item) => new Types.ObjectId(item.userId))
    const adminUsers = await this.adminModel.find(
      {
        _id: { $in: userObjectIds }
      },
      { _id: 1, name: 1 }
    )
    return {
      totalSize,
      page: query.page,
      size: query.size,
      totalPage: Math.ceil(totalSize / query.size),
      data: items.map((item) => ({
        id: item._id,
        type: item.type,
        version: item.version,
        publishName: adminUsers.find((user) => user._id.equals(item.userId))?.name,
        isForce: item.isForce,
        notice: item.notice,
        requestUrl: item.requestUrl,
        createdAt: item.createdAt?.getTime()
      }))
    }
  }

  /**
   * 创建桌面版本
   * @param body
   * @returns
   */
  async createDesktopApp(request: CreateDesktopAppRequest): Promise<DesktopAppDetailsDto> {
    const user = this.request.user
    const type = this.desktopType(request.type)
    // 先查询数据库中最新版本
    const [latest] = await this.model
      .find<OnlineScriptEntity>({ type: type })
      .sort({ createdAt: -1 })
      .limit(1)
    if (latest) {
      // 版本号只能递增
      if (VersionService.compareVersions(latest.version, request.version) <= 0) {
        throw new ForbiddenException(
          `版本号 (${request.version}) 必须高于当前最新的版本号 (${latest.version})`
        )
      }
    }

    let checkUrl: string //检测oss版本路径
    let fileUrl: string = '' //存储路径
    switch (process.env.NODE_ENV) {
      case 'dev':
        fileUrl = 'staging/' + request.version
        break
      case 'test':
        fileUrl = 'staging/' + request.version
        break
      case 'prod':
        fileUrl = 'production/' + request.version
        break
      default:
        break
    }

    if (type == 'ios') {
      //苹果版本添加不需要检测包
      checkUrl = ''
    } else if (type == 'android') {
      checkUrl = fileUrl + '/app/'
      fileUrl = request.scripts
    } else {
      checkUrl = fileUrl + '/'
    }

    if (checkUrl) {
      try {
        const result = await this.adminOssService.getListObjects(checkUrl)
        const existingFiles = result.data.Contents?.map((obj) => obj.Key) || []
        if (existingFiles.length <= 0) {
          throw new BadRequestException('该版本未构建')
        }
        if (type == 'windows') {
          // 判断目标文件是否存在
          const fileNames = ['win32-x64.yml', 'win32-ia32.yml']
          if (!fileNames.every((fileName) => existingFiles.includes(checkUrl + fileName))) {
            throw new BadRequestException('该版本未全部构建')
          }
        } else if (type == 'macos') {
          const fileNames = ['darwin-x64-mac.yml', 'darwin-arm64-mac.yml']
          if (!fileNames.every((fileName) => existingFiles.includes(checkUrl + fileName))) {
            throw new BadRequestException('该版本未全部构建')
          }
        } else if (type == 'android') {
          const fileNames = ['app-release.apk']
          if (!fileNames.every((fileName) => existingFiles.includes(checkUrl + fileName))) {
            throw new BadRequestException('该版本还未构建')
          }
        }
      } catch (error) {
        throw new BadRequestException(error)
      }
    }
    const newest = await this.model.create<OnlineScriptEntity>({
      version: request.version,
      numberVersion: VersionService.versionToNumber(
        ...(VersionService.parseVersion(request.version) as [number, number, number])
      ),
      type: type,
      userId: user.id,
      notice: request.notice,
      isForce: request.isForce,
      storage: fileUrl,
      requestUrl: request.requestUrl
    })

    // 发送 webhook 通知
    if (type == 'windows' || type == 'macos') {
      await this.webhookService.sendToDevice(type, {
        event: WebhookEvents.DesktopAppsUpdated,
        body: {
          [type]: {
            version: newest.version,
            url: `${process.env.OSS_DOWNLOAD_URL}/${fileUrl}`
          }
        }
      })
    }

    return {
      type: newest.type,
      notice: newest.notice,
      publishName: user.name,
      isForce: newest.isForce,
      version: newest.version,
      requestUrl: newest.requestUrl,
      createdAt: newest.createdAt?.getTime()
    }
  }

  async getDesktopAppLatest(query: GetDesktopAppLatestRequest) {
    const type = this.desktopType(query.type)

    // 先查询数据库中最新版本
    const [latest] = await this.model
      .find<OnlineScriptEntity>({ type: type })
      .sort({ createdAt: -1 })
      .limit(1)

    return {
      type: latest?.type,
      version: latest?.version
    }
  }

  /**
   * 修改桌面版本信息
   * @param id
   * @param body
   */
  async putDesktopApp(id: string, body: PutDesktopAppRequest) {
    const desktopApp = await this.model.findOne({ _id: new Types.ObjectId(id) })
    if (!desktopApp) {
      throw new BadRequestException('桌面应用版本不存在')
    }
    let updateData: { notice: string; isForce: boolean; scripts?: string } = {
      notice: body.notice,
      isForce: body.isForce
    }
    if (desktopApp.type == 'android' && body.scripts) {
      updateData.scripts = body.scripts
    }

    await this.model.updateOne(
      { _id: new Types.ObjectId(id) },
      {
        $set: updateData
      }
    )
  }

  /**
   * 删除桌面版本
   * @param id
   */
  async deleteDesktopApp(id: string) {
    await this.model.deleteOne({ _id: new Types.ObjectId(id) })
  }
}
