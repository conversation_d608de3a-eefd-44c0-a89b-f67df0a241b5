import { Module } from '@nestjs/common'
import { AuthorizationService } from '../../common/security/authorization.service'
import {
  MemberMongoose,
  PlatformAccountSummaryMongoose,
  PlatformAccountTrendMongoose,
  RobotMongoose,
  TeamMongoose
} from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { RobotsService } from './robots.service'
import { RobotsController } from './robots.controller'
import { ScheduleModule } from '@nestjs/schedule'
import { RobotsCornService } from './robots.cron.service'

@Module({
  imports: [
    RobotMongoose,
    TeamMongoose,
    MemberMongoose,
    PlatformAccountTrendMongoose,
    PlatformAccountSummaryMongoose,
    CommonModule,
    ScheduleModule.forRoot()
  ],
  controllers: [RobotsController],
  providers: [RobotsService, RobotsCornService, AuthorizationService]
})
export class RobotsModule {}
