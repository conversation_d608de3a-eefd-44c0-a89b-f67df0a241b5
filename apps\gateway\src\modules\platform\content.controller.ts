import { Body, Controller, Get, Inject, Post, Query, Res } from '@nestjs/common'
import {
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseResponseDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { BaseNotFoundRequestDTO } from '../../common/dto/BaseRequestDTO'
import { ContentService } from './content.service'
import {
  ContentOverviewListRequest,
  ContentOverviewListResponseDTO,
  ContentOverviewSummaryRequest,
  ReportContentRequest
} from './content.dto'
import { FastifyReply, FastifyRequest } from 'fastify'
import dayjs from 'dayjs'
import { ContentOverviewSummaryResponseDTO } from './content-trend.dto'
import { REQUEST } from '@nestjs/core'

@Controller('contents')
@ApiTags('媒体账号管理/作品管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiNotFoundResponse({ description: '账号不存在', type: BaseNotFoundRequestDTO })
@ApiHeader({ name: 'authorization', required: true })
export class ContentController {
  constructor(
    private readonly contentService: ContentService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 账号作品数据上报
   * @returns
   */
  @Post('report')
  @ApiOperation({ summary: '作品数据上报' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postContents(@Body() body: ReportContentRequest) {
    const { teamId: currentTeamId } = this.request.session

    return await this.contentService.postContents(currentTeamId, body)
  }

  @Get('overviews/summary')
  @ApiOperation({ summary: '作品数据概览', deprecated: true })
  @ApiOkResponse({ type: ContentOverviewSummaryResponseDTO })
  async getContentOverviewSummary(@Query() query: ContentOverviewSummaryRequest) {
    return await this.contentService.getContentOverviewSummary(query)
  }

  /**
   * 账号作品数据列表
   * @returns
   */
  @Get('overviews')
  @ApiOperation({ summary: '作品数据列表' })
  @ApiOkResponse({ type: ContentOverviewListResponseDTO })
  async getContentOverviews(@Query() query: ContentOverviewListRequest) {
    return await this.contentService.getContentOverviews(query)
  }

  /**
   * 导出账号作品数据
   * @returns
   */
  @Get('export')
  @ApiOperation({ summary: '导出账号作品数据' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async exportContentOverviews(
    @Query() query: ContentOverviewListRequest,
    @Res() res: FastifyReply
  ) {
    const exportData = await this.contentService.exportContentOverviews(query)
    if (exportData.length <= 0) {
      res.status(200).send({
        statusCode: 0
      })
      return
    }
    // 定义一个映射，将字段名映射为中文
    const titleMap = {
      platformName: '平台名称',
      cType: '类型',
      title: '标题',
      desc: '描述',
      publishUserName: '发布人',
      publishTime: '发布时间',
      accountName: '账号名称',
      updatedAt: '数据更新时间',
      pageUrl: '作品地址',
      reCommand: '推荐量',
      play: '播放量',
      read: '阅读量',
      great: '点赞量',
      comment: '评论量',
      share: '分享量',
      collect: '收藏量',
      addFans: '涨粉量',
      finishPlay: '完播率',
      love: '爱心',
      danmu: '弹幕',
      coin: '硬币',
      like: '喜欢',
      vote: '投票数',
      playTime: '播放时长'
    }

    // 动态生成 CSV 头部，并将字段名转换为中文
    const headers = Object.keys(exportData[0]).map((key) => ({
      id: key,
      title: titleMap[key] || key.charAt(0).toUpperCase() + key.slice(1) // 默认为字段名首字母大写
    }))

    // 生成 CSV 内容
    const csvContent = await this.contentService.generateCsv(exportData, headers)
    const time = dayjs().tz('Asia/Shanghai').format('YYYY-MM-DD')
    const exportFileName = exportData[0].platformName + '作品数据' + time + '.csv'

    // 设置响应头
    res.header('Content-Type', 'text/csv')
    res.header('Content-Disposition', 'attachment;filename=' + encodeURIComponent(exportFileName))

    // 返回 CSV 内容
    res.send(csvContent)
  }
}
