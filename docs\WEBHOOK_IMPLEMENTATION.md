# 开放平台Webhook功能实现

## 概述

在开放平台应用管理系统中实现了完整的Webhook功能，包括Webhook URL设置、验证机制、状态管理和错误处理。该功能允许开放平台用户为其应用设置Webhook URL，系统会自动验证URL的可访问性，并在后续的业务事件中发送通知。

## 功能特性

### 1. **数据库实体扩展**
- ✅ 在 `OpenPlatformApplicationEntity` 中添加了 `webhookUrl` 字段（可选字符串类型）
- ✅ 添加了 `webhookStatus` 字段（枚举：未设置/验证中/验证成功/验证失败）
- ✅ 添加了 `webhookVerifiedAt` 字段（最后验证成功时间）

### 2. **Webhook状态管理**
- ✅ **NOT_SET**: 未设置Webhook
- ✅ **VERIFYING**: 正在验证中
- ✅ **VERIFIED**: 验证成功
- ✅ **FAILED**: 验证失败

### 3. **API接口设计**
- ✅ **PUT /applications/:id/webhook** - 设置Webhook URL
- ✅ **GET /applications/:id/webhook** - 获取Webhook状态
- ✅ **DELETE /applications/:id/webhook** - 删除Webhook配置

## 技术实现

### 1. 数据库实体扩展

**文件**: `packages/mongo/lib/schemas/open_platform_application.schema.ts`

```typescript
@Prop({
  type: String,
  required: false,
  maxlength: 500
})
webhookUrl?: string

@Prop({
  type: String,
  enum: WebhookStatus,
  required: true,
  default: WebhookStatus.NOT_SET
})
webhookStatus: WebhookStatus

@Prop({
  type: Date,
  required: false
})
webhookVerifiedAt?: Date
```

### 2. Webhook状态枚举

**文件**: `packages/common/open-platform.constants.ts`

```typescript
/**
 * Webhook状态枚举
 */
export enum WebhookStatus {
  NOT_SET = 'NOT_SET',           // 未设置
  VERIFYING = 'VERIFYING',       // 验证中
  VERIFIED = 'VERIFIED',         // 验证成功
  FAILED = 'FAILED'              // 验证失败
}
```

### 3. DTO定义

**文件**: `apps/admin/src/modules/open-platform/dto/application.dto.ts`

#### 3.1 设置Webhook请求DTO
```typescript
export class SetWebhookRequestDto {
  @ApiProperty({
    description: 'Webhook URL地址',
    example: 'https://example.com/webhook'
  })
  @IsString()
  @IsNotEmpty()
  @IsUrl({}, { message: 'webhookUrl必须是有效的URL地址' })
  @Length(1, 500, { message: 'webhookUrl长度必须在1-500字符之间' })
  webhookUrl: string
}
```

#### 3.2 Webhook验证请求格式
```typescript
export class WebhookVerificationRequestDto {
  @ApiProperty({
    description: '验证类型',
    example: 'webhook_verification'
  })
  type: string

  @ApiProperty({
    description: '时间戳',
    example: 1672531200000
  })
  timestamp: number

  @ApiProperty({
    description: '验证挑战值',
    example: 'abc123def456'
  })
  challenge: string
}
```

#### 3.3 Webhook验证响应格式
```typescript
export class WebhookVerificationResponseDto {
  @ApiProperty({
    description: '验证是否成功',
    example: true
  })
  success: boolean

  @ApiProperty({
    description: '返回的挑战值',
    example: 'abc123def456'
  })
  challenge: string
}
```

### 4. Webhook验证服务

**文件**: `apps/admin/src/modules/open-platform/services/webhook.service.ts`

#### 4.1 核心验证方法
```typescript
/**
 * 验证Webhook URL的可访问性
 */
async verifyWebhookUrl(webhookUrl: string): Promise<{
  success: boolean
  message: string
  statusCode?: number
}> {
  const challenge = this.generateChallenge()
  const verificationData: WebhookVerificationRequestDto = {
    type: 'webhook_verification',
    timestamp: Date.now(),
    challenge
  }

  // 重试机制
  for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
    try {
      const response = await firstValueFrom(
        this.httpService.post(webhookUrl, verificationData, {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'YiXiaoEr-Webhook-Verifier/1.0'
          },
          timeout: this.VERIFICATION_TIMEOUT
        }).pipe(
          timeout(this.VERIFICATION_TIMEOUT),
          catchError((error: AxiosError) => {
            throw error
          })
        )
      )

      // 验证响应格式和challenge值
      const responseData = response.data as WebhookVerificationResponseDto
      
      if (!this.isValidVerificationResponse(responseData)) {
        return {
          success: false,
          message: '响应格式不正确，期望格式: { success: true, challenge: "原challenge值" }'
        }
      }

      if (responseData.challenge !== challenge) {
        return {
          success: false,
          message: `Challenge值不匹配，期望: ${challenge}, 实际: ${responseData.challenge}`
        }
      }

      if (!responseData.success) {
        return {
          success: false,
          message: '响应中success字段为false'
        }
      }

      return {
        success: true,
        message: '验证成功'
      }

    } catch (error) {
      // 重试逻辑和错误处理
      if (attempt < this.MAX_RETRIES) {
        await this.sleep(1000 * attempt)
      }
    }
  }

  return this.handleVerificationError(lastError, webhookUrl)
}
```

#### 4.2 错误处理机制
```typescript
private handleVerificationError(error: any, webhookUrl: string): {
  success: boolean
  message: string
  statusCode?: number
} {
  if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
    return {
      success: false,
      message: `网络连接失败: ${error.message}`
    }
  }

  if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
    return {
      success: false,
      message: `请求超时，服务器未在${this.VERIFICATION_TIMEOUT / 1000}秒内响应`
    }
  }

  if (error.response) {
    const status = error.response.status
    const statusText = error.response.statusText || '未知错误'
    
    if (status >= 400 && status < 500) {
      return {
        success: false,
        message: `客户端错误: ${status} ${statusText}`
      }
    }
    
    if (status >= 500) {
      return {
        success: false,
        message: `服务器错误: ${status} ${statusText}`
      }
    }
  }

  return {
    success: false,
    message: `未知错误: ${error.message}`
  }
}
```

### 5. ApplicationService扩展

**文件**: `apps/admin/src/modules/open-platform/services/application.service.ts`

#### 5.1 设置Webhook方法
```typescript
/**
 * 设置应用Webhook
 */
async setWebhook(applicationId: string, setWebhookDto: SetWebhookRequestDto): Promise<WebhookStatusDto> {
  const { session } = this.request
  const { webhookUrl } = setWebhookDto

  // 验证用户权限
  if (session?.userType !== UserType.OPEN_PLATFORM) {
    throw new ForbiddenException('只有开放平台用户可以设置Webhook')
  }

  // 验证应用存在性和权限
  const application = await this.applicationModel.findOne({
    _id: new Types.ObjectId(applicationId),
    userId: new Types.ObjectId(session.userId)
  })

  if (!application) {
    throw new NotFoundException('应用不存在或无权限访问')
  }

  try {
    // 更新应用状态为验证中
    await this.applicationModel.updateOne(
      { _id: new Types.ObjectId(applicationId) },
      {
        webhookUrl,
        webhookStatus: WebhookStatus.VERIFYING,
        webhookVerifiedAt: null
      }
    )

    // 验证Webhook URL
    const verificationResult = await this.webhookService.verifyWebhookUrl(webhookUrl)

    if (verificationResult.success) {
      // 验证成功，更新状态
      await this.applicationModel.updateOne(
        { _id: new Types.ObjectId(applicationId) },
        {
          webhookStatus: WebhookStatus.VERIFIED,
          webhookVerifiedAt: new Date()
        }
      )

      return {
        webhookUrl,
        webhookStatus: WebhookStatus.VERIFIED,
        webhookVerifiedAt: Date.now()
      }
    } else {
      // 验证失败，更新状态
      await this.applicationModel.updateOne(
        { _id: new Types.ObjectId(applicationId) },
        {
          webhookStatus: WebhookStatus.FAILED,
          webhookVerifiedAt: null
        }
      )

      throw new BadRequestException(`Webhook验证失败: ${verificationResult.message}`)
    }
  } catch (error) {
    // 错误处理逻辑
    if (error instanceof BadRequestException) {
      throw error
    }

    await this.applicationModel.updateOne(
      { _id: new Types.ObjectId(applicationId) },
      {
        webhookStatus: WebhookStatus.FAILED,
        webhookVerifiedAt: null
      }
    )

    throw new BadRequestException(`设置Webhook失败: ${error.message}`)
  }
}
```

### 6. Controller API端点

**文件**: `apps/admin/src/modules/open-platform/controllers/application.controller.ts`

#### 6.1 设置Webhook端点
```typescript
@Put(':id/webhook')
@OpenPlatformAccess()
@ApiOperation({
  summary: '设置应用Webhook',
  description: '设置指定应用的Webhook URL，设置后会立即进行可访问性验证'
})
@ApiOkResponse({
  description: '设置成功',
  type: SetWebhookResponseDto
})
async setWebhook(@Param('id') id: string, @Body() setWebhookDto: SetWebhookRequestDto) {
  const webhookStatus = await this.applicationService.setWebhook(id, setWebhookDto)
  return webhookStatus
}
```

#### 6.2 获取Webhook状态端点
```typescript
@Get(':id/webhook')
@OpenPlatformAccess()
@ApiOperation({
  summary: '获取应用Webhook状态',
  description: '获取指定应用的Webhook配置和验证状态'
})
async getWebhookStatus(@Param('id') id: string) {
  const webhookStatus = await this.applicationService.getWebhookStatus(id)
  return webhookStatus
}
```

#### 6.3 删除Webhook端点
```typescript
@Delete(':id/webhook')
@OpenPlatformAccess()
@ApiOperation({
  summary: '删除应用Webhook',
  description: '删除指定应用的Webhook配置'
})
async removeWebhook(@Param('id') id: string) {
  const webhookStatus = await this.applicationService.removeWebhook(id)
  return webhookStatus
}
```

## Webhook验证机制

### 1. **验证流程**
1. 用户设置Webhook URL
2. 系统生成32位随机challenge值
3. 向目标URL发送POST请求，包含验证数据
4. 验证响应格式和challenge值
5. 更新应用的Webhook状态

### 2. **验证请求格式**
```json
{
  "type": "webhook_verification",
  "timestamp": 1672531200000,
  "challenge": "abc123def456"
}
```

### 3. **期望响应格式**
```json
{
  "success": true,
  "challenge": "abc123def456"
}
```

### 4. **验证参数**
- **超时时间**: 5秒
- **重试次数**: 3次
- **重试间隔**: 递增等待（1秒、2秒、3秒）

## 错误处理

### 1. **网络错误**
- **连接失败**: `ECONNREFUSED`, `ENOTFOUND`, `ECONNRESET`
- **超时错误**: `ETIMEDOUT`, `TimeoutError`
- **返回具体的网络错误信息**

### 2. **HTTP错误**
- **4xx错误**: 客户端错误
- **5xx错误**: 服务器错误
- **包含状态码和错误描述**

### 3. **验证错误**
- **响应格式不正确**: 缺少必要字段或类型错误
- **Challenge值不匹配**: 返回的challenge与发送的不一致
- **Success字段为false**: 目标服务明确拒绝验证

## 安全特性

### 1. **权限控制**
- ✅ 使用 `@OpenPlatformAccess()` 装饰器
- ✅ 验证用户对应用的所有权
- ✅ 确保数据隔离

### 2. **URL验证**
- ✅ 使用 `@IsUrl()` 验证器
- ✅ 限制URL长度（1-500字符）
- ✅ 支持HTTPS和HTTP协议

### 3. **请求安全**
- ✅ 设置合理的超时时间
- ✅ 使用标识性User-Agent
- ✅ 限制重试次数防止滥用

## 使用示例

### 1. **设置Webhook**
```bash
curl -X PUT "https://api.example.com/open-platform/applications/123/webhook" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "webhookUrl": "https://your-app.com/webhook"
  }'
```

### 2. **获取Webhook状态**
```bash
curl -X GET "https://api.example.com/open-platform/applications/123/webhook" \
  -H "Authorization: Bearer your-token"
```

### 3. **删除Webhook**
```bash
curl -X DELETE "https://api.example.com/open-platform/applications/123/webhook" \
  -H "Authorization: Bearer your-token"
```

### 4. **Webhook端点实现示例**
```javascript
// Express.js示例
app.post('/webhook', (req, res) => {
  const { type, timestamp, challenge } = req.body

  if (type === 'webhook_verification') {
    // 验证请求
    res.json({
      success: true,
      challenge: challenge
    })
  } else {
    // 处理业务事件
    console.log('收到Webhook事件:', req.body)
    res.json({ success: true })
  }
})
```

## 后续扩展

### 1. **事件通知**
- 订单状态变更通知
- 用户注册通知
- 余额变动通知
- 系统维护通知

### 2. **安全增强**
- 签名验证机制
- IP白名单限制
- 请求频率限制

### 3. **监控和统计**
- Webhook调用成功率统计
- 响应时间监控
- 失败重试机制
- 事件发送历史记录

这个Webhook功能为开放平台提供了强大的事件通知能力，确保了系统的可扩展性和集成友好性。
