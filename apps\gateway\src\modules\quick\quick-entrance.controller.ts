import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { QuickEntranceService } from './quick-entrance.service'
import { QuickEntrancesCreateRequest, QuickEntrancesResponseDTO } from './quick-entrance.dto'

@Controller('quick-entrance')
@ApiTags('快捷入口管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class QuickEntranceController {
  constructor(private readonly quickEntranceService: QuickEntranceService) {}

  @Get()
  @ApiOperation({ summary: '获取快捷入口列表' })
  @ApiOkResponse({ description: '操作成功', type: QuickEntrancesResponseDTO })
  async getQuickEntrances() {
    return this.quickEntranceService.getQuickEntrances()
  }

  @Post()
  @ApiOperation({ summary: '设置快捷入口' })
  @ApiOkResponse({ type: QuickEntrancesResponseDTO })
  async createQuickEntrance(@Body() body: QuickEntrancesCreateRequest[]) {
    return this.quickEntranceService.createQuickEntrance(body)
  }

  @Delete(':quickId')
  @ApiOperation({ summary: '删除快捷入口' })
  @ApiOkResponse({ type: QuickEntrancesResponseDTO })
  async deleteQuickEntrance(@Param('quickId') quickId: string) {
    return this.quickEntranceService.deleteQuickEntrance(quickId)
  }
}
