import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiOkResponse,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse
} from '@nestjs/swagger'
import { GatewayUserService } from '../services/gateway-user.service'
import {
  ApplicationAccess,
  AdminOnly,
  AllAuthenticatedAccess,
  AdminAndOpenPlatformAccess,
  OpenPlatformAccess
} from '../../../common/decorators/access-control.decorator'
import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseNotFoundResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import {
  CreateGatewayUserRequestDto,
  CreateOpenPlatformGatewayUserRequestDto,
  UpdateGatewayUserRequestDto,
  GetGatewayUsersRequestDto,
  GatewayUserResponseDto,
  GatewayUsersListResponseDto,
  CreateGatewayUserResponseDto,
  CreateOpenPlatformGatewayUserResponseDto,
  UpdateGatewayUserResponseDto,
  GenerateTokenRequestDto,
  GetAppUsersRequestDto,
  GetAppTeamsRequestDto,
  AppUsersListResponseDto,
  AppTeamsListResponseDto,
  OrdersListResponseDto,
  GetOrdersRequestDto
} from '../dto/gateway-user.dto'

@ApiTags('开放平台-Gateway用户管理')
@Controller('gateway/users')
@ApiBearerAuth()
export class GatewayUserController {
  constructor(private readonly gatewayUserService: GatewayUserService) {}

  @Post()
  @ApplicationAccess(true) // 需要数据隔离
  @ApiOperation({
    summary: '创建Gateway用户',
    description: '应用Token创建的用户会自动设置source和sourceAppId，实现数据隔离'
  })
  @ApiResponse({
    status: 201,
    description: '用户创建成功',
    type: CreateGatewayUserResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或手机号已存在',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '团队不存在或无权限访问',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async createUser(@Body() createUserDto: CreateGatewayUserRequestDto) {
    const result = await this.gatewayUserService.createUser(createUserDto)
    return result
  }

  @Get()
  @ApplicationAccess(true) // 需要数据隔离
  @ApiOperation({
    summary: '查询Gateway用户列表',
    description: '应用Token只能查询自己创建的用户（通过sourceAppId过滤）'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: GatewayUsersListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getUsers(@Query() query: GetGatewayUsersRequestDto) {
    const result = await this.gatewayUserService.getUsers(query)
    return result
  }

  @Get(':id')
  @ApplicationAccess(true) // 需要数据隔离
  @ApiOperation({
    summary: '获取Gateway用户详情',
    description: '应用Token只能查询自己创建的用户'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: GatewayUserResponseDto
  })
  @ApiNotFoundResponse({
    description: '用户不存在或无权限访问',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getUserById(@Param('id') id: string) {
    const result = await this.gatewayUserService.getUserById(id)
    return result
  }

  @Put(':id')
  @ApplicationAccess(true) // 需要数据隔离
  @ApiOperation({
    summary: '更新Gateway用户',
    description: '应用Token只能更新自己创建的用户'
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: UpdateGatewayUserResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '用户不存在或无权限访问',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async updateUser(@Param('id') id: string, @Body() updateUserDto: UpdateGatewayUserRequestDto) {
    const result = await this.gatewayUserService.updateUser(id, updateUserDto)
    return result
  }

  @Delete(':id')
  @ApplicationAccess(true) // 需要数据隔离
  @ApiOperation({
    summary: '删除Gateway用户',
    description: '应用Token只能删除自己创建的用户'
  })
  @ApiResponse({
    status: 200,
    description: '删除成功'
  })
  @ApiNotFoundResponse({
    description: '用户不存在或无权限访问',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async deleteUser(@Param('id') id: string) {
    await this.gatewayUserService.deleteUser(id)
    return { message: '用户删除成功' }
  }

  // 管理员专用接口
  @Get('admin/all')
  @AdminOnly()
  @ApiOperation({
    summary: '管理员查询所有Gateway用户',
    description: '管理员可以查询所有用户，不受数据隔离限制'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: GatewayUsersListResponseDto
  })
  async getAllUsersForAdmin(@Query() query: GetGatewayUsersRequestDto) {
    // 管理员查询时不应用数据隔离
    const result = await this.gatewayUserService.getUsers(query)
    return result
  }

  @Post(':userId/access-token')
  @ApplicationAccess(true)
  @ApiOperation({
    summary: '为用户生成访问Token',
    description: '为指定用户生成访问Token，用于登录'
  })
  @ApiResponse({
    status: 200,
    description: 'Token生成成功',
    type: String
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '用户不存在或无权限访问',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async postAccessToken(@Param('userId') userId: string, @Body() body: GenerateTokenRequestDto) {
    return await this.gatewayUserService.generateAccessToken(userId, body.teamId)
  }

  // 应用管理接口
  @Get('app/:appId/users')
  @AdminAndOpenPlatformAccess()
  @ApiOperation({
    summary: '获取应用下的用户列表',
    description: '管理员和开放平台用户可以查询指定应用下注册的所有用户'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: AppUsersListResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getAppUsers(@Param('appId') appId: string, @Query() query: GetAppUsersRequestDto) {
    const result = await this.gatewayUserService.getAppUsers(appId, query)
    return result
  }

  @Get('app/:appId/teams')
  @AdminAndOpenPlatformAccess()
  @ApiOperation({
    summary: '获取应用下的团队列表',
    description: '管理员和开放平台用户可以查询指定应用下对应的所有团队'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: AppTeamsListResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getAppTeams(@Param('appId') appId: string, @Query() query: GetAppTeamsRequestDto) {
    const result = await this.gatewayUserService.getAppTeams(appId, query)
    return result
  }

  @Get('app/:appId/orders')
  @AdminAndOpenPlatformAccess()
  @ApiOperation({
    summary: '查询订单列表',
    description: '查询指定应用下的所有订单信息，支持多种查询条件和分页'
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: OrdersListResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getOrders(@Param('appId') appId: string, @Query() query: GetOrdersRequestDto) {
    return this.gatewayUserService.getOrders(appId, query)
  }

  @Post('open-platform')
  @OpenPlatformAccess()
  @ApiOperation({
    summary: '创建OpenPlatform Gateway用户（用户名+密码）',
    description: '开放平台用户通过用户名和密码创建Gateway用户，支持指定应用ID和团队ID'
  })
  @ApiOkResponse({
    description: '创建成功',
    type: CreateOpenPlatformGatewayUserResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或用户已存在',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足或应用不存在',
    type: BaseForbiddenResponseDTO
  })
  async createOpenPlatformUser(@Body() createUserDto: CreateOpenPlatformGatewayUserRequestDto) {
    const user = await this.gatewayUserService.createOpenPlatformUser(createUserDto)
    return user
  }
}
