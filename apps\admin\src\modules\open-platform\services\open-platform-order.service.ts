import { Injectable, Inject, ForbiddenException, Logger, NotFoundException, BadRequestException } from '@nestjs/common'
import { InjectModel, InjectConnection } from '@nestjs/mongoose'
import { Model, Types, Connection } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  MemberEntity,
  OrderEntity,
  TeamEntity,
  OpenPlatformApplicationEntity,
  OpenPlatformApplicationBalanceEntity,
  AdminEntity
} from '@yxr/mongo'
import {
  OpenPlatformOrderResourceType,
  OrderStatus,
  TeamRoleNames,
  UserType,
  PayType,
  OrderSource
} from '@yxr/common'
import {
  CreateAccountPointsOrderRequestDto,
  CreateTrafficOrderRequestDto,
  CreateOpenOrderRequestDto,
  CreateUpgradeOrderRequestDto,
  CreateRenewOrderRequestDto
} from '../dto/open-platform-order.dto'
import { OpenPlatformOrderManagerService } from '@yxr/open-platform-order'
import { OrderManagerService } from '@yxr/order'
import dayjs from 'dayjs'
import { Cron } from '@nestjs/schedule'

/**
 * 开放平台订单控制服务
 */
// 权益包订单创建DTO
export interface CreateOpenPlatformOrderDto {
  applicationId: string
  interestId: string
  interestCount: number
  month?: number
  days?: number
  teamId: string
  isPay: boolean
  payAmount: number
  remark?: string
}

export interface OpenPlatformOrderResponseDto {
  orderNo: string
}

@Injectable()
export class OpenPlatformOrderService {
  logger = new Logger('OpenPlatformOrderService')
  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(OrderEntity.name)
    private orderModel: Model<OrderEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformApplicationBalanceEntity.name)
    private balanceModel: Model<OpenPlatformApplicationBalanceEntity>,
    @InjectModel(AdminEntity.name)
    private adminModel: Model<AdminEntity>,
    @InjectConnection() private connection: Connection,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManagerService: OpenPlatformOrderManagerService,
    private readonly legacyOrderManagerService: OrderManagerService
  ) {}

  async createAccountPointsOrder(data: CreateAccountPointsOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建账号点数订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createAccountPointsOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId,
        startTime: new Date(data.startTime)
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建账号点数订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建账号点数订单失败: ${error.message}`
      }
    }
  }

  async createTrafficOrder(data: CreateTrafficOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建流量订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createTrafficOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建流量订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建流量订单失败: ${error.message}`
      }
    }
  }

  async calculateTeamAccountPoints(teamId: string) {
    return await this.orderManagerService.calculateTeamAccountPoints(teamId)
  }

  /**
   * 手动执行团队账号点数同步
   * 提供给管理员手动触发的接口
   */
  async manualSyncTeamAccountCapacity(): Promise<{
    success: boolean
    processedTeams: number
    totalUpdatedCapacity: number
    message: string
    duration: number
  }> {
    const startTime = new Date()
    this.logger.log('手动执行团队账号点数同步任务')

    try {
      let processedTeams = 0
      let totalUpdatedCapacity = 0

      const teamAccountData = await this.orderManagerService.getTeamAccountCapacityData()

      // 分批处理
      const batchSize = 50
      for (let i = 0; i < teamAccountData.length; i += batchSize) {
        const batch = teamAccountData.slice(i, i + batchSize)
        const batchResult = await this.orderManagerService.processBatchTeamAccountCapacity(batch)
        processedTeams += batchResult.processedCount
        totalUpdatedCapacity += batchResult.totalCapacity
      }

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: true,
        processedTeams,
        totalUpdatedCapacity,
        message: `同步完成: 处理${processedTeams}个团队, 总账号点数${totalUpdatedCapacity}`,
        duration
      }

      this.logger.log(`手动团队账号点数同步完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: false,
        processedTeams: 0,
        totalUpdatedCapacity: 0,
        message: `同步失败: ${error.message}`,
        duration
      }

      this.logger.error(`手动团队账号点数同步失败: ${JSON.stringify(result)}`)
      return result
    }
  }

  /**
   * 创建开通订单（新团队首次开通VIP权益）
   */
  async createOpenOrder(createOrderDto: CreateOpenOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
    const { applicationId, interestId, interestCount, month, days, teamId, isPay, payAmount, remark } = createOrderDto

    // 验证基础权限和参数
    await this.validateBasicPermissions(applicationId, teamId)
    await this.validatePaymentParams(isPay, payAmount)

    // 检查应用余额
    if (isPay && payAmount > 0) {
      await this.validateApplicationBalance(applicationId, payAmount)
    }

    // 获取团队创建者
    const member = await this.getTeamMaster(teamId)

    // 创建虚拟管理员
    const virtualAdmin = this.createVirtualAdmin()

    const dbSession = await this.connection.startSession()

    try {
      let orderNo: string

      await dbSession.withTransaction(async () => {
        // 创建开通订单
        orderNo = await this.legacyOrderManagerService.createOrder({
          teamId: teamId,
          interestCount: interestCount,
          interestId: interestId,
          isCorporateTransfer: false,
          month: month,
          days: days,
          userId: member.userId.toString(),
          remark: remark,
          creatorId: virtualAdmin.id,
          creatorName: virtualAdmin.username,
          payAmount: payAmount,
          isPay: isPay,
          applicationId,
          openPlatformUserId: this.request.session.userId
        })

        // 处理支付和订单完成
        await this.handleOrderPaymentAndCompletion(orderNo, isPay, payAmount, applicationId, teamId, dbSession, '开通')
      })

      // 处理订单完成逻辑
      await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

      return { orderNo }
    } catch (error) {
      this.logger.error(`创建开通订单失败: ${error.message}`, error.stack)
      throw this.handleOrderError(error)
    } finally {
      await dbSession.endSession()
    }
  }

  /**
   * 创建升级订单（增加现有团队的权益包数量）
   */
  async createUpgradeOrder(upgradeOrderDto: CreateUpgradeOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
    const { applicationId, interestId, interestCount, teamId, isPay, payAmount, remark } = upgradeOrderDto

    // 验证基础权限和参数
    await this.validateBasicPermissions(applicationId, teamId)
    await this.validatePaymentParams(isPay, payAmount)

    // 检查应用余额
    if (isPay && payAmount > 0) {
      await this.validateApplicationBalance(applicationId, payAmount)
    }

    // 获取团队创建者
    const member = await this.getTeamMaster(teamId)

    // 创建虚拟管理员
    const virtualAdmin = this.createVirtualAdmin()

    const dbSession = await this.connection.startSession()

    try {
      let orderNo: string

      await dbSession.withTransaction(async () => {
        // 创建升级订单
        orderNo = await this.legacyOrderManagerService.upgradeOrder({
          teamId: teamId,
          interestCount: interestCount,
          interestId: interestId,
          isCorporateTransfer: false,
          userId: member.userId.toString(),
          remark: remark,
          creatorId: virtualAdmin.id,
          creatorName: virtualAdmin.username,
          payAmount: payAmount,
          isPay: isPay,
          applicationId,
          openPlatformUserId: this.request.session.userId
        })

        // 处理支付和订单完成
        await this.handleOrderPaymentAndCompletion(orderNo, isPay, payAmount, applicationId, teamId, dbSession, '升级')
      })

      // 处理订单完成逻辑
      await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

      return { orderNo }
    } catch (error) {
      this.logger.error(`创建升级订单失败: ${error.message}`, error.stack)
      throw this.handleOrderError(error)
    } finally {
      await dbSession.endSession()
    }
  }

  /**
   * 创建续费订单（延长现有团队的VIP有效期）
   */
  async createRenewOrder(renewOrderDto: CreateRenewOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
    const { applicationId, interestId, month, days, teamId, isPay, payAmount, remark } = renewOrderDto

    // 验证基础权限和参数
    await this.validateBasicPermissions(applicationId, teamId)
    await this.validatePaymentParams(isPay, payAmount)

    // 检查应用余额
    if (isPay && payAmount > 0) {
      await this.validateApplicationBalance(applicationId, payAmount)
    }

    // 获取团队创建者
    const member = await this.getTeamMaster(teamId)

    // 创建虚拟管理员
    const virtualAdmin = this.createVirtualAdmin()

    const dbSession = await this.connection.startSession()

    try {
      let orderNo: string

      await dbSession.withTransaction(async () => {
        // 创建续费订单
        orderNo = await this.legacyOrderManagerService.renewOrder({
          teamId: teamId,
          interestId: interestId,
          isCorporateTransfer: false,
          month: month,
          days: days,
          userId: member.userId.toString(),
          remark: remark,
          creatorId: virtualAdmin.id,
          creatorName: virtualAdmin.username,
          payAmount: payAmount,
          isPay: isPay,
        })

        // 处理支付和订单完成
        await this.handleOrderPaymentAndCompletion(orderNo, isPay, payAmount, applicationId, teamId, dbSession, '续费')
      })

      // 处理订单完成逻辑
      await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

      return { orderNo }
    } catch (error) {
      this.logger.error(`创建续费订单失败: ${error.message}`, error.stack)
      throw this.handleOrderError(error)
    } finally {
      await dbSession.endSession()
    }
  }

  /**
   * 获取应用余额
   */
  private async getApplicationBalance(applicationId: string): Promise<{
    totalBalance: number
    availableBalance: number
    frozenBalance: number
  }> {
    let balance = await this.balanceModel.findOne({
      applicationId: new Types.ObjectId(applicationId)
    })

    // 如果余额记录不存在，创建一个初始记录
    if (!balance) {
      balance = await this.balanceModel.create({
        applicationId: new Types.ObjectId(applicationId),
        totalBalance: 0,
        frozenBalance: 0,
        availableBalance: 0,
        totalRecharge: 0,
        totalConsumption: 0
      })
    }

    return {
      totalBalance: balance.totalBalance,
      availableBalance: balance.availableBalance,
      frozenBalance: balance.frozenBalance
    }
  }

  /**
   * 扣除应用余额
   */
  private async deductApplicationBalance(
    applicationId: string,
    amount: number, // 单位：分
    session?: any
  ): Promise<void> {
    const options = session ? { session } : {}

    const result = await this.balanceModel.updateOne(
      {
        applicationId: new Types.ObjectId(applicationId),
        availableBalance: { $gte: amount } // 确保余额足够
      },
      {
        $inc: {
          availableBalance: -amount,
          totalConsumption: amount
        },
        $set: {
          updatedAt: new Date()
        }
      },
      options
    )

    if (result.matchedCount === 0) {
      throw new BadRequestException('余额不足或应用不存在')
    }

    this.logger.log(
      `应用余额扣除成功: applicationId=${applicationId}, amount=${amount}元`
    )
  }

  /**
   * 验证基础权限（用户权限、应用权限、团队权限、未支付订单检查）
   */
  private async validateBasicPermissions(applicationId: string, teamId: string): Promise<void> {
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建订单')
    }

    // 验证应用权限
    const application = await this.applicationModel.findOne({
      _id: new Types.ObjectId(applicationId),
      userId: new Types.ObjectId(session.userId)
    }).lean()

    if (!application) {
      throw new ForbiddenException('无权限访问该应用')
    }

    // 验证团队是否属于该应用
    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(teamId),
      source: 'open_platform_app',
      sourceAppId: applicationId,
      isDeleted: false
    }).lean()

    if (!team) {
      throw new NotFoundException('团队不存在或无权限访问')
    }

    // 检查是否有未支付的订单
    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }
  }

  /**
   * 验证支付参数
   */
  private async validatePaymentParams(isPay: boolean, payAmount: number): Promise<void> {
    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }
  }

  /**
   * 验证应用余额是否足够
   */
  private async validateApplicationBalance(applicationId: string, payAmount: number): Promise<void> {
    const balance = await this.getApplicationBalance(applicationId)
    if (balance.availableBalance < payAmount ) { // 转换为分
      throw new BadRequestException(
        `应用余额不足，当前可用余额：${(balance.availableBalance).toFixed(2)}元，需要支付：${payAmount.toFixed(2)}元`
      )
    }
  }

  /**
   * 获取团队创建者
   */
  private async getTeamMaster(teamId: string) {
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    if (!member) {
      throw new NotFoundException('团队创建者信息无法找到')
    }

    return member
  }

  /**
   * 创建虚拟管理员用户（用于订单创建）
   */
  private createVirtualAdmin() {
    const { session } = this.request
    return {
      id: session.userId,
      username: `open_platform_${session.userId}`
    }
  }

  /**
   * 处理订单支付和完成逻辑
   */
  private async handleOrderPaymentAndCompletion(
    orderNo: string,
    isPay: boolean,
    payAmount: number,
    applicationId: string,
    teamId: string,
    dbSession: any,
    orderType: string
  ): Promise<void> {
    await this.orderModel.updateOne(
      { orderNo },
      {
        orderStatus: OrderStatus.Paid,
        payAmount: payAmount,
        payTime: new Date(),
        payType: PayType.Other
      },
      { session: dbSession }
    )

    // 扣除应用余额
    await this.deductApplicationBalance(applicationId, payAmount, dbSession)

    this.logger.log(
      `开放平台${orderType}订单支付成功: ${orderNo}, 应用ID: ${applicationId}, ` +
        `团队ID: ${teamId}, 支付金额: ${payAmount}元`
    )
  }

  /**
   * 处理订单错误
   */
  private handleOrderError(error: any): Error {
    // 根据错误类型抛出更具体的异常
    if (
      error instanceof NotFoundException ||
      error instanceof BadRequestException ||
      error instanceof ForbiddenException
    ) {
      return error
    }
    return new BadRequestException('订单创建失败，请稍后重试')
  }
}
