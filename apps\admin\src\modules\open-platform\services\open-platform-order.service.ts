import { Injectable, Inject, ForbiddenException, Logger, NotFoundException, BadRequestException } from '@nestjs/common'
import { InjectModel, InjectConnection } from '@nestjs/mongoose'
import { Model, Types, Connection } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  MemberEntity,
  OrderEntity,
  TeamEntity,
  OpenPlatformApplicationEntity,
  OpenPlatformApplicationBalanceEntity,
  AdminEntity
} from '@yxr/mongo'
import {
  OpenPlatformOrderResourceType,
  OrderStatus,
  TeamRoleNames,
  UserType,
  PayType
} from '@yxr/common'
import {
  CreateAccountPointsOrderRequestDto,
  CreateTrafficOrderRequestDto,
  CreateOpenPlatformOrderRequestDto
} from '../dto/open-platform-order.dto'
import { OpenPlatformOrderManagerService } from '@yxr/open-platform-order'
import { OrderManagerService } from '@yxr/order'
import dayjs from 'dayjs'
import { <PERSON>ron } from '@nestjs/schedule'

/**
 * 开放平台订单控制服务
 */
// 权益包订单创建DTO
export interface CreateOpenPlatformOrderDto {
  applicationId: string
  interestId: string
  interestCount: number
  month?: number
  days?: number
  teamId: string
  isPay: boolean
  payAmount: number
  remark?: string
}

export interface OpenPlatformOrderResponseDto {
  orderNo: string
}

@Injectable()
export class OpenPlatformOrderService {
  logger = new Logger('OpenPlatformOrderService')
  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(OrderEntity.name)
    private orderModel: Model<OrderEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformApplicationBalanceEntity.name)
    private balanceModel: Model<OpenPlatformApplicationBalanceEntity>,
    @InjectModel(AdminEntity.name)
    private adminModel: Model<AdminEntity>,
    @InjectConnection() private connection: Connection,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManagerService: OpenPlatformOrderManagerService,
    private readonly legacyOrderManagerService: OrderManagerService
  ) {}

  async createAccountPointsOrder(data: CreateAccountPointsOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建账号点数订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createAccountPointsOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId,
        startTime: new Date(data.startTime)
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建账号点数订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建账号点数订单失败: ${error.message}`
      }
    }
  }

  async createTrafficOrder(data: CreateTrafficOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建流量订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createTrafficOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建流量订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建流量订单失败: ${error.message}`
      }
    }
  }

  async calculateTeamAccountPoints(teamId: string) {
    return await this.orderManagerService.calculateTeamAccountPoints(teamId)
  }

  /**
   * 手动执行团队账号点数同步
   * 提供给管理员手动触发的接口
   */
  async manualSyncTeamAccountCapacity(): Promise<{
    success: boolean
    processedTeams: number
    totalUpdatedCapacity: number
    message: string
    duration: number
  }> {
    const startTime = new Date()
    this.logger.log('手动执行团队账号点数同步任务')

    try {
      let processedTeams = 0
      let totalUpdatedCapacity = 0

      const teamAccountData = await this.orderManagerService.getTeamAccountCapacityData()

      // 分批处理
      const batchSize = 50
      for (let i = 0; i < teamAccountData.length; i += batchSize) {
        const batch = teamAccountData.slice(i, i + batchSize)
        const batchResult = await this.orderManagerService.processBatchTeamAccountCapacity(batch)
        processedTeams += batchResult.processedCount
        totalUpdatedCapacity += batchResult.totalCapacity
      }

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: true,
        processedTeams,
        totalUpdatedCapacity,
        message: `同步完成: 处理${processedTeams}个团队, 总账号点数${totalUpdatedCapacity}`,
        duration
      }

      this.logger.log(`手动团队账号点数同步完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: false,
        processedTeams: 0,
        totalUpdatedCapacity: 0,
        message: `同步失败: ${error.message}`,
        duration
      }

      this.logger.error(`手动团队账号点数同步失败: ${JSON.stringify(result)}`)
      return result
    }
  }

  /**
   * 创建开放平台权益包订单
   * 支持三种订单类型：开通、升级、续费
   * 包含余额验证、自动支付和余额扣除功能
   */
  async createOpenPlatformOrder({
    applicationId,
    interestId,
    interestCount,
    month,
    days,
    teamId,
    isPay,
    payAmount,
    remark
  }: CreateOpenPlatformOrderDto): Promise<OpenPlatformOrderResponseDto> {
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建订单')
    }

    // 验证应用权限
    const application = await this.applicationModel.findOne({
      _id: new Types.ObjectId(applicationId),
      userId: new Types.ObjectId(session.userId)
    }).lean()

    if (!application) {
      throw new ForbiddenException('无权限访问该应用')
    }

    // 验证团队是否属于该应用
    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(teamId),
      source: 'open_platform_app',
      sourceAppId: applicationId,
      isDeleted: false
    }).lean()

    if (!team) {
      throw new NotFoundException('团队不存在或无权限访问')
    }

    // 检查是否有未支付的订单
    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    // 查找团队创建者
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    if (!member) {
      throw new NotFoundException('团队创建者信息无法找到')
    }

    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    // 检查应用余额是否足够
    if (isPay && payAmount > 0) {
      const balance = await this.getApplicationBalance(applicationId)
      if (balance.availableBalance < payAmount * 100) { // 转换为分
        throw new BadRequestException(
          `应用余额不足，当前可用余额：${(balance.availableBalance / 100).toFixed(2)}元，需要支付：${payAmount.toFixed(2)}元`
        )
      }
    }

    // 创建虚拟管理员用户（用于订单创建）
    const virtualAdmin = {
      id: session.userId,
      username: `open_platform_${session.userId}`
    }

    const dbSession = await this.connection.startSession()

    try {
      let orderNo: string

      await dbSession.withTransaction(async () => {
        // 创建订单
        orderNo = await this.legacyOrderManagerService.createOrder({
          teamId: teamId,
          interestCount: interestCount,
          interestId: interestId,
          isCorporateTransfer: false,
          month: month,
          days: days,
          userId: member.userId.toString(),
          remark: remark,
          creatorId: virtualAdmin.id,
          creatorName: virtualAdmin.username,
          payAmount: payAmount,
          isPay: isPay
        })

        // 如果需要支付，立即处理支付和余额扣除
        if (isPay && payAmount > 0) {
          // 更新订单状态为已支付
          await this.orderModel.updateOne(
            { orderNo },
            {
              orderStatus: OrderStatus.Paid,
              payAmount: payAmount,
              payTime: new Date(),
              payType: PayType.Other
            },
            { session: dbSession }
          )

          // 扣除应用余额
          await this.deductApplicationBalance(applicationId, payAmount * 100, dbSession)

          // 处理订单完成逻辑
          await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

          this.logger.log(
            `开放平台订单支付成功: ${orderNo}, 应用ID: ${applicationId}, ` +
              `团队ID: ${teamId}, 支付金额: ${payAmount}元`
          )
        } else {
          // 免费订单，直接设置为已支付并处理
          await this.orderModel.updateOne(
            { orderNo },
            {
              orderStatus: OrderStatus.Paid,
              payAmount: payAmount,
              payTime: new Date(),
              payType: PayType.Other
            },
            { session: dbSession }
          )

          await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

          this.logger.log(
            `开放平台免费订单创建成功: ${orderNo}, 应用ID: ${applicationId}, 团队ID: ${teamId}`
          )
        }
      })

      return { orderNo }
    } catch (error) {
      this.logger.error(`创建开放平台订单失败: ${error.message}`, error.stack)

      // 根据错误类型抛出更具体的异常
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error
      }
      throw new BadRequestException('订单创建失败，请稍后重试')
    } finally {
      await dbSession.endSession()
    }
  }

  /**
   * 获取应用余额
   */
  private async getApplicationBalance(applicationId: string): Promise<{
    totalBalance: number
    availableBalance: number
    frozenBalance: number
  }> {
    let balance = await this.balanceModel.findOne({
      applicationId: new Types.ObjectId(applicationId)
    })

    // 如果余额记录不存在，创建一个初始记录
    if (!balance) {
      balance = await this.balanceModel.create({
        applicationId: new Types.ObjectId(applicationId),
        totalBalance: 0,
        frozenBalance: 0,
        availableBalance: 0,
        totalRecharge: 0,
        totalConsumption: 0
      })
    }

    return {
      totalBalance: balance.totalBalance,
      availableBalance: balance.availableBalance,
      frozenBalance: balance.frozenBalance
    }
  }

  /**
   * 扣除应用余额
   */
  private async deductApplicationBalance(
    applicationId: string,
    amount: number, // 单位：分
    session?: any
  ): Promise<void> {
    const options = session ? { session } : {}

    const result = await this.balanceModel.updateOne(
      {
        applicationId: new Types.ObjectId(applicationId),
        availableBalance: { $gte: amount } // 确保余额足够
      },
      {
        $inc: {
          availableBalance: -amount,
          totalConsumption: amount
        },
        $set: {
          updatedAt: new Date()
        }
      },
      options
    )

    if (result.matchedCount === 0) {
      throw new BadRequestException('余额不足或应用不存在')
    }

    this.logger.log(
      `应用余额扣除成功: applicationId=${applicationId}, amount=${amount / 100}元`
    )
  }
}
