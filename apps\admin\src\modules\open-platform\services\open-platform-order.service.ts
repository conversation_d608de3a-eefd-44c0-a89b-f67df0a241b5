import { Injectable, Inject, ForbiddenException, Logger, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import { MemberEntity, OrderEntity, TeamEntity } from '@yxr/mongo'
import { OpenPlatformOrderResourceType, OrderStatus, TeamRoleNames, UserType } from '@yxr/common'
import {
  CreateAccountPointsOrderRequestDto,
  CreateTrafficOrderRequestDto
} from '../dto/open-platform-order.dto'
import { OpenPlatformOrderManagerService } from '@yxr/open-platform-order'
import dayjs from 'dayjs'
import { Cron } from '@nestjs/schedule'

/**
 * 开放平台订单控制服务
 */
@Injectable()
export class OpenPlatformOrderService {
  logger = new Logger('OpenPlatformOrderService')
  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManagerService: OpenPlatformOrderManagerService
  ) {}

  async createAccountPointsOrder(data: CreateAccountPointsOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建账号点数订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createAccountPointsOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId,
        startTime: new Date(data.startTime)
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建账号点数订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建账号点数订单失败: ${error.message}`
      }
    }
  }

  async createTrafficOrder(data: CreateTrafficOrderRequestDto) {
    try {
      const { session } = this.request

      if (session?.userType !== UserType.APPLICATION) {
        throw new ForbiddenException('只有开放平台用户可以创建流量订单')
      }

      // 验证团队存在
      const team = await this.teamModel.findById(data.teamId)
      if (!team) {
        throw new NotFoundException('团队不存在')
      }

      if (team.sourceAppId !== session.applicationId.toString()) {
        throw new NotFoundException('该团队不属于当前应用')
      }

      const teamMember = await this.memberModel.findOne({
        teamId: new Types.ObjectId(data.teamId),
        roles: { $in: [TeamRoleNames.MASTER] }
      })

      if (!teamMember) {
        throw new ForbiddenException('团队不存在')
      }

      await this.orderManagerService.createTrafficOrder({
        ...data,
        userId: teamMember.userId.toString(),
        sourceAppId: session?.applicationId
      })

      return {
        success: true,
        message: '创建成功'
      }
    } catch (error) {
      this.logger.error(`创建流量订单失败: ${error.message}`, error.stack)
      return {
        success: false,
        message: `创建流量订单失败: ${error.message}`
      }
    }
  }

  async calculateTeamAccountPoints(teamId: string) {
    return await this.orderManagerService.calculateTeamAccountPoints(teamId)
  }

  /**
   * 手动执行团队账号点数同步
   * 提供给管理员手动触发的接口
   */
  async manualSyncTeamAccountCapacity(): Promise<{
    success: boolean
    processedTeams: number
    totalUpdatedCapacity: number
    message: string
    duration: number
  }> {
    const startTime = new Date()
    this.logger.log('手动执行团队账号点数同步任务')

    try {
      let processedTeams = 0
      let totalUpdatedCapacity = 0

      const teamAccountData = await this.orderManagerService.getTeamAccountCapacityData()

      // 分批处理
      const batchSize = 50
      for (let i = 0; i < teamAccountData.length; i += batchSize) {
        const batch = teamAccountData.slice(i, i + batchSize)
        const batchResult = await this.orderManagerService.processBatchTeamAccountCapacity(batch)
        processedTeams += batchResult.processedCount
        totalUpdatedCapacity += batchResult.totalCapacity
      }

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: true,
        processedTeams,
        totalUpdatedCapacity,
        message: `同步完成: 处理${processedTeams}个团队, 总账号点数${totalUpdatedCapacity}`,
        duration
      }

      this.logger.log(`手动团队账号点数同步完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      const result = {
        success: false,
        processedTeams: 0,
        totalUpdatedCapacity: 0,
        message: `同步失败: ${error.message}`,
        duration
      }

      this.logger.error(`手动团队账号点数同步失败: ${JSON.stringify(result)}`)
      return result
    }
  }
}
