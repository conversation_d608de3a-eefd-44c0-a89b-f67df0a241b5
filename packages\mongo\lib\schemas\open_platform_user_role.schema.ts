import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { OpenPlatformRoleNames, OpenPlatformStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_user_roles',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformUserRoleEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: String,
    enum: OpenPlatformRoleNames.All,
    required: true
  })
  role: string

  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  invitedBy?: Types.ObjectId

  @Prop({
    type: Number,
    enum: OpenPlatformStatus,
    required: true,
    default: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

// 创建复合索引确保用户在同一应用中只能有一个角色
const schema = SchemaFactory.createForClass(OpenPlatformUserRoleEntity)
schema.index({ userId: 1, applicationId: 1 }, { unique: true })

export const OpenPlatformUserRoleSchema: ModelDefinition = {
  name: OpenPlatformUserRoleEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformUserRoleEntity)
}

export const OpenPlatformUserRoleMongoose = MongooseModule.forFeature([OpenPlatformUserRoleSchema])
