import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { ChannelService } from './channel.service'
import {
  ChannelCreateRequestDTO,
  ChannelDetailResponseDTO,
  ChannelListRequestDTO,
  ChannelListResponseDTO,
  ChannelPatchRequestDTO,
  PutChannelEnabledRequestDTO
} from './channel.dto'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('channels')
@ApiTags('渠道管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class ChannelController {
  constructor(private readonly channelService: ChannelService) {}

  @Get()
  @ApiOperation({ summary: '渠道列表' })
  @ApiOkResponse({ type: ChannelListResponseDTO })
  getAds(@Query() query: ChannelListRequestDTO) {
    return this.channelService.getChannels(query)
  }

  @Post()
  @ApiOperation({ summary: '创建渠道' })
  @ApiOkResponse({ description: '操作成功' })
  createAd(@Body() body: ChannelCreateRequestDTO) {
    return this.channelService.createChannel(body)
  }

  @Patch(':channelId')
  @ApiOperation({ summary: '更新渠道' })
  @ApiOkResponse({ type: ChannelDetailResponseDTO })
  patchAdDetail(@Param('channelId') channelId: string, @Body() body: ChannelPatchRequestDTO) {
    return this.channelService.patchChannelDetail(channelId, body)
  }

  @Put(':channelId/enabled')
  @ApiOperation({ summary: '更新上下架状态' })
  @ApiOkResponse({ type: ChannelDetailResponseDTO })
  putAdEnabled(@Param('channelId') channelId: string, @Body() body: PutChannelEnabledRequestDTO) {
    return this.channelService.putChannelEnabled(channelId, body)
  }

  @Get(':channelId')
  @ApiOperation({ summary: '渠道详情' })
  @ApiOkResponse({ type: ChannelDetailResponseDTO })
  getAdDetail(@Param('channelId') channelId: string) {
    return this.channelService.getChannelDetail(channelId)
  }

  @Delete(':channelId')
  @ApiOperation({ summary: '渠道删除' })
  @ApiOkResponse({ description: '操作成功' })
  deleteAdDetail(@Param('channelId') channelId: string) {
    return this.channelService.deleteChannel(channelId)
  }
}
