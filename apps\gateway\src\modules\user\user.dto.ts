import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsOptional, IsString, Length, ValidateIf } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export enum ActiveState {
  Unavailable = 0,
  Available = 1
}

export enum SMSCodeSence {
  Auth = 'auth',
  ResetPassword = 'resetPassword',
  ChangePhone = 'changePhone',
  CheckPhone = 'checkPhone',
  BindPhone = 'bindPhone',
  BindAccount = 'bindAccount'
}

export class UserLoginResphoneDTO {
  /**
   * @description
   * 唯一凭证
   */
  @ApiResponseProperty({
    type: String,
    format: 'nanoid',
    example: '1300120012DE89D1DE89D'
  })
  authorization: string
}

export class UserLoginRegisterRequestBodyDTO {
  @ApiProperty({
    description: '手机号码',
    example: '***********',
    required: true
  })
  @IsNotEmpty({ message: '手机号码不能为空' })
  @IsString({ message: '手机号码格式不正确' })
  @Length(11, 11, { message: '请输入11位手机号' })
  phone: string
  
  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: false
  })
  @ValidateIf((o) => !o.password) // 当 password 为空时，code 必填
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string

  @ApiProperty({
    description: '密码',
    required: false
  })
  @ValidateIf((o) => !o.code) // 当 code 为空时，password 必填
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string

  @ApiProperty({
    type: String,
    required: false,
    description: '渠道码',
    example: '10086'
  })
  @IsOptional()
  @IsString()
  channel: string

  @ApiProperty({
    type: String,
    required: false,
    description: '设备号唯一ID',
    example: 'windows 10家庭中文版'
  })
  @IsOptional()
  @IsString()
  deviceId: string
}

export class UserLoginRegisterRequestV2BodyDTO {
  @ApiProperty({
    description: '用户名（支持手机号或账号）',
    example: '***********',
    examples: {
      phone: {
        summary: '手机号登录',
        value: '***********'
      },
      account: {
        summary: '账号登录',
        value: 'user_account_123'
      }
    }
  })
  @IsNotEmpty({ message: '用户名不能为空' })
  @IsString({ message: '用户名格式不正确' })
  username: string
  
  @ApiProperty({
    description: '验证码',
    example: '123456',
    required: false
  })
  @ValidateIf((o) => !o.password) // 当 password 为空时，code 必填
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  @Length(6, 6, { message: '请输入6位验证码' })
  code: string

  @ApiProperty({
    description: '密码',
    required: false
  })
  @ValidateIf((o) => !o.code) // 当 code 为空时，password 必填
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string

  @ApiProperty({
    type: String,
    required: false,
    description: '渠道码',
    example: '10086'
  })
  @IsOptional()
  @IsString()
  channel: string

  @ApiProperty({
    type: String,
    required: false,
    description: '设备号唯一ID',
    example: 'windows 10家庭中文版'
  })
  @IsOptional()
  @IsString()
  deviceId: string
}

export class UserLoginOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserLoginResphoneDTO
  })
  data: UserLoginResphoneDTO
}

class SmsCodeResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '896752'
  })
  code?: string
}

export class UserSendCodeResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: SmsCodeResponseDTO
  })
  data: SmsCodeResponseDTO
}

export class UserSendCodeRequestBodyDTO {
  /**
   * @description
   * phone number
   */
  @ApiProperty({
    description: 'Phone number',
    example: '***********',
    required: true
  })
  @IsString({ message: 'Phone number format is incorrect' })
  @Length(11, 11, { message: 'Please enter an 11-digit phone number' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    description: '验证码场景',
    enum: Object.values(SMSCodeSence),
    example: 'auth',
    required: true
  })
  @IsEnum(SMSCodeSence, { message: '验证码场景不正确' })
  sence: SMSCodeSence
}

export class UserInfoResponseDTO {
  @ApiResponseProperty({
    type: String,
    example: '1300120012DE89D1DE89D'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '***********',
  })
  phone?: string

  @ApiResponseProperty({
    type: String,
    example: 'user_account_123',
  })
  account?: string

  @ApiResponseProperty({
    type: String,
    example: '张三'
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: 'http://example.com/avatar.png'
  })
  avatarUrl: string

  @ApiResponseProperty({
    type: String,
    example: 'http://example.com/avatar.png'
  })
  avatarKey: string

  @ApiProperty({
    type: Number,
    description: '创建时间',
    example: '*************'
  })
  createdAt: number

  @ApiProperty({
    type: Number,
    description: '更新时间',
    example: '*************'
  })
  updatedAt: number

  @ApiProperty({
    type: Boolean,
    description: '是否已设置密码',
    example: false
  })
  isPassword: boolean

  @ApiProperty({
    description: '用户最后一次使用的团队',
    type: String,
    example: 'http://example.com/avatar.png'
  })
  latestTeamId: string
}

export class UserOkUserInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserInfoResponseDTO
  })
  data: UserInfoResponseDTO
}

export class UserAccountRegisterRequestBodyDTO {
  @ApiProperty({
    description: '账号名称',
    example: 'user_account_123'
  })
  @IsNotEmpty({ message: '账号不能为空' })
  @IsString({ message: '账号格式不正确' })
  @Length(8, 20, { message: '账号长度必须在8-20个字符之间' })
  account: string

  @ApiProperty({
    description: '密码',
    example: 'password123'
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string

  @ApiProperty({
    description: '昵称',
    example: '用户昵称'
  })
  @IsNotEmpty({ message: '昵称不能为空' })
  @IsString({ message: '昵称格式不正确' })
  @Length(1, 64, { message: '昵称长度不能超过64个字符' })
  nickName: string

  @ApiProperty({
    type: String,
    required: false,
    description: '渠道码',
    example: '10086'
  })
  @IsOptional()
  @IsString()
  channel?: string

  @ApiProperty({
    type: String,
    required: false,
    description: '设备号唯一ID',
    example: 'windows 10家庭中文版'
  })
  @IsOptional()
  @IsString()
  deviceId?: string
}

export class patchUserRequestBodyDTO {
  @ApiProperty({
    type: String,
    description: '用户昵称',
    example: '很帅的精神小伙',
    required: false
  })
  @IsString()
  @IsOptional()
  nickName: string

  @ApiProperty({
    type: String,
    description: '用户头像, 以图片存储key形式提供, 由获取资源上传接口返回',
    example: 'foo/bar/xxx.png',
    required: false
  })
  @IsString()
  @IsOptional()
  avatarKey: string
}

export class resetPasswordRequestDto {
  @ApiProperty({
    description: '密码',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(8, 16, { message: '请输入8-16位密码' })
  password: string

  @ApiProperty({
    type: String,
    description: '验证码',
    example: '896752'
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  code: string
}
export class changePhoneRequestDto {
  @ApiProperty({
    description: '手机号码',
    example: '***********',
    required: true
  })
  @IsString({ message: '手机号的格式错误' })
  @Length(11, 11, { message: '请输入11位手机号码' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    type: String,
    description: '验证码',
    example: '896752',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  code: string

  @ApiProperty({
    type: String,
    description: '更换手机token',
    required: true
  })
  @IsNotEmpty({ message: '更换手机凭证不能为空' })
  @IsString({ message: '更换手机凭证格式不正确' })
  authorization: string
}
export class TeamAreaResponse {
  @ApiProperty({
    type: String,
    description: '地区名称',
    example: '湖南/长沙市'
  })
  name: string

  @ApiProperty({
    type: String,
    description: '地区编号',
    example: '430100'
  })
  code: string
}

export class bindPhoneRequestDto {
  @ApiProperty({
    description: '手机号码',
    example: '***********',
    required: true
  })
  @IsString({ message: '手机号的格式错误' })
  @Length(11, 11, { message: '请输入11位手机号码' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @ApiProperty({
    type: String,
    description: '验证码',
    example: '896752',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  code: string
}

export class bindAccountRequestDto {
  @ApiProperty({
    description: '手机号码',
    example: '***********',
    required: true
  })
  @IsString({ message: '手机号的格式错误' })
  @Length(11, 11, { message: '请输入11位手机号码' })
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string
  
  @ApiProperty({
    description: '账号',
    example: 'user_account_123',
    required: true
  })
  @IsString({ message: '账号的格式错误' })
  @Length(8, 20, { message: '请输入8-20位账号' })
  @IsNotEmpty({ message: '账号不能为空' })
  account: string

  @ApiProperty({
    type: String,
    description: '验证码',
    example: '896752',
    required: true
  })
  @IsNotEmpty({ message: '验证码不能为空' })
  @IsString({ message: '验证码格式不正确' })
  code: string
}

export class TeamAreaResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamAreaResponse]
  })
  data: TeamAreaResponse[]
}

export class TokenValidateRequestBodyDTO {
  @ApiProperty({
    type: String,
    description: '登录凭证',
    example: 'TrGgGhUfEe3VV9WvxEDBF'
  })
  @IsString({message:'登录凭证格式不正确'})
  @IsNotEmpty({message:'登录凭证不能为空'})
  token: string
}
