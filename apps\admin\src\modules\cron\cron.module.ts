import { Module } from '@nestjs/common'
import {
  ContentMongoose,
  DataStatisticsMongoose,
  TaskMongoose,
  TeamMongoose,
  UserMongoose,
  OrderMongoose,
  AdminMongoose,
  TeamStatisticMongoose,
  ContentStatisticMongoose,
  PlatformAccountMongoose,
  PlatformAccountTrendMongoose,
  PlatformAccountSummaryMongoose,
  ContentTrendEntity,
  ContentTrendMongoose,
  PlatformDataStatisticMongoose
} from '@yxr/mongo'
import { WebhookModule } from '../webhook/webhook.module'
import { ScheduleModule } from '@nestjs/schedule'
import { DataStatisticCornService } from './data_statistic.cron.service'
import { OrderManagerModule } from '@yxr/order'
import { TeamStatisticCornService } from './team_statistic.cron.service'
import { TeamModule } from '../team/team.module'
import { ContentStatisticCleanCornService } from './platform_statistic_clean.cron.service'
import { OverviewModule } from '../overview/overview.module'
import { PlatformAccountCornService } from './platform_account.cron.service'
import { CommonModule } from '@yxr/common'
import { HuoshanModule } from '@yxr/huoshan'
import { OpenPlatformOrderManagerModule } from '@yxr/open-platform-order'

@Module({
  imports: [
    DataStatisticsMongoose,
    UserMongoose,
    TaskMongoose,
    ContentMongoose,
    TeamMongoose,
    OrderMongoose,
    TeamStatisticMongoose,
    AdminMongoose,
    PlatformAccountMongoose,
    ContentStatisticMongoose,
    PlatformAccountTrendMongoose,
    ContentTrendMongoose,
    PlatformAccountSummaryMongoose,
    PlatformDataStatisticMongoose,
    WebhookModule,
    OrderManagerModule,
    ScheduleModule.forRoot(),
    HuoshanModule,
    TeamModule,
    OverviewModule,
    CommonModule,
    OpenPlatformOrderManagerModule
  ],
  controllers: [],
  providers: [
    DataStatisticCornService,
    TeamStatisticCornService,
    ContentStatisticCleanCornService,
    PlatformAccountCornService
  ]
})
export class CronModule {}
