import { ForbiddenException, Inject, NotFoundException, Injectable } from '@nestjs/common'
import { BrowserPublishRequest, PatchStatusRequest } from './task.dto'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types, UpdateQuery } from 'mongoose'
import { FastifyRequest } from 'fastify'
import { EventEmitter2 } from '@nestjs/event-emitter'
import {
  TaskEntity,
  BrowserPublishStatisticEntity,
  TaskSetEntity,
  ContentEntity,
  ContentStatisticEntity,
  MemberEntity,
  TeamEntity,
  TrafficBillingEntity,
  TeamComponentEntity
} from '@yxr/mongo'
import {
  ContentType,
  EventNames,
  PublishChannel,
  StageStatus,
  TaskAuditStatusEvent,
  TaskStages,
  TeamRoleNames
} from '@yxr/common'
import { ExportTaskSetsRequest } from './taskSet.dto'
import { WebhookEventEmitterService } from '../webhook-event/webhook-event-emitter.service'
import dayjs from 'dayjs'
import { createObjectCsvStringifier } from 'csv-writer'
import { TlsService } from '@yxr/huoshan'
import axios from 'axios'

@Injectable()
export class TaskService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(BrowserPublishStatisticEntity.name)
    private browserPublishStatisticModel: Model<BrowserPublishStatisticEntity>,
    @InjectModel(TrafficBillingEntity.name)
    private trafficBillingModel: Model<TrafficBillingEntity>,
    private eventEmitter: EventEmitter2,
    private loggerService: TlsService,
    @InjectModel(TeamComponentEntity.name)
    private teamComponentModel: Model<TeamComponentEntity>,
    private readonly webhookEventEmitterService: WebhookEventEmitterService
  ) {}

  async patchStatus(taskId: string, request: PatchStatusRequest) {
    const { teamId: currentTeamId } = this.request.session

    const task = await this.taskModel
      .findOne({
        taskId: taskId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('taskSetId contentId publishChannel publishType stages stageStatus')

    if (!task) {
      throw new NotFoundException('任务不存在')
    }
    if (task.stages === TaskStages.Success || task.stageStatus === StageStatus.Fail) {
      console.log('任务已结束,不再更新')
      return
    }
    if (task.stages !== request.stages || task.stageStatus !== request.stageStatus) {
      await this.loggerService.info(this.request, '任务状态变更日志', {
        taskId: taskId,
        task_stages: task.stages,
        task_stageStatus: task.stageStatus,
        request_stages: request.stages,
        request_stageStatus: request.stageStatus
      })
    }

    const update: UpdateQuery<TaskEntity> = {}
    if (request.documentId) {
      update.documentId = request.documentId
    }
    if (request.publishId) {
      update.publishId = request.publishId
    }
    if (request.openUrl) {
      update.openUrl = request.openUrl
    }
    if (request.mediaType) {
      update.mediaType = request.mediaType
    }
    if (request.stages !== TaskStages.NotFount) {
      //未找到作品只更新信息
      if (request.stages) {
        update.stages = request.stages
      }
      if (request.stageStatus) {
        update.stageStatus = request.stageStatus
      }
    }

    update.errorMessage = request.errorMessage ?? ''
    update.stageTime = new Date()
    await this.taskModel.updateOne(
      {
        taskId: taskId,
        teamId: new Types.ObjectId(currentTeamId)
      },
      update
    )

    const content = await this.contentModel
      .findOne({
        _id: new Types.ObjectId(task.contentId)
      })
      .select('teamId videoSize userId isAppContent superId superLockId')

    //云发布视频流量扣除
    if (
      task.publishType == 'video' &&
      (task.publishChannel == PublishChannel.cloud || content.isAppContent === true) &&
      request.stages !== TaskStages.Upload &&
      request.stageStatus == StageStatus.Success
    ) {
      const charged = await this.trafficBillingModel.findOne({
        taskId: taskId
      })
      if (!charged) {
        await this.teamModel.updateOne(
          {
            _id: new Types.ObjectId(content.teamId)
          },
          {
            $inc: {
              useNetworkTraffic: content.videoSize
            }
          }
        )
        //流量扣费记录
        await this.trafficBillingModel.create({
          teamId: new Types.ObjectId(content.teamId),
          userId: new Types.ObjectId(content.userId),
          taskId: taskId,
          useNetworkTraffic: content.videoSize
        })
        await this.loggerService.info(this.request, '云发布流量扣费成功', {
          taskId: taskId,
          useNetworkTraffic: content.videoSize
        })
      }
    }

    if (update.stages == TaskStages.Upload || update.stages == TaskStages.Push) {
      // 触发任务状态变更事件
      await this.eventEmitter.emitAsync(
        EventNames.TaskAuditStatusChangedEvent,
        new TaskAuditStatusEvent(currentTeamId, task.taskSetId)
      )

      if (content.superId && content.superLockId) {
        if (request.stageStatus === StageStatus.Fail) {
          const superdir = await this.teamComponentModel
            .findOne({
              teamId: new Types.ObjectId(content.teamId),
              name: 'superdir'
            })
            .lean()

          const res = await axios.post(
            `${process.env.SUPER_API_URL}/v1/openapi/matrix/video/release`,
            {
              lock_source: 'douyin_yixiaoer',
              video_ids: [Number(content.superId)]
            },
            {
              headers: {
                Token: superdir?.componentArgs?.token
              }
            }
          )

          await this.loggerService.info(this.request, '超级编导-失败 Fail', {
            taskId: taskId,
            task_stages: task.stages,
            task_stageStatus: task.stageStatus,
            request_stages: request.stages,
            request_stageStatus: request.stageStatus,
            res: JSON.stringify(res.data),
            superId: content.superId
          })
        } else if (
          update.stages === TaskStages.Push &&
          request.stageStatus === StageStatus.Success
        ) {
          const superdir = await this.teamComponentModel
            .findOne({
              teamId: new Types.ObjectId(content.teamId),
              name: 'superdir'
            })
            .lean()

          const res = await axios.post(
            `${process.env.SUPER_API_URL}/v1/openapi/matrix/video/publish`,
            {
              lock_source: 'douyin_yixiaoer',
              video_id: Number(content.superId)
            },
            {
              headers: {
                Token: superdir?.componentArgs?.token
              }
            }
          )

          await this.loggerService.info(this.request, '超级编导-成功 Success', {
            taskId: taskId,
            task_stages: task.stages,
            task_stageStatus: task.stageStatus,
            request_stages: request.stages,
            request_stageStatus: request.stageStatus,
            res: JSON.stringify(res.data),
            superId: content.superId
          })
        }
      }
    }
  }

  async getTaskStatus(taskId: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const task = await this.taskModel.findOne({
      taskId: taskId,
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!task) {
      throw new NotFoundException('任务不存在')
    }
  }

  async postBrowserPublish(body: BrowserPublishRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const browserPublish = await this.browserPublishStatisticModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId),
      publishType: body.publishType
    })

    if (browserPublish) {
      await this.browserPublishStatisticModel.updateOne(
        {
          _id: browserPublish._id
        },
        {
          $inc: {
            publishCount: 1
          }
        }
      )
    } else {
      await this.browserPublishStatisticModel.create({
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(currentUserId),
        publishType: body.publishType,
        publishCount: 1
      })
    }
  }

  async exportTasks(query: ExportTaskSetsRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const filter: FilterQuery<TaskSetEntity> = {}
    filter.teamId = new Types.ObjectId(currentTeamId)

    const thirtyDaysAgo = dayjs().tz('Asia/Shanghai').subtract(30, 'day').startOf('day').unix()
    if (thirtyDaysAgo > query.publishStartTime) {
      throw new ForbiddenException('只能导出最近30天的数据')
    }
    if (query.publishEndTime && query.publishStartTime) {
      filter.createdAt = {
        $lt: new Date(query.publishEndTime),
        $gt: new Date(query.publishStartTime)
      }
    }
    if (query.publishType) {
      filter.publishType = query.publishType
    }
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      filter.userId = new Types.ObjectId(currentUserId)
    } else {
      if (query.userId) {
        filter.userId = new Types.ObjectId(query.userId)
      }
    }
    const result = await this.taskModel
      .find(filter)
      .select('userId teamId platformAccountId contentId publishId stages openUrl')
      .sort({ createdAt: -1 })
      .limit(20000)

    if (!result || result.length === 0) {
      return []
    }
    const data = await Promise.all(
      result.map(async (item) => {
        const content = await this.contentModel
          .findOne({
            _id: new Types.ObjectId(item.contentId),
            teamId: new Types.ObjectId(item.teamId)
          })
          .select(
            'desc createdAt publishType nickName phone platformName platformAccountName isDraft'
          )

        let contentStatistic: any = {}
        if (item.publishId) {
          contentStatistic = await this.contentStatisticModel
            .findOne({
              publishId: item.publishId,
              teamId: new Types.ObjectId(item.teamId)
            })
            .select('play great comment share collect updatedAt')
        }

        if (item.stages !== TaskStages.Success || !item.publishId || !contentStatistic) {
          return {
            desc: content.desc,
            createdAt: dayjs(content.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
            pulishType: content.publishType,
            nickName: content.nickName,
            phone: content.phone,
            platformName: content.platformName,
            platformAccountName: content.platformAccountName,
            isDraft: content.isDraft === true ? '是' : '否',
            viewCount: 0,
            greatCount: 0,
            commentCount: 0,
            shareCount: 0,
            collectCount: 0,
            updatedAt: null,
            openUrl: null
          }
        }

        return {
          desc: content.desc,
          createdAt: dayjs(content.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
          pulishType: content.publishType,
          nickName: content.nickName,
          phone: content.phone,
          platformName: content.platformName,
          platformAccountName: content.platformAccountName,
          isDraft: content.isDraft === true ? '是' : '否',
          viewCount:
            content.publishType === ContentType.video ||
            content.publishType === ContentType.miniVideo
              ? contentStatistic.play
              : contentStatistic.read,
          greatCount: contentStatistic.great,
          commentCount: contentStatistic.comment,
          shareCount: contentStatistic.share,
          collectCount: contentStatistic.collect,
          updatedAt: dayjs(contentStatistic.updatedAt)
            .tz('Asia/Shanghai')
            .format('YYYY-MM-DD HH:mm:ss'),
          openUrl: item.openUrl
        }
      })
    )

    return data
  }

  async generateCsv(data: any[], headers: { id: string; title: string }[]): Promise<string> {
    // 创建 CSV 字符串生成器
    const csvStringifier = createObjectCsvStringifier({ header: headers })

    // 拼接 CSV 内容
    const headerString = csvStringifier.getHeaderString()
    const recordsString = csvStringifier.stringifyRecords(data)

    return headerString + recordsString
  }
}
