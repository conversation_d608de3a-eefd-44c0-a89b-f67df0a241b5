import { Module } from '@nestjs/common'
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core'
import { AuthModule } from './common/auth/auth.module'
import { OptimizedUnifiedTokenGuard } from './common/auth/guards/unified-token.guard'
import { AccessControlGuard } from './common/guards/access-control.guard'
import { MemberModule } from './modules/member/member.module'
import { UserModule } from './modules/user/user.module'
import { OverviewModule } from './modules/overview/overview.module'
import { baseModule } from './common/baseModule'
import { GlobalExceptionFilter } from './common/filters'
import { ResponseTransformInterceptor } from './common/interceptors'
import { TeamModule } from './modules/team/team.module'
import { WebhookModule } from './modules/webhook/webhook.module'
import { OnlineScriptModule } from './modules/online-script/online-script.module'
import { OrderModule } from './modules/order/order.module'
import { AdminOssModule } from './modules/ali-oss/admin-oss.module'
import { AdModule } from './modules/ad/ad.module'
import { KuaidaialiModule } from './modules/kuaidaiali/kuaidaiali.module'
import { ChannelModule } from './modules/channel/channel.module'
import { PlatformAccountModule } from './modules/platform-account/platform-account.module'
import { MessageModule } from './modules/message/message.module'
import { CronModule } from './modules/cron/cron.module'
import { HuoshanModule } from '@yxr/huoshan'
import { OpenPlatformModule } from './modules/open-platform/open-platform.module'
import { StatisticsModule } from './modules/statistics/statistics.module'

@Module({
  imports: [
    ...baseModule,
    // 导入统一认证模块（全局模块，其他模块自动可用）
    AuthModule,
    MemberModule,
    UserModule,
    OverviewModule,
    TeamModule,
    WebhookModule,
    OnlineScriptModule,
    OrderModule,
    AdminOssModule,
    AdModule,
    KuaidaialiModule,
    ChannelModule,
    PlatformAccountModule,
    MessageModule,
    HuoshanModule,
    CronModule,
    OpenPlatformModule,
    StatisticsModule
  ],
  providers: [
    // 使用优化后的认证守卫（第一层：身份认证）
    { provide: APP_GUARD, useClass: OptimizedUnifiedTokenGuard },
    // 访问控制守卫（第二层：权限验证）
    { provide: APP_GUARD, useClass: AccessControlGuard },
    { provide: APP_FILTER, useClass: GlobalExceptionFilter },
    { provide: APP_INTERCEPTOR, useClass: ResponseTransformInterceptor }
  ]
})
export class AdminModule {}
