import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { OverviewService } from '../overview/overview.service'
import {
  AllPlatforms,
  ContentType,
  FirstLevelPlatform,
  StageStatus,
  StatisticCommonService,
  TaskStages
} from '@yxr/common'
import dayjs from 'dayjs'
import { Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import {
  ContentStatisticEntity,
  ContentTrendEntity,
  PlatformAccountEntity,
  PlatformAccountSummaryEntity,
  PlatformAccountTrendEntity,
  PlatformDataStatisticEntity,
  TaskEntity,
  TeamEntity
} from '@yxr/mongo'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class PlatformAccountCornService implements OnModuleInit {
  private lockValue: string = 'handleAccountOnlineScale'
  private lockPlatformDataValue: string = 'handlePlatformData'
  private lockContentTrendValue: string = 'handleContentTrend'
  private readonly lockPrefix = 'lock:'

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformDataStatisticEntity.name)
    private platformDataStatisticModel: Model<PlatformDataStatisticEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @InjectModel(PlatformAccountTrendEntity.name)
    private platformAccountTrendModel: Model<PlatformAccountTrendEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @InjectModel(ContentTrendEntity.name)
    private contentTrendModel: Model<ContentTrendEntity>,
    private readonly loggerService: TlsService,
    private readonly overviewService: OverviewService,
    private readonly statisticCommonService: StatisticCommonService
  ) {}

  async onModuleInit() {
    // const currentDate = new Date()
    // for (let i = 30; i >= 1; i--) {
    //   const prevDate = new Date(currentDate)
    //   prevDate.setDate(currentDate.getDate() - i)
    //   // await this.contentTrendStatisticWorker(dayjs(prevDate).format('YYYY-MM-DD'))
    //   await this.platformDataStatisticWorker(dayjs(prevDate).format('YYYY-MM-DD'))
    // }
  }

  /**
   * 登录保持统计定时 
   * 每天00:05 分执行
   */
  @Cron('0 5 0 * * *', {
    name: 'accountOnlineScale',
    timeZone: 'Asia/Shanghai'
  })
  async AccountOnlineScaleCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        await this.overviewService.getOnlineScale()
      } catch (e) {
        await this.loggerService.error(null, '登录保持统计定时处理失败', {
          error: e
        })
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  /**
   * 平台数据统计定时
   * 每天00:10 分执行
   */ 
  @Cron('0 10 0 * * *', {
    name: 'platformDataStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async platformDataStatisticCronTask() {
    if (await this.acquireLock(this.lockPlatformDataValue, 60)) {
      try {
        const yesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
        await this.platformDataStatisticWorker(yesterday)
      } catch (e) {
        await this.loggerService.error(null, '账号数据归档定时处理失败', {
          error: e
        })
      } finally {
        await this.releaseLock(this.lockPlatformDataValue)
      }
    }
  }

  /**
   * 平台相关数据统计执行
   */
  async platformDataStatisticWorker(yesterday: string) {
    const beijingStart = dayjs(yesterday).tz('Asia/Shanghai').startOf('day')
    const beijingEnd = dayjs(yesterday).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfYesterday = beijingStart.utc().toDate()
    const endOfYesterday = beijingEnd.utc().toDate()
    const platforms = AllPlatforms
    for (const platform of platforms) {
      const increment = await this.platformDataStatisticModel.findOne({
        platformName: platform,
        createTime: yesterday
      })

      const count = await this.platformAccountModel.countDocuments({
        platformName: platform,
        createdAt: {
          $gt: startOfYesterday,
          $lte: endOfYesterday
        }
      })

      const failPublish = await this.taskModel.aggregate([
        {
          $match: {
            createdAt: {
              $gt: startOfYesterday,
              $lt: endOfYesterday
            },
            stages: { $in: [TaskStages.Upload, TaskStages.Push] },
            stageStatus: StageStatus.Fail
          }
        },
        {
          $lookup: {
            from: 'platformaccountentities',
            let: { platformAccountId: '$platformAccountId' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$platformAccountId'] },
                  platformName: platform
                }
              }
            ],
            as: 'platformAccounts'
          }
        },
        {
          $match: {
            platformAccounts: { $ne: [] }
          }
        },
        {
          $count: 'total'
        }
      ])

      const successPublish = await this.taskModel.aggregate([
        {
          $match: {
            createdAt: {
              $gt: startOfYesterday,
              $lt: endOfYesterday
            },
            stages: {
              $in: [
                TaskStages.Transcoding,
                TaskStages.Scheduled,
                TaskStages.Review,
                TaskStages.Success
              ]
            },
            stageStatus: StageStatus.Success
          }
        },
        {
          $lookup: {
            from: 'platformaccountentities',
            let: { platformAccountId: '$platformAccountId' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$platformAccountId'] },
                  platformName: platform
                }
              }
            ],
            as: 'platformAccounts'
          }
        },
        {
          $match: {
            platformAccounts: { $ne: [] }
          }
        },
        {
          $count: 'total'
        }
      ])

      if (increment) {
        await this.platformDataStatisticModel.updateOne(
          {
            platformName: platform,
            createTime: yesterday
          },
          {
            $set: {
              incrementAccountTotal: count,
              failPublishTotal: failPublish[0]?.total ?? 0,
              successPublishTotal: successPublish[0]?.total ?? 0
            }
          }
        )
      } else {
        await this.platformDataStatisticModel.insertOne({
          platformName: platform,
          createTime: yesterday,
          incrementAccountTotal: count,
          failPublishTotal: failPublish[0]?.total ?? 0,
          successPublishTotal: successPublish[0]?.total ?? 0
        })
      }
    }
  }

  /**
   * 统计作品趋势数据归档
   */
  @Cron('0 30 1 * * *', {
    name: 'contentTrendStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async contentTrendCronTask() {
    if (await this.acquireLock(this.lockContentTrendValue, 60)) {
      try {
        const yesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
        await this.contentTrendStatisticWorker(yesterday)
      } catch (e) {
        await this.loggerService.error(null, '作品趋势数据归档定时处理失败', {
          error: e
        })
      } finally {
        await this.releaseLock(this.lockContentTrendValue)
      }
    }
  }

  async contentTrendStatisticWorker(yesterday: string) {
    const batchSize = 500 // 每批次查询的数量
    const totalTeams = await this.teamModel.countDocuments({
      accountCount: { $gt: 0 }
    })
    const totalBatches = Math.ceil(totalTeams / batchSize)
    for (let i = 0; i < totalBatches; i++) {
      const teams = await this.teamModel
        .find({
          accountCount: { $gt: 0 }
        })
        .skip(i * batchSize)
        .limit(batchSize)
        .select('_id')

      for (const team of teams) {
        const platformAccounts = await this.platformAccountModel
          .find({
            teamId: new Types.ObjectId(team._id),
            platformName: { $in: FirstLevelPlatform }
          })
          .select('_id platformName')
          .lean()
        const platformNames = [...new Set(platformAccounts.map((item) => item.platformName))]
        if (platformNames.length > 0) {
          for (const platformName of platformNames) {
            // 账号概览归档
            const platformAccountIds = platformAccounts
              .filter((account) => account.platformName === platformName)
              .map((account) => new Types.ObjectId(account._id))
            const fields = await this.statisticCommonService.getAccountOverviewField(platformName)
            const groupStage: any = {
              _id: '$platformName'
            }
            fields.forEach((item) => {
              groupStage[item.key] = { $sum: `$${item.key}` }
            })
            const result = await this.platformAccountSummaryModel.aggregate([
              {
                $match: {
                  teamId: new Types.ObjectId(team._id),
                  platformName: platformName,
                  platformAccountId: { $in: platformAccountIds }
                }
              },
              { $group: groupStage },
              {
                $project: {
                  _id: 0,
                  ...fields.reduce((acc, item) => {
                    // 动态保留其他字段
                    acc[item.key] = 1
                    return acc
                  }, {})
                }
              }
            ])
            if (result[0]) {
              console.log(result[0], 'platformAccountSummary')
              const platformAccountTrend = await this.platformAccountTrendModel.findOne({
                teamId: team._id,
                platformName: platformName,
                createTime: yesterday
              })
              if (platformAccountTrend) {
                await this.platformAccountTrendModel.updateOne(
                  {
                    teamId: team._id,
                    createTime: yesterday,
                    platformName: platformName
                  },
                  {
                    $set: {
                      ...result[0]
                    }
                  }
                )
              } else {
                await this.platformAccountTrendModel.insertOne({
                  teamId: team._id,
                  createTime: yesterday,
                  platformName: platformName,
                  ...result[0]
                })
              }
            }

            //作品概览归档
            const platformContentTypes = StatisticCommonService.platformContentTypes[platformName]
            if (platformContentTypes.length > 0) {
              for (const contentType of platformContentTypes) {
                let type = contentType
                if (contentType === ContentType.all) {
                  type = null
                }
                const contentFields = await this.statisticCommonService.getContentOverviewField(
                  platformName,
                  type
                )
                // 动态构建 $group 语句
                const contentGroupStage: any = {
                  _id: '$platformName'
                }
                contentFields.forEach((item) => {
                  contentGroupStage[item.key] = { $sum: `$${item.key}` }
                })
                const contentWhere: any = {
                  $match: {
                    teamId: new Types.ObjectId(team._id),
                    platformName: platformName,
                    platformAccountId: { $in: platformAccountIds }
                  }
                }
                if (type) {
                  contentWhere.$match.contentType = type
                }

                // 进行聚合查询
                const contentResult = await this.contentStatisticModel.aggregate([
                  contentWhere,
                  { $group: contentGroupStage },
                  {
                    $project: {
                      _id: 0,
                      ...contentFields.reduce((acc, item) => {
                        // 动态保留其他字段
                        acc[item.key] = 1
                        return acc
                      }, {})
                    }
                  }
                ])

                if (contentResult[0]) {
                  const contentTrend = await this.contentTrendModel.findOne({
                    teamId: team._id,
                    createTime: yesterday,
                    platformName: platformName,
                    contentType: type
                  })
                  if (contentTrend) {
                    await this.contentTrendModel.updateOne(
                      {
                        teamId: team._id,
                        createTime: yesterday,
                        platformName: platformName,
                        contentType: type
                      },
                      {
                        $set: {
                          ...contentResult[0]
                        }
                      }
                    )
                  } else {
                    await this.contentTrendModel.insertOne({
                      teamId: team._id,
                      createTime: yesterday,
                      platformName: platformName,
                      contentType: type,
                      ...contentResult[0]
                    })
                  }
                }
              }
            }
          }
        }
      }
      console.log(`已插入批次: ${i + 1}/${totalBatches}`)
    }
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
