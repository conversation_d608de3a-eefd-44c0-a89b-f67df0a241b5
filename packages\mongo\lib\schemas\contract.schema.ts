import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class ContractEntity {
  /**
   * 订单id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  orderId: Types.ObjectId

  /**
   * 团队id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 订单编号
   */
  @Prop({
    type: String,
    required: true
  })
  orderNo: string

  /**
   * 权益包id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  interestId: Types.ObjectId

  /**
   * 权益包数量
   */
  @Prop({
    type: Number,
    required: true
  })
  interestCount: number

  /**
   * 合同开始时间
   */
  @Prop({
    type: Date,
    required: true
  })
  startTime: Date

  /**
   * 合同结束时间
   */
  @Prop({
    type: Date,
    required: true
  })
  endTime: Date

  /**
   * 实付金额
   */
  @Prop({
    type: Number,
    required: true
  })
  payAmount: number

  /**
   * 创建时间
   */
  @Prop({
    type: Date
  })
  createdAt?: Date

  /**
   * 是否退费
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isRefund?: boolean

  /**
   * 是否免费- 升级权益包时，原权益包为免费
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isFree?: boolean
}

export const ContractSchema: ModelDefinition = {
  name: ContractEntity.name,
  schema: SchemaFactory.createForClass(ContractEntity)
}

export const ContractMongoose = MongooseModule.forFeature([ContractSchema])
