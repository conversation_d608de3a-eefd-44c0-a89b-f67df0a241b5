import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformAccountSummaryEntity {
  //账号ID
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false,
    index: true
  })
  platformName: string

  //总粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansTotal: number

  //总播放
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  playTotal: number

  //总点赞
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  likesTotal: number

  //总评论
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  commentsTotal: number

  //总分享
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  sharesTotal: number

  //总收藏
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  favoritesTotal: number

  //净增粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  netFansGrowth: number

  //取关粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansLost: number

  //涨粉
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansGrowth: number

  //推荐数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  recommendationsTotal: number

  //作品数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  worksTotal: number

  //主页访问数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  profileVisits: number

  //完播率
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  completionRate: number

  //曝光量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  impressionsTotal: number

  //粉丝曝光量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fanImpressions: number

  //观看时长
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  watchDuration: number

  //阅读文章篇数或播放视频数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  viewsTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  zhuanZanPingTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  readTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  blogTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  incomeTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  danmuTotal: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const PlatformAccountSummarySchema: ModelDefinition = {
  name: PlatformAccountSummaryEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountSummaryEntity)
}

export const PlatformAccountSummaryMongoose = MongooseModule.forFeature([
  PlatformAccountSummarySchema
])
