import {
  ConflictException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import {
  getAppTaskPagedListRequest,
  AppPublishTaskDetailsDto,
  AppTaskDetailsDto,
  AppTaskSetCreateRequest,
  AppTaskSetPagedListResponse
} from './appTask.dto'
import { Error, Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { ContentEntity, PlatformAccountEntity, TaskEntity, TaskSetEntity } from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { getTeamOnlineUsersRedisKey } from '@yxr/utils'
import { PlatformNameEnum, PublishChannel } from '@yxr/common'
import { TaskCloudPushDTO, TaskDTO, YxrOpenPlatformService } from '@yxr/yxr-open-platform'

@Injectable()
export class AppTaskService {
  logger = new Logger('AppTaskService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly yxrOpenPlatformService: YxrOpenPlatformService
  ) {}

  /**
   * @deprecated 已废弃
   * 获取移动端分发任务集列表
   * @param query
   */
  async getPublishTaskSets(
    query: getAppTaskPagedListRequest
  ): Promise<AppTaskSetPagedListResponse> {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * @deprecated 已废弃
   * 获取移动端分发任务列表
   * @param query
   */
  async getPublishTasks(taskSetId: string): Promise<AppTaskDetailsDto[]> {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * @deprecated 已废弃
   * 创建移动端分发任务
   * @param input
   */
  async createTasks(input: AppTaskSetCreateRequest) {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * @deprecated 已废弃
   * 获取可发布的任务集对象详情
   * @param taskSetId
   * @param version
   */
  async getPublishTaskSetDetails(
    taskSetId: string,
    version: number
  ): Promise<AppPublishTaskDetailsDto[]> {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * 获取可发布的任务集对象
   * @param taskSetId
   */
  async getPublishTaskSet(taskSetId: string): Promise<TaskCloudPushDTO> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const message = '无法获取发布任务, 该任务不存在或已经分配给其他客户端!'

    const taskSet = await this.taskSetModel
      .findOne({
        teamId: new Types.ObjectId(currentTeamId),
        taskIdentityId: taskSetId,
        progressToken: { $eq: null },
        isAppContent: true,
        publishChannel: PublishChannel.local
      })
      .select('taskIdentityId publishArgs publishType isAppContent teamId userId')

    if (taskSet) {
      try {
        taskSet.progressToken = currentUserId
        // 这里记录分配给了谁?

        await taskSet.save()
      } catch (error) {
        if (error instanceof Error.VersionError) {
          // 不可重试, 应该放弃, TODO: 特殊的错误码
          this.logger.warn(error.message)
          throw new ConflictException(message)
        } else {
          // 服务器未知异常, 通常可以重试
          this.logger.error(error)
          throw new HttpException('网络异常, 请稍后再试', -1)
        }
      }
    } else {
      throw new NotFoundException(message)
    }

    const contents = await this.contentModel
      .find({
        taskSetId: taskSetId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('platformAccountId cover video _id')

    const tasks = await this.taskModel
      .find({
        taskSetId: taskSetId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('_id platformAccountId taskId contentId')
    const resTaskMap = tasks.reduce((ts, task) => {
      ts[task.contentId.toString()] = task
      return ts
    }, {})

    const grouped: TaskDTO[] = [] //数据组装
    for (const item of contents) {
      const platformAccount = await this.platformAccountModel
        .findById(item.platformAccountId)
        .select(
          '_id platformAuthorId platformAccountName platformName spaceId kuaidailiArea parentId token'
        )
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }

      const platformAccountObject: TaskDTO = {
        taskId: resTaskMap[item._id.toString()].taskId,
        platform: platformAccount.platformName,
        videoPath: item.video,
        cover: item.cover,
        platformAccount: {
          platformAccountId: platformAccount._id.toString(),
          platformAuthorId: platformAccount.platformAuthorId,
          cookie: cookie
        }
      }

      grouped.push(platformAccountObject)
    }

    return {
      callBackData: {
        teamId: taskSet.teamId.toString(),
        userId: taskSet.userId.toString()
      },
      publishType: taskSet.publishType,
      formData: taskSet.publishArgs,
      data: Object.values(grouped)
    }
  }

  /**
   * 获取移动端分发任务可用节点数
   * @returns 可用节点数
   */
  getTaskNodeCount() {
    const { teamId: currentTeamId } = this.request.session
    return this.cacheManager.store.client.zcard(getTeamOnlineUsersRedisKey(currentTeamId))
  }
}
