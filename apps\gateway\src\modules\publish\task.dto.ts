import { ApiProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator'
import { StageStatus, TaskStages, TaskStatusEmun } from '@yxr/common'

export class BrowserPublishRequest {
  @ApiProperty({
    type: String,
    description: '发布类型',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '发布类型不能为空' })
  publishType: string
}

export class ContentStatisticDTO {
  @ApiProperty({
    type: Number,
    description: '播放或阅读数',
    example: 10
  })
  viewCount?: number

  @ApiProperty({
    type: Number,
    description: '评论数',
    example: 100
  })
  commentCount?: number

  @ApiProperty({
    type: Number,
    description: '点赞数',
    example: 100
  })
  greatCount?: number

  @ApiProperty({
    type: Number,
    description: '分享数',
    example: 100
  })
  shareCount?: number

  @ApiProperty({
    type: Number,
    description: '收藏数',
    example: 100
  })
  collectCount?: number
}

export class TaskDetailResponse {
  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  taskSetId: string

  @ApiProperty({
    type: String,
    example: 'http://xxxxxxxx.png'
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    example: '抖音'
  })
  platformName: string

  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  platformAccountId: string

  @ApiProperty({
    type: String,
    example: '夏天的风'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  taskId: string

  @ApiProperty({
    type: String,
    example: 'd334444'
  })
  documentId: string

  @ApiProperty({
    type: String,
    example: 'dd1123333'
  })
  publishId: string

  @ApiProperty({
    description: '编辑类型',
    type: String
  })
  publishType: string

  @ApiProperty({
    type: String,
    description: '发布类型'
  })
  mediaType: string

  @ApiProperty({
    type: String,
    example: '***********'
  })
  phone: string

  @ApiProperty({
    type: String,
    example: '张山'
  })
  nickName: string

  @ApiProperty({
    type: String,
    example: 'https://xxxxxxxx.png'
  })
  cover: string

  @ApiProperty({
    type: String,
    example: '视频描述'
  })
  desc: string

  @ApiProperty({
    type: String,
    example: '错误信息',
    description: '任务新版错误信息',
    deprecated: true
  })
  errorMessage: string

  @ApiProperty({
    type: Number,
    example: '1231094801'
  })
  createdAt: number

  @ApiProperty({
    type: String,
    enum: StageStatus,
    description: '任务阶段状态',
    example: StageStatus.Success,
    required: true
  })
  stageStatus: StageStatus

  @ApiProperty({
    type: String,
    enum: TaskStages,
    description: '任务阶段upload上传,push推送,transcoding转码,review审核,scheduled定时,success成功',
    example: TaskStages.Upload,
    required: true
  })
  stages: TaskStages

  @ApiProperty({
    type: Number,
    description: '是否定时任务',
    required: false
  })
  isTimed?: number

  @ApiProperty({
    type: ContentStatisticDTO,
    description: '统计数据',
    required: false
  })
  statistic?: ContentStatisticDTO

  @ApiProperty({
    type: String,
    description: '审核成功后可访问内容的链接地址(ps:微信公众号是一个JSON数组)',
    example: 'https://www.bilibili.com/video/BV1qvyNY8EpF/'
  })
  openUrl: string

  @ApiProperty({
    type: String,
    enum: TaskStatusEmun,
    description: '任务状态,正常为空,cancel已取消, deleted已删除',
    example: TaskStatusEmun.Cancel
  })
  taskStatus: TaskStatusEmun
}

export class PatchStatusRequest {
  @ApiProperty({
    type: String,
    deprecated: true,
    description:
      '发布任务投递令牌(多客户端任务分发场景下用于避免任务在多客户端被多次投递, 此时更新状态必须提供次令牌, 只有令牌一致时才允许更新状态, 令牌不一致会导致 409 异常, 此时应该终止发布流程避免重复投递)',
    example: 'eBLWxpPcYBqE9DbFVE920',
    required: false
  })
  @IsOptional()
  @IsString()
  deliveryToken: string

  @ApiProperty({
    type: String,
    description: '审核成功后平台分配的文档Id',
    example: '113322041018191',
    required: false
  })
  @IsOptional()
  @IsString()
  documentId: string

  @ApiProperty({
    type: String,
    description: '投递成功后平台分配的任务发布Id',
    example:
      'rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkia2OibbRgfadviaYjyVyoBSYj1F0qv6s2kJIic4vRe4I1DRiaASUBkWyFSAibic6CzEs9XibFAR0iacrjCJLibftgm6TcibMnQ',
    required: false
  })
  @IsOptional()
  @IsString()
  publishId: string

  @ApiProperty({
    type: String,
    description: '审核成功后可访问内容的链接地址',
    example: 'https://www.bilibili.com/video/BV1qvyNY8EpF/',
    required: false
  })
  @IsOptional()
  @IsString()
  openUrl: string

  @ApiProperty({
    type: String,
    enum: StageStatus,
    description: '任务阶段状态',
    example: StageStatus.Success,
    required: true
  })
  @IsEnum(StageStatus, {
    message: `任务阶段状态不正确`
  })
  stageStatus: StageStatus

  @ApiProperty({
    type: String,
    enum: TaskStages,
    description: '任务阶段upload上传,push推送,transcoding转码,review审核,scheduled定时,success成功',
    example: TaskStages.Upload,
    required: true
  })
  @IsEnum(TaskStages, { message: '任务阶段不正确' })
  stages: TaskStages

  @ApiProperty({
    type: String,
    description: '在某个阶段的错误信息',
    example: '内容违反社区公约, 不适合对外展示',
    required: false
  })
  @IsOptional()
  @IsString()
  errorMessage: string

  @ApiProperty({
    type: String,
    description: '媒体类型(横板视频,竖版视频,图文,文章)',
    required: false
  })
  @IsString()
  @IsOptional()
  mediaType: string
}
