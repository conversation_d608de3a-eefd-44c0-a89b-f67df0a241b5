import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsMobilePhone, IsOptional, IsEnum } from 'class-validator'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { InvitationStatus, OpenPlatformRoleNames } from '@yxr/common'

export class CreateInvitationRequestDto {
  @ApiProperty({
    description: '被邀请人手机号',
    example: '13800138000'
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string

  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  applicationId: string

  @ApiProperty({
    description: '邀请角色类型',
    example: 'channel',
    enum: OpenPlatformRoleNames.All,
    required: false,
    default: OpenPlatformRoleNames.CHANNEL
  })
  @IsOptional()
  @IsEnum(OpenPlatformRoleNames.All, { message: '角色类型无效' })
  invitedRole?: string = OpenPlatformRoleNames.CHANNEL

  @ApiProperty({
    description: '邀请备注',
    example: '邀请加入渠道合作',
    required: false
  })
  @IsOptional()
  @IsString()
  remark?: string
}

export class HandleInvitationRequestDto {
  @ApiProperty({
    description: '处理动作',
    example: 'accept',
  })
  @IsNotEmpty()
  action: 'accept' | 'reject'
}

export class InvitationDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiResponseProperty({
    example: '我的应用'
  })
  applicationName: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  inviterId: string

  @ApiResponseProperty({
    example: '13800138001'
  })
  inviterPhone: string

  @ApiResponseProperty({
    example: '张三'
  })
  inviterNickname: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  inviteeId: string

  @ApiResponseProperty({
    example: '13800138000'
  })
  inviteePhone: string

  @ApiResponseProperty({
    example: '李四'
  })
  inviteeNickname: string

  @ApiResponseProperty({
    example: 0,
  })
  status: number

  @ApiResponseProperty({
    example: 'channel'
  })
  invitedRole: string

  @ApiResponseProperty({
    example: '邀请加入渠道合作'
  })
  remark: string

  @ApiResponseProperty({
    example: 1640995200000
  })
  createdAt: number

  @ApiResponseProperty({
    example: 1640995200000
  })
  updatedAt: number
}

export class InvitationListRequestDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  size?: number = 10

  @ApiProperty({
    description: '邀请状态筛选',
    example: 0,
    required: false
  })
  @IsOptional()
  status?: string

  @ApiProperty({
    description: '邀请类型',
    example: 'sent',
    required: false
  })
  @IsOptional()
  type?: 'sent' | 'received'
}

export class InvitationListResponseDto {
  @ApiResponseProperty({
    type: [InvitationDto]
  })
  data: InvitationDto[]

  @ApiResponseProperty({
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  size: number
}

export class CreateInvitationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationDto
  })
  data: InvitationDto
}

export class GetInvitationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationDto
  })
  data: InvitationDto
}

export class GetInvitationListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationListResponseDto
  })
  data: InvitationListResponseDto
}

export class HandleInvitationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Object
  })
  data: {
    message: string
    status: number
  }
}
