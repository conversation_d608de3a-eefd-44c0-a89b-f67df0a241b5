import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiBadRequestResponse, ApiHeader, ApiOkResponse, ApiOperation, ApiQuery, ApiTags, ApiUnauthorizedResponse } from "@nestjs/swagger";
import { BaseBadRequestResponseDTO, BaseUnauthorizedResponseDTO } from "../../common/dto/BaseResponseDTO";
import { GroupsDetailResponseDTO, GroupsListResponseDTO, PatchGroupsRequest, PostGroupsRequest } from "./group.dto";
import { GroupService } from "./group.service";

@Controller('groups')
@ApiTags("分组管理")
export class GroupController{

    constructor(private readonly groupService: GroupService) { }

    @Post()
    @ApiOperation({ summary: '创建分组' })
    @ApiOkResponse({ type: GroupsDetailResponseDTO })
    @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
    @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
    @ApiHeader({ name: 'authorization', required: true })
    async postGroups(
        @Body() body: PostGroupsRequest
    ){
        return await this.groupService.postGroupsAsync(body)
    }

    @Get()
    @ApiOperation({ summary: '分组列表' })
    @ApiOkResponse({ type: GroupsListResponseDTO })
    @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
    @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
    @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
    @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
    @ApiQuery({ name: 'name', required: false, type: String, description: '分组名称' })
    @ApiHeader({ name: 'authorization', required: true })
    async getGroups(
        @Query('name') name: string,
        @Query('size', { transform: (value) => value || 10 }) size: number,
        @Query('page', { transform: (value) => value || 1 }) page: number,
    ){
        return await this.groupService.getGroupsAsync(name,page,size)
    }

    @Patch(':groupId')
    @ApiOperation({ summary: '更新分组' })
    @ApiOkResponse({ type: GroupsDetailResponseDTO })
    @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
    @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
    @ApiHeader({ name: 'authorization', required: true })
    async patchGroups(
        @Param('groupId') groupId: string,
        @Body() body: PatchGroupsRequest
    ){
        return await this.groupService.patchGroupsAsync(groupId,body)
    }

    @Delete(':groupId')
    @ApiOperation({ summary: '删除分组' })
    @ApiOkResponse()
    @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
    @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
    @ApiHeader({ name: 'authorization', required: true })
    async deleteGroups(
        @Param('groupId') groupId: string
    ){
        await this.groupService.deleteGroupsAsync(groupId)
    }

}