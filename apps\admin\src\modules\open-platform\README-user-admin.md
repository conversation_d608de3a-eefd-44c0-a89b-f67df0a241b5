# 开放平台用户管理API（管理员专用）

## 概述

开放平台用户管理API为管理员提供了全面的用户管理功能，包括用户列表查询、用户应用详情查看和用户备注管理。这些接口仅限管理员访问，提供了强大的数据分析和管理能力。

## 核心功能

### 1. 开放平台用户管理
- **用户列表查询**: 支持分页、搜索和筛选
- **用户备注管理**: 管理员可以为用户添加备注信息
- **数据统计**: 显示每个用户的应用数量等统计信息

### 2. 用户应用详情管理
- **应用列表查看**: 查看指定用户下的所有应用
- **详细统计信息**: 包括关联用户数、团队数、账号总数、流量使用情况等
- **应用状态监控**: 实时查看应用的运行状态

## 数据库修改

### OpenPlatformUserEntity新增字段
```typescript
@Prop({
  type: String,
  required: false,
  maxlength: 500
})
remark?: string  // 备注信息
```

## API接口

### 1. 获取开放平台用户列表

**接口地址：** `GET /admin/open-platform/users`

**权限要求：** 仅限管理员访问（@AdminOnly装饰器）

**查询参数：**
- `page` (number, 可选): 页码，默认1
- `limit` (number, 可选): 每页数量，默认20，最大100
- `phone` (string, 可选): 手机号码模糊搜索
- `status` (OpenPlatformStatus, 可选): 用户状态筛选

**请求示例：**
```bash
GET /admin/open-platform/users?page=1&limit=20&phone=138&status=ACTIVE
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取开放平台用户列表成功",
  "data": {
    "list": [
      {
        "id": "507f1f77bcf86cd799439011",
        "phone": "***********",
        "nickname": "张三",
        "status": "ACTIVE",
        "applicationCount": 3,
        "remark": "重要客户",
        "createdAt": *************,
        "updatedAt": *************
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5
  }
}
```

**返回字段说明：**
- `id`: 用户ID
- `phone`: 手机号码
- `nickname`: 用户昵称
- `status`: 用户状态（ACTIVE/INACTIVE等）
- `applicationCount`: 该用户创建的应用总数
- `remark`: 管理员备注信息
- `createdAt`: 注册时间
- `updatedAt`: 最后更新时间

### 2. 获取用户应用详情

**接口地址：** `GET /admin/open-platform/users/{userId}/applications`

**权限要求：** 仅限管理员访问

**路径参数：**
- `userId` (string): 用户ID

**请求示例：**
```bash
GET /admin/open-platform/users/507f1f77bcf86cd799439011/applications
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取用户应用详情成功",
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "userPhone": "***********",
    "applications": [
      {
        "appId": "app_123456",
        "name": "我的应用",
        "userCount": 15,
        "teamCount": 8,
        "totalAccountCount": 120,
        "trafficUsage": **********,
        "trafficUsageMB": 1000.5,
        "status": "ACTIVE",
        "createdAt": *************,
        "updatedAt": *************
      }
    ],
    "totalApplications": 3
  }
}
```

**应用详情字段说明：**
- `appId`: 应用ID
- `name`: 应用名称
- `userCount`: 关联用户数（该应用下的user用户数量）
- `teamCount`: 关联团队数（该应用下的团队数量）
- `totalAccountCount`: 账号总数（该应用下所有团队的平台账号总数）
- `trafficUsage`: 流量使用情况（字节）
- `trafficUsageMB`: 流量使用情况（MB，便于阅读）
- `status`: 应用状态
- `createdAt`: 应用注册时间
- `updatedAt`: 最后更新时间

### 3. 修改用户备注

**接口地址：** `PUT /admin/open-platform/users/remark`

**权限要求：** 仅限管理员访问

**请求参数：**
```json
{
  "userId": "507f1f77bcf86cd799439011",
  "remark": "重要客户，需要优先处理"
}
```

**参数说明：**
- `userId` (string): 用户ID
- `remark` (string, 可选): 备注信息，最大500字符，可为空

**响应示例：**
```json
{
  "code": 200,
  "message": "用户备注修改成功",
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "remark": "重要客户，需要优先处理",
    "updatedAt": *************
  }
}
```

## 数据聚合查询优化

### 1. 用户应用数量统计
使用MongoDB聚合管道优化查询性能：
```typescript
const counts = await this.applicationModel.aggregate([
  {
    $match: {
      userId: { $in: userIds }
    }
  },
  {
    $group: {
      _id: '$userId',
      count: { $sum: 1 }
    }
  }
])
```

### 2. 应用统计信息聚合
并行查询多个统计指标，提高查询效率：
```typescript
const [userCount, teamCount, totalAccountCount, trafficStats] = await Promise.all([
  this.getUserCountForApplication(appId),
  this.getTeamCountForApplication(appId),
  this.getAccountCountForApplication(appId),
  this.getTrafficStatsForApplication(appId)
])
```

### 3. 流量统计优化
通过团队关联查询，避免直接关联复杂查询：
```typescript
const trafficStats = await this.trafficBillingModel.aggregate([
  {
    $match: {
      teamId: { $in: teamIds }
    }
  },
  {
    $group: {
      _id: null,
      totalTraffic: { $sum: '$traffic' }
    }
  }
])
```

## 性能优化策略

### 1. 分页查询优化
- 使用`skip`和`limit`进行高效分页
- 并行查询总数和列表数据
- 合理设置每页最大数量限制（100条）

### 2. 索引优化建议
```javascript
// 用户表索引
db.open_platform_users.createIndex({ phone: 1 })
db.open_platform_users.createIndex({ status: 1 })
db.open_platform_users.createIndex({ createdAt: -1 })

// 应用表索引
db.open_platform_applications.createIndex({ userId: 1 })

// 团队表索引
db.teams.createIndex({ latestTeamId: 1 })

// 账号表索引
db.platform_accounts.createIndex({ teamId: 1 })

// 流量计费表索引
db.traffic_billings.createIndex({ teamId: 1 })
```

### 3. 查询缓存策略
- 用户基本信息适当缓存
- 应用统计数据可设置短期缓存
- 流量统计数据可按小时缓存

## 错误处理

### 常见错误场景
1. **参数验证错误**: 分页参数超出范围、备注长度超限
2. **数据不存在**: 用户ID不存在
3. **权限错误**: 非管理员用户访问
4. **系统错误**: 数据库查询失败、聚合计算异常

### 错误响应格式
```json
{
  "code": 400,
  "message": "参数错误：每页数量不能超过100",
  "timestamp": *************
}
```

## 操作日志

### 日志记录内容
- 用户列表查询操作（查询条件、结果数量）
- 用户应用详情查询（用户ID、应用数量）
- 用户备注修改操作（用户ID、备注内容）
- 错误信息和异常堆栈

### 日志示例
```
[UserAdminService] 获取开放平台用户列表: page=1, limit=20, phone=138, status=ACTIVE
[UserAdminService] 用户列表查询完成: 总数=100, 当前页=1, 返回20条记录
[UserAdminService] 获取用户应用详情: userId=507f1f77bcf86cd799439011
[UserAdminService] 用户应用详情查询完成: userId=507f1f77bcf86cd799439011, 应用数量=3
[UserAdminService] 修改用户备注: userId=507f1f77bcf86cd799439011
[UserAdminService] 用户备注修改成功: userId=507f1f77bcf86cd799439011, 备注="重要客户"
```

## 使用示例

### 1. 查询活跃用户列表
```bash
curl -X GET "https://api.example.com/admin/open-platform/users?status=ACTIVE&page=1&limit=50" \
  -H "Authorization: Bearer admin_token"
```

### 2. 搜索特定手机号用户
```bash
curl -X GET "https://api.example.com/admin/open-platform/users?phone=138&page=1&limit=20" \
  -H "Authorization: Bearer admin_token"
```

### 3. 查看用户应用详情
```bash
curl -X GET "https://api.example.com/admin/open-platform/users/507f1f77bcf86cd799439011/applications" \
  -H "Authorization: Bearer admin_token"
```

### 4. 修改用户备注
```bash
curl -X PUT "https://api.example.com/admin/open-platform/users/remark" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin_token" \
  -d '{
    "userId": "507f1f77bcf86cd799439011",
    "remark": "VIP客户，优先处理"
  }'
```

### 5. 清空用户备注
```bash
curl -X PUT "https://api.example.com/admin/open-platform/users/remark" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin_token" \
  -d '{
    "userId": "507f1f77bcf86cd799439011",
    "remark": ""
  }'
```

## 安全考虑

### 1. 权限控制
- 严格的管理员权限验证
- 基于token的身份认证
- 防止权限提升攻击

### 2. 数据保护
- 敏感信息的适当脱敏
- 查询结果的数量限制
- 防止数据泄露

### 3. 输入验证
- 严格的参数类型验证
- 防止SQL注入和NoSQL注入
- 输入长度和格式限制

## 监控和告警

### 关键指标
- API调用频率和响应时间
- 查询结果的数据量分布
- 错误率和异常情况
- 管理员操作频率

### 告警规则
- 大量数据查询的性能告警
- 异常的管理员操作频率告警
- API错误率超过阈值的告警
- 数据库查询超时告警

## 扩展功能建议

### 1. 数据导出
- 支持用户列表的Excel导出
- 应用统计数据的报表导出
- 自定义查询条件的数据导出

### 2. 批量操作
- 批量修改用户状态
- 批量设置用户备注
- 批量应用管理操作

### 3. 高级筛选
- 按注册时间范围筛选
- 按应用数量范围筛选
- 按流量使用量筛选

### 4. 实时统计
- 实时用户在线状态
- 实时应用使用情况
- 实时流量监控
