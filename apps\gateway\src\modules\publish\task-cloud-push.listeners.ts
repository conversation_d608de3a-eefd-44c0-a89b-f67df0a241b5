import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  EventNames,
  PlatformNameEnum,
  PlatformType,
  PublishChannel,
  TaskCloudPushEvent
} from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
  ContentEntity,
  PlatformAccountEntity,
  TaskEntity,
  TaskSetEntity
} from '@yxr/mongo'
import { TlsService } from '@yxr/huoshan'
import { TaskCloudPushDTO, TaskDTO, YxrOpenPlatformService } from '@yxr/yxr-open-platform'

@Injectable()
export class TaskCloudPushListener {
  logger = new Logger('TaskCloudPushListener')

  constructor(
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    private readonly loggerService: TlsService,
    private readonly yxrOpenPlatformService: YxrOpenPlatformService
  ) {}

  @OnEvent(EventNames.TaskCloudPushEvent, { async: true })
  async handleTaskCloudPushEvent(payload: TaskCloudPushEvent) {
    const taskSet = await this.taskSetModel
      .findOne({
        teamId: new Types.ObjectId(payload.teamId),
        taskIdentityId: payload.taskIdentityId,
        publishChannel: PublishChannel.cloud
      })
      .select('taskIdentityId publishArgs publishType isAppContent teamId userId')
    if (!taskSet) {
      await this.loggerService.error(null, '云端推送任务未找到', {
        teamId: payload.teamId,
        taskIdentityId: payload.taskIdentityId,
        authorization: payload.authorization
      })
      return
    }
    const contents = await this.contentModel
      .find({
        taskSetId: payload.taskIdentityId,
        teamId: new Types.ObjectId(payload.teamId)
      })
      .select('platformAccountId teamId cover video _id')

    const tasks = await this.taskModel
      .find({
        taskSetId: payload.taskIdentityId,
        teamId: new Types.ObjectId(payload.teamId)
      })
      .select('_id taskId platformAccountId contentId')
    const resTaskMap = tasks.reduce((ts, task) => {
      ts[task.contentId.toString()] = task
      return ts
    }, {})

    const grouped: TaskDTO[] = [] //数据组装
    for (const item of contents) {
      const platformAccount = await this.platformAccountModel
        .findById(item.platformAccountId)
        .select(
          '_id teamId platformAuthorId platformAccountName platformName spaceId kuaidailiIp parentId'
        )
      let cookie = null
      let localStorage = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage: accountLocalStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
        localStorage = accountLocalStorage
      }

      // 过滤掉海外平台
      if (platformAccount.platformType === PlatformType.海外平台) {
        continue
      }

      if (!platformAccount.kuaidailiIp) {
        await this.loggerService.info(null, '云端任务推送失败', {
          teamId: payload.teamId,
          authorization: payload.authorization,
          taskIdentityId: payload.taskIdentityId,
          publishType: taskSet.publishType,
          isAppContent: taskSet.isAppContent,
          returnData: '未设置代理IP'
        })
        return
      }
      const [ip, port] = platformAccount.kuaidailiIp.split(':')
      const serverAdd = ip
      const serverPort = port
      const platformAccountObject: TaskDTO = {
        taskId: resTaskMap[item._id.toString()].taskId,
        platform: platformAccount.platformName,
        videoPath: item.video,
        cover: item.cover,
        proxy: {
          serverAdd,
          serverPort
        },
        platformAccount: {
          platformAccountId: platformAccount._id.toString(),
          platformAuthorId: platformAccount.platformAuthorId,
          cookie: cookie,
          localStorage: localStorage
        }
      }

      grouped.push(platformAccountObject)
    }

    const data: TaskCloudPushDTO = {
      callBackData: {
        teamId: taskSet.teamId.toString(),
        userId: taskSet.userId.toString()
      },
      publishType: taskSet.publishType,
      formData: taskSet.publishArgs,
      data: Object.values(grouped)
    }

    try {
      const result = await this.yxrOpenPlatformService.pushTask(data)
      await this.loggerService.info(
        null,
        result.data.statusCode === 0 ? '云端任务推送成功' : '云端任务推送失败',
        {
          teamId: payload.teamId,
          authorization: payload.authorization,
          taskIdentityId: payload.taskIdentityId,
          publishType: taskSet.publishType,
          isAppContent: taskSet.isAppContent,
          returnData: result?.data ? JSON.stringify(result.data) : '无法解析'
        }
      )
    } catch (error) {
      await this.loggerService.error(null, '云端任务推送失败', {
        teamId: payload.teamId,
        authorization: payload.authorization,
        taskIdentityId: payload.taskIdentityId,
        publishType: taskSet.publishType,
        isAppContent: taskSet.isAppContent,
        error: error.message
      })
    }
  }
}
