import { Body, Inject, Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  CacheKeyService,
  EventNames,
  PlatformNameEnum, PlatformType,
  PublishChannel,
  TaskCloudPushEvent
} from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { ContentEntity, PlatformAccountEntity, TaskEntity, TaskSetEntity } from '@yxr/mongo'
import axios from 'axios'
import { TaskCloudPushDTO, TaskDTO } from './taskCloudPush.dto'
import crypto from 'crypto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class TaskCloudPushListener {
  logger = new Logger('TaskCloudPushListener')

  constructor(
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    private readonly loggerService: TlsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  @OnEvent(EventNames.TaskCloudPushEvent, { async: true })
  async handleTaskCloudPushEvent(payload: TaskCloudPushEvent) {
    const taskSet = await this.taskSetModel
      .findOne({
        teamId: new Types.ObjectId(payload.teamId),
        taskIdentityId: payload.taskIdentityId,
        publishChannel: PublishChannel.cloud
      })
      .select('taskIdentityId publishArgs publishType isAppContent teamId userId')
    if (!taskSet) {
      await this.loggerService.error(null, '云端推送任务未找到', {
        teamId: payload.teamId,
        taskIdentityId: payload.taskIdentityId,
        authorization: payload.authorization
      })
      return
    }
    const contents = await this.contentModel
      .find({
        taskSetId: payload.taskIdentityId,
        teamId: new Types.ObjectId(payload.teamId)
      })
      .select('platformAccountId cover video _id')

    const tasks = await this.taskModel
      .find({
        taskSetId: payload.taskIdentityId,
        teamId: new Types.ObjectId(payload.teamId)
      })
      .select('_id taskId platformAccountId contentId')
    const resTaskMap = tasks.reduce((ts, task) => {
      ts[task.contentId.toString()] = task
      return ts
    }, {})

    const grouped: TaskDTO[] = [] //数据组装
    for (const item of contents) {
      const platformAccount = await this.platformAccountModel
        .findById(item.platformAccountId)
        .select(
          '_id teamId platformAuthorId platformAccountName platformName spaceId kuaidailiIp parentId'
        )

      // 过滤掉海外平台
      if(platformAccount.platformType === PlatformType.海外平台) {
        continue
      }

      let wxkey = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        wxkey = await this.cacheManager.get<string>(
          CacheKeyService.getWeiXinAccountLockKey(
            platformAccount.teamId.toString(),
            platformAccount.parentId.toString()
          )
        )
      }
      if (!platformAccount.kuaidailiIp) {
        await this.loggerService.info(null, '云端任务推送失败', {
          teamId: payload.teamId,
          authorization: payload.authorization,
          taskIdentityId: payload.taskIdentityId,
          publishType: taskSet.publishType,
          isAppContent: taskSet.isAppContent,
          returnData: '未设置代理IP'
        })
        return
      }
      const [ip, port] = platformAccount.kuaidailiIp.split(':')
      const serverAdd = ip
      const serverPort = port
      const platformAccountObject: TaskDTO = {
        taskId: resTaskMap[item._id.toString()].taskId,
        platform: platformAccount.platformName,
        wxkey: wxkey,
        videoPath: item.video,
        cover: item.cover,
        proxy: {
          serverAdd,
          serverPort,
          type: 'kuaidaili',
          userName: null,
          password: null,
          area: null,
          province: null,
          city: null,
          sid: null
        },
        platformAccount: {
          platformAccountId: platformAccount._id.toString(),
          platformAuthorId: platformAccount.platformAuthorId,
          platformAccountName: platformAccount.platformAccountName,
          platformAccountSpaceId: platformAccount?.spaceId?.toString(),
          parentId: platformAccount?.parentId ? platformAccount.parentId.toString() : null
        }
      }

      grouped.push(platformAccountObject)
    }

    const data: TaskCloudPushDTO = {
      publishType: taskSet.publishType,
      formData: taskSet.publishArgs,
      authToken: `${taskSet.teamId.toString()}:${taskSet.userId.toString()}`,
      data: Object.values(grouped)
    }

    try {
      const timestamp = Date.now()
      const tokenString = `${timestamp}yixiaoer_cloud_publish`
      const md5Hash = crypto.createHash('md5').update(tokenString).digest('hex')
      const result = await axios.post(`${process.env.YIXIAOER_CLOUD_PUSH_URL}/task/push`, data, {
        headers: {
          token: md5Hash,
          timestamp: timestamp
        }
      })
      await this.loggerService.info(
        null,
        result.data.statusCode === 0 ? '云端任务推送成功' : '云端任务推送失败',
        {
          teamId: payload.teamId,
          authorization: payload.authorization,
          taskIdentityId: payload.taskIdentityId,
          publishType: taskSet.publishType,
          isAppContent: taskSet.isAppContent,
          returnData: result?.data ? JSON.stringify(result.data) : '无法解析'
        }
      )
    } catch (error) {
      await this.loggerService.error(null, '云端任务推送失败', {
        teamId: payload.teamId,
        authorization: payload.authorization,
        taskIdentityId: payload.taskIdentityId,
        publishType: taskSet.publishType,
        isAppContent: taskSet.isAppContent,
        error: error.message
      })
    }
  }
}
