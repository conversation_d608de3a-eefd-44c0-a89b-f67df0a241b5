import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'

import { OnlineScriptService } from './online-script.service'
import {
  CreateDesktopAppRequest,
  CreateDesktopAppResponseDTO,
  CreateOnlineScriptRequest,
  CreateOnlineScriptResponseDTO,
  GetDesktopAppLatestRequest,
  GetDesktopAppListRequest,
  GetDesktopAppListResponseDTO,
  GetOnlineScriptListRequest,
  GetOnlineScriptListResponseDTO,
  OnlineScriptDetailsDto,
  OnlineScriptListResponseDTO,
  PutDesktopAppRequest
} from './online-script.dto'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('online-scripts')
@ApiTags('在线脚本')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class OnlineScriptController {
  constructor(private readonly onlineScriptService: OnlineScriptService) {}

  @Get()
  @ApiOperation({ summary: '获取在线脚本列表' })
  @ApiOkResponse({ type: GetOnlineScriptListResponseDTO })
  @ApiQuery({ type: GetOnlineScriptListRequest })
  async getOnlineScriptList(
    @Query() query: GetOnlineScriptListRequest
  ): Promise<OnlineScriptListResponseDTO> {
    return await this.onlineScriptService.getOnlineScriptList(query)
  }

  @Post()
  @ApiOperation({ summary: '创建在线脚本版本' })
  @ApiOkResponse({ type: CreateOnlineScriptResponseDTO })
  @ApiBody({ type: CreateOnlineScriptRequest })
  createOnlineScript(@Body() request: CreateOnlineScriptRequest): Promise<OnlineScriptDetailsDto> {
    return this.onlineScriptService.createOnlineScript(request)
  }

  @Get('desktop')
  @ApiOperation({ summary: '获取桌面应用列表' })
  @ApiOkResponse({ type: GetDesktopAppListResponseDTO })
  @ApiQuery({ type: GetDesktopAppListRequest })
  async getDesktopAppList(@Query() query: GetDesktopAppListRequest) {
    return await this.onlineScriptService.getDesktopAppList(query)
  }

  @Get('desktop/latest')
  @ApiOperation({ summary: '获取最新版本' })
  @ApiOkResponse({ type: GetDesktopAppListResponseDTO })
  @ApiQuery({ type: GetDesktopAppLatestRequest })
  async getDesktopAppLatest(@Query() query: GetDesktopAppLatestRequest) {
    return await this.onlineScriptService.getDesktopAppLatest(query)
  }

  @Post('desktop')
  @ApiOperation({ summary: '创建桌面应用版本' })
  @ApiOkResponse({ type: CreateDesktopAppResponseDTO })
  @ApiBody({ type: CreateDesktopAppRequest })
  async createDesktopApp(@Body() request: CreateDesktopAppRequest) {
    return this.onlineScriptService.createDesktopApp(request)
  }

  @Put('desktop/:id')
  @ApiOperation({ summary: '修改桌面应用版本信息' })
  @ApiOkResponse({ type: GetDesktopAppListResponseDTO })
  @ApiQuery({ type: GetDesktopAppListRequest })
  async PutDesktopApp(@Param('id') id: string, @Body() body: PutDesktopAppRequest) {
    return await this.onlineScriptService.putDesktopApp(id, body)
  }

  @Delete('desktop/:id')
  @ApiOperation({ summary: '删除桌面应用版本' })
  @ApiOkResponse({ type: GetDesktopAppListResponseDTO })
  @ApiQuery({ type: GetDesktopAppListRequest })
  async DeleteDesktopApp(@Param('id') id: string) {
    return await this.onlineScriptService.deleteDesktopApp(id)
  }
}
