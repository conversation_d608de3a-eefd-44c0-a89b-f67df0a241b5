import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { BrowserDeleteEvent, EventNames } from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { BrowserEntity, BrowserFavoritesEntity, FavoritesGroupItemEntity } from '@yxr/mongo'

@Injectable()
export class BrowsersDeleteChangedListener {
  logger = new Logger('BrowsersDeleteChangedListener')

  constructor(
    @InjectModel(BrowserFavoritesEntity.name)
    private browserFavoritesModel: Model<BrowserFavoritesEntity>,
    @InjectModel(FavoritesGroupItemEntity.name)
    private favoritesGroupItemModel: Model<FavoritesGroupItemEntity>
  ) {}

  @OnEvent(EventNames.BrowserDeleteEvent, { async: true })
  async handleBrowserDeleteEvent(payload: BrowserDeleteEvent) {
    if (payload.originalId === payload.accountId) {
      //网站空间删除触发事件
      await this.browserFavoritesModel.deleteMany({
        platformAccountId: new Types.ObjectId(payload.accountId)
      })

      //网站收藏分组关联网站清理
      await this.favoritesGroupItemModel.deleteMany({
        accountId: new Types.ObjectId(payload.accountId)
      })
    } else {
      //网站收藏删除触发
      await this.favoritesGroupItemModel.deleteMany({
        accountId: new Types.ObjectId(payload.accountId),
        originalId: new Types.ObjectId(payload.originalId)
      })
    }
  }
}
