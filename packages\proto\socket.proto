syntax = "proto3";

// import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";

package socket;


service Socket {
  rpc send (SocketParam) returns (stream google.protobuf.Empty) {}
  rpc sendToAll (ToAllSocketParam) returns (stream google.protobuf.Empty) {}
  rpc sendToDevice (ToDeviceSocketParam) returns (stream google.protobuf.Empty) {}
}

message SocketParam {
 string list = 1;
}

message ToAllSocketParam {
 string data = 1;
}

message ToDeviceSocketParam {
 string deviceType = 1;
 string data = 2;
}

