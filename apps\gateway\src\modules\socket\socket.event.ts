import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Worker } from 'bullmq'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { TaskSetEntity, UserDevicesEntity } from '@yxr/mongo'
import { WebhookEvents } from '../webhook/constant'
import { WebhookService } from '../webhook/webhook.service'
import { PublishChannel, TaskSetStatusEnum, VersionService } from '@yxr/common'

@Injectable()
export class SocketEventService implements OnModuleInit {
  logger = new Logger('SocketEventService')

  taskWorker: Worker

  constructor(
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(UserDevicesEntity.name) private userDevicesModel: Model<UserDevicesEntity>,
    private readonly webhookService: WebhookService
  ) {}

  onModuleInit() {
    this.logger.log('SocketEventService init')

    this.taskWorker = new Worker(
      'socket-event-queue',
      async (job) => {
        const { socketId, teamId, userId, deviceId, version } = job.data
        if (job.name == 'disconnect') {
          await this.disconnectTasks(teamId, userId)
        } else {
          await this.connectTasks(userId, deviceId, version)
          await this.resendAppTasks(teamId, userId)
        }
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_NORMAL_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.taskWorker.on('completed', (job) => {
    //   this.logger.log(`taskWorker completed------------${job.id}`)
    // })

    this.taskWorker.on('failed', (job, err) => {
      this.logger.error(`Job ${job.id} failed with ${err.message}`)
    })
  }

  private async resendAppTasks(teamId: string, userId: string) {
    // 分配APP未发布的任务集

    const taskSets = await this.taskSetModel
      .find(
        {
          isAppContent: true,
          progressToken: { $eq: null },
          publishChannel: PublishChannel.local,
          taskSetStatus: TaskSetStatusEnum.Publishing,
          teamId: new Types.ObjectId(teamId)
        },
        { _id: 1, taskIdentityId: 1, __v: 1 }
      )
      .sort({ updatedAt: 'desc' })

    if (taskSets.length === 0) return

    await this.webhookService.grpchook([userId], teamId, {
      event: WebhookEvents.AppTaskPushing,
      body: taskSets.map((taskSet) => ({
        taskIdentityId: taskSet.taskIdentityId,
        version: taskSet.__v
      }))
    })
  }

  /**
   * 重连事件处理
   * @param teamId
   * @param userId
   */
  private async connectTasks(userId: string, deviceId: string, version: string) {
    await this.userDevicesModel.updateOne(
      {
        userId: new Types.ObjectId(userId),
        deviceId: deviceId
      },
      {
        $set: {
          version: version,
          numberVersion: version
            ? VersionService.versionToNumber(
                ...(VersionService.parseVersion(version) as [number, number, number])
              )
            : 0,
          isActive: true
        }
      }
    )
  }

  /**
   * 掉线事件处理
   * @param teamId
   * @param userId
   */
  private async disconnectTasks(teamId: string, userId: string) {
    await this.userDevicesModel.updateMany(
      {
        userId: new Types.ObjectId(userId)
      },
      {
        $set: {
          isActive: false
        }
      }
    )
  }
}
