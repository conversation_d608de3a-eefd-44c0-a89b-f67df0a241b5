import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})

/**
 * 团队插件表
 */
export class TeamComponentEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 组件名称
   */
  @Prop({
    type: String,
    required: true
  })
  name: string

  /**
   * 是否开启
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  enabled: boolean

  /**
   * 组件数据
   */
  @Prop({
    type: Types.Map,
    required: true
  })
  componentArgs: Record<string, any>
}

export const TeamComponentSchema: ModelDefinition = {
  name: TeamComponentEntity.name,
  schema: SchemaFactory.createForClass(TeamComponentEntity)
}

export const TeamComponentMongoose = MongooseModule.forFeature([TeamComponentSchema])
