import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { FilterQuery, Model, Types } from 'mongoose'
import {
  BrowserEntity,
  MemberEntity,
  PlatformAccountEntity,
  TeamEntity,
  UserEntity
} from '@yxr/mongo'
import { type FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import {
  CacheKeyService,
  EventNames,
  MemberAccountsChangedEvent,
  MemberRolesChangedEvent,
  MemberStatusEnum,
  TeamRoleNames
} from '@yxr/common'
import { MemberDetailsDto, MemberPagedListResponse } from './member.dto'
import { AuthorizationService } from '../../common/security/authorization.service'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService, TosService } from '@yxr/huoshan'

@Injectable()
export class MemberService {
  logger = new Logger('MemberService')

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    private readonly ossService: TosService,
    @Inject(REQUEST) private request: FastifyRequest,
    private authorizationService: AuthorizationService,
    private readonly webhookService: WebhookService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  /**
   * 分页获取团队列表
   */
  async getPagedMembers({
    statuses,
    page,
    size
  }: {
    statuses: MemberStatusEnum[]
    size: number
    page: number
  }): Promise<MemberPagedListResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, TeamRoleNames.All)

    const condition: FilterQuery<any>[] = [{ teamId: new Types.ObjectId(currentTeamId) }]

    if (statuses) {
      condition.push({ status: { $in: statuses } })
    }

    const result = await this.memberModel
      .aggregate([
        { $match: { $and: condition } },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [
              { $sort: { createdAt: -1 } },
              { $skip: (page - 1) * size },
              { $limit: size },
              {
                $lookup: {
                  from: 'userentities', // UserEntity.name,
                  localField: 'userId', // MemberEntity 中的字段
                  foreignField: '_id', // UserEntity 中的字段
                  as: 'users' // 结果集别名
                }
              }
            ]
          }
        }
      ])
      .exec()

    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      data: await Promise.all(
        result[0]?.items.map(async (item) => ({
          memberId: item._id,
          id: item.users[0]?._id,
          nickName: item.remark ?? item.users[0]?.nickName,
          avatarUrl: item.users[0]?.avatar
            ? await this.ossService.getAccessSignatureUrl(item.users[0]?.avatar)
            : null,
          avatarKey: item.users[0]?.avatarKey,
          roles: item.roles,
          status: item.status,
          remark: undefined,
          phone: item.users[0]?.phone,
          createdAt: item.createdAt.getTime(),
          accountCount: item.accounts?.length ?? 0,
          browserCount: 0,
          isFreeze: item.isFreeze ?? false
        }))
      ),
      page,
      size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / size)
    }
  }

  /**
   * 获取成员详情
   * @param userId
   */
  async getMember(userId: string): Promise<MemberDetailsDto> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, TeamRoleNames.All)

    const result = await this.memberModel
      .aggregate([
        {
          $match: {
            $and: [
              { userId: new Types.ObjectId(userId) },
              { teamId: new Types.ObjectId(currentTeamId) },
              { status: MemberStatusEnum.Joined }
            ]
          }
        },
        {
          $lookup: {
            from: 'userentities', // UserEntity.name,
            localField: 'userId', // MemberEntity 中的字段
            foreignField: '_id', // UserEntity 中的字段
            as: 'user' // 结果集别名
          }
        }
      ])
      .unwind('$user')
      .exec()

    // 当前用户需要在这个团队中, 否则抛出异常
    if (result.length === 0) {
      throw new NotFoundException('用户不存在')
    }

    const member = result[0]
    return {
      id: member.user?.id,
      nickName: member.remark ?? member.user?.nickName,
      avatarUrl: await this.ossService.getAccessSignatureUrl(member.user?.avatar),
      avatarKey: member.user?.avatar,
      roles: member.roles,
      status: member.status,
      phone: member.user?.phone,
      remark: undefined,
      createdAt: member.createdAt.getTime(),
      accountCount: member.accounts?.length ?? 0,
      browserCount: 0,
      isFreeze: member.isFreeze ?? false
    }
  }

  /**
   * 设置团队成员角色
   * @param param
   */
  async setRoles({ userId, roles }: { roles: string[]; userId: string }) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    if (userId === currentUserId) throw new ForbiddenException('不能修改自己的角色')

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN
    ])

    // 为空或者空数组时, 视为默认角色
    roles = roles ?? [TeamRoleNames.MEMBER]
    roles = roles.length === 0 ? [TeamRoleNames.MEMBER] : roles

    // 过滤掉不合规的角色名
    roles = roles.filter((item) => TeamRoleNames.All.includes(item))

    const member = await this.memberModel
      .findOne(
        {
          teamId: new Types.ObjectId(currentTeamId),
          userId: new Types.ObjectId(userId),
          status: MemberStatusEnum.Joined
        },
        { _id: -1, roles: 1 }
      )
      .exec()

    if (member === null) {
      throw new NotFoundException('成员不存在')
    }

    // 如果角色没有发生改变, 则直接返回
    if (
      member.roles.length === roles.length &&
      roles.filter((item) => member.roles.includes(item)).length === member.roles.length
    ) {
      this.logger.debug(`用户角色没有发生变化, 无须继续变更， ${member.roles} => ${roles}`)
      return
    }

    // 更新角色到数据库
    await this.memberModel.updateOne({ _id: member._id }, { $set: { roles: roles } })

    // 触发事件
    await this.eventEmitter.emitAsync(
      EventNames.MemberRolesChangedEvent,
      new MemberRolesChangedEvent(userId, currentTeamId, member.roles, roles)
    )

    await this.cacheManager.del(CacheKeyService.getHomeOverviewKey(currentTeamId, currentUserId))

    await this.webhookService.grpchook([userId], currentTeamId, {
      event: WebhookEvents.TeamRoleChange,
      body: { roles: roles }
    })
  }

  /**
   * 设置团队成员账号
   * @param teamId
   * @param userId
   * @param accountIds
   */
  async setAccounts({ userId, accountIds }: { accountIds: string[]; userId: string }) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN
    ])

    const member = await this.memberModel
      .findOne(
        {
          teamId: new Types.ObjectId(currentTeamId),
          userId: new Types.ObjectId(userId),
          status: MemberStatusEnum.Joined
        },
        { _id: -1, accounts: 1, maxAccountCount: 1 }
      )
      .exec()

    if (member === null) {
      throw new NotFoundException('成员不存在')
    }

    // 查询一下账号表, 以确保这些账号Id对应的账号是存在的
    const accounts = await this.platformAccountModel
      .find({ _id: { $in: accountIds } }, { _id: 1 })
      .exec()

    const confirmedAccountIds = accounts.map((item) => item.id)

    if (member.maxAccountCount !== 0 && confirmedAccountIds.length > member.maxAccountCount) {
      throw new BadRequestException(
        `修改失败，当前运营账号数大于设置的最大运营数 ${member.maxAccountCount}`
      )
    }

    // 如果角色没有发生改变, 则直接返回
    if (
      member.accounts.length === confirmedAccountIds.length &&
      confirmedAccountIds.filter((item) => member.accounts.includes(item)).length ===
        member.accounts.length
    ) {
      this.logger.debug(
        `用户运营账号没有发生变化, 无须继续变更， ${member.accounts} => ${confirmedAccountIds}`
      )
      return
    }

    // 更新角色到数据库
    await this.memberModel.updateOne(
      { _id: member._id },
      { $set: { accounts: confirmedAccountIds } }
    )

    this.logger.debug(
      `成员运营账号设置成功。 teamId: ${currentTeamId}, userId: ${userId}, accountIds: ${confirmedAccountIds}`
    )

    const addAccountIds = confirmedAccountIds.filter((item) => !member.accounts.includes(item))
    const delAccountIds = member.accounts.filter((item) => !confirmedAccountIds.includes(item))

    if (delAccountIds.length) {
      await this.platformAccountModel.updateMany(
        {
          teamId: new Types.ObjectId(currentTeamId),
          _id: { $in: delAccountIds.map((x) => new Types.ObjectId(x)) },
          principalId: member._id.toString()
        },
        {
          $set: { principalId: '' }
        }
      )
    }

    // 触发事件
    const event = new MemberAccountsChangedEvent(
      userId,
      currentTeamId,
      addAccountIds,
      delAccountIds
    )
    await this.eventEmitter.emitAsync(EventNames.MemberAccountsChangedEvent, event)
    this.logger.debug(
      `event has emitted: [${EventNames.MemberAccountsChangedEvent}] ${JSON.stringify(event)}`
    )

    // 发送 socket 消息
    await this.webhookService.grpchook([userId], currentTeamId, {
      event: WebhookEvents.MemberAccountsChangedEvent
    })
  }

  /**
   * 设置团队成员网站空间 6.3 发版后废弃
   * @deprecated
   * @param teamId
   * @param userId
   * @param browserIds
   */
  async setBrowsers({ userId, browserIds }: { browserIds: string[]; userId: string }) {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * 获取团队成员运营账号
   * @param userId
   */
  async getAccounts(userId: string) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN
    ])

    const member = await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(userId),
        status: MemberStatusEnum.Joined
      })
      .exec()

    return member ? member.accounts : []
  }

  /**
   * @deprecated
   * 获取团队成员运营网站空间
   * @param userId
   */
  async getBrowsers(userId: string) {
    throw new ForbiddenException('该功能不可用，请更新版本')
  }

  /**
   * 移除团队成员
   * @param userId
   */
  async remove(userId: string) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, [
      TeamRoleNames.MASTER,
      TeamRoleNames.ADMIN
    ])

    await this.removeMember(currentTeamId, userId)
  }

  /**
   * 当前用户退出团队
   */
  async quit() {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(
      currentTeamId,
      currentUserId,
      [TeamRoleNames.ADMIN, TeamRoleNames.MEMBER],
      '您是团队超管, 无法退出团队'
    )

    await this.removeMember(currentTeamId, currentUserId)
  }

  /**
   * 通过角色获取当前团队成员ID
   */
  async getMembersByRoles(roles: string[], teamId: string) {
    const userIds = await this.memberModel
      .find(
        {
          teamId: new Types.ObjectId(teamId),
          status: MemberStatusEnum.Joined,
          roles: { $in: roles }
        },
        {
          _id: 0,
          userId: 1
        }
      )
      .exec()

    return userIds
  }

  private async removeMember(currentTeamId: string, userId: string) {
    const member = await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(userId)
      })
      .exec()

    if (member === null) {
      throw new NotFoundException('成员不存在')
    }

    if (member.roles.includes(TeamRoleNames.MASTER))
      throw new BadRequestException('团队创建人无法离开团队')

    const session = await this.memberModel.db.startSession()
    session.startTransaction()

    try {
      await this.memberModel.deleteOne(
        {
          teamId: new Types.ObjectId(currentTeamId),
          userId: new Types.ObjectId(userId)
        },
        { session }
      )

      await this.userModel.updateOne(
        { _id: new Types.ObjectId(userId), latestTeamId: new Types.ObjectId(currentTeamId) },
        { latestTeamId: null },
        { session: session }
      )

      // 团队成员人数处理
      const memberCount = await this.memberModel.countDocuments(
        { teamId: new Types.ObjectId(currentTeamId) },
        { session }
      )
      await this.teamModel.updateOne(
        { _id: new Types.ObjectId(currentTeamId) },
        { memberCount: memberCount },
        { session }
      )

      await session.commitTransaction()
    } catch (error) {
      await session.abortTransaction()
      throw new HttpException('移除成员失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }

    // 触发移除成员相关事件
    if (member.accounts && member.accounts.length > 0) {
      await this.eventEmitter.emitAsync(
        EventNames.MemberAccountsChangedEvent,
        new MemberAccountsChangedEvent(userId, currentTeamId, [], member.accounts)
      )
    }

    // 发送消息
    await this.webhookService.grpchook([userId], currentTeamId, { event: WebhookEvents.TeamExit })
  }

  async putFreeze(userId: string, isFreeze: boolean) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(
      currentTeamId,
      currentUserId,
      [TeamRoleNames.MASTER],
      '您没有权限执行此操作, 请联系团队管理员'
    )

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(userId)
    })

    if (!member) {
      throw new NotFoundException('团队成员不存在')
    }

    if (!isFreeze) {
      const memberCount = await this.memberModel.countDocuments({
        teamId: new Types.ObjectId(currentTeamId),
        isFreeze: false
      })

      const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))
      if (memberCount >= team.memberCountLimit) {
        throw new ForbiddenException(`该团队成员已达上限！`)
      }
    }

    member.isFreeze = isFreeze
    await member.save()
  }

  /**
   * 修改成员信息昵称
   * @param remark
   * @param memberUserId
   */
  async putMemberRemark(memberUserId: string, remark: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const adminMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (
      !adminMember.roles.some(
        (role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN
      )
    ) {
      throw new ForbiddenException('修改失败，您没有修改的权限')
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(memberUserId)
    })

    if (member === null) {
      throw new NotFoundException('成员不存在')
    }

    // 修改团队成员备注
    await this.memberModel.updateOne(
      {
        userId: new Types.ObjectId(memberUserId),
        teamId: new Types.ObjectId(currentTeamId)
      },
      {
        remark: remark
      }
    )
  }

  /**
   * 查询是否由管理员权限
   * @param teamId
   * @param userId
   * @returns
   */
  async checkIsAdminRole(teamId: string, userId: string): Promise<boolean> {
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      userId: new Types.ObjectId(userId)
    })

    if (!member || !Array.isArray(member.roles)) {
      return false // roles 为空或者为 undefined 时返回 false
    }

    return member.roles.some(
      (role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN
    )
  }

  async setAccountCount(userId: string, count: number) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const adminMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    if (
      !adminMember.roles.some(
        (role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN
      )
    ) {
      throw new ForbiddenException('修改失败，您没有修改的权限')
    }

    const tergetMember = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(userId)
    })

    if (!tergetMember) {
      throw new NotFoundException('成员不存在')
    }

    if (tergetMember.accounts.length > count) {
      throw new BadRequestException(`修改失败，当前运营账号数大于设置的最大运营数 ${count}`)
    }

    await this.memberModel.updateOne(
      {
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(userId)
      },
      {
        maxAccountCount: count
      }
    )
  }

  async checkMemberAccountCount(teamId: string, userId: string) {
    const member = await this.memberModel.findOne({
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId)
    })

    if (member.maxAccountCount !== 0 && member.accounts.length + 1 > member.maxAccountCount) {
      const user = await this.userModel.findOne({
        userId: new Types.ObjectId(userId)
      })

      throw new BadRequestException(
        `${user.nickName} 成员运营账号数大于设置的最大运营数 ${member.maxAccountCount}`
      )
    }
  }
}
