import { Body, Controller, Get, Param, Post, Query, Request } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiHeader,
  ApiResponse,
  ApiForbiddenResponse
} from '@nestjs/swagger'
import {
  ApplicationAccess,
  OpenPlatformAccess
} from '../../../common/decorators/access-control.decorator'
import {
  BaseBadRequestDTO,
  BaseUnauthorizedResponseDTO,
  BaseForbiddenResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import {
  CreateAccountPointsOrderRequestDto,
  CreateOpenOrderRequestDto,
  CreateRenewOrderRequestDto,
  CreateTrafficOrderRequestDto,
  CreateUpgradeOrderRequestDto
} from '../dto/open-platform-order.dto'
import { OpenPlatformOrderService } from '../services/open-platform-order.service'

@Controller('open-platform/orders')

@ApiTags('开放平台订单管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class OpenPlatformOrderController {
  constructor(private readonly openPlatformOrderService: OpenPlatformOrderService) {}

  @Post('account-points')
  @ApplicationAccess()
  @ApiOperation({
    summary: '创建账号点数订单',
    description: '创建账号点数订单，支持时间重叠，重叠期间账号点数会叠加计算'
  })
  @ApiBadRequestResponse({
    description: '参数错误或开始时间无效',
    type: BaseBadRequestDTO
  })
  async createAccountPointsOrder(@Body() createDto: CreateAccountPointsOrderRequestDto) {
    return this.openPlatformOrderService.createAccountPointsOrder(createDto)
  }

  @Post('traffic')
  @ApplicationAccess()
  @ApiOperation({
    summary: '创建流量订单',
    description: '创建流量订单，流量直接累积到团队，有效期1年'
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async createTrafficOrder(@Body() createDto: CreateTrafficOrderRequestDto) {
    return this.openPlatformOrderService.createTrafficOrder(createDto)
  }

  @Post('openOrder')
  @OpenPlatformAccess()
  @ApiOperation({ summary: '创建订单' })
  async createOrder(@Body() body: CreateOpenOrderRequestDto) {
    return this.openPlatformOrderService.createOpenOrder(body)
  }

  @Post('openUpgradeOrder')
  @OpenPlatformAccess()
  @ApiOperation({ summary: '升级订单' })
  async createUpgradeOrder(@Body() body: CreateUpgradeOrderRequestDto) {
    return this.openPlatformOrderService.createUpgradeOrder(body)
  }

  @Post('openRenewOrder')
  @OpenPlatformAccess()
  @ApiOperation({ summary: '续费vip' })
  async renewVip(@Body() body: CreateRenewOrderRequestDto) {
    return this.openPlatformOrderService.createRenewOrder(body)
  }
}
