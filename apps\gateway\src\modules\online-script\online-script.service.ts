import { Injectable } from '@nestjs/common'
import {
  BaiduReportRequest,
  DesktopLatestRequest,
  DesktopLatestResponseDTO,
  DesktopTypeEnum,
  LatestResponseDTO
} from './online-script.dto'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { OnlineScriptEntity } from '@yxr/mongo'
import { VersionService } from '@yxr/common'
import axios from 'axios'

@Injectable()
export class OnlineScriptService {
  constructor(@InjectModel(OnlineScriptEntity.name) private model: Model<OnlineScriptEntity>) {}

  async getLatest(): Promise<LatestResponseDTO> {
    const items = await this.model.aggregate([
      {
        $group: {
          _id: '$type',
          docs: { $push: '$$ROOT' }
        }
      },
      {
        $project: {
          docs: {
            $slice: [{ $sortArray: { input: '$docs', sortBy: { createdAt: -1 } } }, 1]
          }
        }
      },
      {
        $unwind: '$docs'
      },
      {
        $replaceRoot: { newRoot: '$docs' }
      },
      {
        $match: { type: { $in: ['spider', 'rpa'] } }
      },
      {
        $project: {
          _id: 0,
          type: 1,
          version: 1,
          storage: 1
        }
      }
    ])

    const result = {}
    items.forEach((item) => {
      const { type, version, storage } = item
      result[type] = {
        version,
        url: `${process.env.OSS_DOWNLOAD_URL}/${storage}`
      }
    })
    return result
  }

  async getDesktopLatest(query: DesktopLatestRequest): Promise<DesktopLatestResponseDTO> {
    const parsedVersion = VersionService.versionToNumber(
      ...(VersionService.parseVersion(query.version) as [number, number, number])
    )
    const latest = await this.model
      .findOne({
        type: query.desktopType,
        numberVersion: { $gt: parsedVersion }
      })
      .sort({ createdAt: -1 })
    let isForce = false
    let isUpdate = false
    let url = ''

    if (latest) {
      if (latest.isForce) {
        isForce = latest.isForce
      } else {
        const forceVersion = await this.model
          .findOne({
            numberVersion: { $gt: parsedVersion },
            type: query.desktopType,
            isForce: true
          })
          .sort({ createdAt: -1 })

        isForce = !!forceVersion
      }

      isUpdate = VersionService.compareVersions(latest.version, query.version) < 0
      url = latest?.storage ? `https://lite-download.yixiaoer.cn/${latest?.storage}` : ''

      if (query.desktopType == DesktopTypeEnum.ios) {
        url = latest.requestUrl
      }
    }

    return {
      version: latest?.version ?? '',
      url: url,
      isForce: isForce,
      isUpdate: isUpdate,
      notice: latest?.notice ?? ''
    }
  }

  async postBaiduReport(body: BaiduReportRequest, origin: string) {
    const bdApiUrl = 'https://ocpc.baidu.com/ocpcapi/api/uploadConvertData'

    let postData = {}
    if (origin === 'https://yxr.katyusa.cn') {
      postData = {
        token: 'qHhhEH7rKtAT4Gsajdr5tTqFf0BRqwyE@IUq1Bvu11TI7SgfdR0A0McXrrh6Z39Cb',
        conversionTypes: [
          {
            logidUrl: `https://yxr.katyusa.cn/?bd_vid=${body.bd}`,
            newType: body.newType
          }
        ]
      }
    } else {
      postData = {
        token: 'NWW6Qgo66KIWD88zUYEdqa9yf4QEb8dF@nl655GVbq15tFH5zhPel7tjLPmjCUKaf',
        conversionTypes: [
          {
            logidUrl: `https://www.yxiaoer.cn/?bd_vid=${body.bd}`,
            newType: body.newType
          }
        ]
      }
    }

    try {
      await axios.post(bdApiUrl, postData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30秒超时
      })
    } catch (error) {
      console.error(`百度上报失败: ${error.message}, channel=${body.bd}`)
    }
  }
}
