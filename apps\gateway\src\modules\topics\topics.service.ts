import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { CommonTopicsEntity, CommonTopicsGroupEntity, TeamEntity } from '@yxr/mongo'
import { PostTopicsRequest, TopicsDetailResponse, TopicsListDTO } from './topics.dto'
import { TopicFeatures } from '@yxr/common'

@Injectable()
export class TopicsService {
  logger = new Logger('TopicsService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(CommonTopicsGroupEntity.name)
    private topicsGroupModel: Model<CommonTopicsGroupEntity>,
    @InjectModel(CommonTopicsEntity.name)
    private topicsModel: Model<CommonTopicsEntity>
  ) {}

  /**
   * 获取话题分页
   * @param groupId
   * @param page
   * @param size
   * @returns
   */
  async getTopics(groupId: string, page: number, size: number): Promise<TopicsListDTO> {
    const skip = (page - 1) * size
    const { session } = this.request
    const query: any = {}

    query.teamId = new Types.ObjectId(session.teamId)

    if (groupId) {
      if (Types.ObjectId.isValid(groupId)) {
        query.groupId = new Types.ObjectId(groupId)
      } else {
        throw new ForbiddenException('分组参数错误')
      }
    }

    const totalSize = await this.topicsModel.countDocuments(query)
    const result = await this.topicsModel
      .find(query)
      .sort({ createdAt: 'desc' })
      .skip(skip)
      .limit(size)

    const data = await Promise.all(
      result.map(async (item) => ({
        id: item._id.toString(),
        name: item.name,
        groupId: item.groupId
      }))
    )

    return {
      page,
      size,
      totalSize,
      maxTopicCountLimit: TopicFeatures.MixGroupTopicsCountLimit,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }

  /**
   * 新增话题
   * @param body
   */
  async postTopics(body: PostTopicsRequest): Promise<TopicsDetailResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const group = await this.topicsGroupModel.findById({
      _id: new Types.ObjectId(body.groupId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!group) {
      throw new NotFoundException('添加分组失败，分组不存在')
    }

    const groupTopicCount = await this.topicsModel
      .find({
        groupId: new Types.ObjectId(body.groupId),
        teamId: new Types.ObjectId(currentTeamId)
      })
      .countDocuments()

    if (groupTopicCount >= TopicFeatures.MixGroupTopicsCountLimit) {
      throw new ForbiddenException('分组话题数达到上限')
    }
    const topicsInfo = await this.topicsModel.create({
      groupId: body.groupId ? new Types.ObjectId(body.groupId) : null,
      name: body.name,
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    return {
      id: topicsInfo.id,
      name: topicsInfo.name,
      groupId: topicsInfo.groupId?.toString()
    }
  }

  /**
   * 删除话题
   * @param topicsId
   * @returns
   */
  async deleteTopics(topicsId: string) {
    const { teamId: currentTeamId } = this.request.session
    const topics = await this.topicsModel.findOne({
      _id: new Types.ObjectId(topicsId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!topics) {
      throw new NotFoundException('话题不存在')
    }

    await this.topicsModel.deleteOne({
      _id: topics._id
    })
  }
}
