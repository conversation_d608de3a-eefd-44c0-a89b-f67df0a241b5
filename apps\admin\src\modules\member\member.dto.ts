import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'

export class FollowRecord {
  @ApiProperty({
    type: String,
    description: '客服ID'
  })
  customerId: string

  @ApiProperty({
    type: String,
    description: '客服名称'
  })
  customerName: string

  @ApiProperty({
    type: String,
    description: '跟进内容'
  })
  content: string

  @ApiProperty({
    type: Number,
    description: '时间'
  })
  createdAt: number
}

export class MemberDTO {
  @ApiProperty({
    type: String,
    description: '手机号',
    example: '13011112222'
  })
  phone: string

  @ApiProperty({
    type: String,
    description: '昵称',
    example: '张三'
  })
  nickName: string

  @ApiProperty({
    type: String,
    description: '头像',
    example: '头像'
  })
  avatar?: string

  @ApiProperty({
    type: Number,
    description: '注册时间',
    example: 1231094801
  })
  createdAt: number

  @ApiProperty({
    type: Number,
    description: '最后使用时间',
    example: 1231094801
  })
  updatedAt: number

  @ApiProperty({
    type: Number,
    description: '团队数',
    example: 1
  })
  teamCount: number

  @ApiProperty({
    type: String,
    description: '渠道码',
    example: '2001'
  })
  channelCode: string

  @ApiProperty({
    type: String,
    description: '归属客服名称',
    example: '刘强'
  })
  customerName: string

  @ApiProperty({
    type: String,
    description: '最新跟进记录',
    example: '刘强'
  })
  newfollowRecord: string

  @ApiProperty({
    type: Number,
    description: '跟进记录总数',
    example: 1
  })
  followRecordCount: number

  @ApiProperty({
    type: String,
    description: '注册来源 0.桌面应用 1.网站 2.移动应用 3.管理员手动注册 4.API注册 5.其他'
  })
  registrationSource?: string
}

export class MemberResponse {
  @ApiResponseProperty({
    type: [MemberDTO]
  })
  data: MemberDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class MemberResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MemberResponse
  })
  data: MemberResponse
}

export class MemberQueryDTO {
  @ApiProperty({
    type: String,
    description: '手机号',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  phone: string

  @ApiProperty({
    type: Number,
    description: '注册开始时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  createStartTime: number

  @ApiProperty({
    type: Number,
    description: '注册结束时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  createEndTime: number

  @ApiProperty({
    type: Number,
    description: '最后使用开始时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  lastUseStartTime: number

  @ApiProperty({
    type: Number,
    description: '最后使用结束时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  lastUseEndTime: number

  @ApiProperty({
    type: String,
    description: '归属人ID',
    example: '67330e084e0bc74cf56035fb',
    required: false
  })
  @IsOptional()
  customerId: string

  @ApiProperty({
    type: String,
    description: '应用id',
    example: '67330e084e0bc74cf56035fb',
    required: false
  })
  @IsOptional()
  applicationId: string

  @ApiProperty({
    type: String,
    description: '渠道码',
    example: '1001',
    required: false
  })
  @IsOptional()
  channelCode: string

  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class PatchCustomerRequest {
  @ApiProperty({
    type: String,
    description: '客服ID',
    example: '67330e084e0bc74cf56035fb',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '请选择客服' })
  customerId: string
}
