import { BadRequestException, ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { WxMediaType } from './wx-publish.dto'
import axios from 'axios'
import fileTypeFromBuffer from 'file-type'
import { WxBasicService } from './wx-basic.service'
import { load } from 'cheerio'
import { WxCacheKey } from './wx-cache-key'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { PlatformAccountEntity } from '@yxr/mongo'
import { Model } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import sharp from 'sharp'

/**
 * 微信相关业务的公共业务
 * 注意此服务有调用事件不能引入 FastifyRequest 微信相关功能会失效
 */
@Injectable()
export class WxCommonService {
  logger = new Logger('WxCommonService')

  private static readonly skipDomain = ['mmbiz.qpic.cn']

  constructor(
    private readonly wxBasicService: WxBasicService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>
  ) {}

  // 判断图片是否来自指定域名
  static shouldSkipProcessing(src) {
    return WxCommonService.skipDomain.some((domain) => src.includes(domain))
  }

  /**
   * 获取第三方平台接口调用凭证
   * @returns
   */
  async getComponetAccessToken(): Promise<string> {
    const componentAccessTokenCacheKey = WxCacheKey.ComponentAccessToken
    const accessToken = await this.cacheManager.get<string>(componentAccessTokenCacheKey)
    if (accessToken) {
      return accessToken
    }

    const componentVerifyTicketCacheKey = WxCacheKey.ComponentVerifyTicket
    //设置ticket票据
    const component_verify_ticket = await this.cacheManager.get<string>(
      componentVerifyTicketCacheKey
    )
    if (!componentVerifyTicketCacheKey) {
      this.logger.error('第三方微信票据不存在，请稍后再试')
      throw new ForbiddenException('系统繁忙，请稍后再试')
    }

    const component_appid = process.env.WX_THIRD_COMPONENT_APPID
    const component_appsecret = process.env.WX_THIRD_COMPONENT_APPSECRET
    const result = await this.wxBasicService.getComponentToken(
      component_verify_ticket,
      component_appid,
      component_appsecret
    )

    // 设置accessToken 缓存
    await this.cacheManager.set(
      componentAccessTokenCacheKey,
      result.component_access_token,
      WxCacheKey.AccessTokenTime
    )

    return result.component_access_token
  }

  /**
   * 获取授权账号access_token
   * @param authorizer_appid 授权账号的api_id
   * @param authorizer_refresh_token 授权账号的刷新token
   * @returns
   */
  async getAuthorizerAccessToken(authorizer_appid: string, authorizer_refresh_token: string) {
    const accessTokenCacheKey = WxCacheKey.getAccessToken(authorizer_appid)
    const accessToken = await this.cacheManager.get<string>(accessTokenCacheKey)

    if (accessToken) {
      return accessToken
    }

    const component_appid = process.env.WX_THIRD_COMPONENT_APPID
    const componentAccessToken = await this.getComponetAccessToken()
    const result = await this.wxBasicService.getAuthorizerAccessToken(
      authorizer_appid,
      component_appid,
      componentAccessToken,
      authorizer_refresh_token
    )
    if (result.data?.errcode) {
      await this.cacheManager.del(accessTokenCacheKey)
      throw new ForbiddenException('账号异常,稍后再试')
    }
    const access_token = result.authorizer_access_token
    await this.platformAccountModel.updateOne(
      {
        platformAuthorId: authorizer_appid
      },
      {
        token: result.authorizer_refresh_token
      }
    )
    // 微信授权账号access_token
    await this.cacheManager.set(accessTokenCacheKey, access_token, WxCacheKey.AccessTokenTime)

    return access_token
  }

  /**
   * 检查资源是否符合微信要求
   * @param type
   * @param fileName
   * @param byteLength
   * @returns
   */
  static checkMaterial(type: WxMediaType, fileName: string, byteLength: number) {
    switch (type) {
      case WxMediaType.image:
        const imageReg = /(bmp|png|jpeg|jpg|gif)$/i
        if (!fileName || !imageReg.test(fileName)) {
          console.log('图片仅支持bmp/png/jpeg/jpg/gif格式')
          return false
          // throw new BadRequestException('图片仅支持bmp/png/jpeg/jpg/gif格式')
        }
        if (byteLength > 1024 * 1024 * 10) {
          console.log('图片大小不能超过10MB')
          // return false
          throw new BadRequestException('图片大小不能超过10MB')
        }
        break
      case WxMediaType.voice:
        const voiceReg = /(mp3|wma|wav|amr)$/i
        if (!fileName || !voiceReg.test(fileName)) {
          console.log('语音仅支持bmp/png/jpeg/jpg/gif格式')
          return false
          // throw new BadRequestException('语音仅支持bmp/png/jpeg/jpg/gif格式')
        }
        if (byteLength > 1024 * 1024 * 2) {
          console.log('语音大小不能超过2MB')
          throw new BadRequestException('语音大小不能超过2MB')
        }
        break
      case WxMediaType.video:
        const videoReg = /(mp4)$/i
        if (!fileName || !videoReg.test(fileName)) {
          console.log('视频仅支持mp4格式')
          // return false
          throw new BadRequestException('视频仅支持mp4格式')
        }
        if (byteLength > 1024 * 1024 * 10) {
          console.log('视频大小不能超过10MB')
          throw new BadRequestException('视频大小不能超过10MB')
        }
        break
      case WxMediaType.thumb:
        const thumbReg = /(jpg)$/i
        if (!fileName || !thumbReg.test(fileName)) {
          console.log('缩略图仅支持jpg格式')
          return false
          // throw new BadRequestException('缩略图仅支持jpg格式')
        }
        if (byteLength > 1024 * 64) {
          console.log('缩略图大小不能超过64KB')
          return false
          // throw new BadRequestException('缩略图大小不能超过64KB')
        }
        break
      default:
        return false
        break
    }
    return true
  }

  /**
   * 上传媒体资源到微信素材库
   * @param access_token
   * @param url
   * @param type
   * @returns
   */
  async uploadWxMaterial(access_token: string, url: string, type: WxMediaType) {
    let response = null
    try {
      response = await axios({
        method: 'get',
        url: url,
        responseType: 'arraybuffer'
      })
    } catch (error) {
      console.log('图片获取失败：' + error)
      return null
    }

    let fileType = await fileTypeFromBuffer.fromBuffer(response.data)
    let fileBuffer = response.data
    const checkResult = WxCommonService.checkMaterial(type, fileType.mime, fileBuffer.length)
    if (!checkResult) {
      if (type == WxMediaType.image) {
        // 将不合规的图片转换为 JPG 格式
        const jpgBuffer = await sharp(response.data)
          .jpeg() // 转换为 JPG 格式
          .toBuffer()
        fileType = await fileTypeFromBuffer.fromBuffer(jpgBuffer)
        fileBuffer = jpgBuffer
      } else {
        //校验资源类型是否合法，不合法就不处理
        return null
      }
    }

    const fileName = 'lite微信发布_' + type + '_' + Date.now() + '.' + fileType.ext // 获取文件名，包含扩展名
    const result = await this.wxBasicService.postAddmaterial(
      access_token,
      fileBuffer,
      fileName,
      fileBuffer.length,
      fileType.mime,
      type
    )

    return result
  }

  /**
   * 替换内容里面的资源地址
   * @param access_token
   * @param content
   * @returns
   */
  async imgTagReplace(access_token: string, content: string) {
    const $ = load(content)
    //替换图片资源
    const imgTags = $('img')
    for (const imgTag of imgTags) {
      const src = $(imgTag).attr('src')
      if (src && !WxCommonService.shouldSkipProcessing(src)) {
        const imgResult = await this.uploadWxMaterial(access_token, src, WxMediaType.image)
        if (imgResult && imgResult.url) {
          const newSrc = imgResult.url
          $(imgTag).attr('src', newSrc)
        }
      }
    }

    //替换视频资源
    const videoTags = $('video')
    for (const videoTag of videoTags) {
      const videoSrc = $(videoTag).attr('src')
      if (videoSrc && !WxCommonService.shouldSkipProcessing(videoSrc)) {
        const videoResult = await this.uploadWxMaterial(access_token, videoSrc, WxMediaType.video)
        if (videoResult && videoResult.url) {
          const newVideoSrc = videoResult.url
          $(videoSrc).attr('src', newVideoSrc)
        }
      }
    }
    return $.html()
  }
}
