import { <PERSON>, HttpC<PERSON>, Inject, Logger, Post, Req, Res } from '@nestjs/common'
import { FastifyReply, FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import { TlsService } from '@yxr/huoshan'
import { CallBackDataEvent } from '@yxr/common'
import { PatchStatusRequest } from '../publish/task.dto'
import { OpenPlatformService } from './open-platform.service'
import { OpenPlatformBody } from './open-platform.dto'

@Controller()
export class OpenPlatformController {
  logger = new Logger('WebhookController')

  constructor(
    private readonly openPlatformService: OpenPlatformService,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly loggerService: TlsService
  ) {}

  @Post('open-platform/webhook')
  @HttpCode(200)
  async YxrOpenPlatformWebhook(@Req() req: FastifyRequest, @Res() res: FastifyReply) {
    const { body } = req as { body: OpenPlatformBody; headers: Record<string, string> }
    await this.loggerService.info(this.request, '蚁小二开放平台回调信息：', {
      body: JSON.stringify(body)
    })
    if(!body){
      return res.status(400).send('Invalid request body')
    }
    switch (body.data?.event) {
      case CallBackDataEvent.publishProcess:
      case CallBackDataEvent.publishResult:
      case CallBackDataEvent.auditStatus:
        await this.openPlatformService.patchStatus(
          body.data.callBackData.teamId,
          body.data.taskId,
          {
            stageStatus: body.data.stageStatus,
            stages: body.data.stages,
            documentId: body.data.documentId,
            publishId: body.data.publishId,
            openUrl: body.data.openUrl,
            errorMessage: body.data.errorMessage
          } as PatchStatusRequest
        )
        break
      case CallBackDataEvent.accountOverview:
        await this.openPlatformService.putPlatformAccountOverviews(
          body.data.callBackData.teamId,
          body.data.callBackData.platformAccountId,
          {
            overviewData: body.data.overviewData
          }
        )
        break
      case CallBackDataEvent.contentList:
        await this.openPlatformService.postContents(body.data.callBackData.teamId, {
          platformAccountId: body.data.callBackData.platformAccountId,
          contentStatisticsData: body.data.contents
        })
        break
      default:
        await this.loggerService.warn(this.request, '未知的蚁小二开放平台事件', {
          event: body.data?.event
        })
        break
    }
    res.status(200).send('success')
  }
}
