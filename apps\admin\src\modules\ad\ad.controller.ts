import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query
} from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { AdService } from './ad.service'
import {
  AdCreateRequestDTO,
  AdDetailResponseDTO,
  AdListRequestDTO,
  AdListResponseDTO,
  PutAdEnabledRequestDTO
} from './ad.dto'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('ad')
@ApiTags('广告管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class AdController {
  constructor(private readonly adService: AdService) {}

  @Get()
  @ApiOperation({ summary: '广告列表' })
  @ApiOkResponse({ type: AdListResponseDTO })
  getAds(@Query() query: AdListRequestDTO) {
    return this.adService.getAds(query)
  }

  @Post()
  @ApiOperation({ summary: '创建广告' })
  @ApiOkResponse({ description: '操作成功' })
  createAd(@Body() body: AdCreateRequestDTO) {
    if (!body.adUrl) {
      throw new ForbiddenException('请上传广告图片')
    }
    return this.adService.createAd(body)
  }

  @Patch(':adId')
  @ApiOperation({ summary: '更新广告' })
  @ApiOkResponse({ type: AdDetailResponseDTO })
  patchAdDetail(@Param('adId') adId: string, @Body() body: AdCreateRequestDTO) {
    return this.adService.patchAdDetail(adId, body)
  }

  @Put(':adId/enabled')
  @ApiOperation({ summary: '更新上下架状态' })
  @ApiOkResponse({ type: AdDetailResponseDTO })
  putAdEnabled(@Param('adId') adId: string, @Body() body: PutAdEnabledRequestDTO) {
    return this.adService.putAdEnabled(adId, body)
  }

  @Get(':adId')
  @ApiOperation({ summary: '广告详情' })
  @ApiOkResponse({ type: AdDetailResponseDTO })
  getAdDetail(@Param('adId') adId: string) {
    return this.adService.getAdDetail(adId)
  }

  @Delete(':adId')
  @ApiOperation({ summary: '广告删除' })
  @ApiOkResponse({ type: AdDetailResponseDTO })
  deleteAdDetail(@Param('adId') adId: string) {
    return this.adService.deleteAd(adId)
  }
}
