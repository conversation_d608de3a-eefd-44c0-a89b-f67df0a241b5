import { Global, Module } from '@nestjs/common'
import { JwtModule } from '@nestjs/jwt'
import {
  AdminMongoose,
  OpenPlatformUserMongoose,
  OpenPlatformApplicationMongoose
} from '@yxr/mongo'
import { OptimizedUnifiedTokenGuard } from './guards/unified-token.guard'
import { AccessControlGuard } from '../guards/access-control.guard'
import { OptimizedUnifiedAuthService } from './services/unified-auth.service'
import { ApplicationTokenService } from './services/application-token.service'
import { DataIsolationService } from '../services/data-isolation.service'
// 保持对原有服务的兼容性
import { UnifiedAuthService } from '../services/unified-auth.service'

/**
 * 统一认证模块
 * 
 * 这个模块集中管理所有认证相关的功能：
 * - 用户认证守卫
 * - 权限控制守卫
 * - 认证服务
 * - 相关的数据库实体
 * 
 * 使用 @Global() 装饰器，使得其他模块无需显式导入即可使用认证功能
 */
@Global()
@Module({
  imports: [
    // JWT模块配置
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' }
    }),
    // 认证相关的数据库实体
    AdminMongoose,
    OpenPlatformUserMongoose,
    OpenPlatformApplicationMongoose
  ],
  providers: [
    // 优化后的认证守卫
    OptimizedUnifiedTokenGuard,
    AccessControlGuard,

    // 优化后的认证服务
    OptimizedUnifiedAuthService,
    ApplicationTokenService,
    DataIsolationService,

    // 保持原有服务的兼容性
    UnifiedAuthService,

    // 为了向后兼容，提供别名
    {
      provide: 'UNIFIED_TOKEN_GUARD',
      useClass: OptimizedUnifiedTokenGuard
    }
  ],
  exports: [
    // 导出优化后的守卫
    OptimizedUnifiedTokenGuard,
    AccessControlGuard,

    // 导出认证服务
    OptimizedUnifiedAuthService,
    ApplicationTokenService,
    DataIsolationService,

    // 保持原有服务的兼容性
    UnifiedAuthService,

    // 导出JWT模块
    JwtModule,

    // 导出别名
    'UNIFIED_TOKEN_GUARD'
  ]
})
export class AuthModule {}
