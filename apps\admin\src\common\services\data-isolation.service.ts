import { Injectable, Inject } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import { UserType } from '@yxr/common'
import { FilterQuery } from 'mongoose'

/**
 * 数据隔离服务基类
 * 为应用Token访问提供自动数据过滤功能
 */
@Injectable()
export class DataIsolationService {
  constructor(@Inject(REQUEST) protected request: FastifyRequest) {}

  /**
   * 为查询条件添加数据隔离过滤
   * @param filter 原始查询条件
   * @param options 隔离选项
   */
  protected applyDataIsolation<T>(
    filter: FilterQuery<T>,
    options: {
      /**
       * 是否强制应用隔离（默认仅对APPLICATION用户类型应用）
       */
      forceIsolation?: boolean
      /**
       * 自定义隔离字段名（默认为sourceAppId）
       */
      isolationField?: string
      /**
       * 额外的隔离条件
       */
      additionalConditions?: FilterQuery<T>
    } = {}
  ): FilterQuery<T> {
    const {
      forceIsolation = false,
      isolationField = 'sourceAppId',
      additionalConditions = {}
    } = options

    // 检查是否需要应用数据隔离
    const shouldApplyIsolation = 
      forceIsolation || 
      (this.request.session?.userType === UserType.APPLICATION && this.request.dataIsolation)

    if (!shouldApplyIsolation) {
      return { ...filter, ...additionalConditions }
    }

    // 应用数据隔离
    const isolationFilter = {
      [isolationField]: this.request.dataIsolation!.applicationId
    }

    return {
      ...filter,
      ...isolationFilter,
      ...additionalConditions
    }
  }

  /**
   * 为创建数据添加隔离字段
   * @param data 原始数据
   * @param options 隔离选项
   */
  protected applyDataIsolationForCreate<T extends Record<string, any>>(
    data: T,
    options: {
      /**
       * 是否强制应用隔离
       */
      forceIsolation?: boolean
      /**
       * 自定义隔离字段名
       */
      isolationField?: string
      /**
       * 来源字段名
       */
      sourceField?: string
      /**
       * 开放平台用户ID字段名
       */
      openPlatformUserIdField?: string
    } = {}
  ): T {
    const {
      forceIsolation = false,
      isolationField = 'sourceAppId',
      sourceField = 'source',
      openPlatformUserIdField = 'openPlatformUserId'
    } = options

    // 检查是否需要应用数据隔离
    const shouldApplyIsolation = 
      forceIsolation || 
      (this.request.session?.userType === UserType.APPLICATION && this.request.dataIsolation)

    if (!shouldApplyIsolation) {
      return data
    }

    // 应用数据隔离
    const isolationData = {
      [sourceField]: 'open_platform_app',
      [isolationField]: this.request.dataIsolation!.applicationId
    }

    // 如果有开放平台用户ID，也添加进去
    if (this.request.session?.userId) {
      isolationData[openPlatformUserIdField] = this.request.session.userId
    }

    return {
      ...data,
      ...isolationData
    }
  }

  /**
   * 验证用户是否有权限访问指定数据
   * @param data 要验证的数据
   * @param options 验证选项
   */
  protected validateDataAccess<T extends Record<string, any>>(
    data: T,
    options: {
      /**
       * 隔离字段名
       */
      isolationField?: string
      /**
       * 是否允许admin用户访问所有数据
       */
      allowAdminAccess?: boolean
    } = {}
  ): boolean {
    const {
      isolationField = 'sourceAppId',
      allowAdminAccess = true
    } = options

    // Admin用户默认有权限访问所有数据
    if (allowAdminAccess && this.request.session?.userType === UserType.ADMIN) {
      return true
    }

    // 开放平台用户访问自己创建的数据
    if (this.request.session?.userType === UserType.OPEN_PLATFORM) {
      return true // 开放平台用户权限由其他机制控制
    }

    // 应用Token只能访问自己的数据
    if (this.request.session?.userType === UserType.APPLICATION && this.request.dataIsolation) {
      return data[isolationField] === this.request.dataIsolation.applicationId
    }

    return false
  }

  /**
   * 获取当前用户的隔离信息
   */
  protected getCurrentIsolationInfo(): {
    userType: string
    sourceAppId?: string
    userId?: string
  } | null {
    if (!this.request.session) {
      return null
    }

    return {
      userType: this.request.session.userType,
      sourceAppId: this.request.dataIsolation?.applicationId,
      userId: this.request.session.userId
    }
  }

  /**
   * 检查是否为应用Token访问
   */
  protected isApplicationTokenAccess(): boolean {
    return this.request.session?.userType === UserType.APPLICATION
  }

  /**
   * 检查是否为Admin用户访问
   */
  protected isAdminAccess(): boolean {
    return this.request.session?.userType === UserType.ADMIN
  }

  /**
   * 检查是否为开放平台用户访问
   */
  protected isOpenPlatformAccess(): boolean {
    return this.request.session?.userType === UserType.OPEN_PLATFORM
  }
}
