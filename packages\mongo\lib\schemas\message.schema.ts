import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { NoticeTypesEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class MessageEntity {
  /**
   * 消息标题
   */
  @Prop({
    type: String,
    required: true
  })
  title: string

  /**
   * 消息内容
   */
  @Prop({
    type: String,
    required: true
  })
  content: string

  @Prop({
    type: String,
    enum: {
      values: [NoticeTypesEnum.System],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: NoticeTypesEnum.System
  })
  type: NoticeTypesEnum

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isPopUp: boolean

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const MessageSchema: ModelDefinition = {
  name: MessageEntity.name,
  schema: SchemaFactory.createForClass(MessageEntity)
}

export const MessageMongoose = MongooseModule.forFeature([MessageSchema])
