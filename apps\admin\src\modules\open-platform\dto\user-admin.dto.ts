import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsOptional, IsString, IsNumber, Min, Max, MaxLength } from 'class-validator'
import { Transform } from 'class-transformer'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformStatus } from '@yxr/common'

/**
 * 获取开放平台用户列表请求DTO
 */
export class GetOpenPlatformUsersRequestDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 20,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => parseInt(value) || 20)
  size?: number = 20

  @ApiPropertyOptional({
    description: '手机号码搜索（支持模糊搜索）',
    example: '138'
  })
  @IsOptional()
  @IsString()
  phone?: string

  @ApiPropertyOptional({
    description: '注册开始日期',
    example: 20
  })
  @IsOptional()
  @IsString()
  registeredStartTime?: string

  @ApiPropertyOptional({
    description: '注册结束日期',
    example: 20
  })
  @IsOptional()
  @IsString()
  registeredEndTime?: string
}

/**
 * 修改用户备注请求DTO
 */
export class UpdateUserRemarkRequestDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  userId: string

  @ApiPropertyOptional({
    description: '备注信息',
    example: '重要客户，需要优先处理',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  remark?: string
}

/**
 * 获取用户应用详情请求DTO
 */
export class GetUserApplicationsRequestDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  userId: string
}

export class GetApplicationsRequestDto {
  @ApiProperty({
    description: '用户手机号',
    example: '13655429968'
  })
  @IsString()
  phone: string
}

/**
 * 开放平台用户信息DTO
 */
export class OpenPlatformUserInfoDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '手机号码',
    example: '13800138000'
  })
  phone: string

  @ApiPropertyOptional({
    description: '用户昵称',
    example: '张三'
  })
  nickname?: string

  @ApiProperty({
    description: '用户状态',
    enum: OpenPlatformStatus,
    example: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @ApiProperty({
    description: '应用数量',
    example: 3
  })
  applicationCount: number

  @ApiPropertyOptional({
    description: '备注信息',
    example: '重要客户'
  })
  remark?: string

  @ApiProperty({
    description: '注册时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '最后更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 用户应用详情DTO
 */
export class UserApplicationDetailDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: 'appId',
    example: 'app_123456'
  })
  appId: string

  @ApiProperty({
    description: '应用名称',
    example: '我的应用'
  })
  name: string

  @ApiProperty({
    description: '关联用户数',
    example: 15
  })
  userCount: number

  @ApiProperty({
    description: '关联团队数',
    example: 8
  })
  teamCount: number

  @ApiProperty({
    description: '账号总数',
    example: 120
  })
  totalAccountCount: number

  @ApiProperty({
    description: '流量使用情况（字节）',
    example: **********
  })
  totalTraffic: number

  @ApiProperty({
    description: '流量使用情况（MB）',
    example: 1000.5
  })
  totalTrafficMB: number

  @ApiProperty({
    description: '流量使用情况（字节）',
    example: **********
  })
  usedTraffic: number

  @ApiProperty({
    description: '流量使用情况（MB）',
    example: 1000.5
  })
  usedTrafficMB: number

  @ApiProperty({
    description: '蚁币',
    example: 1000.5
  })
  availableBalance: number

  @ApiProperty({
    description: '账号价格',
    example: 1000.5
  })
  accountPrice: number

  @ApiProperty({
    description: '流量价格',
    example: 1000.5
  })
  trafficPrice: number

  @ApiProperty({
    description: '应用状态',
    enum: OpenPlatformStatus,
    example: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @ApiProperty({
    description: '应用注册时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '最后更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 开放平台用户列表响应DTO
 */
export class OpenPlatformUsersListDto {
  @ApiProperty({
    description: '用户列表',
    type: [OpenPlatformUserInfoDto]
  })
  data: OpenPlatformUserInfoDto[]

  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 20
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 5
  })
  totalPage: number
}

/**
 * 用户应用列表DTO
 */
export class UserApplicationsListDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiProperty({
    description: '用户手机号',
    example: '13800138000'
  })
  userPhone: string

  @ApiProperty({
    description: '应用列表',
    type: [UserApplicationDetailDto]
  })
  applications: UserApplicationDetailDto[]

  @ApiProperty({
    description: '应用总数',
    example: 3
  })
  totalApplications: number
}

/**
 * 获取开放平台用户列表响应DTO
 */
export class GetOpenPlatformUsersResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: OpenPlatformUsersListDto
  })
  data: OpenPlatformUsersListDto
}

/**
 * 获取用户应用详情响应DTO
 */
export class GetUserApplicationsResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: UserApplicationsListDto
  })
  data: UserApplicationsListDto
}

/**
 * 修改用户备注响应DTO
 */
export class UpdateUserRemarkResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: 'object',
    properties: {
      userId: { type: 'string', example: '507f1f77bcf86cd799439011' },
      remark: { type: 'string', example: '重要客户，需要优先处理' },
      updatedAt: { type: 'number', example: ************* }
    }
  })
  data: {
    userId: string
    remark?: string
    updatedAt: number
  }
}

export class UpdateApplicationPriceRequestDto {
  @ApiProperty({
    description: '账号价格',
    example: 1
  })
  @IsNumber()
  accountPrice: number

  @ApiProperty({
    description: '流量价格',
    example: 0.5
  })
  @IsNumber()
  trafficPrice: number
}
