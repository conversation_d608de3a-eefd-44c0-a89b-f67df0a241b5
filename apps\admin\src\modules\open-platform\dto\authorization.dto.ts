import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsArray, IsEnum, IsOptional } from 'class-validator'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformPermissions, OpenPlatformStatus } from '@yxr/common'

export class CreateAuthorizationRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  applicationId: string

  @ApiProperty({
    description: '渠道商用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  channelUserId: string

  @ApiProperty({
    description: '授权权限列表',
    example: ['view_application', 'generate_token'],
    type: [String],
    enum: OpenPlatformPermissions.All
  })
  @IsArray()
  @IsEnum(OpenPlatformPermissions.All, { each: true, message: '权限值无效' })
  permissions: string[]
}

export class UpdateAuthorizationRequestDto {
  @ApiProperty({
    description: '授权权限列表',
    example: ['view_application', 'generate_token'],
    type: [String],
    enum: OpenPlatformPermissions.All
  })
  @IsArray()
  @IsEnum(OpenPlatformPermissions.All, { each: true, message: '权限值无效' })
  permissions: string[]

  @ApiProperty({
    description: '授权状态',
    example: 0,
    enum: OpenPlatformStatus
  })
  @IsEnum(OpenPlatformStatus, { message: '状态值无效' })
  status: OpenPlatformStatus
}

export class AuthorizationDto {
  @ApiResponseProperty({
    example: '授权ID'
  })
  id: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiResponseProperty({
    example: '我的应用'
  })
  applicationName: string

  @ApiResponseProperty({
    example: 'app_1234567890abcdef'
  })
  applicationAppId: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  channelUserId: string

  @ApiResponseProperty({
    example: '13800138000'
  })
  channelUserPhone: string

  @ApiResponseProperty({
    example: '张三'
  })
  channelUserNickname: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  authorizedBy: string

  @ApiResponseProperty({
    example: '13800138001'
  })
  authorizedByPhone: string

  @ApiResponseProperty({
    example: '李四'
  })
  authorizedByNickname: string

  @ApiResponseProperty({
    example: ['view_application', 'generate_token'],
    type: [String]
  })
  permissions: string[]

  @ApiResponseProperty({
    example: 0
  })
  status: number

  @ApiResponseProperty({
    example: 1640995200000
  })
  createdAt: number

  @ApiResponseProperty({
    example: 1640995200000
  })
  updatedAt: number
}

export class AuthorizationListRequestDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  size?: number = 10

  @ApiProperty({
    description: '应用ID筛选',
    example: '507f1f77bcf86cd799439011',
    required: false
  })
  @IsOptional()
  @IsString()
  applicationId?: string

  @ApiProperty({
    description: '授权状态筛选',
    example: 0,
    enum: OpenPlatformStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(OpenPlatformStatus)
  status?: OpenPlatformStatus

  @ApiProperty({
    description: '搜索关键词（渠道商手机号或昵称）',
    example: '张三',
    required: false
  })
  @IsOptional()
  @IsString()
  keyword?: string
}

export class AuthorizationListResponseDto {
  @ApiResponseProperty({
    type: [AuthorizationDto]
  })
  data: AuthorizationDto[]

  @ApiResponseProperty({
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  size: number
}

export class ChannelUserDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '13800138000'
  })
  phone: string

  @ApiResponseProperty({
    example: '张三'
  })
  nickname: string

  @ApiResponseProperty({
    example: false
  })
  isAuthorized: boolean
}

export class ChannelUserListResponseDto {
  @ApiResponseProperty({
    type: [ChannelUserDto]
  })
  list: ChannelUserDto[]

  @ApiResponseProperty({
    example: 100
  })
  total: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  limit: number
}

export class CreateAuthorizationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AuthorizationDto
  })
  data: AuthorizationDto
}

export class GetAuthorizationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AuthorizationDto
  })
  data: AuthorizationDto
}

export class GetAuthorizationListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AuthorizationListResponseDto
  })
  data: AuthorizationListResponseDto
}

export class GetChannelUserListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ChannelUserListResponseDto
  })
  data: ChannelUserListResponseDto
}
