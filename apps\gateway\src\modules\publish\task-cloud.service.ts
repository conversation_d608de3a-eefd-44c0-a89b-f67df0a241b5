import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
  ContentEntity,
  PlatformAccountCookieEntity,
  PlatformAccountEntity,
  TaskEntity
} from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import {
  CacheKeyService,
  EventNames,
  PlatformNameEnum,
  PlatformType,
  StageStatus,
  TaskAuditStatusEvent,
  TaskStages,
  TaskStatusEmun
} from '@yxr/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService } from '@yxr/huoshan'
import { WxPublishService } from '../wx-third-platform/wx-publish.service'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { CloudBaseDTO, ContentDeleteDTO, YxrOpenPlatformService } from '@yxr/yxr-open-platform'

@Injectable()
export class TaskCloudService {
  constructor(
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(ContentEntity.name)
    private contentModel: Model<ContentEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly loggerService: TlsService,
    private readonly wxPublishService: WxPublishService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private eventEmitter: EventEmitter2,
    private readonly yxrOpenPlatformService: YxrOpenPlatformService
  ) {}

  /**
   * 取消发布中任务
   * @param taskId
   * @param query
   * @returns
   */
  async cancelTask(taskId: string) {
    const { teamId: currentTeamId } = this.request.session

    const task = await this.taskModel.findOne({
      taskId: taskId,
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!task) {
      throw new NotFoundException('任务不存在')
    }
    if (task.taskStatus) {
      throw new ForbiddenException('任务已撤销')
    }
    if (task.stages !== TaskStages.Upload && task.stages !== TaskStages.Push) {
      throw new ForbiddenException('任务不是发布状态，无法撤销')
    }
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(task.platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }
    if (
      platformAccount.platformName === PlatformNameEnum.微信公众号 &&
      platformAccount.platformType === PlatformType.开放平台
    ) {
      throw new ForbiddenException(`${platformAccount.platformName}发布无法撤销`)
    }
    try {
      const result = await this.yxrOpenPlatformService.taskCancel(taskId)
      if (result.data.statusCode === 0) {
        await this.taskModel.updateOne(
          {
            taskId: taskId
          },
          {
            taskStatus: TaskStatusEmun.Cancel
          }
        )
      } else {
        await this.loggerService.error(this.request, '撤销发布任务失败', {
          taskId: taskId,
          error: JSON.stringify(result.data)
        })
        throw new ForbiddenException('撤销发布任务失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '撤销发布任务失败', {
        taskId: taskId,
        error: error.message
      })
      throw new ForbiddenException('撤销发布任务失败')
    }
  }

  /**
   * 删除平台发布上内容
   * @param platformAccountId
   * @returns
   */
  async deletePublishTask(taskId: string) {
    const { teamId: currentTeamId } = this.request.session

    const task = await this.taskModel.findOne({
      taskId: taskId,
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!task) {
      throw new NotFoundException('任务不存在')
    }
    if (task.taskStatus) {
      throw new ForbiddenException('作品已删除')
    }
    const content = await this.contentModel
      .findOne({
        _id: new Types.ObjectId(task.contentId)
      })
      .select('isDraft')
      .lean()
    if (!content) {
      throw new NotFoundException('内容不存在')
    }
    if (
      task.stages === TaskStages.Upload ||
      (task.stages === TaskStages.Push && content.isDraft === false)
    ) {
      throw new ForbiddenException('任务不是发布状态，无法删除')
    }
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(task.platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }
    try {
      if (
        platformAccount.platformName === PlatformNameEnum.微信公众号 &&
        platformAccount.platformType === PlatformType.开放平台
      ) {
        await this.wxPublishService.deletePublishTask(
          platformAccount.platformAuthorId,
          platformAccount.token,
          task.publishId,
          task.documentId
        )
      } else {
        let cookie = null
        if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
          cookie = platformAccount.token
        } else {
          const { cookie: accountCookie, localStorage } =
            await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
          cookie = accountCookie
        }

        const contentDeleteData: ContentDeleteDTO = {
          callBackData: {
            teamId: platformAccount.teamId.toString(),
            userId: platformAccount.userId.toString()
          },
          platform: platformAccount.platformName,
          platformAccountId: platformAccount.id.toString(),
          cookie: cookie,
          docId: task.documentId,
          platformContentType: task.publishType,
          isDraft: content.isDraft
        }
        const result = await this.yxrOpenPlatformService.contentDelete(contentDeleteData)
        if (result.data.statusCode === 0) {
          await this.taskModel.updateOne(
            {
              taskId: taskId
            },
            {
              taskStatus: TaskStatusEmun.Deleted,
              stageStatus: StageStatus.Fail,
              errorMessage: '作品已删除'
            }
          )
          // 触发任务状态变更事件
          await this.eventEmitter.emitAsync(
            EventNames.TaskAuditStatusChangedEvent,
            new TaskAuditStatusEvent(currentTeamId, task.taskSetId)
          )
        } else {
          await this.loggerService.error(this.request, '删除发布内容失败', {
            platformAccountId: platformAccount.id,
            error: JSON.stringify(result.data),
            requestData: JSON.stringify(contentDeleteData)
          })
          throw new ForbiddenException('删除发布内容失败')
        }
      }
    } catch (error) {
      await this.loggerService.error(this.request, '删除发布内容失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('删除发布内容失败')
    }
  }

  private async getShipinhaoInfo(teamId: string, parentId: string) {
    const wxkey = await this.cacheManager.get<string>(
      CacheKeyService.getWeiXinAccountLockKey(teamId.toString(), parentId.toString())
    )
    const shipinhao = await this.platformAccountModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        parentId: new Types.ObjectId(parentId)
      })
      .sort({ updatedAt: -1 })
      .select('token')
      .lean()

    const token = shipinhao.token

    return { wxkey: wxkey, token: token }
  }
}
