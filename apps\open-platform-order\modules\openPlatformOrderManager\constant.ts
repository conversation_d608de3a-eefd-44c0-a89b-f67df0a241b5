/**
 * 开放平台订单相关常量
 */

/**
 * 订单编号前缀
 */
export const ORDER_NO_PREFIX = 'OP'

/**
 * 默认订单配置
 */
export const DEFAULT_ORDER_CONFIG = {
  // 默认账号数量
  DEFAULT_ACCOUNT_COUNT: 1,
  // 默认流量数量（GB）
  DEFAULT_TRAFFIC_COUNT: 10,
  // 默认时长（月）
  DEFAULT_DURATION: 1,
  // 最小时长（月）
  MIN_DURATION: 0.1,
  // 最大时长（月）
  MAX_DURATION: 120,
  // 最小账号数量
  MIN_ACCOUNT_COUNT: 1,
  // 最大账号数量
  MAX_ACCOUNT_COUNT: 10000,
  // 最小流量数量（GB）
  MIN_TRAFFIC_COUNT: 1,
  // 最大流量数量（GB）
  MAX_TRAFFIC_COUNT: 100000
}

/**
 * 订单价格配置（以分为单位，避免浮点数精度问题）
 */
export const ORDER_PRICING_CONFIG = {
  // 主订单价格配置
  MAIN_ORDER: {
    // 账号单价：每个账号每月价格（分）
    ACCOUNT_PRICE_PER_MONTH: 1000, // 10.00元
    // 流量单价：每GB每月价格（分）
    TRAFFIC_PRICE_PER_GB_PER_MONTH: 50 // 0.50元
  },
  // 增购订单价格配置
  ADDON_ORDER: {
    // 账号单价：每个账号每月价格（分）
    ACCOUNT_PRICE_PER_MONTH: 1000, // 10.00元
    // 流量单价：每GB每月价格（分）
    TRAFFIC_PRICE_PER_GB_PER_MONTH: 50 // 0.50元
  }
}

/**
 * 金额计算配置
 */
export const AMOUNT_CONFIG = {
  // 货币单位转换
  CENTS_PER_YUAN: 100,
  // 金额精度（小数位数）
  AMOUNT_PRECISION: 2,
  // 最小订单金额（分）
  MIN_ORDER_AMOUNT: 1, // 0.01元
  // 最大订单金额（分）
  MAX_ORDER_AMOUNT: ********* // 1,000,000.00元
}

/**
 * 订单状态管理配置
 */
export const ORDER_STATUS_CONFIG = {
  // 订单过期检查间隔（毫秒）
  EXPIRY_CHECK_INTERVAL: 60 * 60 * 1000, // 1小时
  // 批量处理订单数量
  BATCH_PROCESS_SIZE: 50,
  // VIP状态更新延迟（毫秒）
  VIP_UPDATE_DELAY: 5 * 1000, // 5秒
  // 定时任务批次间延迟（毫秒）
  BATCH_DELAY: 100,
  // 已取消订单清理天数
  CLEANUP_CANCELLED_ORDERS_DAYS: 30
}

/**
 * 团队VIP配置
 */
export const TEAM_VIP_CONFIG = {
  // 默认账号数量限制
  DEFAULT_ACCOUNT_LIMIT: 5,
  // 默认流量限制（GB）
  DEFAULT_TRAFFIC_LIMIT: 50,
  // VIP过期缓冲时间（毫秒）
  EXPIRY_BUFFER_TIME: 24 * 60 * 60 * 1000 // 24小时
}

/**
 * 错误消息
 */
export const ERROR_MESSAGES = {
  TEAM_NOT_FOUND: '团队不存在',
  ORDER_NOT_FOUND: '订单不存在',
  APPLICATION_NOT_FOUND: '应用不存在',
  OVERLAPPING_MAIN_ORDER: '新主订单的时间与现有主订单重叠，主订单时间不能重叠',
  NO_VALID_MAIN_ORDER: '团队必须至少有一个有效的主订单才能创建增购订单',
  INVALID_START_TIME: '订单生效时间不能小于当天',
  INVALID_ADDON_START_TIME: '增购订单生效时间不能小于当天',
  INVALID_DURATION: '订单时长必须大于0',
  INVALID_ACCOUNT_COUNT: '账号数量必须大于0',
  INVALID_TRAFFIC_COUNT: '流量数量必须大于0',
  INSUFFICIENT_BALANCE: '应用余额不足，无法创建订单',
  INVALID_ORDER_AMOUNT: '订单金额计算错误',
  AMOUNT_CALCULATION_FAILED: '金额计算失败',
  BALANCE_DEDUCTION_FAILED: '余额扣除失败',
  ORDER_CREATE_FAILED: '订单创建失败',
  VIP_UPDATE_FAILED: 'VIP状态更新失败',
  PERMISSION_DENIED: '权限不足'
}

/**
 * 成功消息
 */
export const SUCCESS_MESSAGES = {
  ORDER_CREATED: '订单创建成功',
  ORDER_UPDATED: '订单更新成功',
  ORDER_CANCELLED: '订单取消成功',
  VIP_STATUS_UPDATED: 'VIP状态更新成功',
  ADDON_ORDERS_INVALIDATED: '增购订单已失效'
}

/**
 * 日志消息模板
 */
export const LOG_TEMPLATES = {
  ORDER_CREATED: (orderNo: string, teamId: string) =>
    `订单创建成功: ${orderNo}, 团队: ${teamId}`,
  ORDER_FAILED: (error: string) =>
    `订单创建失败: ${error}`,
  VIP_UPDATED: (teamId: string, isVip: boolean, accountCount: number, traffic: number) =>
    `团队VIP状态更新: teamId=${teamId}, isVip=${isVip}, accountCount=${accountCount}, traffic=${traffic}`,
  ADDON_INVALIDATED: (count: number, teamId: string) =>
    `主订单失效，自动取消 ${count} 个增购订单: teamId=${teamId}`,
  ADDON_BENEFIT_ADDED: (orderNo: string, accountBenefit: number, trafficBenefit: number) =>
    `增购订单权益累加: 订单${orderNo}, 权益=[账号:${accountBenefit}, 流量:${trafficBenefit}]`,
  SCHEDULER_START: () => '开始执行定时任务：检查过期订单和更新团队VIP状态',
  SCHEDULER_COMPLETE: () => '定时任务执行完成',
  BATCH_UPDATE_START: (count: number) => `开始批量更新 ${count} 个团队的VIP状态`,
  BATCH_UPDATE_COMPLETE: () => '批量更新团队VIP状态完成'
}

/**
 * 缓存键前缀
 */
export const CACHE_KEYS = {
  TEAM_VIP_STATUS: 'team_vip_status:',
  ORDER_STATISTICS: 'order_statistics:',
  TEAM_ORDER_SUMMARY: 'team_order_summary:'
}

/**
 * 事件名称
 */
export const EVENT_NAMES = {
  ORDER_CREATED: 'order.created',
  ORDER_UPDATED: 'order.updated',
  ORDER_CANCELLED: 'order.cancelled',
  VIP_STATUS_CHANGED: 'vip.status.changed',
  ADDON_ORDERS_INVALIDATED: 'addon.orders.invalidated'
}
