import { ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types } from 'mongoose'
import { TeamEntity, UserDeviceLogsEntity, UserDevicesEntity, UserEntity } from '@yxr/mongo'
import {
  DeviceLogsQueryDTO,
  MemberDeviceLogsResponse,
  MemberDevicesResponse
} from './member-devices.dto'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { TosService } from '@yxr/huoshan'

@Injectable()
export class MemberDevicesService {
  logger = new Logger('MemberDevicesService')

  constructor(
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(UserDevicesEntity.name) private userDevicesModel: Model<UserDevicesEntity>,
    @InjectModel(UserDeviceLogsEntity.name)
    private userDeviceLogsModel: Model<UserDeviceLogsEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    private readonly webhookService: WebhookService,
    private readonly ossService: TosService
  ) {}

  /**
   * 生成设备日志
   * @param id
   * @param deviceId
   */
  async postDeviceLog(id: string) {
    const userDevice = await this.userDevicesModel.findById(new Types.ObjectId(id))
    if (userDevice.isActive === false) {
      throw new ForbiddenException('该设备是离线状态不能获取日志')
    }

    const user = await this.userModel.findOne({
      _id: new Types.ObjectId(userDevice.userId)
    })
    if (!user) {
      throw new NotFoundException('用户不存在')
    }
    const createData: UserDeviceLogsEntity = {
      userId: new Types.ObjectId(userDevice.userId),
      phone: user.phone,
      avatar: user.avatar,
      nickName: user.nickName,
      deviceId: userDevice.deviceId
    }

    const deviceLogs = await this.userDeviceLogsModel.create(createData)

    if (deviceLogs) {
      await this.webhookService.grpchook([userDevice.userId.toString()], null, {
        event: WebhookEvents.DeviceLogDownload,
        body: {
          id: deviceLogs.id,
          deviceId: deviceLogs.deviceId
        }
      })
    }
  }

  /**
   * 查看设备
   * @returns
   */
  async getMemberDevices(id: string): Promise<MemberDevicesResponse[]> {
    const list = await this.userDevicesModel
      .find({
        userId: new Types.ObjectId(id)
      })
      .lean()

    return list.map((item) => {
      return {
        id: item._id.toString(),
        userId: item.userId.toString(),
        version: item.version,
        deviceId: item.deviceId,
        loginTime: item.loginTime.getTime(),
        isActive: item.isActive
      }
    })
  }

  /**
   * 获取设备日志列表
   * @param query
   * @returns
   */
  async getDeviceLogs(query: DeviceLogsQueryDTO): Promise<MemberDeviceLogsResponse> {
    const page = query.page
    const size = query.size

    const filter: FilterQuery<UserDeviceLogsEntity> = {}

    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' }
    }
    if (query.nickName) {
      filter.nickName = { $regex: query.nickName, $options: 'i' }
    }

    if (query.createStartTime && query.createEndTime) {
      filter.createdAt = {
        $gte: new Date(Number(query.createStartTime)),
        $lte: new Date(Number(query.createEndTime))
      }
    }

    const result = await this.userDeviceLogsModel
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.userDeviceLogsModel.find(filter).countDocuments()

    const data = await Promise.all(
      result.map(async (item) => {
        return {
          id: item.id.toString(),
          phone: item.phone,
          avatar: `${process.env.OSS_DOWNLOAD_URL}/${item.avatar}`,
          nickName: item.nickName,
          version: item.version,
          deviceId: item.deviceId,
          downloadLink: item?.downloadLink ? await this.ossService.getAccessSignatureUrl(item.downloadLink): null,
          createdAt: item.createdAt.getTime()
        }
      })
    )

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }

  /**
   * 删除日志
   * @param id
   */
  async deleteDeviceLogs(id: string) {
    const userDeviceLog = await this.userDeviceLogsModel.findById(new Types.ObjectId(id))
    if (!userDeviceLog) {
      throw new ForbiddenException('日志不存在')
    }

    if (userDeviceLog.downloadLink) {
      await this.ossService.deleteOssObject(userDeviceLog.downloadLink)
    }
    await this.userDeviceLogsModel.deleteOne({
      _id: userDeviceLog.id
    })
  }
}
