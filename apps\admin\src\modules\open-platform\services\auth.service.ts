import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { ConfigService } from '@nestjs/config'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import crypto from 'crypto'
import captcha20230305, * as $captcha20230305 from '@alicloud/captcha20230305'
import * as $OpenApi from '@alicloud/openapi-client'
import * as $Util from '@alicloud/tea-util'
import { OpenPlatformUserEntity } from '@yxr/mongo'
import { OpenPlatformStatus, UserType } from '@yxr/common'
import { RootConfigMap } from '@yxr/config'
import { UnifiedAuthService } from '../../../common/services/unified-auth.service'
import {
  SendCodeRequestDto,
  LoginRequestDto,
  LoginResponseDto,
  RegisterRequestDto,
  UnifiedAuthRequestDto,
  LogoutRequestDto,
  LogoutResponseDto
} from '../dto/auth.dto'

@Injectable()
export class OpenPlatformAuthService {
  private readonly logger = new Logger(OpenPlatformAuthService.name)

  constructor(
    @InjectModel(OpenPlatformUserEntity.name)
    private openPlatformUserModel: Model<OpenPlatformUserEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService,
    private unifiedAuthService: UnifiedAuthService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 发送验证码
   */
  async sendVerificationCode(sendCodeDto: SendCodeRequestDto): Promise<string | void> {
    const { phone, captchaVerifyParam } = sendCodeDto

    // 首先进行蚁小二验证码验证
    const captchaVerified = await this.verifyAliyunIntelligentCaptcha(
      '5aun0gpl',
      captchaVerifyParam
    )
    if (!captchaVerified) {
      throw new BadRequestException('人机验证失败，请重新验证')
    }

    const { smsCodeTime } = this.configService.get<RootConfigMap['app']>('app')

    const realSendSms = process.env.NODE_ENV === 'prod'
    const code = this.generateRandomCode().toString().padStart(6, '0')

    // 验证码发送逻辑（复用现有的短信服务配置）
    if (realSendSms) {
      // 这里应该调用实际的短信服务
      // 为了简化，暂时省略具体的短信发送实现
      this.logger.log(`发送验证码到 ${phone}: ${code}`)
    } else {
      this.logger.log(`验证码发送成功, 手机号: ${phone}, 验证码: ${code}`)
    }

    // 缓存验证码
    const cacheKey = `open_platform_sms:${phone}`
    await this.cacheManager.set(cacheKey, code, smsCodeTime)

    if (!realSendSms) {
      return code // 开发环境返回验证码
    }
  }

  /**
   * 统一认证（合并登录/注册逻辑）
   */
  async unifiedAuth(authDto: UnifiedAuthRequestDto): Promise<LoginResponseDto> {
    const { phone, code, password, nickname } = authDto

    // 验证验证码
    await this.validateSmsCode(phone, code)

    // 查找用户
    let user = await this.openPlatformUserModel.findOne({ phone })

    if (user) {
      // 用户已存在，执行登录逻辑
      if (user.status === OpenPlatformStatus.DISABLED) {
        throw new ForbiddenException('账号已被禁用')
      }

      // 如果提供了密码，更新用户密码
      if (password) {
        const { salt, hash } = this.hashPassword(password)
        user.password = hash
        user.salt = salt
        await user.save()
      }

      this.logger.log(`用户登录成功: ${phone}`)
    } else {
      // 用户不存在，执行注册逻辑
      const userData: Partial<OpenPlatformUserEntity> = {
        phone,
        nickname: nickname || `用户${phone.slice(-4)}`,
        status: OpenPlatformStatus.ACTIVE
      }

      // 如果提供了密码，则加密存储
      if (password) {
        const { salt, hash } = this.hashPassword(password)
        userData.password = hash
        userData.salt = salt
      }

      user = await this.openPlatformUserModel.create(userData)
      this.logger.log(`用户注册成功: ${phone}`)
    }

    // 生成Token
    const authorization = await this.unifiedAuthService.generateOpenPlatformToken(user)

    // 清除验证码缓存
    await this.cacheManager.del(`open_platform_sms:${phone}`)

    return {
      authorization,
      userInfo: {
        id: user._id.toString(),
        phone: user.phone,
        nickname: user.nickname,
        status: user.status
      }
    }
  }

  /**
   * 用户注册
   */
  async register(registerDto: RegisterRequestDto): Promise<LoginResponseDto> {
    const { phone, code, password, nickname } = registerDto

    // 验证验证码
    await this.validateSmsCode(phone, code)

    // 检查用户是否已存在
    const existingUser = await this.openPlatformUserModel.findOne({ phone })
    if (existingUser) {
      throw new BadRequestException('该手机号已注册')
    }

    // 创建用户
    const userData: Partial<OpenPlatformUserEntity> = {
      phone,
      nickname: nickname || `用户${phone.slice(-4)}`,
      status: OpenPlatformStatus.ACTIVE
    }

    // 如果提供了密码，则加密存储
    if (password) {
      const { salt, hash } = this.hashPassword(password)
      userData.password = hash
      userData.salt = salt
    }

    const newUser = await this.openPlatformUserModel.create(userData)

    // 生成Token
    const authorization = await this.unifiedAuthService.generateOpenPlatformToken(newUser)

    // 清除验证码缓存
    await this.cacheManager.del(`open_platform_sms:${phone}`)

    return {
      authorization,
      userInfo: {
        id: newUser._id.toString(),
        phone: newUser.phone,
        nickname: newUser.nickname,
        status: newUser.status
      }
    }
  }

  /**
   * 用户登录
   */
  async login(loginDto: LoginRequestDto): Promise<LoginResponseDto> {
    const { phone, code, password } = loginDto

    // 查找用户
    const user = await this.openPlatformUserModel.findOne({ phone })
    if (!user) {
      throw new NotFoundException('用户不存在，请先注册')
    }

    if (user.status === OpenPlatformStatus.DISABLED) {
      throw new ForbiddenException('账号已被禁用')
    }

    // 验证码登录
    if (code) {
      await this.validateSmsCode(phone, code)
      await this.cacheManager.del(`open_platform_sms:${phone}`)
    }
    // 密码登录
    else if (password) {
      if (!user.password || !user.salt) {
        throw new BadRequestException('该账号未设置密码，请使用验证码登录')
      }

      if (!this.verifyPassword(password, user.salt, user.password)) {
        throw new BadRequestException('密码错误')
      }
    } else {
      throw new BadRequestException('请提供验证码或密码')
    }

    // 生成Token
    const authorization = await this.unifiedAuthService.generateOpenPlatformToken(user)

    return {
      authorization,
      userInfo: {
        id: user._id.toString(),
        phone: user.phone,
        nickname: user.nickname,
        status: user.status
      }
    }
  }

  /**
   * 验证短信验证码
   */
  private async validateSmsCode(phone: string, code: string): Promise<void> {
    const cacheKey = `open_platform_sms:${phone}`
    const cachedCode = await this.cacheManager.get<string>(cacheKey)

    if (!cachedCode || cachedCode !== code) {
      throw new BadRequestException('验证码无效或已过期')
    }
  }

  /**
   * 生成随机验证码
   */
  private generateRandomCode(): number {
    return Math.floor(Math.random() * 900000) + 100000
  }

  /**
   * 密码加密
   */
  private hashPassword(password: string): { salt: string; hash: string } {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  /**
   * 密码验证
   */
  private verifyPassword(password: string, salt: string, hash: string): boolean {
    const verifyHash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === verifyHash
  }

  /**
   * 创建阿里云验证码客户端
   */
  private static createCaptchaClient(): captcha20230305 {
    let config = new $OpenApi.Config({
      accessKeyId: process.env.CAPTCHA_ACCESS_KEY_ID,
      accessKeySecret: process.env.CAPTCHA_ACCESS_KEY_SECRET
    })
    config.endpoint = `captcha.cn-shanghai.aliyuncs.com`
    return new captcha20230305(config)
  }

  /**
   * 退出登录
   * 清除用户的认证Token缓存和相关会话信息
   */
  async logout(logoutDto: LogoutRequestDto = {}): Promise<LogoutResponseDto> {
    const { logoutAllDevices = false } = logoutDto
    const { session } = this.request

    // 验证当前用户身份
    if (!session || session.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('无效的用户会话')
    }

    const userId = session.userId
    let clearedTokensCount = 0

    try {
      if (logoutAllDevices) {
        // 退出所有设备：清除该用户的所有Token
        clearedTokensCount = await this.clearAllUserTokens(userId)
        this.logger.log(`用户 ${userId} 退出所有设备，清除了 ${clearedTokensCount} 个Token`)
      } else {
        // 只退出当前设备：清除当前Token
        const currentToken = this.request.authorization
        if (currentToken) {
          clearedTokensCount = await this.clearCurrentToken(userId, currentToken)
          this.logger.log(`用户 ${userId} 退出当前设备，清除了当前Token`)
        }
      }

      return {
        success: true,
        message: logoutAllDevices ? '已退出所有设备' : '退出登录成功',
        clearedTokensCount
      }
    } catch (error) {
      this.logger.error(`用户 ${userId} 退出登录失败: ${error.message}`, error.stack)
      throw new BadRequestException('退出登录失败，请重试')
    }
  }

  /**
   * 清除用户的所有Token（退出所有设备）
   */
  private async clearAllUserTokens(userId: string): Promise<number> {
    let clearedCount = 0

    try {
      // 清除开放平台用户的主要缓存键
      const userIdKey = `open_platform_user:${userId}`
      const oldAuthorization = await this.cacheManager.get<string>(userIdKey)

      if (oldAuthorization) {
        // 清除相关的缓存键
        await Promise.all([
          this.cacheManager.del(userIdKey),
          this.cacheManager.del(`open_platform:${oldAuthorization}`),
          this.cacheManager.del(`unified:${oldAuthorization}`)
        ])
        clearedCount += 3
      }

      // 清除可能存在的gateway-user相关Token
      const gatewayUserIdKey = `session:ui-${userId}:open-platform`
      const gatewayToken = await this.cacheManager.get<string>(gatewayUserIdKey)

      if (gatewayToken) {
        await Promise.all([
          this.cacheManager.del(gatewayUserIdKey),
          this.cacheManager.del(gatewayToken)
        ])
        clearedCount += 2
      }

      return clearedCount
    } catch (error) {
      this.logger.error(`清除用户 ${userId} 所有Token失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 清除当前Token（只退出当前设备）
   */
  private async clearCurrentToken(userId: string, currentToken: string): Promise<number> {
    let clearedCount = 0

    try {
      // 清除当前Token相关的缓存
      const cacheKeysToDelete = [
        `open_platform:${currentToken}`,
        `unified:${currentToken}`,
        `session:au-${currentToken}`
      ]

      // 检查并清除存在的缓存键
      for (const key of cacheKeysToDelete) {
        const exists = await this.cacheManager.get(key)
        if (exists) {
          await this.cacheManager.del(key)
          clearedCount++
        }
      }

      // 清除用户ID映射（如果当前Token是最新的）
      const userIdKey = `open_platform_user:${userId}`
      const currentUserToken = await this.cacheManager.get<string>(userIdKey)

      if (currentUserToken === `open_platform:${currentToken}` || currentUserToken === `unified:${currentToken}`) {
        await this.cacheManager.del(userIdKey)
        clearedCount++
      }

      // 清除gateway-user相关Token（如果匹配）
      const gatewayUserIdKey = `session:ui-${userId}:open-platform`
      const gatewayToken = await this.cacheManager.get<string>(gatewayUserIdKey)

      if (gatewayToken === `session:au-${currentToken}`) {
        await Promise.all([
          this.cacheManager.del(gatewayUserIdKey),
          this.cacheManager.del(gatewayToken)
        ])
        clearedCount += 2
      }

      return clearedCount
    } catch (error) {
      this.logger.error(`清除用户 ${userId} 当前Token失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 验证阿里云智能验证码
   */
  private async verifyAliyunIntelligentCaptcha(
    sceneId: string,
    captchaVerifyParam: string
  ): Promise<boolean> {
    try {
      let client = OpenPlatformAuthService.createCaptchaClient()
      let verifyCaptchaRequest = new $captcha20230305.VerifyCaptchaRequest({
        sceneId: sceneId,
        captchaVerifyParam: captchaVerifyParam
      })

      const response = await client.verifyCaptchaWithOptions(
        verifyCaptchaRequest,
        new $Util.RuntimeOptions({
          ConnectTimeout: 5000,
          ReadTimeout: 5000
        })
      )

      const result = response.body.result.verifyResult
      this.logger.log(`验证码验证结果: ${result}`)
      return result
    } catch (error) {
      this.logger.error('阿里云验证码验证错误', error.message)
      return false
    }
  }
}
