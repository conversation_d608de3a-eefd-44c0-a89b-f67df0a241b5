import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import crypto from 'crypto'
import { nanoid } from 'nanoid'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformAppAuthorizationEntity,
  TeamEntity,
  MemberEntity,
  TrafficBillingEntity,
  PlatformAccountEntity,
  OpenPlatformApplicationBalanceEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  UserType,
  ApplicationType,
  WebhookStatus
} from '@yxr/common'
import {
  CreateApplicationRequestDto,
  UpdateApplicationRequestDto,
  ApplicationDto,
  ApplicationListRequestDto,
  ApplicationListResponseDto,
  ApplicationOverviewRequestDto,
  ApplicationOverviewDto,
  SetWebhookRequestDto,
  WebhookStatusDto
} from '../dto/application.dto'
import { WebhookService } from './webhook.service'

@Injectable()
export class ApplicationService {
  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformAppAuthorizationEntity.name)
    private authorizationModel: Model<OpenPlatformAppAuthorizationEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(TrafficBillingEntity.name)
    private trafficBillingModel: Model<TrafficBillingEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(OpenPlatformApplicationBalanceEntity.name)
    private balanceModel: Model<OpenPlatformApplicationBalanceEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private webhookService: WebhookService
  ) {}

  /**
   * 创建应用
   */
  async createApplication(createDto: CreateApplicationRequestDto): Promise<ApplicationDto> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建应用')
    }

    // 生成唯一的appId和secretKey
    const appId = this.generateAppId()
    const secretKey = this.generateSecretKey()

    const application = await this.applicationModel.create({
      userId: new Types.ObjectId(session.userId),
      name: createDto.name,
      appId,
      secretKey,
      description: createDto.description || '',
      status: OpenPlatformStatus.ACTIVE,
      applicationType: ApplicationType.TECHNICAL_SERVICE
    })

    // 创建用户角色关系（创建者自动成为管理员）
    await this.userRoleModel.create({
      userId: new Types.ObjectId(session.userId),
      applicationId: application._id,
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    return this.formatApplicationDto(application, OpenPlatformRoleNames.ADMIN, false, true)
  }

  /**
   * 获取应用列表
   */
  async getApplicationList(
    queryDto: ApplicationListRequestDto
  ): Promise<ApplicationListResponseDto> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问应用列表')
    }

    const { page = 1, size = 10, keyword, status, applicationType } = queryDto
    const skip = (page - 1) * size

    // 获取用户有权限访问的应用ID列表
    const userRoles = await this.userRoleModel.find({
      userId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    })

    const authorizations = await this.authorizationModel.find({
      channelUserId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    })

    // 合并用户角色和授权的应用ID
    const applicationIds = [
      ...userRoles.map((role) => role.applicationId),
      ...authorizations.map((auth) => auth.applicationId)
    ]

    if (applicationIds.length === 0) {
      return {
        data: [],
        totalPage: 0,
        totalSize: 0,
        page,
        size
      }
    }

    // 构建查询条件
    const query: any = {
      _id: { $in: applicationIds }
    }

    if (keyword) {
      query.name = { $regex: keyword, $options: 'i' }
    }

    if (status !== undefined) {
      query.status = status
    }

    if (applicationType !== undefined) {
      query.applicationType = applicationType
    }

    const [applications, total] = await Promise.all([
      this.applicationModel.find(query).skip(skip).limit(size).sort({ createdAt: -1 }).exec(),
      this.applicationModel.countDocuments(query)
    ])

    // 获取用户在每个应用中的角色
    const applicationDtos = await Promise.all(
      applications.map(async (app) => {
        const userRole = userRoles.find(
          (role) => role.applicationId.toString() === app._id.toString()
        )
        const authorization = authorizations.find(
          (auth) => auth.applicationId.toString() === app._id.toString()
        )

        const role = userRole?.role || OpenPlatformRoleNames.CHANNEL
        const isOwner = app.userId.toString() === session.userId

        return this.formatApplicationDto(app, role, !userRole, isOwner)
      })
    )

    return {
      data: applicationDtos,
      totalSize: total,
      totalPage: Math.ceil(total / size),
      page,
      size
    }
  }

  /**
   * 获取应用详情
   */
  async getApplicationById(applicationId: string): Promise<ApplicationDto> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问应用详情')
    }

    const application = await this.applicationModel
      .findById(new Types.ObjectId(applicationId))
      .lean()
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission) {
      throw new ForbiddenException('您没有权限访问此应用')
    }

    const isOwner = application.userId.toString() === session.userId
    return this.formatApplicationDto(
      application,
      role,
      role === OpenPlatformRoleNames.CHANNEL,
      isOwner
    )
  }

  /**
   * 更新应用
   */
  async updateApplication(
    applicationId: string,
    updateDto: UpdateApplicationRequestDto
  ): Promise<ApplicationDto> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以更新应用')
    }

    const application = await this.applicationModel
      .findById(new Types.ObjectId(applicationId))
      .lean()
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以更新）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以更新应用')
    }

    const updatedApplication = await this.applicationModel.findByIdAndUpdate(
      applicationId,
      {
        name: updateDto.name,
        description: updateDto.description,
        status: updateDto.status
      },
      { new: true }
    )

    const isOwner = updatedApplication.userId.toString() === session.userId
    return this.formatApplicationDto(updatedApplication, role, false, isOwner)
  }

  /**
   * 删除应用
   */
  async deleteApplication(applicationId: string): Promise<void> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以删除应用')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以删除）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以删除应用')
    }

    // 删除应用及相关数据
    await Promise.all([
      this.applicationModel.findByIdAndDelete(applicationId),
      this.userRoleModel.deleteMany({ applicationId: new Types.ObjectId(applicationId) }),
      this.authorizationModel.deleteMany({ applicationId: new Types.ObjectId(applicationId) })
    ])
  }

  /**
   * 重新生成应用密钥
   */
  async regenerateSecret(applicationId: string): Promise<string> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以重新生成密钥')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以重新生成密钥）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以重新生成密钥')
    }

    const newSecretKey = this.generateSecretKey()

    await this.applicationModel.findByIdAndUpdate(applicationId, {
      secretKey: newSecretKey
    })

    return newSecretKey
  }

  /**
   * 检查用户权限
   */
  private async checkUserPermission(
    userId: string,
    applicationId: string
  ): Promise<{
    role: string
    hasPermission: boolean
  }> {
    // 检查用户角色
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(userId),
      applicationId: new Types.ObjectId(applicationId),
      status: OpenPlatformStatus.ACTIVE
    })

    if (userRole) {
      return {
        role: userRole.role,
        hasPermission: true
      }
    }

    // 检查授权
    const authorization = await this.authorizationModel.findOne({
      applicationId: new Types.ObjectId(applicationId),
      channelUserId: new Types.ObjectId(userId),
      status: OpenPlatformStatus.ACTIVE
    })

    if (authorization) {
      return {
        role: OpenPlatformRoleNames.CHANNEL,
        hasPermission: true
      }
    }

    return {
      role: '',
      hasPermission: false
    }
  }

  /**
   * 格式化应用DTO
   */
  private async formatApplicationDto(
    application: OpenPlatformApplicationEntity,
    userRole: string,
    hideSecret: boolean = false,
    isOwner: boolean = false
  ): Promise<ApplicationDto> {
    const userIdentity: 'owner' | 'invited' = isOwner ? 'owner' : 'invited'
    const userIdentityLabel = isOwner ? '拥有者' : '受邀'

    // 安全要求：当应用类型为渠道商时，隐藏敏感信息
    const shouldHideSensitiveInfo = application.applicationType === ApplicationType.CHANNEL_PARTNER

    const teams = await this.teamModel
      .find({
        source: 'open_platform_app',
        sourceAppId: (application as any)._id.toString(),
        isDeleted: false
      })
      .select('_id')
      .lean()

    const teamIds = teams.map((team) => team._id)

    const [accountStats, trafficStats] = await Promise.all([
      this.getAccountPointsStats(teamIds),
      this.getTrafficStats(teamIds)
    ])

    const balance = await this.balanceModel
      .findOne({
        applicationId: (application as any)._id
      })
      .lean()

    const availableBalance = balance?.availableBalance || 0

    return {
      id: (application as any)._id.toString(),
      name: application.name,
      appId: application.appId,
      secretKey: hideSecret ? undefined : application.secretKey,
      availableBalance,
      totalAccountPoints: accountStats.total,
      usedAccountPoints: accountStats.used,
      totalTraffic: trafficStats.total,
      usedTraffic: trafficStats.used,
      description: application.description || '',
      status: application.status,
      applicationType: application.applicationType,
      userId: application.userId.toString(),
      createdAt: application.createdAt?.getTime() || 0,
      updatedAt: application.updatedAt?.getTime() || 0,
      userRole,
      canManage: userRole === OpenPlatformRoleNames.ADMIN,
      userIdentity,
      userIdentityLabel
    }
  }

  /**
   * 获取应用概览统计
   */
  async getApplicationOverview(applicationId: string): Promise<ApplicationOverviewDto> {
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看应用统计')
    }

    // 验证应用存在性和权限
    const application = await this.applicationModel.findById(new Types.ObjectId(applicationId)).lean()
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    const balance = await this.balanceModel
      .findOne({
        applicationId: new Types.ObjectId(applicationId)
      })
      .lean()

    // 验证用户是否有权限访问该应用
    const userRole = await this.userRoleModel
      .findOne({
        userId: new Types.ObjectId(session.userId),
        applicationId: new Types.ObjectId(applicationId)
      })
      .lean()

    if (!userRole) {
      throw new ForbiddenException('无权限访问该应用')
    }

    // 获取应用下的所有团队ID
    const teams = await this.teamModel
      .find({
        source: 'open_platform_app',
        sourceAppId: applicationId,
        isDeleted: false
      })
      .select('_id')
      .lean()

    const teamIds = teams.map((team) => team._id)

    // 如果没有团队，返回空统计
    if (teamIds.length === 0) {
      return {
        availableBalance: balance?.availableBalance || 0,
        totalAccountPoints: 0,
        usedAccountPoints: 0,
        totalTraffic: 0,
        usedTraffic: 0,
        totalUsers: 0,
        totalTeams: 0,
        dailyStats: []
      }
    }

    // 使用聚合查询获取统计数据
    const [accountStats, trafficStats, userStats, dailyStatsData] = await Promise.all([
      this.getAccountPointsStats(teamIds),
      this.getTrafficStats(teamIds),
      this.getUserStats(teamIds),
      this.getDailyStats(teamIds)
    ])

    return {
      availableBalance: balance?.availableBalance || 0,
      totalAccountPoints: accountStats.total,
      usedAccountPoints: accountStats.used,
      totalTraffic: trafficStats.total,
      usedTraffic: trafficStats.used,
      totalUsers: userStats.totalUsers,
      totalTeams: teamIds.length,
      dailyStats: dailyStatsData
    }
  }

  /**
   * 获取账号点数统计
   */
  private async getAccountPointsStats(
    teamIds: Types.ObjectId[]
  ): Promise<{ total: number; used: number }> {
    try {
      // 从团队实体获取账号点数总数
      const teamAccountStats = await this.teamModel.aggregate([
        { $match: { _id: { $in: teamIds } } },
        {
          $group: {
            _id: null,
            totalAccountPoints: { $sum: '$accountCountLimit' },
            usedAccountPoints: { $sum: '$accountCount' }
          }
        }
      ])

      const stats = teamAccountStats[0] || { totalAccountPoints: 0, usedAccountPoints: 0 }

      return {
        total: stats.totalAccountPoints || 0,
        used: stats.usedAccountPoints || 0
      }
    } catch (error) {
      console.error('获取账号点数统计失败:', error)
      return { total: 0, used: 0 }
    }
  }

  /**
   * 获取流量统计
   */
  private async getTrafficStats(
    teamIds: Types.ObjectId[]
  ): Promise<{ total: number; used: number }> {
    try {
      // 获取总流量（从团队的累积流量字段，单位：KB转GB）
      const totalTrafficStats = await this.teamModel.aggregate([
        { $match: { _id: { $in: teamIds } } },
        {
          $group: {
            _id: null,
            totalTrafficKB: { $sum: { $ifNull: ['$networkTraffic', 0] } }
          }
        }
      ])

      // 获取已使用流量（从流量计费记录，单位：字节转GB）
      const usedTrafficStats = await this.trafficBillingModel.aggregate([
        { $match: { teamId: { $in: teamIds } } },
        {
          $group: {
            _id: null,
            usedTrafficBytes: { $sum: { $ifNull: ['$useNetworkTraffic', 0] } }
          }
        }
      ])

      const totalKB = totalTrafficStats[0]?.totalTrafficKB || 0
      const usedBytes = usedTrafficStats[0]?.usedTrafficBytes || 0

      return {
        total: Math.round((totalKB / (1024 * 1024 * 1024)) * 100) / 100, // KB转GB，保留2位小数
        used: Math.round((usedBytes / (1024 * 1024 * 1024 * 1024)) * 100) / 100 // 字节转GB，保留2位小数
      }
    } catch (error) {
      console.error('获取流量统计失败:', error)
      return { total: 0, used: 0 }
    }
  }

  /**
   * 获取用户统计
   */
  private async getUserStats(teamIds: Types.ObjectId[]): Promise<{ totalUsers: number }> {
    try {
      // 统计团队成员数（去重用户）
      const userStats = await this.memberModel.aggregate([
        {
          $match: {
            teamId: { $in: teamIds },
            status: 'joined' // 只统计已加入的成员
          }
        },
        {
          $group: {
            _id: '$userId' // 按用户ID去重
          }
        },
        {
          $count: 'totalUsers'
        }
      ])

      return {
        totalUsers: userStats[0]?.totalUsers || 0
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return { totalUsers: 0 }
    }
  }

  /**
   * 获取近30天趋势数据
   */
  private async getDailyStats(teamIds: Types.ObjectId[]): Promise<any[]> {
    try {
      // 生成近30天的日期数组
      const dates = []
      for (let i = 30; i >= 1; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        dates.push(date.toISOString().split('T')[0]) // YYYY-MM-DD格式
      }

      // 获取每日流量使用统计
      const dailyTrafficStats = await this.trafficBillingModel.aggregate([
        {
          $match: {
            teamId: { $in: teamIds },
            createdAt: {
              $gte: new Date(dates[0]),
              $lte: new Date(dates[dates.length - 1] + 'T23:59:59.999Z')
            }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            usedTraffic: { $sum: { $ifNull: ['$useNetworkTraffic', 0] } }
          }
        }
      ])

      // 获取每日账号新增统计
      const dailyAccountStats = await this.platformAccountModel.aggregate([
        {
          $match: {
            teamId: { $in: teamIds },
            createdAt: {
              $gte: new Date(dates[0]),
              $lte: new Date(dates[dates.length - 1] + 'T23:59:59.999Z')
            }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
            },
            newAccountPoints: { $sum: 1 } // 每个账号算1个点数
          }
        }
      ])

      // 转换为Map以便快速查找
      const trafficMap = new Map(
        dailyTrafficStats.map((item) => [
          item._id,
          Math.round((item.usedTraffic / (1024 * 1024 * 1024)) * 100) / 100 // 字节转GB
        ])
      )

      const accountMap = new Map(dailyAccountStats.map((item) => [item._id, item.newAccountPoints]))

      // 构建完整的30天数据
      return dates.map((date) => ({
        date,
        newTraffic: 0, // 新开通流量暂时设为0，需要从订单系统获取
        usedTraffic: trafficMap.get(date) || 0,
        newAccountPoints: 0, // 新开通账号点数暂时设为0，需要从订单系统获取
        usedAccountPoints: accountMap.get(date) || 0
      }))
    } catch (error) {
      console.error('获取日统计数据失败:', error)
      return []
    }
  }

  /**
   * 生成应用ID
   */
  private generateAppId(): string {
    return `app_${nanoid(16)}`
  }

  /**
   * 生成密钥
   */
  private generateSecretKey(): string {
    return `sk_${crypto.randomBytes(16).toString('hex')}`
  }

  /**
   * 设置应用Webhook
   */
  async setWebhook(
    applicationId: string,
    setWebhookDto: SetWebhookRequestDto
  ): Promise<WebhookStatusDto> {
    const { session } = this.request
    const { webhookUrl } = setWebhookDto

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以设置Webhook')
    }

    // 验证应用存在性和权限
    const application = await this.applicationModel.findOne({
      _id: new Types.ObjectId(applicationId),
      userId: new Types.ObjectId(session.userId)
    })

    if (!application) {
      throw new NotFoundException('应用不存在或无权限访问')
    }

    try {
      // 更新应用状态为验证中
      await this.applicationModel.updateOne(
        { _id: new Types.ObjectId(applicationId) },
        {
          webhookUrl,
          webhookStatus: WebhookStatus.VERIFYING,
          webhookVerifiedAt: null
        }
      )

      if (process.env.NODE_ENV !== 'prod') {
        // 验证成功，更新状态
        await this.applicationModel.updateOne(
          { _id: new Types.ObjectId(applicationId) },
          {
            webhookStatus: WebhookStatus.VERIFIED,
            webhookVerifiedAt: new Date()
          }
        )

        return {
          webhookUrl,
          webhookStatus: WebhookStatus.VERIFIED,
          webhookVerifiedAt: Date.now()
        }
      }

      // 验证Webhook URL
      const verificationResult = await this.webhookService.verifyWebhookUrl(webhookUrl)

      if (verificationResult.success) {
        // 验证成功，更新状态
        await this.applicationModel.updateOne(
          { _id: new Types.ObjectId(applicationId) },
          {
            webhookStatus: WebhookStatus.VERIFIED,
            webhookVerifiedAt: new Date()
          }
        )

        return {
          webhookUrl,
          webhookStatus: WebhookStatus.VERIFIED,
          webhookVerifiedAt: Date.now()
        }
      } else {
        // 验证失败，更新状态
        await this.applicationModel.updateOne(
          { _id: new Types.ObjectId(applicationId) },
          {
            webhookStatus: WebhookStatus.FAILED,
            webhookVerifiedAt: null
          }
        )

        throw new BadRequestException(`Webhook验证失败: ${verificationResult.message}`)
      }
    } catch (error) {
      // 如果是我们抛出的BadRequestException，直接重新抛出
      if (error instanceof BadRequestException) {
        throw error
      }

      // 其他错误，更新状态为失败
      await this.applicationModel.updateOne(
        { _id: new Types.ObjectId(applicationId) },
        {
          webhookStatus: WebhookStatus.FAILED,
          webhookVerifiedAt: null
        }
      )

      throw new BadRequestException(`设置Webhook失败: ${error.message}`)
    }
  }

  /**
   * 获取应用Webhook状态
   */
  async getWebhookStatus(applicationId: string): Promise<WebhookStatusDto> {
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看Webhook状态')
    }

    // 验证应用存在性和权限
    const application = await this.applicationModel
      .findOne({
        _id: new Types.ObjectId(applicationId),
        userId: new Types.ObjectId(session.userId)
      })
      .lean()

    if (!application) {
      throw new NotFoundException('应用不存在或无权限访问')
    }

    return {
      webhookUrl: application.webhookUrl,
      webhookStatus: application.webhookStatus,
      webhookVerifiedAt: application.webhookVerifiedAt?.getTime()
    }
  }

  /**
   * 删除应用Webhook
   */
  async removeWebhook(applicationId: string): Promise<WebhookStatusDto> {
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以删除Webhook')
    }

    // 验证应用存在性和权限
    const application = await this.applicationModel.findOne({
      _id: new Types.ObjectId(applicationId),
      userId: new Types.ObjectId(session.userId)
    })

    if (!application) {
      throw new NotFoundException('应用不存在或无权限访问')
    }

    // 清除Webhook设置
    await this.applicationModel.updateOne(
      { _id: new Types.ObjectId(applicationId) },
      {
        webhookUrl: null,
        webhookStatus: WebhookStatus.NOT_SET,
        webhookVerifiedAt: null
      }
    )

    return {
      webhookUrl: undefined,
      webhookStatus: WebhookStatus.NOT_SET,
      webhookVerifiedAt: undefined
    }
  }
}
