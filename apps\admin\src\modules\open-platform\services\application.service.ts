import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import crypto from 'crypto'
import { nanoid } from 'nanoid'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformAppAuthorizationEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  UserType
} from '@yxr/common'
import {
  CreateApplicationRequestDto,
  UpdateApplicationRequestDto,
  ApplicationDto,
  ApplicationListRequestDto,
  ApplicationListResponseDto
} from '../dto/application.dto'

@Injectable()
export class ApplicationService {
  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformAppAuthorizationEntity.name)
    private authorizationModel: Model<OpenPlatformAppAuthorizationEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建应用
   */
  async createApplication(createDto: CreateApplicationRequestDto): Promise<ApplicationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建应用')
    }

    // 生成唯一的appId和secretKey
    const appId = this.generateAppId()
    const secretKey = this.generateSecretKey()

    const application = await this.applicationModel.create({
      userId: new Types.ObjectId(session.userId),
      name: createDto.name,
      appId,
      secretKey,
      description: createDto.description || '',
      status: OpenPlatformStatus.ACTIVE
    })

    // 创建用户角色关系（创建者自动成为管理员）
    await this.userRoleModel.create({
      userId: new Types.ObjectId(session.userId),
      applicationId: application._id,
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    return this.formatApplicationDto(application, OpenPlatformRoleNames.ADMIN, false, true)
  }

  /**
   * 获取应用列表
   */
  async getApplicationList(queryDto: ApplicationListRequestDto): Promise<ApplicationListResponseDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问应用列表')
    }

    const { page = 1, size = 10, keyword, status } = queryDto
    const skip = (page - 1) * size

    // 获取用户有权限访问的应用ID列表
    const userRoles = await this.userRoleModel.find({
      userId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    })

    const authorizations = await this.authorizationModel.find({
      channelUserId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    })

    // 合并用户角色和授权的应用ID
    const applicationIds = [
      ...userRoles.map(role => role.applicationId),
      ...authorizations.map(auth => auth.applicationId)
    ]

    if (applicationIds.length === 0) {
      return {
        data: [],
        totalPage: 0,
        totalSize: 0,
        page,
        size
      }
    }

    // 构建查询条件
    const query: any = {
      _id: { $in: applicationIds }
    }

    if (keyword) {
      query.name = { $regex: keyword, $options: 'i' }
    }

    if (status !== undefined) {
      query.status = status
    }

    const [applications, total] = await Promise.all([
      this.applicationModel
        .find(query)
        .skip(skip)
        .limit(size)
        .sort({ createdAt: -1 })
        .exec(),
      this.applicationModel.countDocuments(query)
    ])

    // 获取用户在每个应用中的角色
    const applicationDtos = await Promise.all(
      applications.map(async (app) => {
        const userRole = userRoles.find(role => 
          role.applicationId.toString() === app._id.toString()
        )
        const authorization = authorizations.find(auth => 
          auth.applicationId.toString() === app._id.toString()
        )
        
        const role = userRole?.role || OpenPlatformRoleNames.CHANNEL
        const isOwner = app.userId.toString() === session.userId
        return this.formatApplicationDto(app, role, !userRole, isOwner)
      })
    )

    return {
      data: applicationDtos,
      totalSize: total,
      totalPage: Math.ceil(total / size),
      page,
      size
    }
  }

  /**
   * 获取应用详情
   */
  async getApplicationById(applicationId: string): Promise<ApplicationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问应用详情')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission) {
      throw new ForbiddenException('您没有权限访问此应用')
    }

    const isOwner = application.userId.toString() === session.userId
    return this.formatApplicationDto(application, role, role === OpenPlatformRoleNames.CHANNEL, isOwner)
  }

  /**
   * 更新应用
   */
  async updateApplication(
    applicationId: string,
    updateDto: UpdateApplicationRequestDto
  ): Promise<ApplicationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以更新应用')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以更新）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以更新应用')
    }

    const updatedApplication = await this.applicationModel.findByIdAndUpdate(
      applicationId,
      {
        name: updateDto.name,
        description: updateDto.description,
        status: updateDto.status
      },
      { new: true }
    )

    const isOwner = updatedApplication.userId.toString() === session.userId
    return this.formatApplicationDto(updatedApplication, role, false, isOwner)
  }

  /**
   * 删除应用
   */
  async deleteApplication(applicationId: string): Promise<void> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以删除应用')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以删除）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以删除应用')
    }

    // 删除应用及相关数据
    await Promise.all([
      this.applicationModel.findByIdAndDelete(applicationId),
      this.userRoleModel.deleteMany({ applicationId: new Types.ObjectId(applicationId) }),
      this.authorizationModel.deleteMany({ applicationId: new Types.ObjectId(applicationId) })
    ])
  }

  /**
   * 重新生成应用密钥
   */
  async regenerateSecret(applicationId: string): Promise<string> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以重新生成密钥')
    }

    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查用户权限（只有管理员可以重新生成密钥）
    const { role, hasPermission } = await this.checkUserPermission(session.userId, applicationId)
    if (!hasPermission || role !== OpenPlatformRoleNames.ADMIN) {
      throw new ForbiddenException('只有应用管理员可以重新生成密钥')
    }

    const newSecretKey = this.generateSecretKey()
    
    await this.applicationModel.findByIdAndUpdate(applicationId, {
      secretKey: newSecretKey
    })

    return newSecretKey
  }

  /**
   * 检查用户权限
   */
  private async checkUserPermission(userId: string, applicationId: string): Promise<{
    role: string
    hasPermission: boolean
  }> {
    // 检查用户角色
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(userId),
      applicationId: new Types.ObjectId(applicationId),
      status: OpenPlatformStatus.ACTIVE
    })

    if (userRole) {
      return {
        role: userRole.role,
        hasPermission: true
      }
    }

    // 检查授权
    const authorization = await this.authorizationModel.findOne({
      applicationId: new Types.ObjectId(applicationId),
      channelUserId: new Types.ObjectId(userId),
      status: OpenPlatformStatus.ACTIVE
    })

    if (authorization) {
      return {
        role: OpenPlatformRoleNames.CHANNEL,
        hasPermission: true
      }
    }

    return {
      role: '',
      hasPermission: false
    }
  }

  /**
   * 格式化应用DTO
   */
  private formatApplicationDto(
    application: OpenPlatformApplicationEntity,
    userRole: string,
    hideSecret: boolean = false,
    isOwner: boolean = false
  ): ApplicationDto {
    const userIdentity: 'owner' | 'invited' = isOwner ? 'owner' : 'invited'
    const userIdentityLabel = isOwner ? '拥有者' : '受邀'

    return {
      id: (application as any)._id.toString(),
      name: application.name,
      appId: application.appId,
      secretKey: hideSecret ? undefined : application.secretKey,
      description: application.description || '',
      status: application.status,
      userId: application.userId.toString(),
      createdAt: application.createdAt?.getTime() || 0,
      updatedAt: application.updatedAt?.getTime() || 0,
      userRole,
      canManage: userRole === OpenPlatformRoleNames.ADMIN,
      userIdentity,
      userIdentityLabel
    }
  }

  /**
   * 生成应用ID
   */
  private generateAppId(): string {
    return `app_${nanoid(16)}`
  }

  /**
   * 生成密钥
   */
  private generateSecretKey(): string {
    return `sk_${crypto.randomBytes(16).toString('hex')}`
  }
}
