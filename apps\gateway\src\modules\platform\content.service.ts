import {
  ContentStatisticEntity,
  MemberEntity,
  PlatformAccountEntity,
  TaskEntity,
  UserEntity
} from '@yxr/mongo'
import {
  ContentOverviewListRequest,
  ContentOverviewListResponse,
  ContentOverviewSummaryRequest,
  ContentStatisticDataDTO,
  ExportContentStatisticDataDTO,
  ReportContentRequest
} from './content.dto'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types, UpdateQuery } from 'mongoose'
import { Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { eventKey, contentStatisticEventEmitter } from './content.event'
import { createObjectCsvStringifier } from 'csv-writer'
import dayjs from 'dayjs'
import {
  TeamRoleNames,
  StatisticCommonService,
  PlatformNameEnum,
  ContentType,
  PlatformType
} from '@yxr/common'
import { WxPublishService } from '../wx-third-platform/wx-publish.service'

@Injectable()
export class ContentService {
  constructor(
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly statisticCommonService: StatisticCommonService,
    private readonly wxPublishService: WxPublishService
  ) {}

  async postContents(currentTeamId: string, body: ReportContentRequest) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(body.platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    if (
      platformAccount.platformName === PlatformNameEnum.微信公众号 &&
      platformAccount.platformType === PlatformType.开放平台
    ) {
      await this.wxContentStatisticUpdate(
        currentTeamId,
        platformAccount._id.toString(),
        platformAccount.platformName
      )
    } else {
      const dataStatistic = body.contentStatisticsData as ContentStatisticDataDTO[]
      if (dataStatistic.length > 0) {
        contentStatisticEventEmitter.emit(eventKey, {
          teamId: currentTeamId,
          platformAccountId: platformAccount._id,
          platformName: platformAccount.platformName,
          contentData: dataStatistic
        })
      }
    }
  }

  async getContentOverviewSummary(filter: ContentOverviewSummaryRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    const fields = await this.statisticCommonService.getContentOverviewField(
      filter.platform,
      filter.type
    )
    const where: any = {
      $match: {
        teamId: new Types.ObjectId(currentTeamId),
        platformName: filter.platform
      }
    }

    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      const platformAccounts = await this.platformAccountModel
        .find({
          members: currentUserId,
          teamId: new Types.ObjectId(currentTeamId),
          platformName: filter.platform
        })
        .select('_id')
        .lean()

      const platformAccountIds = platformAccounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
      where.$match.platformAccountId = { $in: platformAccountIds }
    }
    if (filter.platformAccountId) {
      where.$match.platformAccountId = new Types.ObjectId(filter.platformAccountId)
    }
    if (filter.title) {
      where.$match.$or = [
        { title: { $regex: filter.title, $options: 'i' } },
        { desc: { $regex: filter.title, $options: 'i' } }
      ]
    }
    if (filter.publishUserId) {
      where.$match.publishUserId = new Types.ObjectId(filter.publishUserId)
    }
    if (filter.type) {
      where.$match.contentType = filter.type
    }
    if (filter.publishStartTime && filter.publishEndTime) {
      where.$match.publishTime = {
        $gt: new Date(filter.publishStartTime),
        $lte: new Date(filter.publishEndTime)
      }
    }
    // 动态构建 $group 语句
    const groupStage: any = {
      _id: '$platformName'
    }
    fields.forEach((item) => {
      groupStage[item.value] = { $sum: `$${item.key}` }
    })
    // 进行聚合查询
    const result = await this.contentStatisticModel.aggregate([where, { $group: groupStage }])
    return result[0] ?? {}
  }

  async getContentOverviews(
    filter: ContentOverviewListRequest
  ): Promise<ContentOverviewListResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const page = filter.page
    const size = filter.size
    const where: any = {
      $match: {
        teamId: new Types.ObjectId(currentTeamId)
      }
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      const platformAccounts = await this.platformAccountModel
        .find({
          members: currentUserId,
          teamId: new Types.ObjectId(currentTeamId)
        })
        .select('_id')
        .lean()

      const platformAccountIds = platformAccounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
      where.$match.platformAccountId = {
        $in: platformAccountIds
      }
    }

    if (filter.platformAccountId) {
      where.$match.platformAccountId = new Types.ObjectId(filter.platformAccountId)
    }
    if (filter.title) {
      where.$match.$or = [
        { title: { $regex: filter.title, $options: 'i' } },
        { desc: { $regex: filter.title, $options: 'i' } }
      ]
    }
    if (filter.platform) {
      where.$match.platformName = filter.platform
    }
    if (filter.publishUserId) {
      where.$match.publishUserId = new Types.ObjectId(filter.publishUserId)
    }
    if (filter.type) {
      where.$match.contentType = filter.type
    }
    if (filter.publishStartTime && filter.publishEndTime) {
      where.$match.publishTime = {
        $gt: new Date(filter.publishStartTime),
        $lte: new Date(filter.publishEndTime)
      }
    }
    const result = await this.contentStatisticModel
      .aggregate([
        where,
        {
          $lookup: {
            from: 'platformaccountentities',
            localField: 'platformAccountId',
            foreignField: '_id',
            pipeline: [
              {
                $project: {
                  platformAccountName: 1,
                  platformAvatar: 1,
                  status: 1
                }
              }
            ],
            as: 'platformAccount'
          }
        },
        {
          $match: {
            platformAccount: { $ne: [] } // 确保 platformAccount 数组不为空
          }
        },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [{ $sort: { publishTime: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
          }
        }
      ])
      .exec()

    const data = result[0]?.items.map((item: any) => ({
      ...item
    }))

    const totalSize = result[0]?.counts[0]?.total ?? 0

    const userIds = [
      ...new Set(
        data
          .map((item: { publishUserId: string }) => item.publishUserId)
          .filter((publishUserId: null) => publishUserId !== null && publishUserId !== undefined)
      )
    ] as string[]

    const users = await this.userModel.find(
      {
        _id: userIds.map((item) => new Types.ObjectId(item))
      },
      { nickName: 1, _id: 1 }
    )
    const userToNameMap = users.reduce((ucc, uitem) => {
      ucc[uitem._id.toString()] = uitem.nickName
      return ucc
    }, {})

    const members = await this.memberModel.find(
      {
        userId: userIds.map((item) => new Types.ObjectId(item)),
        teamId: new Types.ObjectId(currentTeamId)
      },
      { userId: 1, remark: 1 }
    )
    const memberToNameMap = members.reduce((mcc, mitem) => {
      mcc[mitem.userId.toString()] = mitem.remark
      return mcc
    }, {})

    let outputData = []
    for (let index = 0; index < data.length; index++) {
      const element = data[index]

      let publishUserName = '-'
      if (element.publishUserId) {
        if (memberToNameMap[element.publishUserId.toString()]) {
          publishUserName = memberToNameMap[element.publishUserId.toString()]
        } else {
          publishUserName = userToNameMap[element.publishUserId.toString()]
        }
      }

      outputData.push({
        teamId: element.teamId,
        platformAccountId: element.platformAccountId,
        accountAvatar: element.platformAccount[0].platformAvatar,
        accountName: element.platformAccount[0].platformAccountName,
        accountStatus: element.platformAccount[0].status,
        type: element.contentType,
        publishUserName: publishUserName,
        remark: undefined,
        updatedAt: element.updatedAt.getTime(),
        contentData: element.contentData
      })
    }

    return {
      data: outputData,
      page,
      size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / size)
    }
  }

  /**
   * 统计非爬虫微信公众号内容数据
   * @param contentId
   * @returns
   */
  async wxContentStatisticUpdate(teamId: string, platformAccountId: string, platformName: string) {
    const result = await this.wxPublishService.getArticleTotal(teamId, platformAccountId)
    if (result.list.length <= 0) {
      return
    }
    const contentDatas = result.list
    const dataArr = []
    for (const item of contentDatas) {
      const parts = item.msgid.split('_')
      const mainId = parts[0]
      if (!dataArr[mainId]) {
        dataArr[mainId] = {
          publishId: mainId,
          publishTime: new Date(item.ref_date),
          title: item.title,
          contentType: ContentType.dynamic,
          share: item.share_count ?? 0,
          collect: item.add_to_fav_count ?? 0,
          read: item.int_page_read_count ?? 0
        }
      } else {
        dataArr[mainId].share += item.share_count
        dataArr[mainId].collect += item.add_to_fav_count
        dataArr[mainId].read += item.int_page_read_count
      }
    }
    if (dataArr.length > 0) {
      for (const element of dataArr) {
        const contentStatistic = await this.contentStatisticModel.findOne({
          publishId: element.publishId,
          teamId: new Types.ObjectId(teamId)
        })
        const task = await this.taskModel.findOne({
          teamId: new Types.ObjectId(teamId),
          publishId: element.publishId
        })
        const data: UpdateQuery<ContentStatisticEntity> = {
          platformName: platformName,
          publishId: element.publishId,
          publishTime: element.publishTime,
          contentType: element.contentType,
          title: element.title,
          publishUserId: task?.userId ? new Types.ObjectId(task?.userId) : null,
          read: element.read ? StatisticCommonService.convertToNumber(element.read) : 0,
          share: element.share ? StatisticCommonService.convertToNumber(element.share) : 0,
          collect: element.collect ? StatisticCommonService.convertToNumber(element.collect) : 0
        }
        if (contentStatistic) {
          //存在就只更新统计数据
          await this.contentStatisticModel.updateOne(
            {
              _id: contentStatistic._id
            },
            data
          )
        } else {
          data.teamId = new Types.ObjectId(teamId)
          data.platformAccountId = new Types.ObjectId(platformAccountId)
          await this.contentStatisticModel.create(data)
        }
      }
    }
  }

  async exportContentOverviews(filter: ContentOverviewListRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const where: FilterQuery<ContentStatisticEntity> = {
      teamId: new Types.ObjectId(currentTeamId)
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      const accounts = await this.platformAccountModel
        .find({
          members: currentUserId,
          teamId: new Types.ObjectId(currentTeamId)
        })
        .select('_id')
        .lean()

      const accountIds = accounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
      where.platformAccountId = {
        $in: accountIds
      }
    }

    if (filter.platformAccountId) {
      where.platformAccountId = new Types.ObjectId(filter.platformAccountId)
    }
    if (filter.title) {
      where.$or = [
        { title: { $regex: filter.title, $options: 'i' } },
        { desc: { $regex: filter.title, $options: 'i' } }
      ]
    }
    if (filter.platform) {
      where.platformName = filter.platform
    }
    if (filter.publishUserId) {
      where.publishUserId = new Types.ObjectId(filter.publishUserId)
    }
    if (filter.type) {
      where.contentType = filter.type
    }
    if (filter.publishStartTime && filter.publishEndTime) {
      where.publishTime = {
        $gt: new Date(filter.publishStartTime),
        $lte: new Date(filter.publishEndTime)
      }
    }
    const results = await this.contentStatisticModel.find(where).sort({ publishTime: -1 }).exec()

    const userIds = [
      ...new Set(
        results
          .map((item) => item.publishUserId)
          .filter((publishUserId: null) => publishUserId !== null && publishUserId !== undefined)
      )
    ]
    const users = await this.userModel.find(
      {
        _id: userIds.map((item) => new Types.ObjectId(item))
      },
      { nickName: 1, _id: 1 }
    )
    const userToNameMap = users.reduce((ucc, uitem) => {
      ucc[uitem._id.toString()] = uitem.nickName
      return ucc
    }, {})
    const members = await this.memberModel.find(
      {
        userId: userIds.map((item) => new Types.ObjectId(item))
      },
      { userId: 1, remark: 1 }
    )
    const memberToNameMap = members.reduce((mcc, mitem) => {
      mcc[mitem.userId.toString()] = mitem.remark
      return mcc
    }, {})

    const platformAccountIds = [...new Set(results.map((item) => item.platformAccountId))]
    const platformAccounts = await this.platformAccountModel.find(
      {
        _id: platformAccountIds.map((item) => new Types.ObjectId(item))
      },
      { _id: 1, platformAccountName: 1 }
    )

    const platformToNameMap = platformAccounts.reduce((pcc, pitem) => {
      pcc[pitem._id.toString()] = pitem.platformAccountName
      return pcc
    }, {})

    let exportDatas = []
    //组装数据
    for (let index = 0; index < results.length; index++) {
      const element = results[index]
      const contentData = element.contentData as ExportContentStatisticDataDTO
      const fieldsToRemove = ['id', 'title', 'cover', 'type', 'desc', 'date']
      //过滤掉不需要的字段
      const filteredObj = Object.fromEntries(
        Object.entries(contentData).filter(([key]) => !fieldsToRemove.includes(key))
      )

      let publishUserName = '-'
      if (element.publishUserId) {
        if (memberToNameMap[element.publishUserId.toString()]) {
          publishUserName = memberToNameMap[element.publishUserId.toString()]
        } else {
          publishUserName = userToNameMap[element.publishUserId.toString()]
        }
      }

      const ContentTypeName = {
        video: '视频',
        miniVideo: '小视频',
        dynamic: '图文',
        article: '文章'
      }
      const exportData = {
        platformName: element.platformName,
        cType: ContentTypeName[element.contentType],
        title: element?.title,
        desc: element?.desc,
        publishUserName: publishUserName,
        publishTime: dayjs(element.publishTime).format('YYYY-MM-DD HH:mm:ss'),
        accountName: platformToNameMap[element.platformAccountId.toString()],
        updatedAt: dayjs(element.updatedAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss'),
        ...filteredObj
      }
      exportDatas.push(exportData)
    }

    return exportDatas
  }

  async generateCsv(data: any[], headers: { id: string; title: string }[]): Promise<string> {
    // 创建 CSV 字符串生成器
    const csvStringifier = createObjectCsvStringifier({ header: headers })

    // 拼接 CSV 内容
    const headerString = csvStringifier.getHeaderString()
    const recordsString = csvStringifier.stringifyRecords(data)

    return headerString + recordsString
  }
}
