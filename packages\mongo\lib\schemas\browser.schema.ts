import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class BrowserEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  accountId: Types.ObjectId

  @Prop({
    type: [String],
    required: true
  })
  members: string[]

  @Prop({
    type: [String],
    required: true
  })
  browserGroups: string[]

  @Prop({
    type: String,
    required: false
  })
  color: string

  @Prop({
    type: String,
    required: false
  })
  icon: string

  /**
   * 空间地址
   */
  @Prop({
    type: String,
    required: false
  })
  spaceUrl: string

  /**
   * 空间名称
   */
  @Prop({
    type: String,
    index: true,
    required: false
  })
  spaceName: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: Date
  })
  latestUpdateAt?: Date
}

export const BrowserSchema: ModelDefinition = {
  name: BrowserEntity.name,
  schema: SchemaFactory.createForClass(BrowserEntity)
}

export const BrowserMongoose = MongooseModule.forFeature([BrowserSchema])
