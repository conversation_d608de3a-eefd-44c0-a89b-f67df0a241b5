import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountEntity, TeamEntity } from '@yxr/mongo'

export interface AccountCapacityCheckResult {
  isExceeded: boolean
  allowableCount: number
  currentCapacity: number
  capacityLimit: number
}

@Injectable()
export class AccountCapacityService {
  private readonly logger = new Logger(AccountCapacityService.name)

  constructor(
    @InjectModel(PlatformAccountEntity.name) private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>
  ) {}

  /**
   * 检查账号点数限制
   * @param teamId 团队ID
   * @param newAccountsCount 新增的账号数量
   * @param accountCapacity 每个账号占用的点数
   * @returns 检查结果
   */
  async checkAccountCapacityLimit(
    teamId: string,
    newAccountsCount: number,
    accountCapacity: number
  ): Promise<AccountCapacityCheckResult> {
    try {
      // 获取团队信息
      const team = await this.teamModel.findById(new Types.ObjectId(teamId))
      if (!team) {
        this.logger.warn(`团队不存在: ${teamId}`)
        return { isExceeded: true, allowableCount: 0, currentCapacity: 0, capacityLimit: 0 }
      }

      // 获取团队当前所有账号的点数总和
      const accounts = await this.platformAccountModel.find({
        teamId: new Types.ObjectId(teamId),
        isFreeze: { $ne: true } // 排除冻结的账号
      })

      const currentCapacity = accounts.reduce((total, account) => total + (account.capacity || 1), 0)
      const capacityLimit = team.accountCapacityLimit || 5 // 默认限制为5

      // 计算新增账号需要的总点数
      const newAccountsCapacity = newAccountsCount * accountCapacity

      // 检查是否超限
      const totalCapacityAfterAdd = currentCapacity + newAccountsCapacity
      const isExceeded = totalCapacityAfterAdd > capacityLimit

      // 计算允许添加的账号数量
      const remainingCapacity = Math.max(0, capacityLimit - currentCapacity)
      const allowableCount = Math.floor(remainingCapacity / accountCapacity)

      this.logger.debug(`团队 ${teamId} 点数检查: 当前=${currentCapacity}, 限制=${capacityLimit}, 新增需要=${newAccountsCapacity}, 允许添加=${allowableCount}`)

      return {
        isExceeded,
        allowableCount: Math.min(allowableCount, newAccountsCount),
        currentCapacity,
        capacityLimit
      }
    } catch (error) {
      this.logger.error(`检查账号点数限制失败: ${error.message}`, error.stack)
      return { isExceeded: true, allowableCount: 0, currentCapacity: 0, capacityLimit: 0 }
    }
  }

  /**
   * 获取团队当前账号点数使用情况
   * @param teamId 团队ID
   * @returns 点数使用情况
   */
  async getTeamCapacityUsage(teamId: string): Promise<{
    currentCapacity: number
    capacityLimit: number
    usageRate: number
  }> {
    try {
      const team = await this.teamModel.findById(new Types.ObjectId(teamId))
      if (!team) {
        return { currentCapacity: 0, capacityLimit: 0, usageRate: 0 }
      }

      const accounts = await this.platformAccountModel.find({
        teamId: new Types.ObjectId(teamId),
        isFreeze: { $ne: true }
      })

      const currentCapacity = accounts.reduce((total, account) => total + (account.capacity || 1), 0)
      const capacityLimit = team.accountCapacityLimit || 5
      const usageRate = capacityLimit > 0 ? (currentCapacity / capacityLimit) * 100 : 0

      return {
        currentCapacity,
        capacityLimit,
        usageRate
      }
    } catch (error) {
      this.logger.error(`获取团队点数使用情况失败: ${error.message}`, error.stack)
      return { currentCapacity: 0, capacityLimit: 0, usageRate: 0 }
    }
  }
}
