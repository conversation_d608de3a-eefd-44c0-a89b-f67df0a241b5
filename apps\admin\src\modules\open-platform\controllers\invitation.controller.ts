import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { InvitationService } from '../services/invitation.service'
import {
  CreateInvitationRequestDto,
  HandleInvitationRequestDto,
  InvitationListRequestDto,
  CreateInvitationResponseDto,
  GetInvitationResponseDto,
  GetInvitationListResponseDto,
  HandleInvitationResponseDto
} from '../dto/invitation.dto'
import {
  BaseBadRequestDTO,
  BaseNotFoundResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformAccess } from '../../../common/decorators/access-control.decorator'

@Controller('open-platform/invitations')
@OpenPlatformAccess()
@ApiTags('开放平台邀请管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class InvitationController {
  constructor(private readonly invitationService: InvitationService) {}

  @Post()
  @ApiOperation({ summary: '发送邀请' })
  @ApiOkResponse({
    description: '邀请发送成功',
    type: CreateInvitationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或用户已存在',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用或用户不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async createInvitation(@Body() createDto: CreateInvitationRequestDto) {
    const result = await this.invitationService.createInvitation(createDto)
    return result
  }

  @Get()
  @ApiOperation({ summary: '获取邀请列表' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetInvitationListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getInvitationList(@Query() queryDto: InvitationListRequestDto) {
    const result = await this.invitationService.getInvitationList(queryDto)
    return result
  }

  @Get(':id')
  @ApiOperation({ summary: '获取邀请详情' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetInvitationResponseDto
  })
  @ApiNotFoundResponse({
    description: '邀请不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getInvitationById(@Param('id') id: string) {
    const result = await this.invitationService.getInvitationById(id)
    return result
  }

  @Put(':id/handle')
  @ApiOperation({ summary: '处理邀请（接受/拒绝）' })
  @ApiOkResponse({
    description: '处理成功',
    type: HandleInvitationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或邀请已处理',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '邀请不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async handleInvitation(
    @Param('id') id: string,
    @Body() handleDto: HandleInvitationRequestDto
  ) {
    const result = await this.invitationService.handleInvitation(id, handleDto)
    return result
  }

  @Delete(':id')
  @ApiOperation({ summary: '撤销邀请' })
  @ApiOkResponse({
    description: '撤销成功'
  })
  @ApiBadRequestResponse({
    description: '只能撤销待处理的邀请',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '邀请不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async cancelInvitation(@Param('id') id: string) {
    return this.invitationService.cancelInvitation(id)
  }
}
