import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ContentEntity, DataStatisticEntity, TaskEntity, UserEntity } from '@yxr/mongo'
import { Model } from 'mongoose'
import { <PERSON>ron } from '@nestjs/schedule'
import dayjs from 'dayjs'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class DataStatisticCornService implements OnModuleInit {
  private lockValue: string = 'handleDataStatistic'
  private readonly lockPrefix = 'lock:'

  constructor(
    @InjectModel(DataStatisticEntity.name)
    private dataStatisticModel: Model<DataStatisticEntity>,
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @InjectModel(ContentEntity.name)
    private contentModel: Model<ContentEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly loggerService: TlsService
  ) {}

  onModuleInit() {
    // console.log('data-statistic-corn-service init')
  }

  /**
   * 每日用户注册数统计
   * 每天1:00 执行
   */
  @Cron('0 0 1 * * *', {
    name: 'registerUserStatistic',
    timeZone: 'Asia/Shanghai'
  })
  async UpdateStatisticCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        const yesterday = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
        await this.RegisterUserStatisticTask(yesterday)
      } catch (e) {
        await this.loggerService.error(null,'每日后台归档数据定时处理失败', {error:e})
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  async RegisterUserStatisticTask(yesterday: string) {
    const beijingStart = dayjs(yesterday).tz('Asia/Shanghai').startOf('day')
    const beijingEnd = dayjs(yesterday).tz('Asia/Shanghai').endOf('day')
    // 转换为 UTC 时间
    const startOfYesterday = beijingStart.utc().toDate()
    const endOfYesterday = beijingEnd.utc().toDate()

    const count = await this.userModel.countDocuments({
      createdAt: {
        $gt: startOfYesterday,
        $lte: endOfYesterday
      }
    })

    const appUserPublishCount = await this.appUserPublishCount(startOfYesterday, endOfYesterday)
    const { teamPublishCount, publishCount } = await this.teamPublishCount(
      startOfYesterday,
      endOfYesterday
    )
    const registerUser = await this.dataStatisticModel.findOne({
      createTime: yesterday
    })
    if (registerUser) {
      await this.dataStatisticModel.updateOne(
        { createTime: yesterday },
        {
          registerCount: count,
          appUserPublishCount: appUserPublishCount,
          teamPublishCount: teamPublishCount,
          publishCount: publishCount
        },
        { upsert: true } // 如果没有找到该日期的数据，则插入；否则不做任何操作
      )
    } else {
      await this.dataStatisticModel.create({
        createTime: yesterday,
        registerCount: count,
        appUserPublishCount: appUserPublishCount,
        teamPublishCount: teamPublishCount,
        publishCount: publishCount
      })
    }
  }

  /**
   * 每日活跃团队发布数统计
   */
  async teamPublishCount(
    startOfYesterday: Date,
    endOfYesterday: Date
  ): Promise<{ teamPublishCount: number; publishCount: number }> {
    const result = await this.taskModel.aggregate([
      {
        $match: {
          createdAt: {
            $gt: startOfYesterday,
            $lte: endOfYesterday
          }
        }
      },
      {
        $group: {
          _id: '$teamId',
          totalCount: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: null,
          totalGroups: { $sum: 1 }, // 计算分组总数
          totalCountSum: { $sum: '$totalCount' } // 计算所有分组的 totalCount 总和
        }
      },
      {
        $project: {
          _id: 0, // 不返回 _id 字段
          totalGroups: 1,
          totalCountSum: 1
        }
      }
    ])

    const count = result[0]?.totalGroups ?? 0
    const totalCountSum = result[0]?.totalCountSum ?? 0

    return {
      teamPublishCount: count,
      publishCount: totalCountSum
    }
  }

  /**
   * 每日APP用户发布数
   */
  async appUserPublishCount(startOfYesterday: Date, endOfYesterday: Date): Promise<number> {
    const result = await this.contentModel.aggregate([
      {
        $match: {
          createdAt: {
            $gt: startOfYesterday,
            $lte: endOfYesterday
          },
          isAppContent: true
        }
      },
      {
        $group: {
          _id: '$userId' // 按 userId 分组
        }
      },
      {
        $count: 'totalGroups' // 统计分组的数量
      }
    ])

    return result[0]?.totalGroups ?? 0
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
