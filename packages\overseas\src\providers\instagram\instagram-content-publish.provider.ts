import { Injectable, Logger } from '@nestjs/common'
import { ContentPublishProvider } from '../content-publish.provider'
import { 
  OverseasContext, 
  PublishTaskData, 
  PublishResult, 
  PublishContentData, 
  PublishContentType,
  PublishTaskStatus 
} from '../types'
import { InstagramApi } from './instagram-api'

@Injectable()
export class InstagramContentPublishProvider extends ContentPublishProvider {
  private readonly logger = new Logger(InstagramContentPublishProvider.name)

  constructor(private readonly instagramApi: InstagramApi) {
    super()
  }

  /**
   * 验证发布内容是否符合Instagram要求
   */
  async validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    // 检查内容类型支持
    const supportedTypes = this.getSupportedContentTypes()
    if (!supportedTypes.includes(content.type)) {
      errors.push(`不支持的内容类型: ${content.type}`)
    }

    // 检查文本长度
    const limits = this.getContentLimits()
    if (content.text && content.text.length > limits.maxTextLength) {
      errors.push(`文本内容超过最大长度限制 ${limits.maxTextLength} 字符`)
    }

    // 检查图片数量
    if (content.images && content.images.length > limits.maxImageCount) {
      errors.push(`图片数量超过最大限制 ${limits.maxImageCount} 张`)
    }

    // Instagram必须有媒体内容
    if (content.type === PublishContentType.Text) {
      errors.push('Instagram不支持纯文本发布，必须包含图片或视频')
    }

    if (content.type === PublishContentType.Image && (!content.images || content.images.length === 0)) {
      errors.push('图片类型内容必须包含至少一张图片')
    }

    if (content.type === PublishContentType.Video && !content.videoUrl) {
      errors.push('视频类型内容必须包含视频URL')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 发布内容到Instagram
   */
  async publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult> {
    this.logger.log(`开始发布到Instagram: 任务=${taskData.taskId}, 账号=${context.accountOpenId}`)

    try {
      let result: any

      switch (taskData.content.type) {
        case PublishContentType.Image:
          result = await this.publishImageContent(context, taskData)
          break
        case PublishContentType.Video:
          result = await this.publishVideoContent(context, taskData)
          break
        case PublishContentType.Mixed:
          result = await this.publishMixedContent(context, taskData)
          break
        default:
          throw new Error(`不支持的内容类型: ${taskData.content.type}`)
      }

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Success,
        platformContentId: result.id,
        platformContentUrl: result.permalink || `https://instagram.com/p/${result.id}`,
        rawResponse: result,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`Instagram发布失败: 任务=${taskData.taskId}`, error)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'INSTAGRAM_PUBLISH_ERROR',
        rawResponse: error.response?.data,
        completedAt: new Date()
      }
    }
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult> {
    try {
      const media = await this.instagramApi.getMedia(context, platformContentId)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Success,
        platformContentId: platformContentId,
        platformContentUrl: media.permalink || `https://instagram.com/p/${platformContentId}`,
        rawResponse: media,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`查询Instagram发布状态失败: 任务=${taskId}`, error)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        completedAt: new Date()
      }
    }
  }

  /**
   * 删除已发布的内容
   */
  async deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      await this.instagramApi.deleteMedia(context, platformContentId)
      return { success: true }
    } catch (error) {
      this.logger.error(`删除Instagram内容失败: ${platformContentId}`, error)
      return { 
        success: false, 
        errorMessage: error.message 
      }
    }
  }

  /**
   * 获取支持的内容类型
   */
  getSupportedContentTypes(): string[] {
    return [
      PublishContentType.Image,
      PublishContentType.Video,
      PublishContentType.Mixed
    ]
  }

  /**
   * 获取内容限制
   */
  getContentLimits() {
    return {
      maxTextLength: 2200, // Instagram描述限制
      maxImageCount: 10,   // Instagram轮播最大图片数
      maxVideoSize: 4 * 1024 * 1024 * 1024, // 4GB
      maxVideoDuration: 60, // Feed视频60秒，IGTV可以更长
      supportedImageFormats: ['jpg', 'jpeg', 'png'],
      supportedVideoFormats: ['mp4', 'mov']
    }
  }

  /**
   * 发布图片内容
   */
  private async publishImageContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { images, text } = taskData.content
    
    if (images.length === 1) {
      // 单张图片
      return await this.instagramApi.createImagePost(context, {
        image_url: images[0],
        caption: text
      })
    } else {
      // 多张图片（轮播）
      return await this.instagramApi.createCarouselPost(context, {
        children: images.map(url => ({ media_type: 'IMAGE', image_url: url })),
        caption: text
      })
    }
  }

  /**
   * 发布视频内容
   */
  private async publishVideoContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { videoUrl, text, videoCover } = taskData.content
    
    return await this.instagramApi.createVideoPost(context, {
      video_url: videoUrl,
      caption: text,
      cover_url: videoCover
    })
  }

  /**
   * 发布混合内容
   */
  private async publishMixedContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { text, images, videoUrl } = taskData.content
    
    if (videoUrl) {
      // 如果有视频，优先发布视频
      return await this.publishVideoContent(context, taskData)
    } else if (images && images.length > 0) {
      // 否则发布图片
      return await this.publishImageContent(context, taskData)
    } else {
      throw new Error('Instagram发布必须包含图片或视频')
    }
  }
}
