import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, FilterQuery, Model, Types, UpdateQuery } from 'mongoose'
import {
  PlatformAccountEntity,
  LoginStatus,
  MemberEntity,
  GroupEntity,
  BrowserFavoritesEntity,
  PlatformAccountOverviewEntity,
  PlatformAccountStatusLogEntity,
  PlatformAccountSummaryEntity,
  ContentStatisticEntity,
  TeamEntity,
  UserEntity,
  HistoryStatisticEntity,
  PlatformAccountCookieEntity
} from '@yxr/mongo'
import {
  CreateWxIpadAccountRequest,
  PatchPlatformAccountRequest,
  PlatformAccountCookieRequest,
  PlatformAccountDetailResponse,
  PlatformAccountListRequest,
  PlatformAccountListResponse,
  PlatformAccountRequest,
  PutPlatformAccountOverviewRequest,
  PutPlatformAccountOverviewResponse
} from './platform-account.dto'
import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import {
  CacheKeyService,
  EventNames,
  LiteSystemErrorCode,
  PlatformMembersChangedEvent,
  PlatformNameEnum,
  PlatformType,
  TeamRoleNames,
  StatisticCommonService,
  HistoryStatisticTypeEnum
} from '@yxr/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { MemberService } from '../team/member.service'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { AuthorizationService } from '../../common/security/authorization.service'
import { TeamService } from '../team/team.service'
import {
  AccountLockListResponse,
  UpdateVideoAccountResponse,
  WxAccountIsLoginResponse,
  WxAssistantCreateRequest,
  WxAssistantResponse
} from '../wx-third-platform/wx-ipad/wx-ipad.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { WechatIpadSdkService } from '../wechat-ipad/wechat-sdk.service'
import { KuaidailiService } from '../kuaidaili/kuaidaili.service'
import { nanoid } from 'nanoid'
import { TlsService, TosService } from '@yxr/huoshan'
import { HomeOverviewResponse } from '../overview/overview.dto'
import { WxPublishService } from '../wx-third-platform/wx-publish.service'
import { WebhookEventEmitterService } from '../webhook-event/webhook-event-emitter.service'
import dayjs from 'dayjs'
import { eventKey, contentStatisticEventEmitter } from './content.event'

@Injectable()
export class PlatformAccountService {
  constructor(
    @InjectModel(HistoryStatisticEntity.name)
    private historyStatisticModel: Model<HistoryStatisticEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(GroupEntity.name) private groupModel: Model<GroupEntity>,
    @InjectModel(PlatformAccountOverviewEntity.name)
    private platformAccountOverviewModel: Model<PlatformAccountOverviewEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @InjectModel(PlatformAccountStatusLogEntity.name)
    private platformAccountStatusLogModel: Model<PlatformAccountStatusLogEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @InjectModel(BrowserFavoritesEntity.name)
    private browserFavoritesModel: Model<BrowserFavoritesEntity>,
    @InjectModel(PlatformAccountCookieEntity.name)
    private platformAccountCookieModel: Model<PlatformAccountCookieEntity>,
    @InjectConnection() private readonly connection: Connection,
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly authorizationService: AuthorizationService,
    private eventEmitter: EventEmitter2,
    private readonly memberService: MemberService,
    private readonly teamService: TeamService,
    private readonly webhookService: WebhookService,
    private readonly loggerService: TlsService,
    private readonly statisticCommonService: StatisticCommonService,
    private readonly wechatIpadSdkService: WechatIpadSdkService,
    private readonly kuaidailiService: KuaidailiService,
    private readonly ossService: TosService,
    private readonly wxPublishService: WxPublishService,
    private readonly webhookEventEmitterService: WebhookEventEmitterService
  ) {}

  /**
   * 媒体账号创建/更新
   * @param body
   */
  async platformAccountCreateAsync(
    body: PlatformAccountRequest
  ): Promise<PlatformAccountDetailResponse> {
    const { session } = this.request
    let accountEvent = WebhookEvents.AccountUpdate
    let platformAccount = null
    if (body.platformName !== PlatformNameEnum.其他) {
      // 其他类型账号没有 platformAuthorId值，没有更新
      platformAccount = await this.platformAccountModel.findOne({
        teamId: new Types.ObjectId(session.teamId),
        platformName: body.platformName,
        platformAuthorId: body.platformAuthorId,
        platformType: { $lt: 2 },
        parentId: null
      })
    }

    if (platformAccount) {
      const updateData: UpdateQuery<PlatformAccountEntity> = {
        token: body.token,
        credentials: body.credentials,
        status: body.status,
        platformAccountName: body.platformAccountName,
        platformAvatar: body.platformAvatar,
        serviceTypeId: body.serviceTypeId,
        serviceTypeName: body.serviceTypeName,
        accountStatus: body.accountStatus,
        principalName: body.principalName,
        verifyTypeInfo: body.verifyTypeInfo,
        isRealNameVerified: body.isRealNameVerified,
        $addToSet: { members: session.userId }
      }
      if (platformAccount.status != body.status) {
        updateData.loginStatusUpdatedAt = new Date()
      }
      //更新
      platformAccount = await this.platformAccountModel.findOneAndUpdate(
        { _id: new Types.ObjectId(platformAccount._id) },
        updateData
      )
    } else {
      // 新增
      await this.teamService.checkTeamAccountLimit(session.teamId)
      await this.memberService.checkMemberAccountCount(session.teamId, session.userId)
      // 开启数据事务
      const dbTransaction = await this.connection.startSession()
      dbTransaction.startTransaction()
      try {
        const [account] = await this.platformAccountModel.create(
          [
            {
              userId: new Types.ObjectId(session.userId),
              token: body.token,
              credentials: body.credentials,
              teamId: new Types.ObjectId(session.teamId),
              platformAuthorId: body.platformAuthorId ?? '',
              platformAccountName: body.platformAccountName,
              platformAvatar: body.platformAvatar,
              platformName: body.platformName,
              platformType: body.platformType,
              remark: '',
              groups: [],
              members: [session.userId],
              status: LoginStatus.Succesed,
              color: body.color,
              spaceUrl: body.spaceUrl,
              isRealNameVerified: body.isRealNameVerified,
              serviceTypeId: body.serviceTypeId,
              serviceTypeName: body.serviceTypeName,
              accountStatus: body.accountStatus,
              principalName: body.principalName,
              verifyTypeInfo: body.verifyTypeInfo,
              loginStatusUpdatedAt: new Date(),
              capacity: body.platformType === PlatformType.海外平台 ? 4 : 1
            }
          ],
          { session: dbTransaction }
        )
        platformAccount = account
        await dbTransaction.commitTransaction()

        // 触发开放平台Webhook事件
        await this.webhookEventEmitterService.emitPlatformAccountCreated({
          teamId: session.teamId,
          accountId: account._id.toString(),
          platformName: body.platformName,
          nickname: body.platformAccountName,
          userId: session.userId,
          capacity: body.platformType === PlatformType.海外平台 ? 4 : 1
        })
      } catch (error) {
        this.loggerService.error(this.request, error)
        await dbTransaction.abortTransaction()
        throw new HttpException('媒体账号添加失败, 请稍后再试', -1)
      } finally {
        await dbTransaction.endSession()
      }

      accountEvent = WebhookEvents.AccountCreate
    }

    await this.teamService.updateTeamAccount(session.teamId)
    //触发媒体账号变更事件
    await this.eventEmitter.emitAsync(
      EventNames.PlatformMembersChangedEvent,
      new PlatformMembersChangedEvent(
        platformAccount._id.toHexString(),
        session.teamId,
        [session.userId],
        []
      )
    )

    //socket通知发送
    await this.changeAccountWebhookEvent(
      accountEvent,
      session.teamId,
      platformAccount._id.toString(),
      platformAccount.members
    )

    await this.platformAccountStatusLogModel.create({
      platformAccountId: new Types.ObjectId(platformAccount._id),
      userId: new Types.ObjectId(session.userId),
      teamId: new Types.ObjectId(session.teamId)
    })

    return {
      id: platformAccount._id.toString(),
      spaceId: platformAccount.spaceId?.toString(),
      platformAvatar: platformAccount.platformAvatar,
      platformAccountName: platformAccount.platformAccountName,
      platformAuthorId: platformAccount.platformAuthorId,
      platformName: platformAccount.platformName,
      remark: platformAccount.remark,
      groups: platformAccount.groups,
      status: platformAccount.status,
      color: platformAccount.color,
      spaceUrl: platformAccount.spaceUrl,
      parentId: platformAccount?.parentId?.toString(),
      createdAt: platformAccount.createdAt.getTime(),
      isFreeze: platformAccount.isFreeze ?? false,
      platformType: platformAccount.platformType,
      serviceTypeId: platformAccount.serviceTypeId,
      accountStatus: platformAccount.accountStatus,
      verifyTypeInfo: platformAccount.verifyTypeInfo,
      kuaidailiArea: platformAccount.kuaidailiArea,
      isOperate: true,
      isRealNameVerified: platformAccount.isRealNameVerified ?? true
    }
  }

  /**
   * 媒体账号详情
   * @param platformAccountId
   */
  async getPlatformAccountDetailAsync(
    platformAccountId: string
  ): Promise<PlatformAccountDetailResponse> {
    const { session } = this.request

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    //运营账号权限限制
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })
    let isAdminRole = true
    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      isAdminRole = false
    }

    const favorites = await this.browserFavoritesModel
      .find({
        platformAccountId: platformAccount._id
      })
      .select('name _id browserId websiteUrl')
      .lean() // 使用 lean() 返回普通的 JavaScript 对象而不是 mongoose 文档
      .then((favorites) =>
        favorites.map((favorite) => {
          // 这里将 _id 重命名为 id
          return {
            id: favorite._id.toString(),
            name: favorite.name,
            browserId: favorite.browserId?.toString(),
            websiteUrl: favorite.websiteUrl
          }
        })
      )

    return {
      id: platformAccount._id.toString(),
      spaceId: platformAccount?.spaceId?.toString(),
      platformAvatar: platformAccount.platformAvatar,
      platformAccountName: platformAccount.platformAccountName,
      platformAuthorId: platformAccount.platformAuthorId,
      platformName: platformAccount.platformName,
      favorites: favorites,
      groups: platformAccount.groups,
      remark: platformAccount.remark,
      status: platformAccount.status,
      color: platformAccount.color,
      spaceUrl: platformAccount.spaceUrl,
      createdAt: platformAccount.createdAt.getTime(),
      isFreeze: platformAccount.isFreeze ?? false,
      platformType: platformAccount.platformType,
      parentId: platformAccount?.parentId?.toString(),
      serviceTypeId: platformAccount.serviceTypeId,
      accountStatus: platformAccount.accountStatus,
      verifyTypeInfo: platformAccount.verifyTypeInfo,
      kuaidailiArea: platformAccount.kuaidailiArea,
      isOperate: isAdminRole || platformAccount.members.includes(session.userId.toString()),
      isRealNameVerified: platformAccount.isRealNameVerified ?? true
    }
  }

  /**
   * 更新账号信息
   * @param platformAccountId
   * @param body
   */
  async patchPlatformAccountsAsync(
    platformAccountId: string,
    body: PatchPlatformAccountRequest
  ): Promise<PlatformAccountDetailResponse> {
    const { session } = this.request

    let platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    const updateData: UpdateQuery<PlatformAccountEntity> = {}
    const oldIp = platformAccount.kuaidailiIp
    if (body.kuaidailiArea) {
      // 获取可分配ip地址
      const proxyIp = await this.kuaidailiService.getProxyAllocation(
        platformAccount.platformName,
        body.kuaidailiArea
      )
      updateData.kuaidailiIp = proxyIp
      updateData.kuaidailiArea = body.kuaidailiArea
    }

    if (
      body.status &&
      !platformAccount.parentId &&
      platformAccount.platformName !== PlatformNameEnum.微信 &&
      platformAccount.platformName !== PlatformNameEnum.微信公众号
    ) {
      //视频号子账号不更新状态
      // if (
      //   platformAccount.status !== body.status &&
      //   platformAccount.status === LoginStatus.Succesed
      // ) {
      //   // 如果状态由登录成功变更为其他状态, 则需要确保 statusChecksum 一致的情况下才可以变更状态
      //   if (
      //     !platformAccount.statusChecksum ||
      //     body.statusChecksum === platformAccount.statusChecksum
      //   ) {
      //     updateData.status = body.status
      //     updateData.statusChecksum = body.statusChecksum
      //   }
      // } else {
      updateData.status = body.status
      updateData.statusChecksum = body.statusChecksum
      // }
      updateData.loginStatusUpdatedAt = new Date()
    }
    if (body.remark !== undefined) {
      updateData.remark = body.remark
    }

    if (body.groups && body.groups.length >= 0) {
      const groupObjectIds = body.groups.map((id) => new Types.ObjectId(id))
      // 确保所有分组存在
      const existingGroups = await this.groupModel.find({ _id: { $in: groupObjectIds } })
      if (existingGroups.length !== body.groups.length) {
        throw new NotFoundException('一个或者多个分组不存在')
      }

      const groupsToAdd = body.groups.filter(
        (id) => !platformAccount.groups.includes(id.toString())
      )
      if (groupsToAdd.length > 0) {
        // 对需要新增分组 ID 的账号进行更新
        const groupsToAddObjectIds = groupsToAdd.map((id) => new Types.ObjectId(id))
        await this.groupModel.updateMany(
          { _id: { $in: groupsToAddObjectIds } },
          { $addToSet: { accounts: platformAccountId } } // 使用 $addToSet 避免重复
        )
      }

      const groupsToRemove = platformAccount.groups.filter((id) => !body.groups.includes(id))
      if (groupsToRemove.length > 0) {
        // 对需要移除分组 ID 的账号进行更新
        const groupsToRemoveObjectIds = groupsToRemove.map((id) => new Types.ObjectId(id))
        await this.groupModel.updateMany(
          { _id: { $in: groupsToRemoveObjectIds } },
          { $pull: { accounts: platformAccountId } }
        )
      }

      updateData.groups = body.groups
    }

    if (body.isRealNameVerified !== undefined) {
      updateData.isRealNameVerified = body.isRealNameVerified
    }

    if (platformAccount.platformName === PlatformNameEnum.其他) {
      if (body.color) {
        updateData.color = body.color
      }
      if (body.spaceUrl) {
        updateData.spaceUrl = body.spaceUrl
      }
      if (body.platformAccountName) {
        updateData.platformAccountName = body.platformAccountName
      }
    }

    //开启数据事务
    const dbTransaction = await this.connection.startSession()
    dbTransaction.startTransaction()
    try {
      platformAccount = await this.platformAccountModel.findOneAndUpdate(
        {
          _id: new Types.ObjectId(platformAccount._id)
        },
        updateData,
        { new: true, session: dbTransaction }
      )

      if (updateData.kuaidailiIp) {
        // 更新代理IP使用数量
        if (platformAccount.kuaidailiIp) {
          await this.kuaidailiService.updateProxyAllocation(
            platformAccount.platformName,
            oldIp,
            'decrement'
          )
        }

        await this.kuaidailiService.updateProxyAllocation(
          platformAccount.platformName,
          updateData.kuaidailiIp
        )
      }

      await dbTransaction.commitTransaction()
    } catch (error) {
      await dbTransaction.abortTransaction()
      throw new HttpException('媒体账号更新失败, 请稍后再试', -1)
    } finally {
      await dbTransaction.endSession()
    }

    // socket通知发送
    await this.changeAccountWebhookEvent(
      WebhookEvents.AccountUpdate,
      session.teamId,
      platformAccount._id.toString(),
      platformAccount.members
    )

    //运营账号权限限制
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })
    let isAdminRole = true
    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      isAdminRole = false
    }

    return {
      id: platformAccount._id.toString(),
      spaceId: platformAccount.spaceId?.toString(),
      platformAvatar: platformAccount.platformAvatar,
      platformAccountName: platformAccount.platformAccountName,
      platformAuthorId: platformAccount.platformAuthorId,
      platformName: platformAccount.platformName,
      status: platformAccount.status,
      groups: platformAccount.groups,
      remark: platformAccount.remark,
      color: platformAccount.color,
      spaceUrl: platformAccount.spaceUrl,
      parentId: platformAccount?.parentId?.toString(),
      createdAt: platformAccount.createdAt.getTime(),
      isFreeze: platformAccount.isFreeze ?? false,
      platformType: platformAccount.platformType,
      serviceTypeId: platformAccount.serviceTypeId,
      accountStatus: platformAccount.accountStatus,
      verifyTypeInfo: platformAccount.verifyTypeInfo,
      kuaidailiArea: platformAccount.kuaidailiArea,
      isOperate: isAdminRole || platformAccount.members.includes(session.userId.toString()),
      isRealNameVerified: platformAccount.isRealNameVerified ?? true
    }
  }

  /**
   * 获取媒体账号列表
   * @param filter
   */
  async getPlatformAccountListAsync(
    filter: PlatformAccountListRequest,
    platforms: string[]
  ): Promise<PlatformAccountListResponse> {
    const { session } = this.request
    const page = filter.page
    const size = filter.size
    const where: any = {
      $match: {}
    }

    where.$match.teamId = new Types.ObjectId(session.teamId)

    //通过时间游标分页
    if (filter.time > 0) {
      where.$match.createdAt = { $lt: new Date(filter.time) }
    }
    if (filter.name) {
      where.$match.$or = [
        { platformAccountName: { $regex: filter.name, $options: 'i' } },
        { remark: { $regex: filter.name, $options: 'i' } }
      ]
    }
    if (filter.group) {
      where.$match.groups = filter.group
    }
    if (filter.platform) {
      where.$match.platformName = filter.platform
    }
    if (platforms) {
      where.$match.platformName = { $in: platforms }
    }
    if (filter.loginStatus) {
      if (filter.loginStatus == 2) {
        where.$match.status = { $gte: filter.loginStatus }
      } else {
        where.$match.status = filter.loginStatus
      }
    }

    if (filter.parentId) {
      where.$match.parentId = new Types.ObjectId(filter.parentId)
    }
    //运营账号权限限制
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })
    let isAdminRole = true
    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      isAdminRole = false
    }
    if (filter.isolation === 'true' && !isAdminRole) {
      //需要隔离数据
      where.$match.members = session.userId
    }
    if (filter.isRealNameVerified !== undefined) {
      where.$match.isRealNameVerified = filter.isRealNameVerified === 'true'
    }
    const result = await this.platformAccountModel.aggregate([
      where,
      {
        $lookup: {
          from: 'browserfavoritesentities',
          localField: '_id',
          foreignField: 'platformAccountId',
          pipeline: [
            {
              $project: {
                _id: 0,
                id: '$_id',
                name: 1,
                browserId: 1,
                websiteUrl: 1
              }
            }
          ],
          as: 'browserfavorites'
        }
      },
      {
        $facet: {
          counts: [{ $count: 'total' }],
          items: [
            {
              $project: {
                _id: 0,
                id: '$_id',
                favorites: '$browserfavorites',
                createdAt: { $toLong: '$createdAt' },
                spaceId: 1,
                platformAvatar: 1,
                platformAccountName: 1,
                platformAuthorId: 1,
                platformName: 1,
                remark: 1,
                groups: 1,
                status: 1,
                isFreeze: 1,
                platformType: 1,
                serviceTypeId: 1,
                accountStatus: 1,
                verifyTypeInfo: 1,
                parentId: 1,
                kuaidailiArea: 1,
                isRealNameVerified: 1,
                members: 1,
                color: 1,
                spaceUrl: 1
              }
            },
            { $sort: { createdAt: -1 } },
            { $skip: (filter.page - 1) * filter.size },
            { $limit: filter.size }
          ]
        }
      }
    ])

    const data = await Promise.all(
      result[0]?.items.map(async (item) => {
        let isLock = false
        if (item.platformName == PlatformNameEnum.视频号 && item.parentId) {
          const lockCache = await this.cacheManager.get(
            CacheKeyService.getWeiXinAccountLockKey(session.teamId, item.parentId.toString())
          )
          if (lockCache) {
            isLock = true
          }
        }
        return {
          ...item,
          isLock: isLock,
          spaceId: item.spaceId?.toString(),
          color: item.color,
          spaceUrl: item.spaceUrl,
          isOperate: isAdminRole || item.members.includes(session.userId.toString()),
          isFreeze: item.isFreeze ?? false,
          isRealNameVerified: item.isRealNameVerified ?? true
        }
      })
    )
    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }

  /**
   * 删除媒体账号
   * @param platformAccountId
   */
  async deletePlatformAccountAsync(platformAccountId: string) {
    const { session } = this.request
    // 需要判断是否对修改账号有运营权限
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })
    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      if (!member.accounts.includes(platformAccountId)) {
        throw new ForbiddenException('删除失败，没有该账号的权限')
      }
    }
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    if (platformAccount.platformName == PlatformNameEnum.微信) {
      await this.deleteWechatAccountAsync(platformAccountId, session.teamId)
    } else {
      await this.deleteAccountAsync(platformAccountId, session.teamId)
    }
  }

  /**
   * 删除媒体账号
   * @param platformAccountId
   * @param currentTeamId
   */
  async deleteAccountAsync(platformAccountId: string, currentTeamId: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    //开启数据事务
    const dbTransaction = await this.connection.startSession()
    dbTransaction.startTransaction()
    try {
      await this.platformAccountModel.deleteOne(
        {
          _id: new Types.ObjectId(platformAccount._id)
        },
        { session: dbTransaction }
      )

      // 代理ip扣除
      if (platformAccount.kuaidailiIp) {
        await this.kuaidailiService.updateProxyAllocation(
          platformAccount.platformName,
          platformAccount.kuaidailiIp,
          'decrement'
        )
      }
      await this.platformAccountOverviewModel.deleteOne(
        {
          platformAccountId: new Types.ObjectId(platformAccount._id),
          teamId: new Types.ObjectId(currentTeamId)
        },
        { session: dbTransaction }
      )
      await this.platformAccountSummaryModel.deleteOne(
        {
          platformAccountId: new Types.ObjectId(platformAccount._id),
          teamId: new Types.ObjectId(currentTeamId)
        },
        { session: dbTransaction }
      )
      await this.contentStatisticModel.deleteMany(
        {
          platformAccountId: new Types.ObjectId(platformAccount._id),
          teamId: new Types.ObjectId(currentTeamId)
        },
        { session: dbTransaction }
      )
      if (platformAccount.groups.length > 0) {
        const groupObjectIds = platformAccount.groups.map((id) => new Types.ObjectId(id))
        await this.groupModel.updateMany(
          { _id: { $in: groupObjectIds } },
          { $pull: { accounts: platformAccount._id } }
        )
      }

      await dbTransaction.commitTransaction()
    } catch (error) {
      await this.loggerService.error(this.request, '媒体账号删除失败', { error: error })
      await dbTransaction.abortTransaction()
      throw new HttpException('媒体账号删除失败, 请稍后再试', -1)
    } finally {
      await dbTransaction.endSession()
    }

    await this.teamService.updateTeamAccount(currentTeamId)

    //触发媒体账号变更事件
    await this.eventEmitter.emitAsync(
      EventNames.PlatformMembersChangedEvent,
      new PlatformMembersChangedEvent(platformAccountId, currentTeamId, [], platformAccount.members)
    )

    // socket通知发送
    await this.changeAccountWebhookEvent(
      WebhookEvents.AccountDelete,
      currentTeamId,
      platformAccount._id.toString(),
      platformAccount.members
    )

    // 触发开放平台Webhook事件
    await this.webhookEventEmitterService.emitPlatformAccountDeleted({
      teamId: currentTeamId,
      accountId: platformAccountId,
      platformName: platformAccount.platformName,
      nickname: platformAccount.platformAccountName,
      userId: platformAccount.userId.toString()
    })
  }

  /**
   * 删除微信账号
   * @param platformAccountId
   * @param currentTeamId
   */
  async deleteWechatAccountAsync(platformAccountId: string, currentTeamId: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    const wxPlatformAccounts = await this.platformAccountModel.find({
      parentId: new Types.ObjectId(platformAccount.id),
      teamId: new Types.ObjectId(platformAccount.teamId)
    })

    // 代理ip扣除
    if (platformAccount.kuaidailiIp) {
      await this.kuaidailiService.updateProxyAllocation(
        platformAccount.platformName,
        platformAccount.kuaidailiIp,
        'decrement'
      )
    }
    if (wxPlatformAccounts.length > 0) {
      for (let index = 0; index < wxPlatformAccounts.length; index++) {
        const wxPlatformAccount = wxPlatformAccounts[index]

        await this.platformAccountOverviewModel.deleteOne({
          platformAccountId: new Types.ObjectId(wxPlatformAccount._id),
          teamId: new Types.ObjectId(wxPlatformAccount.teamId)
        })
        await this.platformAccountSummaryModel.deleteOne({
          platformAccountId: new Types.ObjectId(wxPlatformAccount._id),
          teamId: new Types.ObjectId(wxPlatformAccount.teamId)
        })
        await this.contentStatisticModel.deleteMany({
          platformAccountId: new Types.ObjectId(wxPlatformAccount._id),
          teamId: new Types.ObjectId(wxPlatformAccount.teamId)
        })
        if (wxPlatformAccount.kuaidailiIp) {
          //删除子账号
          await this.kuaidailiService.updateProxyAllocation(
            wxPlatformAccount.platformName,
            wxPlatformAccount.kuaidailiIp,
            'decrement'
          )
        }
        //触发媒体账号变更事件
        await this.eventEmitter.emitAsync(
          EventNames.PlatformMembersChangedEvent,
          new PlatformMembersChangedEvent(
            wxPlatformAccount.id,
            currentTeamId,
            [],
            wxPlatformAccount.members
          )
        )
        // socket通知发送
        await this.changeAccountWebhookEvent(
          WebhookEvents.AccountDelete,
          currentTeamId,
          wxPlatformAccount._id.toString(),
          wxPlatformAccount.members
        )
      }
      await this.platformAccountModel.deleteMany({
        parentId: new Types.ObjectId(platformAccount.id),
        teamId: new Types.ObjectId(platformAccount.teamId)
      })
    }

    await this.platformAccountModel.deleteOne({
      _id: new Types.ObjectId(platformAccount._id)
    })
    if (platformAccount.groups.length > 0) {
      const groupObjectIds = platformAccount.groups.map((id) => new Types.ObjectId(id))
      await this.groupModel.updateMany(
        { _id: { $in: groupObjectIds } },
        { $pull: { accounts: platformAccount._id } }
      )
    }
    //触发媒体账号变更事件
    await this.eventEmitter.emitAsync(
      EventNames.PlatformMembersChangedEvent,
      new PlatformMembersChangedEvent(platformAccountId, currentTeamId, [], platformAccount.members)
    )
    // socket通知发送
    await this.changeAccountWebhookEvent(
      WebhookEvents.AccountDelete,
      currentTeamId,
      platformAccount._id.toString(),
      platformAccount.members
    )

    // 触发开放平台Webhook事件
    await this.webhookEventEmitterService.emitPlatformAccountDeleted({
      teamId: currentTeamId,
      accountId: platformAccountId,
      platformName: platformAccount.platformName,
      nickname: platformAccount.platformAccountName,
      userId: platformAccount.userId.toString()
    })

    await this.teamService.updateTeamAccount(currentTeamId)
    await this.wechatIpadSdkService.deleteWechatAccount(platformAccount.platformAuthorId)
  }

  async putPlatformAccountFreeze(platformAccountId: string, isFreeze: boolean) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(
      currentTeamId,
      currentUserId,
      [TeamRoleNames.MASTER],
      '您没有权限执行此操作, 请联系团队管理员'
    )

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }
    if (platformAccount.platformName == PlatformNameEnum.微信 || platformAccount.parentId) {
      const isVip = await this.teamService.getTeamVip(currentTeamId)
      if (!isVip) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.IsNotVip,
          message: '开通vip后才能解冻微信号'
        })
      }
    }
    if (!isFreeze) {
      await this.teamService.checkTeamAccountLimit(currentTeamId, isFreeze)

      if (platformAccount.platformType === PlatformType.海外平台) {
        platformAccount.capacity = 4
      } else if (
        platformAccount.parentId &&
        platformAccount.platformName === PlatformNameEnum.视频号
      ) {
        platformAccount.capacity = 2
      } else {
        platformAccount.capacity = 1
      }
    }

    platformAccount.isFreeze = isFreeze

    await platformAccount.save()

    // 账号点数重算
    await this.teamService.updateTeamAccount(currentTeamId)
  }

  /**
   * 账号概览数据上报
   * @param platformAccountId
   * @param body
   */
  async putPlatformAccountOverviews(
    platformAccountId: string,
    body: PutPlatformAccountOverviewRequest
  ) {
    const { session } = this.request
    const platformAccountOverview = await this.platformAccountOverviewModel.findOne({
      platformAccountId: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      return
    }

    const overview = await this.statisticCommonService.getAccountOverview(
      platformAccount.platformName,
      body.overviewData
    )
    if (
      platformAccount.platformName === PlatformNameEnum.微信公众号 &&
      platformAccount.platformType === PlatformType.开放平台
    ) {
      const wxUserCumulate = await this.wxPublishService.getUserCumulate(
        session.teamId,
        platformAccountId
      )
      if (wxUserCumulate.errcode === 0) {
        const firstItem = wxUserCumulate.list[0]
        body.overviewData = JSON.stringify({
          value: {
            dynamic: [
              {
                name: '总粉丝数',
                value: firstItem.cumulate_user || 0,
                list: []
              }
            ]
          },
          updateTime: firstItem.ref_date ? dayjs(firstItem.ref_date).valueOf() : dayjs().valueOf()
        })
        overview.fansTotal = firstItem.cumulate_user || 0

        contentStatisticEventEmitter.emit(eventKey, {
          teamId: session.teamId,
          platformAccountId: platformAccount._id,
          platformName: platformAccount.platformName,
          contentData: null
        })
      } else {
        throw new ForbiddenException('获取微信公众号粉丝数据失败')
      }
    }

    if (platformAccountOverview) {
      //更新
      await this.platformAccountOverviewModel.updateOne(
        {
          _id: platformAccountOverview._id
        },
        {
          overviewData: body.overviewData,
          platformName: platformAccount.platformName
        }
      )
    } else {
      //新增
      await this.platformAccountOverviewModel.create({
        teamId: new Types.ObjectId(session.teamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        overviewData: body.overviewData,
        platformName: platformAccount.platformName
      })
    }

    const summary = await this.platformAccountSummaryModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      platformAccountId: new Types.ObjectId(platformAccountId)
    })
    if (summary) {
      //更新
      await this.platformAccountSummaryModel.updateOne(
        {
          teamId: new Types.ObjectId(session.teamId),
          platformAccountId: new Types.ObjectId(platformAccountId)
        },
        {
          $set: {
            platformName: platformAccount.platformName,
            ...overview
          }
        }
      )
    } else {
      //新增
      await this.platformAccountSummaryModel.create({
        teamId: new Types.ObjectId(session.teamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        platformName: platformAccount.platformName,
        ...overview
      })
    }
  }

  async getPlatformAccountOverviews(
    platformAccountIds: string[]
  ): Promise<PutPlatformAccountOverviewResponse[]> {
    const { session } = this.request

    return await this.platformAccountOverviewModel
      .find({
        teamId: new Types.ObjectId(session.teamId),
        platformAccountId: { $in: platformAccountIds.map((x) => new Types.ObjectId(x)) }
      })
      .select('platformAccountId overviewData')
  }

  /**
   * 微信ipad账号授权
   * @param currentTeamId
   * @param currentUserId
   * @param body
   * @returns
   */
  async postWxIpadAccount(body: CreateWxIpadAccountRequest) {
    const kuaidaili = await this.cacheManager.get<{
      deviceId: string
      teamId: string
      userId: string
      kuaidailiArea: string
      ip: string
    }>(CacheKeyService.getKuaidailiKey(body.uuid))

    if (!kuaidaili) {
      return
    }
    const currentTeamId = kuaidaili.teamId
    const currentUserId = kuaidaili.userId
    let platformAccount = await this.platformAccountModel.findOne({
      platformName: PlatformNameEnum.微信,
      platformAuthorId: body.wxid
    })
    if (platformAccount && currentTeamId !== platformAccount.teamId.toString()) {
      throw new ForbiddenException('无法重新添加，账号已绑定在其他团队')
    }

    let isNew = false
    let accountEvent = WebhookEvents.AccountUpdate
    if (platformAccount) {
      //更新
      await this.platformAccountModel.updateOne(
        {
          platformName: PlatformNameEnum.微信,
          platformAuthorId: body.wxid
        },
        {
          $set: {
            status: LoginStatus.Succesed,
            loginStatusUpdatedAt: new Date(),
            deviceId: kuaidaili.deviceId
          }
        }
      )

      const wxPlatformAccounts = await this.platformAccountModel.find({
        wxid: body.wxid,
        teamId: new Types.ObjectId(platformAccount.teamId)
      })

      if (wxPlatformAccounts) {
        //子账号的视频号
        await this.platformAccountModel.updateMany(
          {
            wxid: body.wxid,
            teamId: new Types.ObjectId(currentTeamId)
          },
          {
            status: LoginStatus.Succesed,
            loginStatusUpdatedAt: new Date()
          }
        )
        for (let index = 0; index < wxPlatformAccounts.length; index++) {
          const wxPlatformAccount = wxPlatformAccounts[index]
          await this.changeAccountWebhookEvent(
            WebhookEvents.AccountUpdate,
            wxPlatformAccount.teamId.toString(),
            wxPlatformAccount._id.toString(),
            wxPlatformAccount.members
          )
        }
      }
    } else {
      // 新增
      await this.teamService.checkTeamAccountLimit(currentTeamId)
      platformAccount = await this.platformAccountModel.create({
        userId: new Types.ObjectId(currentUserId),
        teamId: new Types.ObjectId(currentTeamId),
        platformAuthorId: body.wxid,
        platformAccountName: body.nickName,
        platformAvatar: body.imgHead,
        platformName: PlatformNameEnum.微信,
        platformType: PlatformType.开放平台,
        members: [currentUserId],
        kuaidailiArea: kuaidaili.kuaidailiArea,
        status: LoginStatus.Succesed,
        uuid: body.uuid,
        deviceId: kuaidaili.deviceId,
        kuaidailiIp: kuaidaili.ip,
        loginStatusUpdatedAt: new Date(),
        capacity: 1 // 微信主账号占用1个点数
      })
      accountEvent = WebhookEvents.AccountCreate
      isNew = true

      // 触发开放平台Webhook事件
      await this.webhookEventEmitterService.emitPlatformAccountCreated({
        teamId: currentTeamId,
        accountId: platformAccount._id.toString(),
        platformName: PlatformNameEnum.微信,
        nickname: body.nickName,
        userId: currentUserId,
        capacity: 1
      })
    }

    await this.kuaidailiService.updateProxyAllocation(PlatformNameEnum.微信, kuaidaili.ip)
    await this.teamService.updateTeamAccount(currentTeamId)
    await this.cacheManager.set(
      CacheKeyService.getWxAccountLoginInfoKey(body.uuid),
      {
        isLogin: true,
        isNew: isNew,
        platformAccountId: platformAccount.id
      },
      300 * 1000
    )

    //触发媒体账号变更事件
    await this.eventEmitter.emitAsync(
      EventNames.PlatformMembersChangedEvent,
      new PlatformMembersChangedEvent(
        platformAccount._id.toHexString(),
        currentTeamId,
        [currentUserId],
        []
      )
    )

    //socket通知发送
    await this.changeAccountWebhookEvent(
      accountEvent,
      currentTeamId,
      platformAccount._id.toString(),
      platformAccount.members
    )
  }

  /**
   * 视频号授权
   * @param wxid
   * @param currentTeamId
   * @param currentUserId
   * @param body
   */
  async postWxPlatformAccount(
    parentId: string,
    wxid: string,
    currentTeamId: string,
    currentUserId: string,
    body: WxAssistantCreateRequest[]
  ) {
    let accountEvent = WebhookEvents.AccountCreate

    for (let index = 0; index < body.length; index++) {
      const item = body[index]

      let platformAccount = await this.platformAccountModel.findOne({
        teamId: new Types.ObjectId(currentTeamId),
        platformAuthorId: item.uniqId,
        parentId: { $ne: null }
      })

      if (!platformAccount) {
        platformAccount = await this.platformAccountModel.create({
          teamId: new Types.ObjectId(currentTeamId),
          userId: new Types.ObjectId(currentUserId),
          platformName: PlatformNameEnum.视频号,
          platformAccountName: item.nickname,
          token: null,
          platformAvatar: item.headImgUrl,
          platformType: PlatformType.其他,
          platformAuthorId: item.uniqId,
          wxid: wxid,
          parentId: new Types.ObjectId(parentId),
          finderUsername: item.finderUsername,
          spaceId: null,
          members: [currentUserId],
          status: LoginStatus.Succesed,
          loginStatusUpdatedAt: new Date(),
          capacity: 2 // 微信子账号占用2个点数
        })

        // 触发开放平台Webhook事件
        await this.webhookEventEmitterService.emitPlatformAccountCreated({
          teamId: currentTeamId,
          accountId: platformAccount._id.toString(),
          platformName: PlatformNameEnum.视频号,
          nickname: item.nickname,
          userId: currentUserId,
          capacity: 2
        })

        //触发媒体账号变更事件
        await this.eventEmitter.emitAsync(
          EventNames.PlatformMembersChangedEvent,
          new PlatformMembersChangedEvent(
            platformAccount._id.toHexString(),
            currentTeamId,
            [currentUserId],
            []
          )
        )

        //socket通知发送
        await this.changeAccountWebhookEvent(
          accountEvent,
          currentTeamId,
          platformAccount._id.toString(),
          platformAccount.members
        )
      } else {
        const parentAccount = await this.platformAccountModel.findOne({
          platformAuthorId: wxid,
          teamId: new Types.ObjectId(currentTeamId)
        })
        if (parentAccount) {
          const updateDate: UpdateQuery<PlatformAccountEntity> = {
            parentId: new Types.ObjectId(parentAccount.id),
            status: parentAccount.status,
            token: null // 更换父级账号后，token需要重新授权
          }

          if (platformAccount.status != parentAccount.status) {
            updateDate.loginStatusUpdatedAt = new Date()
          }
          await this.platformAccountModel.updateOne(
            {
              _id: new Types.ObjectId(platformAccount._id)
            },
            updateDate
          )
        }
      }
    }

    await this.teamService.updateTeamAccount(currentTeamId)
  }

  /**
   * 账号变更事件
   * @param accountEvent
   * @param currentTeamId
   * @param platformAccountId
   * @param members
   */
  async changeAccountWebhookEvent(
    accountEvent: WebhookEvents,
    currentTeamId: string,
    platformAccountId: string,
    members: string[]
  ) {
    let userIds = await this.memberService.getMembersByRoles(
      [TeamRoleNames.MASTER, TeamRoleNames.ADMIN],
      currentTeamId
    )
    const ids = userIds.map((doc) => doc.userId.toString())
    //id去重
    const mergeIds = [...new Set([...members, ...ids])]

    if (mergeIds.length > 0) {
      let eventContent = {
        event: accountEvent,
        body: {
          platformAccountId: platformAccountId
        }
      }

      await this.webhookService.grpchook(
        mergeIds.map((value) => value.toString()),
        currentTeamId,
        eventContent
      )
    }
  }

  /**
   * 微信账号掉线
   * @param wxid
   */
  async wxIpadAccountOffline(wxid: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      platformAuthorId: wxid,
      platformType: PlatformType.开放平台
    })
    if (platformAccount) {
      platformAccount.status = LoginStatus.Expired
      await platformAccount.save()

      //socket通知发送
      await this.changeAccountWebhookEvent(
        WebhookEvents.AccountUpdate,
        platformAccount.teamId.toString(),
        platformAccount._id.toString(),
        platformAccount.members
      )

      const wxPlatformAccounts = await this.platformAccountModel.find({
        wxid: wxid,
        teamId: new Types.ObjectId(platformAccount.teamId)
      })

      if (wxPlatformAccounts) {
        await this.platformAccountModel.updateMany(
          {
            wxid: wxid,
            teamId: new Types.ObjectId(platformAccount.teamId)
          },
          { $set: { status: LoginStatus.Expired } }
        )

        for (let index = 0; index < wxPlatformAccounts.length; index++) {
          const wxPlatformAccount = wxPlatformAccounts[index]
          await this.changeAccountWebhookEvent(
            WebhookEvents.AccountUpdate,
            wxPlatformAccount.teamId.toString(),
            wxPlatformAccount._id.toString(),
            wxPlatformAccount.members
          )
        }
      }
    }
  }

  /**
   * 获取微信视频号账号列表
   * @param platformAccountId
   * @returns
   */
  async getAssistantMembers(platformAccountId: string): Promise<WxAssistantResponse[]> {
    try {
      const platformaccount = await this.platformAccountModel.findById(
        new Types.ObjectId(platformAccountId)
      )
      if (!platformaccount) {
        throw new NotFoundException('媒体账号不存在')
      }
      const token = platformAccountId
      const result = await this.wechatIpadSdkService.assistantMembers(
        platformaccount.platformAuthorId,
        token
      )
      return result.map((item) => {
        return {
          finderUsername: item.finderUsername,
          nickname: item.nickname,
          headImgUrl: item.headImgUrl,
          uniqId: item.uniqId
        }
      })
    } catch (e) {
      await this.loggerService.error(null, '获取第三方微信视频号账号列表失败：', { error: e })
      throw new ForbiddenException('获取微信视频号账号列表失败')
    }
  }

  /**
   * 微信视频号账号列表授权
   * @param platformAccountId
   * @param currentTeamId
   * @param body
   */
  async assistantMembersAuthorize(
    platformAccountId: string,
    currentTeamId: string,
    currentUserId: string,
    body: WxAssistantCreateRequest[]
  ) {
    const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))
    // if (team.accountCountLimit < team.accountCount + body.length) {  // 变更为账号点数
    if (team.accountCapacityLimit < team.accountCapacity + body.length * 2) {
      throw new ForbiddenException('当前团队账号点数已达上限！')
    }

    const platformaccount = await this.platformAccountModel.findById(
      new Types.ObjectId(platformAccountId)
    )
    if (!platformaccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    await this.postWxPlatformAccount(
      platformaccount.id,
      platformaccount.platformAuthorId,
      currentTeamId,
      currentUserId,
      body
    )
  }

  /**
   * 获取账号的登录凭证
   * @param platformAccountId
   * @param currentTeamId
   */
  async updateAssistantMember(
    platformAccountId: string,
    currentTeamId: string,
    wxkey: string
  ): Promise<UpdateVideoAccountResponse> {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }
    if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
      const lockCache = await this.cacheManager.get<string>(
        CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.parentId.toString())
      )
      if (!lockCache) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.AccountLockDestroy,
          message: '视频号锁定已失效'
        })
      } else if (lockCache !== wxkey) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.SecretLockError,
          message: '视频号锁定失败'
        })
      }

      try {
        let token = ''
        if (!platformAccount?.token) {
          const wxToken = await this.wechatIpadSdkService.assistantMemberToken(
            platformAccount.wxid,
            platformAccount.parentId.toString(),
            platformAccount.finderUsername
          )
          platformAccount.token = wxToken
          platformAccount.status = LoginStatus.Succesed
          await platformAccount.save()
          token = wxToken
        } else {
          token = platformAccount.token
        }
        return {
          cookie: token
        }
      } catch (e) {
        await this.loggerService.error(null, '更新微信视频号登录状态失败：', { error: e })
        throw new NotFoundException('微信视频号状态失效')
      }
    } else {
      const cookieInfo = await this.platformAccountCookieModel.findOne({
        teamId: new Types.ObjectId(currentTeamId),
        platformAccountId: new Types.ObjectId(platformAccountId)
      })

      return {
        cookie: cookieInfo ? cookieInfo.cookie : null,
        localStorage: cookieInfo ? cookieInfo.localStorage : null
      }
    }
  }

  /**
   * 更新账号的登录凭证
   * @param platformAccountId
   * @param currentTeamId
   */
  async getRefreshToken(
    platformAccountId: string,
    currentTeamId: string,
    wxkey: string,
    body: PlatformAccountCookieRequest
  ): Promise<UpdateVideoAccountResponse> {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }
    if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
      const lockCache = await this.cacheManager.get(
        CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.parentId.toString())
      )
      if (!lockCache) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.AccountLockDestroy,
          message: '视频号锁定已失效'
        })
      } else if (lockCache !== wxkey) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.SecretLockError,
          message: '视频号锁定失败'
        })
      }

      try {
        const token = await this.wechatIpadSdkService.assistantMemberToken(
          platformAccount.wxid,
          platformAccount.parentId.toString(),
          platformAccount.finderUsername
        )
        platformAccount.token = token
        platformAccount.status = LoginStatus.Succesed
        await platformAccount.save()
        return {
          cookie: token
        }
      } catch (e) {
        await this.loggerService.error(null, '更新微信视频号登录状态失败：', { error: e })
        throw new NotFoundException('微信视频号状态失效')
      }
    } else {
      let cookieInfo = await this.platformAccountCookieModel.findOne({
        teamId: new Types.ObjectId(currentTeamId),
        platformAccountId: new Types.ObjectId(platformAccountId)
      })
      if (cookieInfo) {
        cookieInfo = await this.platformAccountCookieModel.findOneAndUpdate(
          {
            _id: new Types.ObjectId(cookieInfo.id)
          },
          {
            $set: { cookie: body.cookie, localStorage: body.localStorage }
          }
        )
      } else {
        cookieInfo = await this.platformAccountCookieModel.insertOne({
          cookie: body.cookie,
          localStorage: body.localStorage,
          teamId: new Types.ObjectId(currentTeamId),
          platformName: platformAccount.platformName,
          platformAccountId: new Types.ObjectId(platformAccount.id)
        })
      }
      return {
        cookie: cookieInfo ? cookieInfo.cookie : null,
        localStorage: cookieInfo ? cookieInfo.localStorage : null
      }
    }
  }

  /**
   * 检测账号登陆状态
   * @param uuid
   * @param currentTeamId
   * @param currentUserId
   */
  async getCheckLogin(uuid: string): Promise<WxAccountIsLoginResponse> {
    try {
      const res = await this.wechatIpadSdkService.checkLogin(uuid)
      let nickname = ''
      let avatar = ''
      let codeStatus = ''
      if (res) {
        if (res.status === 'success') {
          //登陆成功
          await this.postWxIpadAccount({
            wxid: res?.data?.wxid,
            uuid: res?.data?.uuid,
            nickName: res?.data.info?.nickname,
            imgHead: res?.data.info?.imgHead
          })
          nickname = res?.data.info?.nickname
          avatar = res?.data.info?.imgHead
        } else if (res?.status === 'scaned') {
          nickname = res?.data.nickname
          avatar = res?.data.avatar
        } else if (res.status === 'error') {
          await this.loggerService.error(null, '微信扫码失败：', { error: res?.data.message })
          throw new ForbiddenException('登录失败，请重试')
        }

        codeStatus = res.status
      }

      const accountLoginInfo = await this.cacheManager.get<{
        isLogin: boolean
        platformAccountId?: string
        isNew?: boolean
      }>(CacheKeyService.getWxAccountLoginInfoKey(uuid))
      return {
        nickname: nickname,
        avatar: avatar,
        status: codeStatus,
        isLogin: accountLoginInfo?.isLogin,
        platformAccountId: accountLoginInfo?.platformAccountId,
        isNew: accountLoginInfo?.isNew
      }
    } catch (error) {
      throw new ForbiddenException(error.message)
    }
  }

  /**
   * 解锁账号
   * @param platformAccountId
   * @param currentTeamId
   */
  async deleteAccountLock(platformAccountId: string, currentTeamId: string, wxkey: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      platformName: PlatformNameEnum.微信
    })
    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }
    const lockCache = await this.cacheManager.get(
      CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.id.toString())
    )
    if (!lockCache) {
      throw new ForbiddenException({
        code: LiteSystemErrorCode.AccountLockDestroy,
        message: '视频号锁定已失效'
      })
    } else if (lockCache !== wxkey) {
      throw new ForbiddenException({
        code: LiteSystemErrorCode.SecretLockError,
        message: '视频号锁定失败'
      })
    }
    await this.cacheManager.del(
      CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.id.toString())
    )
  }

  /**
   * 锁定账号
   * @param platformAccountId
   * @param currentTeamId
   */
  async postAccountLock(
    platformAccountIds: string[],
    currentTeamId: string
  ): Promise<AccountLockListResponse[]> {
    const platformAccounts = await this.platformAccountModel.find({
      _id: { $in: platformAccountIds.map((x) => new Types.ObjectId(x)) },
      platformName: PlatformNameEnum.微信
    })
    if (platformAccounts.length <= 0 || platformAccounts.length !== platformAccountIds.length) {
      throw new ForbiddenException('账号锁定失败')
    }

    await Promise.all(
      platformAccounts.map(async (item) => {
        const lockCache = await this.cacheManager.get(
          CacheKeyService.getWeiXinAccountLockKey(item.teamId.toString(), item._id.toString())
        )
        if (lockCache) {
          throw new ForbiddenException({
            code: LiteSystemErrorCode.AccountLock,
            message: '账号已锁定'
          })
        }
      })
    )

    const accountLockList: AccountLockListResponse[] = []
    await Promise.all(
      platformAccounts.map(async (account) => {
        const secretKey = nanoid()
        await this.cacheManager.set(
          CacheKeyService.getWeiXinAccountLockKey(currentTeamId, account.id),
          secretKey,
          60 * 1000
        )
        accountLockList.push({
          platformAccountId: account.id.toString(),
          wxkey: secretKey
        })
      })
    )

    return accountLockList
  }

  /**
   * 更新锁定账号锁定时间
   * @param platformAccountId
   * @param currentTeamId
   */
  async putAccountLock(platformAccountId: string, currentTeamId: string, wxkey: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      platformName: PlatformNameEnum.微信
    })
    if (!platformAccount) {
      throw new ForbiddenException('账号不存在')
    }
    const lockCache = await this.cacheManager.get(
      CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.id.toString())
    )
    if (!lockCache) {
      throw new ForbiddenException({
        code: LiteSystemErrorCode.AccountLockDestroy,
        message: '视频号锁定已失效'
      })
    } else if (lockCache !== wxkey) {
      throw new ForbiddenException({
        code: LiteSystemErrorCode.SecretLockError,
        message: '视频号锁定失败'
      })
    }
    // 重置锁定时间 - 注意单位是:秒
    await this.cacheManager.store.client.expire(
      CacheKeyService.getWeiXinAccountLockKey(currentTeamId, platformAccount.id.toString()),
      60
    )
  }

  async getPlatformAccountMembers(platformAccountId: string) {
    const res = await this.memberModel.find({
      accounts: platformAccountId
    })

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })

    const users = await this.userModel.find({
      _id: { $in: res.map((x) => new Types.ObjectId(x.userId)) }
    })

    const usersMap = users.reduce((pre, cur) => {
      pre[cur._id.toString()] = cur
      return pre
    }, {})

    return Promise.all(
      res.map(async (item) => ({
        name: item.remark || usersMap[item.userId.toString()].nickName,
        avatar: await this.ossService.getAccessSignatureUrl(
          usersMap[item.userId.toString()]?.avatar
        ),
        id: item.id,
        isPrincipal: item.id.toString() === platformAccount.principalId
      }))
    )
  }

  async setPlatformAccountPrincipal(
    platformAccountId: string,
    session: { teamId: string; userId: string },
    principalId = ''
  ) {
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      if (!member.accounts.includes(platformAccountId)) {
        throw new ForbiddenException('没有该账号的权限')
      }
    }

    if (platformAccount.principalId && principalId) {
      const isBool = this.platformAccountModel.find({
        members: {
          $in: [platformAccount.principalId]
        }
      })

      if (isBool) {
        const member = await this.memberModel.findOne({
          _id: new Types.ObjectId(platformAccount.principalId),
          teamId: new Types.ObjectId(session.teamId)
        })

        const user = await this.userModel.findOne({
          _id: new Types.ObjectId(member.userId)
        })

        throw new NotFoundException(`账号已设置负责人，<${user.nickName}>。`)
      }
    }

    await this.platformAccountModel.updateOne(
      { _id: new Types.ObjectId(platformAccountId) },
      { principalId }
    )
  }

  async getPrincipalOverview(): Promise<any> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    if (
      !member.roles.some((role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN)
    ) {
      throw new ForbiddenException('没有权限')
    }

    const [res1] = await this.platformAccountModel.aggregate([
      {
        $match: {
          teamId: new Types.ObjectId(currentTeamId)
        }
      },
      {
        $group: {
          _id: null,
          uniquePrincipalIds: { $addToSet: '$principalId' }
        }
      },
      {
        $project: {
          _id: 0,
          uniquePrincipalIds: 1
        }
      }
    ])

    const { uniquePrincipalIds } = res1 as { uniquePrincipalIds: string[] }

    const principalList = uniquePrincipalIds.filter(Boolean)

    const value = await Promise.all(
      principalList.map(async (principalId) => {
        const res = await this.getSignPrincipalOverview(currentTeamId, principalId)
        return {
          memberId: principalId,
          ...res
        }
      })
    )

    const valueMap = value.reduce((pre, cur) => {
      pre[cur.memberId] = cur
      return pre
    }, {})

    const res = await this.memberModel.find({
      _id: { $in: principalList.map((x) => new Types.ObjectId(x)) }
    })

    const users = await this.userModel.find({
      _id: { $in: res.map((x) => new Types.ObjectId(x.userId)) }
    })

    const usersMap = users.reduce((pre, cur) => {
      pre[cur._id.toString()] = cur
      return pre
    }, {})

    return Promise.all(
      res.map(async (item) => ({
        name: item.remark || usersMap[item.userId.toString()].nickName,
        avatar: await this.ossService.getAccessSignatureUrl(
          usersMap[item.userId.toString()]?.avatar
        ),
        id: item.id,
        data: valueMap[item.id]
      }))
    )
  }

  async getSignPrincipalOverview(
    currentTeamId: string,
    memberId: string
  ): Promise<HomeOverviewResponse> {
    const overviewKey = CacheKeyService.getPlatformAccountOverviewKey(currentTeamId, memberId)

    const cache = await this.cacheManager.get<HomeOverviewResponse>(overviewKey)

    if (cache) {
      return cache
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      _id: new Types.ObjectId(memberId)
    })

    const overviewWhere: any = {
      $match: {
        teamId: new Types.ObjectId(currentTeamId)
      }
    }
    const filter: FilterQuery<HistoryStatisticEntity> = {
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(member.userId),
      statisticType: HistoryStatisticTypeEnum.platformAccount
    }
    const homeStatisticData: HistoryStatisticEntity = {
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(member.userId),
      statisticType: HistoryStatisticTypeEnum.platformAccount
    }

    //需要隔离数据
    const platformAccounts = await this.platformAccountModel
      .find({
        principalId: memberId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('_id')
      .lean()

    const platformAccountIds = platformAccounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
    overviewWhere.$match.platformAccountId = {
      $in: platformAccountIds
    }

    const fields = [
      'fansTotal',
      'readTotal',
      'playTotal',
      'commentsTotal',
      'likesTotal',
      'favoritesTotal'
    ]

    // 动态构建 $group 语句
    const groupStage: any = {
      _id: null
    }
    fields.forEach((item) => {
      groupStage[item] = { $sum: `$${item}` }
    })
    const overview = await this.platformAccountSummaryModel.aggregate([
      overviewWhere,
      {
        $group: groupStage
      }
    ])

    const homeStatistic = await this.historyStatisticModel.findOne(filter)
    const dataInfo = {}
    const incInfo = {}
    fields.forEach((item) => {
      if (!overview[0]) {
        dataInfo[item] = 0
        incInfo[item] = 0
      } else {
        dataInfo[item] = overview[0][item]
        const historyValue = homeStatistic?.historyStatistic?.[item] ?? overview[0][item]
        incInfo[item] = overview[0][item] - historyValue
      }
    })
    homeStatisticData.historyStatistic = dataInfo
    if (homeStatistic) {
      await this.historyStatisticModel.updateOne(
        {
          _id: new Types.ObjectId(homeStatistic.id)
        },
        homeStatisticData
      )
    } else {
      await this.historyStatisticModel.create(homeStatisticData)
    }
    const returnData = {
      current: dataInfo,
      increments: incInfo,
      updatedAt: Date.now()
    }
    await this.cacheManager.set(overviewKey, returnData, 1000 * 60 * 60 * 3) //设置3小时缓存

    return returnData
  }

  async getPlatformAccountsByMember(memberId: string) {
    const { teamId, userId } = this.request.session

    const currentMember = await this.memberModel.findOne({
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId)
    })

    if (
      !currentMember.roles.some(
        (role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN
      )
    ) {
      return []
    }

    const member = await this.memberModel.findOne({
      _id: new Types.ObjectId(memberId),
      teamId: new Types.ObjectId(teamId)
    })

    if (!member) {
      return []
    }

    const res = await this.platformAccountModel.find({
      _id: { $in: member.accounts.map((id) => new Types.ObjectId(id)) }
    })

    return res.map((item) => {
      return {
        id: item._id,
        name: item.platformAccountName,
        avatar: item.platformAvatar,
        principalId: item.principalId,
        platformType: item.platformType,
        platformName: item.platformName
      }
    })
  }

  async putPlatformAccount(platformAccountId: string, memberIds: string[]) {
    const { teamId: currentTeamId, userId } = this.request.session

    const currentMember = await this.memberModel.findOne({
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (
      !currentMember.roles.some(
        (role) => role === TeamRoleNames.MASTER || role === TeamRoleNames.ADMIN
      )
    ) {
      throw new ForbiddenException('没有权限')
    }

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    const members = await this.memberModel.find({
      _id: { $in: memberIds.map((id) => new Types.ObjectId(id)) },
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (members.length !== memberIds.length) {
      throw new NotFoundException('成员不存在')
    }

    for (const member of members) {
      if (member.maxAccountCount !== 0 && member.accounts.length + 1 > member.maxAccountCount) {
        throw new ForbiddenException('成员账号数量已达上限')
      }
    }

    const oldMemberIds = platformAccount.members
    const newMemberIds = members.map((item) => item.userId.toString())

    const added = newMemberIds.filter((id) => !oldMemberIds.includes(id))
    const removed = oldMemberIds.filter((id) => !newMemberIds.includes(id))

    if (removed.includes(platformAccount.principalId.toString())) {
      await this.platformAccountModel.updateOne(
        {
          _id: new Types.ObjectId(platformAccountId),
          teamId: new Types.ObjectId(currentTeamId)
        },
        {
          $set: {
            principalId: ''
          }
        }
      )
    }

    await this.platformAccountModel.updateOne(
      {
        _id: new Types.ObjectId(platformAccountId),
        teamId: new Types.ObjectId(currentTeamId)
      },
      {
        $set: {
          members: newMemberIds
        }
      }
    )

    await this.eventEmitter.emitAsync(
      EventNames.PlatformMembersChangedEvent,
      new PlatformMembersChangedEvent(platformAccountId, currentTeamId, added, removed)
    )
  }
}
