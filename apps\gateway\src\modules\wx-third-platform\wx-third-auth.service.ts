import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { WxBasicService } from './wx-basic.service'
import { LoginStatus, PlatformAccountEntity } from '@yxr/mongo'
import { Model, Types, UpdateQuery } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { PlatformNameEnum, PlatformType } from '@yxr/common'
import {
  PlatformAccountDetailResponse,
  PlatformAccountRequest
} from '../platform/platform-account.dto'
import { PreAuthCodeDTO } from './wx-third-auth.dto'
import { WxCacheKey } from './wx-cache-key'
import { WxCommonService } from './wx-common.service'
import { PlatformAccountService } from '../platform/platform-account.service'

@Injectable()
export class WxThirdAuthService {
  logger = new Logger('WxThirdAuthService')

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly wxBasicService: WxBasicService,
    private readonly wxCommonService: WxCommonService,
    private readonly platformAccountService: PlatformAccountService,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>
  ) {}

  /**
   * 获取预授权码
   * @returns
   */
  async getPreAuthCode(currentUserId: string): Promise<PreAuthCodeDTO> {
    // const preAuthCodeCacheKey = WxCacheKey.getPreAuthCode(currentUserId)
    // const preAuthCode = await this.cacheManager.get<string>(preAuthCodeCacheKey)
    const component_appid = process.env.WX_THIRD_COMPONENT_APPID

    // if (preAuthCode) {
    //   return {
    //     pre_auth_code: preAuthCode,
    //     component_appid: component_appid,
    //     authType: 1
    //   }
    // }
    const component_access_token = await this.wxCommonService.getComponetAccessToken()
    const result = await this.wxBasicService.getPreAuthCode(component_appid, component_access_token)

    // 设置预授权码缓存
    // await this.cacheManager.set(
    //   preAuthCodeCacheKey,
    //   result.pre_auth_code,
    //   WxCacheKey.PreAuthCodeTime
    // )
    return {
      pre_auth_code: result.pre_auth_code,
      component_appid: component_appid,
      authType: 1
    }
  }

  /**
   * 获取授权账号信息
   * @param code
   * @returns
   */
  async getAuthInformation(
    currentTeamId: string,
    code: string
  ): Promise<PlatformAccountDetailResponse> {
    const component_appid = process.env.WX_THIRD_COMPONENT_APPID
    const componentAccessToken = await this.wxCommonService.getComponetAccessToken()
    const result = await this.wxBasicService.getAuthorizerCodeInformation(
      component_appid,
      componentAccessToken,
      code
    )
    const access_token = result.authorization_info.authorizer_access_token
    const authorizer_appid = result.authorization_info.authorizer_appid
    const authorizer_refresh_token = result.authorization_info.authorizer_refresh_token
    const accessTokenCacheKey = WxCacheKey.getAccessToken(
      result.authorization_info.authorizer_appid
    )

    // 微信授权账号access_token
    await this.cacheManager.set(accessTokenCacheKey, access_token, WxCacheKey.AccessTokenTime)

    // 获取授权信息
    const authInfo = await this.wxBasicService.getAuthorizerInformation(
      component_appid,
      componentAccessToken,
      authorizer_appid
    )
    this.logger.log(`获取授权账号数据:${JSON.stringify(authInfo)}`)

    const body = {
      token: authorizer_refresh_token,
      status: LoginStatus.Succesed,
      platformAccountName: authInfo?.authorizer_info?.nick_name ?? '未知',
      platformAvatar: authInfo?.authorizer_info?.head_img ?? '',
      platformType: PlatformType.开放平台,
      platformName: PlatformNameEnum.微信公众号,
      platformAuthorId: authorizer_appid,
      serviceTypeId: authInfo?.authorizer_info?.service_type_info?.id,
      serviceTypeName: authInfo?.authorizer_info?.service_type_info?.name,
      accountStatus: authInfo?.authorizer_info?.account_status,
      principalName: authInfo?.authorizer_info?.principal_name,
      verifyTypeInfo: authInfo?.authorizer_info?.verify_type_info?.id.toString()
    } as PlatformAccountRequest

    const existAccount = await this.platformAccountModel.findOne({
      platformAuthorId: body.platformAuthorId,
      platformType: PlatformType.开放平台
    })
    if (existAccount) {
      if (currentTeamId !== existAccount.teamId.toString()) {
        //账号添加失败，但是需要更新微信refresh_token信息
        await this.platformAccountModel.updateOne(
          {
            _id: new Types.ObjectId(existAccount._id)
          },
          {
            token: body.token
          }
        )
        throw new ForbiddenException('无法重新添加，账号已绑定在其他团队')
      }
    }

    const platformAccount = await this.platformAccountService.platformAccountCreateAsync(body)
    return platformAccount
  }

  /**
   * 取消授权
   * @param authorizerAppid
   * @returns
   */
  async cancelAuth(authorizerAppid: string) {
    let platformAccount = await this.platformAccountModel.findOne({
      platformAuthorId: authorizerAppid
    })

    if (!platformAccount) {
      return
    }

    platformAccount.status = LoginStatus.CancelAuth

    await platformAccount.save()
  }

  /**
   * 账号更新
   * @param authorizerAppid
   * @returns
   */
  async updateAuthInfomation(authorizerAppid: string) {
    let platformAccount = await this.platformAccountModel.findOne({
      platformAuthorId: authorizerAppid
    })

    if (!platformAccount) {
      return
    }

    const component_appid = process.env.WX_THIRD_COMPONENT_APPID
    const componentAccessToken = await this.wxCommonService.getComponetAccessToken()

    // 获取授权信息
    const authInfo = await this.wxBasicService.getAuthorizerInformation(
      component_appid,
      componentAccessToken,
      authorizerAppid
    )

    const updateDate:UpdateQuery<PlatformAccountEntity> = {}
    updateDate.platformAccountName = authInfo?.authorizer_info?.nick_name ?? '未知'
    updateDate.platformAvatar = authInfo?.authorizer_info?.head_img ?? ''
    updateDate.serviceTypeId = authInfo?.authorizer_info?.service_type_info?.id
    updateDate.serviceTypeName = authInfo?.authorizer_info?.service_type_info?.name
    updateDate.accountStatus = authInfo?.authorizer_info?.account_status
    updateDate.principalName = authInfo?.authorizer_info?.principal_name
    updateDate.verifyTypeInfo = authInfo?.authorizer_info?.verify_type_info?.id.toString()
    updateDate.status = LoginStatus.Succesed
    updateDate.loginStatusUpdatedAt = new Date()

    await this.platformAccountModel.updateOne({
      platformAuthorId: authorizerAppid
    },updateDate)
  }
}
