import { Inject, Injectable } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { AdEntity } from '@yxr/mongo'
import { Model } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { AdDetailDTO } from './ad.dto'
import { AdTypeEmun } from 'packages/common'

@Injectable()
export class AdService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(AdEntity.name) private adModel: Model<AdEntity>
  ) {}

  async getAds(): Promise<AdDetailDTO[]> {
    const ads = await this.adModel
      .find({
        enabled: true,
        adType: AdTypeEmun.Banner,
        $or: [
          {
            isTimed: true,
            expiredStartAt: { $lte: new Date() },
            expiredEndAt: { $gte: new Date() }
          },
          {
            isTimed: false
          }
        ]
      })
      .sort({ sort: -1, updatedAt: -1 })

    return ads.map((item) => ({
      sort: item.sort,
      name: item.name,
      adPath: `${process.env.OSS_DOWNLOAD_URL}/${item.adUrl}?v=${item.__v}`,
      jumpToUrl: item.isJumpTo ? item.jumpToUrl : ''
    }))
  }

  async getPopups(): Promise<AdDetailDTO> {
    const ad = await this.adModel.findOne({
      enabled: true,
      adType: AdTypeEmun.Popup,
      $or: [
        {
          isTimed: true,
          expiredStartAt: { $lte: new Date() },
          expiredEndAt: { $gte: new Date() }
        },
        {
          isTimed: false
        }
      ]
    })
    if(!ad){
      return new AdDetailDTO
    }
    return {
      id: ad.id,
      sort: ad.sort,
      name: ad.name,
      adPath: `${process.env.OSS_DOWNLOAD_URL}/${ad.adUrl}?v=${ad.__v}`,
      jumpToUrl: ad.isJumpTo ? ad.jumpToUrl : '',
      popupType: ad.popupType
    }
  }
}
