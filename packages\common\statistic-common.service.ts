import { ContentStatisticDataDTO } from '../../apps/gateway/src/modules/platform/content.dto'
import { ForbiddenException, Injectable } from '@nestjs/common'
import { ContentType, OverviewDateTypeEnum } from './enums'
import dayjs from 'dayjs'

@Injectable()
export class StatisticCommonService {
  static platformAccountKeys = {
    抖音: ['总粉丝量', '播放量', '主页访问', '作品点赞', '净增粉丝', '取关粉丝'],
    快手: ['总粉丝', '播放量', '总获赞', '净增粉丝量', '评论量', '分享量'],
    小红书: [
      '总粉丝数',
      // '总获赞与收藏',
      '观看',
      // '观看总时长(小时)',
      '点赞',
      '收藏',
      '评论',
      '笔记涨粉'
    ],
    视频号: ['总关注者', '播放', '曝光量', '喜欢', '点赞', '评论', '分享'],
    哔哩哔哩: ['总粉丝数', '总播放数', '总获赞数', '净增粉丝'],
    新浪微博: ['总粉丝', '视频播放总数', '总转评赞', '视频发布总数', '阅读总数', '发博总数'],
    头条号: ['总粉丝数', '总阅读(播放)量', '总作品量', '展现量', '粉丝展现量', '点赞量', '评论量'],
    百家号: ['总粉丝数', '阅读(播放)量', '推荐量', '评论量', '收藏量', '分享量'],
    知乎: [
      '关注者',
      '阅读总量',
      '播放总量',
      '赞同总量',
      '喜欢总量',
      '评论总量',
      '收藏总量',
      '分享总量'
    ],
    企鹅号: [
      '粉丝量',
      '播放量',
      '播放视频数',
      '平均播放时长(分钟)',
      '点赞量',
      '评论量',
      '分享量',
      '阅读量',
      '阅读篇数'
    ],
    搜狐号: ['内容量', '订阅', '阅读数', '访问(播放)数', '点赞','评论数','分享数'],
    一点号: [
      '粉丝量',
      '内容量',
      '分享量',
      '评论量',
      '收藏量',
      '点赞量'
    ],
    大鱼号: ['粉丝数', '推荐', '播放/阅读', '点赞', '评论', '分享'],
    网易号: ['总粉丝', '总阅读', '总收益', '展现', '跟帖', '分享', '播放'],
    爱奇艺: [
      '总粉丝量',
      '展现量',
      '播放时长',
      '评论量',
      '点赞量',
      '弹幕量',
      '分享量'
    ],
    腾讯微视: ['粉丝', '点赞'],
    微信公众号: ['总粉丝'],
  }

  static platformContentKeys = {
    抖音: ['总播放', '总点赞', '总评论', '总分享', '总收藏'],
    快手: ['总播放量', '总评论', '总点赞', '总收藏', '总涨粉'],
    小红书: ['总阅读', '总评论', '总点赞', '总收藏', '总分享'],
    视频号: {
      video: ['总播放', '总爱心', '总评论', '总分享', '总点赞'],
      dynamic: ['总阅读', '总爱心', '总评论', '总点赞']
    },
    哔哩哔哩: {
      video: ['总播放', '总点赞', '总弹幕', '总评论', '总硬币', '总收藏', '总分享'],
      dynamic: ['总阅读', '总评论', '总点赞', '总收藏', '总硬币']
    },
    新浪微博: {
      video: ['总播放', '总分享', '总评论', '总点赞'],
      article: ['总分享', '总评论', '总点赞']
    },
    头条号: {
      article: ['总展现', '总阅读', '总点赞', '总评论'],
      video: ['总展现', '总播放', '总点赞', '总评论'],
      miniVideo: ['总展现', '总播放', '总点赞', '总评论']
    },
    百家号: {
      video: ['总推荐', '总播放', '总评论', '总点赞', '总收藏', '总分享'],
      miniVideo: ['总阅读', '总评论', '总点赞', '总收藏', '总分享'],
      article: ['总推荐', '总阅读', '总评论', '总点赞', '总收藏', '总分享']
    }
  }

  static platformContentTypes = {
    抖音: [ContentType.all],
    快手: [ContentType.all],
    小红书: [ContentType.all],
    视频号: [ContentType.video, ContentType.dynamic],
    哔哩哔哩: [ContentType.video, ContentType.dynamic],
    新浪微博: [ContentType.video, ContentType.article],
    头条号: [ContentType.article, ContentType.video, ContentType.miniVideo],
    百家号: [ContentType.article, ContentType.video, ContentType.miniVideo]
  }

  // 账号统计入库映射字段
  private static fieldMapping = new Map<string, string>([
    //粉丝
    ['粉丝', 'fansTotal'],
    ['粉丝量', 'fansTotal'],
    ['总粉丝量', 'fansTotal'],
    ['总粉丝', 'fansTotal'],
    ['总粉丝数', 'fansTotal'],
    ['粉丝数', 'fansTotal'],
    ['总关注者', 'fansTotal'],
    ['关注者', 'fansTotal'],
    //播放阅读
    ['内容量', 'playTotal'],
    ['播放量', 'playTotal'],
    ['总播放数', 'playTotal'],
    ['总阅读(播放)量', 'playTotal'],
    ['播放/阅读', 'playTotal'],
    ['播放总量', 'playTotal'],
    ['阅读(播放)量', 'playTotal'],
    ['视频播放总数', 'playTotal'],
    ['访问(播放)数', 'playTotal'],
    ['观看', 'playTotal'],
    ['播放', 'playTotal'],
    //阅读
    ['总阅读', 'readTotal'],
    ['阅读总数', 'readTotal'],
    ['阅读总量', 'readTotal'],
    ['阅读量', 'readTotal'],
    ['阅读数', 'readTotal'],
    // 次数
    ['播放视频数', 'viewsTotal'],
    ['阅读篇数', 'viewsTotal'],
    ['播放时长', 'watchDuration'],
    ['平均观看时长', 'watchDuration'],
    ['平均播放时长(分钟)', 'watchDuration'],
    //点赞
    ['点赞量', 'likesTotal'],
    ['点赞', 'likesTotal'],
    ['总获赞数', 'likesTotal'],
    ['获赞', 'likesTotal'],
    ['总爱心', 'likesTotal'],
    ['作品点赞', 'likesTotal'],
    ['赞同总量', 'likesTotal'],
    //净增粉丝
    ['净增粉丝量', 'netFansGrowth'],
    ['净增粉丝', 'netFansGrowth'],
    //评论
    ['评论量', 'commentsTotal'],
    ['评论', 'commentsTotal'],
    ['总评论', 'commentsTotal'],
    ['评论总量', 'commentsTotal'],
    ['评论数', 'commentsTotal'],
    //分享
    ['分享量', 'sharesTotal'],
    ['分享', 'sharesTotal'],
    ['总分享', 'sharesTotal'],
    ['总分享', 'sharesTotal'],
    ['分享总量', 'sharesTotal'],
    ['分享数', 'sharesTotal'],
    //收藏
    ['喜欢', 'favoritesTotal'],
    ['收藏量', 'favoritesTotal'],
    ['总收藏', 'favoritesTotal'],
    ['收藏总量', 'favoritesTotal'],
    ['订阅', 'favoritesTotal'],
    //转发
    ['曝光量', 'impressionsTotal'],
    ['展现量', 'impressionsTotal'],
    ['展现', 'impressionsTotal'],
    ['转发总量', 'impressionsTotal'],
    ['跟帖', 'impressionsTotal'],
    //推荐
    ['推荐', 'recommendationsTotal'],
    ['推荐量', 'recommendationsTotal'],
    ['赞同总量', 'recommendationsTotal'],
    
    ['粉丝展现量', 'fanImpressions'],
    ['主页访问', 'profileVisits'],
    ['笔记涨粉', 'fansGrowth'],
    ['取关粉丝', 'fansLost'],
    ['总作品量', 'worksTotal'],
    ['视频发布总数', 'videoTotal'],
    ['总收益', 'incomeTotal'],
    ['总转赞评', 'zhuanZanPingTotal'],
    ['发博总数', 'blogTotal'],
    ['弹幕量', 'danmuTotal']
  ])

  // 账号内容统计入库映射字段
  private static contentFieldMapping = new Map<string, string>([
    ['总播放量', 'play'],
    ['总播放', 'play'],
    ['播放', 'play'],
    ['总阅读', 'read'],
    
    ['赞同', 'great'],
    ['总点赞', 'great'],
    ['总爱心', 'great'],
    
    ['评论', 'comment'],
    ['总评论', 'comment'],
    
    ['分享', 'share'],
    ['总分享', 'share'],
    
    ['收藏', 'collect'],
    ['总收藏', 'collect'],
    
    ['总涨粉', 'addFans'],
    ['总推荐', 'reCommand'],
    ['总展现', 'reCommand'],
    ['总弹幕', 'danmu'],
    ['总硬币', 'coin'],

    ['喜欢', 'love'],
    ['总爱心', 'love']
  ])

  /**
   * 获取账号字段
   * @param platform
   * @returns
   */
  async getAccountOverviewField(platform: string) {
    const keys = StatisticCommonService.platformAccountKeys[platform]
    if (!keys) {
      throw new ForbiddenException(`未知的平台: ${platform}`)
    }

    let result = []
    keys.forEach((key: string) => {
      const dbField = StatisticCommonService.fieldMapping.get(key)
      if (dbField) {
        result.push({
          key: dbField,
          value: key
        })
      }
    })

    return result
  }

  /**
   * 获取账号趋势字段
   * @param platform
   * @param key
   * @returns
   */
  async getAccountTrendField(platform: string, key: string) {
    const keys = StatisticCommonService.platformAccountKeys[platform]
    if (!keys) {
      throw new ForbiddenException(`未知的平台: ${platform}`)
    }

    if (!keys.includes(key)) {
      throw new ForbiddenException('账号趋势类型错误')
    }
    return StatisticCommonService.fieldMapping.get(key)
  }

  /**
   * 获取账号内容字段
   * @param platform
   * @param type
   * @returns
   */
  async getContentOverviewField(platform: string, type: ContentType) {
    const keys = StatisticCommonService.platformContentKeys[platform]
    if (!keys || (!type && typeof keys === 'object' && !Array.isArray(keys))) {
      throw new ForbiddenException(`未匹配平台: ${platform}:类型${type}`)
    }

    let fields = null
    if (type) {
      fields = keys[type]
    } else {
      fields = keys
    }
    if (!fields) {
      return null
    }
    let result = []
    fields.forEach((key: string) => {
      const dbField = StatisticCommonService.contentFieldMapping.get(key)
      if (dbField) {
        result.push({
          key: dbField,
          value: key
        })
      }
    })

    return result
  }

  /**
   * 获取作品趋势字段
   * @param platform
   * @param key
   * @returns
   */
  async getContentTrendField(platform: string, type: ContentType, key: string) {
    const keys = StatisticCommonService.platformContentKeys[platform]
    if (!keys || (!type && typeof keys === 'object' && !Array.isArray(keys))) {
      throw new ForbiddenException(`未匹配平台: ${platform}:类型${type}`)
    }
    let fields = null
    if (type) {
      fields = keys[type]
    } else {
      fields = keys
    }
    if (!fields) {
      throw new ForbiddenException('平台内容趋势获取失败')
    }
    if (!fields.includes(key)) {
      throw new ForbiddenException('平台内容趋势类型错误')
    }
    return StatisticCommonService.contentFieldMapping.get(key)
  }

  async getAccountOverview(platform: string, jsonStr: string) {
    // 获取当前平台的键
    const keys = StatisticCommonService.platformAccountKeys[platform]
    if (!keys) {
      throw new ForbiddenException(`未知的平台: ${platform}`)
    }

    let result = null
    if (platform == '视频号') {
      //单独处理需求和
      result = await this.shipinghaoExtractValues(jsonStr, keys)
    } else {
      result = await this.extractValues(jsonStr, keys)
    }

    return result
  }

  /**
   * 提取 JSON 字符串中指定的字段值
   * @param jsonStr JSON 字符串
   * @param keys 字段名数组
   * @returns
   */
  async extractValues(jsonStr: string, keys: string[]) {
    try {
      const data = JSON.parse(jsonStr) // 解析 JSON 字符串
      const result: Record<string, number> = {}

      if (data?.value?.video) {
        const videoData = data.value.video
        keys.forEach((key) => {
          const item = videoData.find((video) => video.name === key)
          const dbField = StatisticCommonService.fieldMapping.get(key)
          if (dbField) {
            result[dbField] = item ? StatisticCommonService.convertToNumber(item.value) : null
          }
        })
      }

      if (data?.value?.dynamic) {
        const dynamicData = data.value.dynamic
        keys.forEach((key) => {
          const item = dynamicData.find((dynamic) => dynamic.name === key)
          const dbField = StatisticCommonService.fieldMapping.get(key)
          if (dbField && !result[dbField]) {
            result[dbField] = item ? StatisticCommonService.convertToNumber(item.value) : null
          }
        })
      }

      return result
    } catch (error) {
      console.error('JSON 解析失败:', error)
      return null
    }
  }

  /**
   * 视频号数据解析
   * @param jsonStr
   * @param keys
   * @returns
   */
  async shipinghaoExtractValues(jsonStr: string, keys: string[]) {
    try {
      const data = JSON.parse(jsonStr) // 解析 JSON 字符串
      const result: Record<string, number> = {}

      if (data?.value?.video) {
        const videoData = data.value.video
        keys.forEach((key) => {
          const item = videoData.find((video) => video.name === key)
          if (item) {
            const dbField = StatisticCommonService.fieldMapping.get(key)
            if (dbField) {
              result[dbField] = item ? StatisticCommonService.convertToNumber(item.value) : null
            }
          }
        })
      }

      if (data?.value?.dynamic) {
        const dynamicData = data.value.dynamic
        keys.forEach((key) => {
          const item = dynamicData.find((dynamic) => dynamic.name === key)
          const dbField = StatisticCommonService.fieldMapping.get(key)
          if (dbField) {
            if (!result[dbField]) {
              result[dbField] = item ? StatisticCommonService.convertToNumber(item.value) : null
            } else if (result[dbField] && dbField != 'fansTotal') {
              const convertToNumber = item ? StatisticCommonService.convertToNumber(item.value) : 0
              result[dbField] += convertToNumber
            }
          }
        })
      }

      return result
    } catch (error) {
      console.error('JSON 解析失败:', error)
      return null
    }
  }

  /**
   * 提取账号作品内容
   * @param jsonStr JSON 字符串
   * @param keys 字段名数组
   * @returns
   */
  async contentExtractValues(data: ContentStatisticDataDTO, keys: string[]) {
    try {
      const result = {} // 初始化结果对象

      // 如果是扁平结构，直接从根对象提取数据
      keys.forEach((key) => {
        const dbField = StatisticCommonService.fieldMapping[key]
        if (dbField) {
          result[dbField] = data[key] !== undefined ? data[key] : null
        }
      })

      return result
    } catch (error) {
      console.error('JSON 解析失败:', error)
      return null
    }
  }

  /**
   * 将字符串转换为数字
   * @param str
   * @returns
   */
  static convertToNumber(str: string) {
    if (str === '-' || str === '' || str == null) {
      return 0
    }
    // 去掉字符串两端的空格
    str = str.trim()

    // 处理带中文单位的数字
    if (str.includes('万')) {
      // 提取数字部分并转换为 float，再乘以 10,000
      return parseFloat(str.replace('万', '')) * 10000
    }

    // 处理包含逗号的数字
    if (str.includes(',')) {
      // 去掉逗号后转换为 number
      return Number(str.replace(/,/g, ''))
    }

    // 直接转换为 number（适用于普通数字）
    return Number(str)
  }

  /**
   * 根据日期类型返回开始日期与结束日期，以及天数
   * @param dateType 日期类型
   * @returns
   */
  getDateRange(dateType: OverviewDateTypeEnum): { startDate: Date; endDate: Date; days: number } {
    let startDateLocal = dayjs().tz('Asia/Shanghai')
    let endDateLocal = dayjs().tz('Asia/Shanghai').endOf('day')
    let days = 30

    switch (dateType) {
      case OverviewDateTypeEnum.Day:
        startDateLocal = startDateLocal.subtract(1, 'day')
        days = 1
        break
      case OverviewDateTypeEnum.Seven:
        startDateLocal = startDateLocal.subtract(7, 'day')
        days = 7
        break
      case OverviewDateTypeEnum.Thirty:
        startDateLocal = startDateLocal.subtract(30, 'day')
        days = 30
        break
      default:
        startDateLocal = startDateLocal.subtract(30, 'day')
        days = 30
        break
    }

    // 转换为 UTC 时间
    const startDate = startDateLocal.startOf('day').utc().toDate()
    const endDate = endDateLocal.utc().toDate()
    return { startDate, endDate, days }
  }

  /**
   * 根据天数返回日期列表
   * @param days 天数
   * @returns
   */
  generateDates(days = 30): { date: string; count: number }[] {
    const dates = []
    const currentDate = new Date()
    for (let i = days; i >= 1; i--) {
      const prevDate = new Date(currentDate)
      prevDate.setDate(currentDate.getDate() - i)
      dates.push({ date: dayjs(prevDate).format('YYYY-MM-DD'), count: 0 })
    }

    return dates
  }

  /**
   * 计算剩余天数
   * @param expiredAt
   * @returns
   */
  static remainingDay(expiredAt?: Date) {
    return expiredAt ? Math.max(dayjs(expiredAt).diff(dayjs(), 'day') + 1, 0) : 0
  }
}
