import { Module } from '@nestjs/common'
import { MemberController } from './member.controller'
import { MemberService } from './member.service'
import {
  AdminMongoose,
  FollowRecordMongoose,
  UserMongoose,
  MemberMongoose,
  TeamMongoose,
  UserDeviceLogsMongoose,
  UserDevicesMongoose,
  CustomerAssignRecordMongoose
} from '@yxr/mongo'
import { FollowRecordController } from './follow-record.controller'
import { FollowRecordService } from './follow-record.service'
import { WebhookModule } from '../webhook/webhook.module'
import { MemberDevicesService } from './member-devices.service'
import { CommonModule } from '@yxr/common'

@Module({
  imports: [
    UserMongoose,
    AdminMongoose,
    FollowRecordMongoose,
    MemberMongoose,
    TeamMongoose,
    UserDeviceLogsMongoose,
    UserDevicesMongoose,
    CustomerAssignRecordMongoose,
    WebhookModule,
    CommonModule,
  ],
  controllers: [MemberController, FollowRecordController],
  providers: [MemberService, FollowRecordService, MemberDevicesService]
})
export class MemberModule {}
