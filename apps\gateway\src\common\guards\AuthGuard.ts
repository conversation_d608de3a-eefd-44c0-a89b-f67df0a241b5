import {
  Injectable,
  CanActivate,
  ExecutionContext,
  Inject,
  UnauthorizedException,
  ForbiddenException,
  Logger
} from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import type { FastifyRequest } from 'fastify'
import { Cache } from 'cache-manager'
import { TeamEntity, UserEntity } from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import crypto from 'crypto'

@Injectable()
export class TokenGuard implements CanActivate {
  private readonly logger = new Logger(TokenGuard.name)

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  private readonly excludedRoutes = [
    '/users/auth',
    '/users/password-login',
    '/users/sms-code',
    '/teams/invite-codes',
    '/online-scripts',
    '/storages-callback',
    '/wx/callback',
    '/wx/ipad/callback',
    `/${process.env.ALIPAY_WEBHOOK_URL}`,
    `/${process.env.WECHATPAY_WEBHOOK_URL}`,
    `/daili/ip`,
    `/downloads/desktop`,
    `/notices/version`,
    '/overseas-integration',
    '/users/token/validate',
    '/open-platform/webhook'
  ]

  private readonly teamNotFoundRoutes = [
    '/teams',
    '/teams/:teamId',
    '/teams/:teamId/auth',
    '/users/info'
  ]

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()

    request.client = {
      name: request.headers['x-client']?.toString(),
      version: request.headers['x-version']?.toString(),
      platform: request.headers['x-platform']?.toString()
    }

    if (request.client.name === 'cloud') {
      //云爬虫鉴权
      const { authorization, timestamp, authtoken } = request.headers
      const tokenString = `${timestamp}yixiaoer_cloud_publish`
      const md5Hash = crypto.createHash('md5').update(tokenString).digest('hex')
      if (authorization !== md5Hash) {
        throw new UnauthorizedException('没有访问权限')
      }
      const [teamId, userId] = authtoken.toString().split(':')
      request.session = {
        userId: userId,
        teamId: teamId
      }
    } else {
      const { authorization } = request.headers
      const { routeOptions } = request

      let cache = null

      // 首先尝在本地/开发环境下使用模拟的 token 验证
      if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev') {
        if (authorization?.indexOf(':') > 0) {
          const [teamId, userId] = authorization.split(':')
          const user = await this.userModel.findById(new Types.ObjectId(userId))
          this.logger.debug(`模拟的会话: userId: ${userId}, teamId: ${teamId}`)
          cache = {
            user: user,
            userId: userId,
            teamId: teamId
          }
        }
      }

      if (cache === null) {
        const tokenKey = `session:au-${authorization}`
        cache = await this.cacheManager.get<{ user: UserEntity; userId: string; teamId: string }>(
          tokenKey
        )
      }

      if (cache) {
        // @ts-ignore
        request.user = cache.user
        request.session = {
          userId: cache.userId,
          teamId: cache.teamId
        }
        request.authorization = authorization

        // 当 token 中包含有 teamId 信息时, 需要判断团队是否存在/或解散
        if (cache.teamId) {
          const team = await this.teamModel.findOne<TeamEntity>({
            _id: new Types.ObjectId(cache.teamId),
            isDeleted: false
          })

          if (team === null || team === undefined) {
            // 团队被解散时允许访问白名单路由
            for (let i = 0; i < this.teamNotFoundRoutes.length; i++) {
              if (routeOptions.url.indexOf(this.teamNotFoundRoutes[i]) >= 0) {
                return true
              }
            }

            throw new ForbiddenException('团队已解散, 无法继续使用')
          }
        }
      } else {
        // 登录失效时允许访问白名单路由
        for (let i = 0; i < this.excludedRoutes.length; i++) {
          if (routeOptions.url.indexOf(this.excludedRoutes[i]) >= 0) {
            return true
          }
        }
        throw new UnauthorizedException('登录失效, 请重新登录')
      }
    }

    return true
  }
}
