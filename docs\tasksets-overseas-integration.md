# TaskSets接口海外平台集成实现

## 概述

根据您的要求，我已经在现有的 `taskSets` 发布接口中集成了海外平台账号的识别和处理逻辑。用户现在可以在客户端混合选择国内和海外平台账号，提交到统一的发布接口，系统会自动识别海外账号并将相关任务推送到香港服务端执行。

## 核心实现

### 1. TaskSetService 集成

**文件位置**: `apps/gateway/src/modules/publish/taskSet.service.ts`

#### 主要修改：

1. **注入海外发布集成服务**：
   ```typescript
   constructor(
     // ... 其他依赖
     private readonly overseasPublishIntegrationService: OverseasPublishIntegrationService
   ) {}
   ```

2. **在 `postTaskSetAsync` 方法中添加海外平台处理**：
   ```typescript
   // 检查是否有海外平台账号，如果有则处理海外发布
   const platformAccountIds = body.platformAccounts.map(item => item.platformAccountId)
   const hasOverseasAccounts = await this.overseasPublishIntegrationService.hasOverseasAccounts(platformAccountIds)
   
   if (hasOverseasAccounts) {
     // 异步处理海外平台发布任务
     setImmediate(async () => {
       try {
         await this.overseasPublishIntegrationService.handleOverseasPublishTasks(taskIdentityId)
       } catch (error) {
         await this.loggerService.error(this.request, '海外平台发布任务处理失败', { 
           taskSetId: taskIdentityId, 
           error: error 
         })
       }
     })
   }
   ```

3. **在 `patchPublishTaskSets` 方法中添加类似逻辑**：
   - 当任务集状态更新为成功时，也会检查和处理海外平台账号

### 2. 模块依赖配置

**文件位置**: `apps/gateway/src/modules/publish/task.model.ts`

添加了 `OverseasPlatformModule` 的导入：
```typescript
@Module({
  imports: [
    // ... 其他模块
    OverseasPlatformModule
  ],
  // ...
})
export class TaskModule {}
```

### 3. 海外发布集成服务

**文件位置**: `apps/gateway/src/modules/overseas-platform/overseas-publish-integration.service.ts`

#### 核心功能：

1. **`hasOverseasAccounts(platformAccountIds: string[])`**：
   - 检查给定的平台账号ID列表中是否包含海外平台账号
   - 通过查询 `PlatformAccountEntity` 的 `platformType` 字段判断

2. **`handleOverseasPublishTasks(taskSetId: string)`**：
   - 查找指定任务集中的海外平台任务
   - 将任务的发布渠道更新为 `PublishChannel.overseas`
   - 构建发布任务数据并发送到香港服务端

3. **内容格式转换**：
   - `transformContentForOverseas()`: 将现有的发布内容格式转换为海外平台统一格式
   - 支持微信公众号文章、视频、图片等多种内容类型

### 4. 数据流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant TaskSet as TaskSetService
    participant Overseas as OverseasPublishIntegrationService
    participant HK as 香港服务端
    participant Platform as 海外平台

    Client->>TaskSet: POST /taskSets (混合账号)
    TaskSet->>TaskSet: 创建任务集和任务
    TaskSet->>Overseas: 检查是否有海外账号
    Overseas->>TaskSet: 返回检查结果
    
    alt 有海外账号
        TaskSet->>Overseas: 异步处理海外任务
        Overseas->>Overseas: 查找海外平台任务
        Overseas->>Overseas: 更新发布渠道为overseas
        Overseas->>HK: 发送任务到香港服务
        HK->>Platform: 调用平台API发布
        Platform->>HK: 返回发布结果
        HK->>TaskSet: 回调发布结果
    end
    
    TaskSet->>Client: 返回任务集ID
```

## 技术特性

### 1. 无缝集成
- 不改变现有客户端调用方式
- 保持统一的发布接口体验
- 自动识别和处理海外平台账号

### 2. 异步处理
- 海外平台任务处理不阻塞主发布流程
- 使用 `setImmediate` 确保响应速度
- 完善的错误处理和日志记录

### 3. 智能识别
- 通过 `PlatformAccountEntity.platformType` 字段识别海外账号
- 支持混合选择国内外平台账号
- 自动分离处理不同类型的账号

### 4. 内容适配
- 智能转换现有内容格式为海外平台格式
- 支持文本、图片、视频、混合内容
- 处理微信公众号文章等特殊格式

### 5. 状态管理
- 海外平台任务保持原有的发布渠道（local/cloud）
- 通过平台账号的 `platformType` 字段识别海外平台
- 保持与现有任务状态管理的一致性
- 支持回调更新任务状态

## 支持的平台

已实现所有五个海外平台的发布功能：

1. **Facebook**: 文本、图片、视频、相册发布
2. **Instagram**: 图片、视频、轮播发布
3. **Twitter**: 文本、图片、视频发布，支持分块上传
4. **TikTok**: 视频发布
5. **YouTube**: 视频发布，支持缩略图

## 配置要求

### 环境变量
```bash
# 香港服务端地址
OVERSEAS_BASE_ADDRESS=https://overseas.yixiaoer.com/

# 国内服务端地址（用于回调）
LITE_BASE_ADDRESS=https://api.yixiaoer.com/

# 各平台API配置
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
# ... 其他平台配置
```

### 数据库字段
- `PlatformAccountEntity.platformType`: 区分国内外平台
- `TaskEntity.publishChannel`: 标识发布渠道（保持原有的 local/cloud）

## 使用示例

客户端调用方式保持不变：

```typescript
// 客户端发布请求（混合选择国内外账号）
const publishRequest = {
  platformAccounts: [
    { platformAccountId: 'wechat_account_1' },    // 微信账号
    { platformAccountId: 'facebook_account_1' },  // Facebook账号
    { platformAccountId: 'instagram_account_1' }, // Instagram账号
  ],
  publishType: 'video',
  content: { /* 发布内容 */ },
  // ... 其他参数
}

// 调用统一发布接口
const response = await fetch('/taskSets', {
  method: 'POST',
  body: JSON.stringify(publishRequest)
})
```

系统会自动：
1. 识别出 Facebook 和 Instagram 是海外平台账号
2. 为微信账号创建本地发布任务
3. 为海外账号创建发布任务并推送到香港服务端执行
4. 返回统一的任务集ID

## 监控和日志

- 完整的错误处理和日志记录
- 海外平台发布失败不影响整体任务集状态
- 支持任务状态查询和监控
- 详细的发布结果回调机制

## 扩展性

- 新增海外平台只需实现 `ContentPublishProvider` 接口
- 支持更多内容类型和发布参数
- 可以轻松添加定时发布、内容审核等功能
- 支持批量发布和并发处理优化
