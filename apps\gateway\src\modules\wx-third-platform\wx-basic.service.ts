import axios from 'axios'
import FormData from 'form-data'
import { WxArticle, WxMediaType } from './wx-publish.dto'
import { Injectable } from '@nestjs/common'
import { TlsService } from '@yxr/huoshan'
/**
 * 微信开放平台的基础接口服务
 * 注意此服务有调用事件不能引入 FastifyRequest 微信相关事件功能会失效
 */
@Injectable()
export class WxBasicService {
  constructor(private readonly loggerService: TlsService) {}

  /**
   * 获取第三方平台接口的调用凭据
   * @param component_verify_ticket 微信后台推送的 ticket
   * @param component_appid 第三方平台 appid
   * @param component_appsecret 第三方平台 appsecret
   * @returns
   */
  async getComponentToken(
    component_verify_ticket: string,
    component_appid: string,
    component_appsecret: string
  ) {
    const data = {
      component_verify_ticket: component_verify_ticket,
      component_appid: component_appid,
      component_appsecret: component_appsecret
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/component/api_component_token',
      data
    )
    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取微信第三方调用凭证响应结果信息', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 获取预授权码，（pre_auth_code）是第三方平台方实现授权托管的必备信息
   * @param component_appid  第三方平台 appid
   * @param component_access_token 第三方平台接口调用凭证
   * @returns
   */
  async getPreAuthCode(component_appid: string, component_access_token: string) {
    const data = {
      component_appid: component_appid
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode?component_access_token=' +
        component_access_token,
      data
    )
    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取微信预授权码响应结果信息', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 获取授权账号调用令牌
   * @param authorizer_appid 授权方 appid
   * @param component_appid 第三方平台 appid
   * @param component_access_token 第三方平台接口调用凭证
   * @param authorizer_refresh_token 刷新令牌，获取授权信息时得到
   * @returns
   */
  async getAuthorizerAccessToken(
    authorizer_appid: string,
    component_appid: string,
    component_access_token: string,
    authorizer_refresh_token: string
  ) {
    const data = {
      component_appid: component_appid,
      authorizer_appid: authorizer_appid,
      authorizer_refresh_token: authorizer_refresh_token
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token?component_access_token=' +
        component_access_token,
      data
    )
    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取授权账号调用令牌响应结果信息', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 使用授权码获取授权信息
   * @param component_appid 第三方平台 appid
   * @param component_access_token 第三方平台接口调用凭证
   * @param authorization_code 授权码, 会在授权成功时返回给第三方平台
   * @returns
   */
  async getAuthorizerCodeInformation(
    component_appid: string,
    component_access_token: string,
    authorization_code: string
  ) {
    const data = {
      component_appid: component_appid,
      authorization_code: authorization_code
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/component/api_query_auth?component_access_token=' +
        component_access_token,
      data
    )
    if (resp.data?.errcode) {
      await this.loggerService.error(null, '使用授权码获取授权信息响应结果', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 获取授权账号详情
   * @param component_appid 第三方平台 appid
   * @param component_access_token 第三方平台接口调用凭证
   * @param authorizer_appid 授权的公众号或者小程序的appid
   * @returns
   */
  async getAuthorizerInformation(
    component_appid: string,
    component_access_token: string,
    authorizer_appid: string
  ) {
    const data = {
      component_appid: component_appid,
      authorizer_appid: authorizer_appid
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info?access_token=' +
        component_access_token,
      data
    )
    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取授权账号详情响应结果', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 新建草稿
   * @param access_token
   * @param articles
   * @returns
   */
  async addWxDraft(access_token: string, wxArticles: WxArticle[]) {
    const data = {
      articles: wxArticles.map((item) => ({
        title: item.title,
        author: item.author,
        digest: item.digest,
        content: item.content,
        content_source_url: item.contentSourceUrl,
        thumb_media_id: item.thumbUrl,
        need_open_comment: item.needOpenComment,
        only_fans_can_comment: item.onlyFansCanComment,
        pic_crop_235_1: item.picCrop235,
        pic_crop_1_1: item.picCrop1
      }))
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/draft/add?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '草稿箱响应结果', { error: JSON.stringify(resp.data) })
    }

    return resp.data
  }

  /**
   * 正式发布
   * @param access_token
   * @param mediaId 草稿箱ID
   * @returns
   */
  async publishSubmit(access_token: string, mediaId: string) {
    const data = {
      media_id: mediaId
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/freepublish/submit?access_token=' + access_token,
      data
    )

    await this.loggerService.info(null, '正式发布响应结果', { data: JSON.stringify(resp.data) })
    return resp.data
  }

  /**
   * 群发
   * @param access_token
   * @param mediaId
   * @param send_ignore_reprint //文章被判定为转载时，且原创文允许转载时，将继续进行群发操作
   * @returns
   */
  async sendAll(access_token: string, mediaId: string, send_ignore_reprint: number = 0) {
    const data = {
      filter: {
        is_to_all: true
      },
      mpnews: {
        media_id: mediaId
      },
      msgtype: 'mpnews',
      send_ignore_reprint: send_ignore_reprint
      // clientmsgid: Date.now()
    }
    // 正式群发
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/message/mass/sendall?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '微信群发响应结果', { error: JSON.stringify(resp.data) })
    }

    return resp.data
  }

  /**
   * 预览接口
   * @param access_token
   * @param mediaId
   * @param send_ignore_reprint
   * @returns
   */
  async publishPreview(access_token: string, mediaId: string, towxname: string) {
    const data = {
      towxname: towxname,
      mpnews: {
        media_id: mediaId
      },
      msgtype: 'mpnews'
    }
    //群发预览接口 发给指定用户 用于测试
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/message/mass/preview?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '预览接口响应结果', { error: JSON.stringify(resp.data) })
    }

    return resp.data
  }

  /**
   * 上传图文消息内的图片获取URL
   * @param access_token
   * @param image
   * @param filename
   * @param filelength
   * @param fileType
   * @returns
   */
  async postUploadimg(
    access_token: string,
    image: Buffer,
    filename: string,
    filelength: number,
    fileType: string
  ) {
    const formData = new FormData()
    formData.append('media', image, {
      filename,
      knownLength: filelength,
      contentType: fileType
    })
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/media/uploadimg?access_token=' + access_token,
      formData,
      {
        headers: {
          'Content-Type': `multipart/form-data`
        }
      }
    )
    return resp.data
  }

  /**
   * 上传到微信素材库
   * @param access_token
   * @param image
   * @param filename
   * @param filelength
   * @param fileType
   * @param type
   * @param title
   * @param introduction
   * @returns
   */
  async postAddmaterial(
    access_token: string,
    image: Buffer,
    filename: string,
    filelength: number,
    fileType: string,
    type: WxMediaType,
    title?: string,
    introduction?: string
  ) {
    const formData = new FormData()
    formData.append('media', image, {
      filename,
      knownLength: filelength,
      contentType: fileType
    })
    formData.append(
      'description',
      JSON.stringify({
        title: title ?? 'title',
        introduction: introduction ?? 'introduction'
      })
    )
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=' +
        access_token +
        '&type' +
        type,
      formData,
      {
        headers: {
          'Content-Type': `multipart/form-data`
        }
      }
    )
    if (resp.data?.errcode) {
      console.log('错误信息', resp.data)
      await this.loggerService.error(null, '微信素材库响应结果', {
        error: JSON.stringify(resp.data)
      })
    }

    return resp.data
  }

  /**
   * 获取发布状态
   * @param access_token
   * @param publish_id
   * @returns
   */
  async freePublishStatus(access_token: string, publish_id: string) {
    const data = {
      publish_id: publish_id
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/freepublish/get?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '发布状态轮询接口响应结果', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 删除发布文章
   * @param access_token
   * @param publish_id
   * @returns
   */
  async deletePublishs(access_token: string, article_id: string, index?: number) {
    const data = {
      article_id: article_id
    }
    if (index > 0) {
      data['index'] = index
    }
    const resp = await axios.post(
      'https://api.weixin.qq.com/cgi-bin/freepublish/delete?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '删除发布文章接口响应结果', {
        error: JSON.stringify(resp.data)
      })
    }
    return resp.data
  }

  /**
   * 获取用户增减数据
   * @param access_token 访问令牌
   * @param begin_date 开始日期，格式为YYYY-MM-DD
   * @param end_date 结束日期，格式为YYYY-MM-DD
   * @returns
   */
  async getUserAnalysis(access_token: string, begin_date: string, end_date: string) {
    const data = {
      begin_date: begin_date,
      end_date: end_date
    }

    const resp = await axios.post(
      'https://api.weixin.qq.com/datacube/getusersummary?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取用户增减数据响应结果', {
        error: JSON.stringify(resp.data)
      })
    }

    return resp.data
  }

  /**
   * 获取用户累计数据
   * @param access_token 访问令牌
   * @param begin_date 开始日期，格式为YYYY-MM-DD
   * @param end_date 结束日期，格式为YYYY-MM-DD
   * @returns
   */
  async getUserCumulate(access_token: string, begin_date: string, end_date: string) {
    const data = {
      begin_date: begin_date,
      end_date: end_date
    }

    const resp = await axios.post(
      'https://api.weixin.qq.com/datacube/getusercumulate?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取用户累计数据响应结果', {
        error: JSON.stringify(resp.data)
      })
    }

    return resp.data
  }

  /**
   * 获取图文群发总数据
   * @param access_token 访问令牌
   * @param begin_date 开始日期，格式为YYYY-MM-DD
   * @param end_date 结束日期，格式为YYYY-MM-DD
   * @returns
   */
  async getArticleTotal(access_token: string, begin_date: string, end_date: string) {
    const data = {
      begin_date: begin_date,
      end_date: end_date
    }

    const resp = await axios.post(
      'https://api.weixin.qq.com/datacube/getarticletotal?access_token=' + access_token,
      data
    )

    if (resp.data?.errcode) {
      await this.loggerService.error(null, '获取图文总数据响应结果', {
        error: JSON.stringify(resp.data)
      })
    }

    return resp.data
  }

}
