import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { QuickEntrance, QuickEntrancesCreateRequest } from './quick-entrance.dto'
import { Connection, Model, Types } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { QuickEntranceEntity } from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'

@Injectable()
export class QuickEntranceService {
  logger = new Logger('QuickEntranceService')

  constructor(
    @InjectModel(QuickEntranceEntity.name) private quickEntranceModel: Model<QuickEntranceEntity>,
    @InjectConnection() private readonly connection: Connection,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 快捷入口列表
   * @returns
   */
  async getQuickEntrances(): Promise<QuickEntrance[]> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const result = await this.quickEntranceModel.find({
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    return result.map((item) => ({
      id: item.id,
      quickArgs: item.quickArgs
    }))
  }

  /**
   * 创建快捷方式
   * @param body
   * @returns
   */
  async createQuickEntrance(body: QuickEntrancesCreateRequest[]) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    if (body.length <= 0) {
      throw new ForbiddenException('快捷入口设置不能为空')
    }
    let quickEntrances: (QuickEntranceEntity & { _id: Types.ObjectId; __v?: Number })[] = null

    try {
      await this.connection.transaction(
        async (session) => {
          // 删除老的快捷入口
          await this.quickEntranceModel.deleteMany(
            {
              userId: new Types.ObjectId(currentUserId),
              teamId: new Types.ObjectId(currentTeamId)
            },
            { session, ordered: true }
          )

          // 新增快捷入口
          const quickEntranceEntities: QuickEntranceEntity[] = body.map((request) => {
            return {
              teamId: new Types.ObjectId(currentTeamId),
              userId: new Types.ObjectId(currentUserId),
              quickArgs: request.quickArgs
            }
          })
          if (quickEntranceEntities.length > 0) {
            quickEntrances = await this.quickEntranceModel.create(quickEntranceEntities, {
              session,
              ordered: true
            })
          }
        },
        { maxTimeMS: 1000 * 10 }
      )
      return quickEntrances.map((item) => ({
        id: item._id,
        quickArgs: item.quickArgs
      }))
    } catch (error) {
      this.logger.error(`Transaction failed: ${error.message}`, error.stack)
      throw new HttpException('创建快捷入口失败, 请稍后再试', -1)
    }
  }

  /**
   * 删除快捷入口
   * @param quickId
   */
  async deleteQuickEntrance(quickId: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const quickEntrance = await this.quickEntranceModel.findOne({
      _id: new Types.ObjectId(quickId),
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!quickEntrance) {
      throw new NotFoundException('该快捷方式未找到')
    }

    await this.quickEntranceModel.deleteOne({
      _id: quickEntrance.id
    })
  }
}
