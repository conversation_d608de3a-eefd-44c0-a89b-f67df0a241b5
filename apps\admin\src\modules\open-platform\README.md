# 开放平台模块

## 概述

本模块实现了完整的开放平台功能，包括：
1. **统一认证机制**：开放平台用户与现有admin系统的统一认证
2. **应用管理**：应用的创建、管理和授权
3. **用户邀请**：渠道商邀请机制
4. **应用充值**：虚拟币充值和余额管理系统

## 功能模块

### 1. 统一认证机制
- **向后兼容性**：现有admin接口无需修改即可正常工作
- **统一Token机制**：开放平台用户和admin用户使用相同的Token验证流程
- **用户类型区分**：通过Redis缓存信息区分用户类型
- **权限隔离**：两套用户系统的权限控制相互独立且不冲突

### 2. 应用充值系统
- **充值类型**：支持对公转账和赠送两种充值方式
- **余额管理**：实时跟踪应用余额、充值和消费记录
- **状态管理**：完整的充值状态流转机制
- **事务保证**：确保充值和余额更新的数据一致性

## 核心设计

### 1. 统一认证守卫 (UnifiedTokenGuard)

基于现有的 `AuthGuard.ts` 实现，保持完全向后兼容：

```typescript
// 第一步：保持原有admin认证方式（完全兼容）
let user = await this.cacheManager.get<AdminEntity>(authorization)

// 第二步：扩展开放平台用户认证（新增功能）
const openPlatformUser = await this.cacheManager.get<OpenPlatformUserEntity>(`open_platform:${authorization}`)

// 第三步：开发环境模拟认证（保持原有逻辑）
if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev') {
  // 模拟认证逻辑
}
```

## 应用充值系统

### 数据模型

#### 1. 充值记录表 (ApplicationRecharge)
- `applicationId`: 应用ID
- `rechargeOrderNo`: 充值订单号（唯一）
- `rechargeType`: 充值类型（BANK_TRANSFER | GIFT）
- `rechargeAmount`: 充值金额（单位：分）
- `virtualCoinAmount`: 虚拟币金额（与充值金额1:1）
- `paymentAmount`: 实际到账金额（单位：分）
- `rechargeStatus`: 充值状态（PENDING | SUCCESS | FAILED | CANCELLED）
- `rechargeTime`: 充值时间
- `remark`: 备注信息
- `operatorId`: 操作员ID

#### 2. 应用余额表 (ApplicationBalance)
- `applicationId`: 应用ID（唯一）
- `totalBalance`: 总余额（单位：分）
- `frozenBalance`: 冻结余额
- `availableBalance`: 可用余额
- `totalRecharge`: 累计充值金额
- `totalConsumption`: 累计消费金额
- `lastRechargeTime`: 最后充值时间

### API接口

#### 1. 创建充值订单
```http
POST /open-platform/recharge
Content-Type: application/json
Authorization: Bearer {token}

{
  "applicationId": "507f1f77bcf86cd799439011",
  "rechargeType": "BANK_TRANSFER",
  "rechargeAmount": 10000,
  "remark": "应用充值"
}
```

#### 2. 更新充值状态
```http
PUT /open-platform/recharge/{rechargeOrderNo}/status
Content-Type: application/json
Authorization: Bearer {token}

{
  "rechargeStatus": "SUCCESS",
  "paymentAmount": 10000,
  "remark": "充值成功"
}
```

#### 3. 查询充值记录
```http
GET /open-platform/recharge?applicationId={id}&page=1&size=10
Authorization: Bearer {token}
```

#### 4. 查询应用余额
```http
GET /open-platform/applications/{applicationId}/balance
Authorization: Bearer {token}
```

### 业务规则

1. **充值类型**：
   - `BANK_TRANSFER`：对公转账，需要管理员审核确认
   - `GIFT`：赠送，由管理员直接操作，立即到账

2. **虚拟币兑换**：
   - 虚拟币与实际支付金额保持1:1关系（1元 = 100虚拟币分）

3. **状态流转**：
   - `PENDING` → `SUCCESS` / `FAILED` / `CANCELLED`
   - 已成功或已取消的充值不能再次修改状态

4. **余额更新**：
   - 充值成功后自动更新应用余额
   - 使用MongoDB事务确保数据一致性

### 权限控制

- **Admin用户**：可以创建充值订单、更新充值状态、查看所有记录
- **开放平台用户**：可以查看自己应用的充值记录和余额（权限待完善）

## 统一认证机制

### 功能特性

1. **蚁小二验证码集成**：
   - 发送验证码前需要通过人机验证
   - 使用阿里云智能验证码服务
   - 防止恶意刷验证码

2. **统一登录/注册流程**：
   - 自动判断用户是否存在
   - 用户不存在时自动注册
   - 用户已存在时执行登录
   - 统一返回格式

### API接口

#### 1. 发送验证码（需要人机验证）
```http
POST /open-platform/auth/send-code
Content-Type: application/json

{
  "phone": "13800138000",
  "captchaVerifyParam": "验证码验证参数"
}
```

#### 2. 统一认证（推荐使用）
```http
POST /open-platform/auth/auth
Content-Type: application/json

{
  "phone": "13800138000",
  "code": "123456",
  "password": "password123",  // 可选
  "nickname": "张三"          // 可选，新用户注册时使用
}
```

#### 3. 传统登录接口（保持兼容）
```http
POST /open-platform/auth/login
POST /open-platform/auth/register
```

### 业务逻辑

1. **验证码发送**：
   - 首先验证蚁小二人机验证码
   - 验证通过后生成6位数字验证码
   - 缓存验证码（开发环境直接返回）

2. **统一认证流程**：
   ```
   验证短信验证码 → 查找用户
   ↓
   用户存在？
   ├─ 是：执行登录逻辑，可选更新密码
   └─ 否：执行注册逻辑，创建新用户
   ↓
   生成统一Token → 返回认证结果
   ```

3. **Token生成**：
   - 使用UnifiedAuthService生成统一格式Token
   - 支持UnifiedTokenGuard验证
   - 与admin系统认证机制一致

### 使用示例

```typescript
// 1. 发送验证码（前端需要先完成人机验证）
const sendCodeResult = await authService.sendVerificationCode({
  phone: '13800138000',
  captchaVerifyParam: 'captcha_param_from_frontend'
});

// 2. 统一认证（自动判断登录/注册）
const authResult = await authService.unifiedAuth({
  phone: '13800138000',
  code: '123456',
  password: 'password123',  // 可选
  nickname: '张三'          // 可选
});

// 3. 充值功能示例
const rechargeRecord = await rechargeService.createRecharge({
  applicationId: '507f1f77bcf86cd799439011',
  rechargeType: RechargeType.GIFT,
  rechargeAmount: 10000,
  remark: '新用户赠送'
}, adminUserId);

// 查询应用余额
const balance = await rechargeService.getApplicationBalance('507f1f77bcf86cd799439011');
console.log(`可用余额: ${balance.availableBalance / 100}元`);
```

### 环境配置

需要在环境变量中配置阿里云验证码服务：

```env
CAPTCHA_ACCESS_KEY_ID=your_access_key_id
CAPTCHA_ACCESS_KEY_SECRET=your_access_key_secret
JWT_SECRET=your_jwt_secret_for_application_tokens
```

## 应用级API访问控制系统

### 功能概述

实现了基于应用Token的API访问控制，允许第三方应用通过appId/secretKey生成JWT Token来管理Gateway用户和团队，确保数据完全隔离。

### 核心特性

1. **应用Token认证**：
   - 基于JWT的应用Token生成和验证
   - Token有效期24小时，支持刷新机制
   - 集成到UnifiedTokenGuard统一认证体系

2. **数据隔离机制**：
   - 用户表和团队表扩展source和sourceAppId字段
   - 应用Token只能访问自己创建的数据
   - 自动在查询条件中添加sourceAppId过滤

3. **权限控制装饰器**：
   - `@ApplicationAccess()` - 应用Token访问
   - `@AdminOnly()` - 仅管理员访问
   - `@GatewayOnly()` - 仅Gateway原生用户访问
   - `@AllAuthenticatedAccess()` - 所有认证用户访问

### API接口

#### 1. 应用Token管理

```http
# 生成应用Token
POST /open-platform/applications/token
{
  "appId": "app_1234567890abcdef",
  "secretKey": "sk_1234567890abcdef1234567890abcdef"
}

# 刷新Token
POST /open-platform/applications/token/refresh
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

# 验证Token
POST /open-platform/applications/token/validate
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 2. Gateway用户管理（需要应用Token）

```http
# 创建用户
POST /gateway/users
Authorization: Bearer {application_token}
{
  "phone": "13800138000",
  "nickName": "张三",
  "latestTeamId": "507f1f77bcf86cd799439011"
}

# 查询用户列表
GET /gateway/users?page=1&size=10
Authorization: Bearer {application_token}

# 获取用户详情
GET /gateway/users/{userId}
Authorization: Bearer {application_token}

# 更新用户
PUT /gateway/users/{userId}
Authorization: Bearer {application_token}
{
  "nickName": "李四"
}

# 删除用户
DELETE /gateway/users/{userId}
Authorization: Bearer {application_token}
```

### 数据模型扩展

#### 用户表扩展字段
```typescript
{
  source: 'gateway' | 'open_platform_app',     // 用户来源
  sourceAppId?: string,                        // 创建用户的应用appId
  openPlatformUserId?: Types.ObjectId         // 关联的开放平台用户ID
}
```

#### 团队表扩展字段
```typescript
{
  source: 'gateway' | 'open_platform_app',     // 团队来源
  sourceAppId?: string,                        // 创建团队的应用appId
  openPlatformUserId?: Types.ObjectId         // 创建者的开放平台用户ID
}
```

### 使用示例

```typescript
// 1. 生成应用Token
const tokenResponse = await fetch('/open-platform/applications/token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    appId: 'app_1234567890abcdef',
    secretKey: 'sk_1234567890abcdef1234567890abcdef'
  })
});
const { accessToken } = await tokenResponse.json();

// 2. 使用应用Token创建用户
const userResponse = await fetch('/gateway/users', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${accessToken}`
  },
  body: JSON.stringify({
    phone: '13800138000',
    nickName: '张三',
    latestTeamId: '507f1f77bcf86cd799439011'
  })
});

// 3. 查询应用创建的用户
const usersResponse = await fetch('/gateway/users', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

### 安全特性

1. **完全数据隔离**：应用间数据完全隔离，通过sourceAppId强制过滤
2. **权限边界清晰**：应用Token不能访问admin功能和其他应用数据
3. **审计日志完整**：记录所有应用Token的操作行为
4. **敏感信息保护**：应用不能访问用户密码等敏感字段
```

### 2. 缓存格式设计

**Admin用户（保持原有格式）：**
```
Redis Key: ${authorization}
Redis Value: AdminEntity对象
```

**开放平台用户（新增格式）：**
```
Redis Key: open_platform:${authorization}
Redis Value: OpenPlatformUserEntity对象
```

### 3. Request对象扩展

保持原有属性的同时，扩展统一的会话信息：

```typescript
interface FastifyRequest {
  user: AdminEntity | OpenPlatformUserEntity  // 保持原有
  authorization: string                        // 保持原有
  
  // 新增：统一的会话信息
  session?: {
    userId: string
    userType: 'admin' | 'open-platform'
    teamId?: string
    applicationId?: string
  }
}
```

## 使用方式

### 1. 开放平台用户注册/登录

```bash
# 发送验证码
POST /open-platform/auth/send-code
{
  "phone": "13800138000"
}

# 用户注册
POST /open-platform/auth/register
{
  "phone": "13800138000",
  "code": "123456",
  "nickname": "张三",
  "password": "password123"  // 可选
}

# 用户登录
POST /open-platform/auth/login
{
  "phone": "13800138000",
  "code": "123456"  // 或使用password字段
}
```

### 2. 访问现有接口

开放平台用户登录后，可以使用返回的authorization token访问现有的admin接口：

```bash
# 访问现有用户列表接口
GET /users
Authorization: ${token}

# 访问现有团队列表接口  
GET /teams
Authorization: ${token}
```

### 3. 兼容性测试接口

```bash
# 获取当前认证信息
GET /open-platform/compatibility/auth-info
Authorization: ${token}

# 测试Admin用户兼容性
GET /open-platform/compatibility/admin-compatibility
Authorization: ${admin_token}

# 测试开放平台用户兼容性
GET /open-platform/compatibility/open-platform-compatibility
Authorization: ${open_platform_token}

# 测试访问现有服务
GET /open-platform/compatibility/existing-service-access
Authorization: ${token}
```

## 技术实现细节

### 1. 向后兼容策略

- **保留原有认证逻辑**：`await this.cacheManager.get<AdminEntity>(authorization)`
- **保持原有Request设置**：`request.user = user; request.authorization = authorization`
- **扩展而非替换**：在原有基础上添加新功能，不修改现有逻辑

### 2. 用户类型识别

通过 `request.session.userType` 区分用户类型：
- `UserType.ADMIN`：admin系统用户
- `UserType.OPEN_PLATFORM`：开放平台用户

### 3. 数据隔离

- **认证隔离**：使用不同的Redis缓存key前缀
- **权限隔离**：基于用户类型实现不同的权限控制
- **数据隔离**：开放平台用户只能访问被授权的数据

### 4. 开发环境支持

保持原有的模拟认证机制，同时扩展支持开放平台用户：
```typescript
// Admin用户模拟（保持原有）
const adminUser = await this.adminModel.findById(authorization)

// 开放平台用户模拟（新增）
const openPlatformUser = await this.openPlatformUserModel.findById(authorization)
```

## 部署和测试

### 1. 数据库迁移

确保以下Schema已创建：
- `open_platform_users`
- `open_platform_applications`
- `open_platform_user_roles`
- `open_platform_app_authorizations`

### 2. 测试流程

1. **Admin用户兼容性测试**：使用现有admin账号登录，验证所有功能正常
2. **开放平台用户测试**：注册开放平台账号，验证可以访问admin接口
3. **混合场景测试**：同时使用两种用户类型，验证互不干扰

### 3. 监控和日志

- 统一认证守卫会记录用户类型识别日志
- 开发环境会记录模拟认证日志
- 可通过兼容性测试接口监控认证状态

## API接口文档

### 认证接口
- `POST /open-platform/auth/send-code` - 发送验证码
- `POST /open-platform/auth/register` - 用户注册
- `POST /open-platform/auth/login` - 用户登录

### 应用管理接口
- `GET /open-platform/applications` - 获取应用列表
- `POST /open-platform/applications` - 创建应用（仅管理员）
- `GET /open-platform/applications/:id` - 获取应用详情
- `PUT /open-platform/applications/:id` - 更新应用（仅管理员）
- `DELETE /open-platform/applications/:id` - 删除应用（仅管理员）
- `POST /open-platform/applications/:id/regenerate-secret` - 重新生成密钥（仅管理员）

### 邀请管理接口
- `GET /open-platform/invitations` - 获取邀请列表
- `POST /open-platform/invitations` - 发送邀请（仅管理员）
- `GET /open-platform/invitations/:id` - 获取邀请详情
- `PUT /open-platform/invitations/:id/handle` - 处理邀请（接受/拒绝）
- `DELETE /open-platform/invitations/:id` - 撤销邀请（仅邀请人）

### 授权管理接口
- `GET /open-platform/authorizations` - 获取授权列表（仅管理员）
- `POST /open-platform/authorizations` - 创建授权（仅管理员）
- `GET /open-platform/authorizations/:id` - 获取授权详情（仅管理员）
- `PUT /open-platform/authorizations/:id` - 更新授权（仅管理员）
- `DELETE /open-platform/authorizations/:id` - 删除授权（仅管理员）
- `GET /open-platform/authorizations/applications/:applicationId/channel-users` - 获取可授权用户列表（仅管理员）

### 测试接口
- `GET /open-platform/test/user-info` - 获取当前用户信息
- `GET /open-platform/test/access-test` - 测试访问权限
- `GET /open-platform/compatibility/auth-info` - 获取认证信息
- `GET /open-platform/compatibility/admin-compatibility` - 测试Admin兼容性
- `GET /open-platform/compatibility/open-platform-compatibility` - 测试开放平台兼容性

## 权限控制矩阵

| 功能 | 管理员 | 渠道商 |
|------|--------|--------|
| 创建应用 | ✅ | ❌ |
| 删除应用 | ✅ | ❌ |
| 修改应用 | ✅ | ❌ |
| 重新生成密钥 | ✅ | ❌ |
| 查看应用列表 | ✅（全部） | ✅（已授权） |
| 查看应用详情 | ✅（完整信息） | ✅（基本信息） |
| 发送邀请 | ✅ | ❌ |
| 处理邀请 | ✅（被邀请时） | ✅（被邀请时） |
| 撤销邀请 | ✅（自己发送的） | ❌ |
| 创建授权 | ✅ | ❌ |
| 管理授权 | ✅ | ❌ |
| 生成Token | ✅ | ✅（已授权应用） |

## 注意事项

1. **平滑迁移**：现有admin功能无需任何修改即可正常工作
2. **性能影响**：新增的认证逻辑对性能影响极小
3. **安全性**：两套用户系统的权限完全隔离
4. **扩展性**：可以轻松添加更多用户类型和认证方式
5. **数据隔离**：渠道商只能访问被授权的应用和数据
6. **角色管理**：基于角色的权限控制确保操作安全性
