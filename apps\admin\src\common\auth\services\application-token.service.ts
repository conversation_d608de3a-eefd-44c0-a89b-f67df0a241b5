import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { JwtService } from '@nestjs/jwt'
import { ConfigService } from '@nestjs/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { OpenPlatformApplicationEntity } from '@yxr/mongo'
import { OpenPlatformStatus, UserType } from '@yxr/common'
import { RootConfigMap } from '@yxr/config'
import { ApplicationTokenPayload } from '../interfaces/auth.interface'

/**
 * 应用Token服务 - 移到认证模块中以减少依赖
 *
 * 新增功能：
 * 1. 单token限制：每个应用同时只能存在一个有效的token
 * 2. token失效机制：新token生成时旧token失效
 * 3. 缓冲期机制：旧token保留3-5分钟有效期，避免并发请求中断
 */
@Injectable()
export class ApplicationTokenService {
  private readonly logger = new Logger(ApplicationTokenService.name)
  private readonly TOKEN_EXPIRES_IN = 7 * 24 * 60 * 60 // 7 * 24小时（秒）
  private readonly BUFFER_PERIOD = 5 * 60 // 缓冲期：5分钟（秒）

  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    private jwtService: JwtService,
    private configService: ConfigService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  /**
   * 获取应用当前token的缓存key
   */
  private getApplicationTokenCacheKey(appId: string): string {
    return `app_token:current:${appId}`
  }

  /**
   * 获取应用旧token的缓存key（缓冲期使用）
   */
  private getApplicationOldTokenCacheKey(appId: string): string {
    return `app_token:old:${appId}`
  }

  /**
   * 获取token详情的缓存key
   */
  private getTokenDetailsCacheKey(tokenHash: string): string {
    return `app_token:details:${tokenHash}`
  }

  /**
   * 生成token的哈希值（用于缓存key）
   */
  private generateTokenHash(token: string): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(token).digest('hex').substring(0, 16)
  }

  /**
   * 验证应用Token（支持缓冲期机制）
   */
  async verifyToken(token: string): Promise<ApplicationTokenPayload | null> {
    try {
      const payload = this.jwtService.verify<ApplicationTokenPayload>(token, {
        secret: this.getJwtSecret()
      })

      // 验证Token类型
      if (payload.userType !== UserType.APPLICATION) {
        return null
      }

      // 验证应用是否仍然有效
      const application = await this.applicationModel.findOne({
        appId: payload.appId,
        _id: payload.applicationId
      })

      if (!application || application.status !== OpenPlatformStatus.ACTIVE) {
        this.logger.warn(`应用Token验证失败: 应用不存在或已禁用, appId=${payload.appId}`)
        return null
      }

      // 检查token是否为当前有效token或缓冲期内的旧token
      const isValidToken = await this.isTokenValid(token, payload.appId)
      if (!isValidToken) {
        this.logger.warn(`应用Token验证失败: Token已失效, appId=${payload.appId}`)
        return null
      }

      return payload
    } catch (error) {
      this.logger.warn(`应用Token验证失败: ${error.message}`)
      return null
    }
  }

  /**
   * 检查token是否有效（当前token或缓冲期内的旧token）
   */
  private async isTokenValid(token: string, appId: string): Promise<boolean> {
    const tokenHash = this.generateTokenHash(token)

    // 检查是否为当前有效token
    const currentTokenHash = await this.cacheManager.get<string>(
      this.getApplicationTokenCacheKey(appId)
    )
    if (currentTokenHash === tokenHash) {
      return true
    }

    // 检查是否为缓冲期内的旧token
    const oldTokenHash = await this.cacheManager.get<string>(
      this.getApplicationOldTokenCacheKey(appId)
    )
    if (oldTokenHash === tokenHash) {
      return true
    }

    return false
  }

  /**
   * 处理旧token（移到缓冲期）
   */
  private async handleOldToken(appId: string): Promise<void> {
    const currentTokenHash = await this.cacheManager.get<string>(
      this.getApplicationTokenCacheKey(appId)
    )

    if (currentTokenHash) {
      // 将当前token移到缓冲期
      await Promise.all([
        this.cacheManager.set(
          this.getApplicationOldTokenCacheKey(appId),
          currentTokenHash,
          this.BUFFER_PERIOD * 1000 // 转换为毫秒
        ),
        this.cacheManager.del(this.getApplicationTokenCacheKey(appId))
      ])

      this.logger.log(`应用旧Token移入缓冲期: appId=${appId}, bufferPeriod=${this.BUFFER_PERIOD}s`)
    }
  }

  /**
   * 缓存新token
   */
  private async cacheNewToken(
    appId: string,
    token: string,
    payload: ApplicationTokenPayload
  ): Promise<void> {
    const tokenHash = this.generateTokenHash(token)

    await Promise.all([
      // 缓存当前token哈希
      this.cacheManager.set(
        this.getApplicationTokenCacheKey(appId),
        tokenHash,
        this.TOKEN_EXPIRES_IN * 1000 // 转换为毫秒
      ),
      // 缓存token详情
      this.cacheManager.set(
        this.getTokenDetailsCacheKey(tokenHash),
        payload,
        this.TOKEN_EXPIRES_IN * 1000 // 转换为毫秒
      )
    ])
  }

  /**
   * 生成应用Token（通过DTO）
   */
  async generateToken(generateTokenDto: { appId: string; secretKey: string }): Promise<{
    accessToken: string
    tokenType: 'Bearer'
    expiresIn: number
    expiresAt: number
  }> {
    return this.generateTokenByCredentials(generateTokenDto.appId, generateTokenDto.secretKey)
  }

  /**
   * 生成应用Token（通过凭据）- 实现单token限制和缓冲期机制
   */
  async generateTokenByCredentials(
    appId: string,
    secretKey: string
  ): Promise<{
    accessToken: string
    tokenType: 'Bearer'
    expiresIn: number
    expiresAt: number
  }> {
    // 查找应用
    const application = await this.applicationModel.findOne({ appId })
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 验证应用状态
    if (application.status !== OpenPlatformStatus.ACTIVE) {
      throw new ForbiddenException('应用已被禁用')
    }

    // 验证secretKey
    if (application.secretKey !== secretKey) {
      throw new BadRequestException('应用密钥错误')
    }

    // 处理旧token（移到缓冲期）
    await this.handleOldToken(appId)

    // 生成JWT Token
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = now + this.TOKEN_EXPIRES_IN

    const payload: ApplicationTokenPayload = {
      userId: application.userId.toString(),
      appId: application.appId,
      applicationId: application._id.toString(),
      userType: UserType.APPLICATION,
      expiresAt
    }

    const accessToken = this.jwtService.sign(payload, {
      secret: this.getJwtSecret(),
      expiresIn: this.TOKEN_EXPIRES_IN
    })

    // 缓存新token
    await this.cacheNewToken(appId, accessToken, payload)

    this.logger.log(`应用Token生成成功: appId=${appId}, applicationId=${application._id}`)

    return {
      accessToken,
      tokenType: 'Bearer',
      expiresIn: this.TOKEN_EXPIRES_IN,
      expiresAt: expiresAt * 1000 // 转换为毫秒
    }
  }

  /**
   * 刷新Token - 实现单token限制和缓冲期机制
   */
  async refreshToken(oldToken: string): Promise<{
    accessToken: string
    tokenType: 'Bearer'
    expiresIn: number
    expiresAt: number
  }> {
    const payload = await this.verifyToken(oldToken)
    if (!payload) {
      throw new BadRequestException('无效的Token')
    }

    // 检查Token是否即将过期（剩余时间少于1小时）
    const now = Math.floor(Date.now() / 1000)
    const timeLeft = payload.expiresAt - now

    if (timeLeft > 60 * 60) {
      // 剩余时间超过1小时
      throw new BadRequestException('Token尚未到刷新时间')
    }

    // 重新生成Token（这会自动处理旧token的缓冲期机制）
    const application = await this.applicationModel.findById(payload.applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    this.logger.log(`应用Token刷新: appId=${payload.appId}, applicationId=${payload.applicationId}`)

    return await this.generateTokenByCredentials(application.appId, application.secretKey)
  }

  /**
   * 通过应用ID生成Token（管理员功能）
   */
  async generateTokenByApplicationId(applicationId: string): Promise<{
    accessToken: string
    tokenType: 'Bearer'
    expiresIn: number
    expiresAt: number
  }> {
    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    if (application.status !== OpenPlatformStatus.ACTIVE) {
      throw new ForbiddenException('应用已被禁用')
    }

    return await this.generateTokenByCredentials(application.appId, application.secretKey)
  }

  /**
   * 撤销应用的所有Token - 实现真正的token撤销
   */
  async revokeAllTokens(applicationId: string): Promise<void> {
    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 清理Redis中的token缓存
    await this.clearApplicationTokenCache(application.appId)

    this.logger.log(`撤销应用所有Token: applicationId=${applicationId}, appId=${application.appId}`)
  }

  /**
   * 清理应用的token缓存
   */
  private async clearApplicationTokenCache(appId: string): Promise<void> {
    try {
      // 获取当前token和旧token的哈希值
      const [currentTokenHash, oldTokenHash] = await Promise.all([
        this.cacheManager.get<string>(this.getApplicationTokenCacheKey(appId)),
        this.cacheManager.get<string>(this.getApplicationOldTokenCacheKey(appId))
      ])

      // 构建需要删除的缓存key列表
      const keysToDelete = [
        this.getApplicationTokenCacheKey(appId),
        this.getApplicationOldTokenCacheKey(appId)
      ]

      if (currentTokenHash) {
        keysToDelete.push(this.getTokenDetailsCacheKey(currentTokenHash))
      }
      if (oldTokenHash) {
        keysToDelete.push(this.getTokenDetailsCacheKey(oldTokenHash))
      }

      // 批量删除缓存
      await Promise.all(keysToDelete.map((key) => this.cacheManager.del(key)))

      this.logger.log(`清理应用Token缓存完成: appId=${appId}, deletedKeys=${keysToDelete.length}`)
    } catch (error) {
      this.logger.error(`清理应用Token缓存失败: appId=${appId}, error=${error.message}`)
    }
  }

  /**
   * 获取JWT密钥
   */
  private getJwtSecret(): string {
    const config = this.configService.get<RootConfigMap['app']>('app')
    return config?.jwtSecret || process.env.JWT_SECRET || 'default-secret'
  }
}
