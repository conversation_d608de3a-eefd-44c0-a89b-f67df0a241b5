import { Controller, Get, Param, Inject, Query } from '@nestjs/common'
import {
  ApiForbiddenResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
  ApiBadRequestResponse,
  ApiTags,
  ApiOkResponse,
  ApiHeader,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger'
import { TeamBucketNamesEnum, TeamRoleNames } from '@yxr/common'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'

import { StorageService } from './storage.service'
import { AuthorizationService } from '../../common/security/authorization.service'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import { getOssResourceUrlResponseDTO, getOssSignatureResponseDTO } from './storage.dto'
import { TianyiyunOssService } from '@yxr/common'

@Controller('storages')
@ApiTags('存储管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class StorageController {
  constructor(
    private readonly storageService: StorageService,
    private readonly tianyiyunOssService: TianyiyunOssService,
    private readonly authorizationService: AuthorizationService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Get(':bucket/upload-url')
  @ApiOperation({ summary: '获取资源直传地址' })
  @ApiOkResponse({ type: getOssSignatureResponseDTO, description: '操作成功' })
  @ApiParam({
    name: 'bucket',
    required: true,
    description: '团队资源存储桶名',
    enum: TeamBucketNamesEnum
  })
  @ApiQuery({
    name: 'fileKey',
    required: false,
    description: '文件存储名, 默认随机生成',
    example: 'foo/bar/avatar.jpg'
  })
  async getOssUploadUrl(
    @Param('bucket') bucket: TeamBucketNamesEnum,
    @Query('fileKey') fileKey: string
  ) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, TeamRoleNames.All)
    if (
      bucket === TeamBucketNamesEnum.CloudPublish ||
      bucket === TeamBucketNamesEnum.MaterialLibrary ||
      bucket === TeamBucketNamesEnum.WechatPublish
    ) {
      return this.tianyiyunOssService.getOssUploadUrl(currentTeamId, bucket, fileKey)
    } else {
      return this.storageService.getOssUploadUrl({
        teamId: currentTeamId,
        bucket,
        fileKey
      })
    }
  }

  @Get(':bucket/access-url')
  @ApiOperation({ summary: '获取资源访问地址' })
  @ApiOkResponse({ type: getOssResourceUrlResponseDTO, description: '操作成功' })
  @ApiQuery({ name: 'key', required: true, description: '资源存储KEY', enum: TeamBucketNamesEnum })
  async getOssAccessUrl(
    @Param('bucket') bucket: TeamBucketNamesEnum,
    @Query('fileKey') fileKey: string
  ) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const client = this.request.client?.name
    let internalNetwork = true //默认走内网
    if (client !== 'cloud') {
      internalNetwork = false
    }
    if (
      bucket === TeamBucketNamesEnum.CloudPublish ||
      bucket === TeamBucketNamesEnum.MaterialLibrary ||
      bucket === TeamBucketNamesEnum.WechatPublish
    ) {
      return this.tianyiyunOssService.getOssAccessUrl(
        // teamId: currentTeamId,
        // bucket,
        fileKey,
        internalNetwork
      )
    } else {
      await this.authorizationService.checkRoleNames(
        currentTeamId,
        currentUserId,
        TeamRoleNames.All
      )
      return this.storageService.getOssAccessUrl({ teamId: currentTeamId, bucket, fileKey })
    }
  }

  @Get(':bucket/head-url')
  @ApiOperation({ summary: '获取资源访问地址' })
  @ApiOkResponse({ type: getOssResourceUrlResponseDTO, description: '操作成功' })
  @ApiQuery({ name: 'key', required: true, description: '资源存储KEY', enum: TeamBucketNamesEnum })
  async getOssHeadUrl(
    @Param('bucket') bucket: TeamBucketNamesEnum,
    @Query('fileKey') fileKey: string
  ) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    await this.authorizationService.checkRoleNames(currentTeamId, currentUserId, TeamRoleNames.All)
    return this.storageService.getOssHeadUrl({ teamId: currentTeamId, bucket, fileKey })
  }
}
