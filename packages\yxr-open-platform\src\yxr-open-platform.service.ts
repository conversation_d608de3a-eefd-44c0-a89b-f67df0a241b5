import { Inject, Injectable } from '@nestjs/common'
import { OPEN_PLATFORM_ROUTES } from './open-platform.routes'
import { TlsService } from '@yxr/huoshan'
import axios from 'axios'
import {
  AccountLocationDTO,
  AuditStatusDTO,
  CloudBaseDTO,
  ContentDeleteDTO,
  TaskCloudPushDTO,
  AccountMusicDTO
} from './dto/taskCloudPush.dto'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { PlatformAccountCookieEntity } from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import pako from 'pako'
import * as buffer from 'buffer'

@Injectable()
export class YxrOpenPlatformService {
  private readonly appId = 'app_e49fec2d9f9d21e7'
  private readonly secret = 'sk_V7G4Umhsgk8FjFYPFHvh6wPO8KjnvAR2'

  constructor(
    @InjectModel(PlatformAccountCookieEntity.name)
    private platformAccountCookieModel: Model<PlatformAccountCookieEntity>,
    private readonly loggerService: TlsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  /**
   * 开放平台授权
   * @returns
   */
  async authorize() {
    const questPath = this.buildRequestPath('authenticate')
    const cacheKey = 'openPlatformToken'
    const data = {
      appId: this.appId,
      secret: this.secret
    }
    if (!this.secret || !this.appId) {
      throw new Error('请配置开放平台的 appId 和 secret')
    }
    let token = await this.cacheManager.get<string>(cacheKey)
    if (token) {
      return `Bearer ${token}`
    }
    const result = await axios.post(questPath, data)
    if (result.status !== 200) {
      throw new Error('开放平台授权失败')
    }
    token = result.data?.access_token
    const expiresIn = result.data?.expires_in - 60 // 减去 60 秒，避免过期
    await this.cacheManager.set(cacheKey, token, expiresIn * 1000)
    return `Bearer ${token}`
  }

  /**
   * 获取请求session_token
   * session_token 是开放平台的会话令牌，用于后续的 API 调用
   * @returns
   */
  async getSessionToken() {
    try {
      const questPath = this.buildRequestPath('sessionToken')
      const openPlatformToken = await this.authorize()
      const result = await axios.get(questPath, {
        headers: {
          Authorization: openPlatformToken
        }
      })
      return result.data?.session_token
    } catch (error) {
      this.loggerService.error(null, '获取session_token失败', {
        error: error.message
      })
    }
  }

  /**
   * 任务推送到开放平台
   * @param data
   * @returns
   */
  async pushTask(data: TaskCloudPushDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('taskPush')
      const result = await axios.post(questPath, data, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台任务推送失败', {
        error: error.message
      })
    }
  }

  /**
   * 取消任务
   * @param data
   * @returns
   */
  async taskCancel(taskId: string) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('taskCancel', { taskId: taskId })
      const result = await axios.patch(
        questPath,
        {},
        {
          headers: {
            Authorization: openPlatformToken
          }
        }
      )

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台任务取消失败', {
        error: error.message
      })
    }
  }

  /**
   * 内容删除
   * @param data
   * @returns
   */
  async contentDelete(contentDeleteDTO: ContentDeleteDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('contentDelete')
      const result = await axios.delete(questPath, {
        data: contentDeleteDTO,
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台内容删除失败', {
        error: error.message
      })
    }
  }

  /**
   * 推送审核状态查询任务
   * @param data
   * @returns
   */
  async auditStatus(auditStatusDTO: AuditStatusDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('taskStatus')
      const result = await axios.post(questPath, auditStatusDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台审核状态查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 检查登录有效性
   * @param data
   * @returns
   */
  async checkStatus(cloudBaseDTO: CloudBaseDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('checkStatus')
      const result = await axios.post(questPath, cloudBaseDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台审核状态查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 地理位置查询
   * @param data
   * @returns
   */
  async location(accountLocationDTO: AccountLocationDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('location')
      const result = await axios.post(questPath, accountLocationDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台地理位置查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 音乐分类查询
   * @param data
   * @returns
   */
  async musicCategory(CloudBaseDTO: CloudBaseDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('getMusicCategory')
      const result = await axios.post(questPath, CloudBaseDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台音乐分类查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 音乐查询
   * @param data
   * @returns
   */
  async searchMusic(accountLocationDTO: AccountLocationDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('searchMusic')
      const result = await axios.post(questPath, accountLocationDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台音乐查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 通过分类获取音乐
   * @param data
   * @returns
   */
  async getMusicByCategory(accountMusicDTO: AccountMusicDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('getMusicByCategory')
      const result = await axios.post(questPath, accountMusicDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台音乐分类查询失败', {
        error: error.message
      })
    }
  }

  /**
   * 同步任务概览和作品
   * @param data
   * @returns
   */
  async accountData(cloudBaseDTO: CloudBaseDTO) {
    try {
      const openPlatformToken = await this.authorize()
      const questPath = this.buildRequestPath('accountData')
      const result = await axios.post(questPath, cloudBaseDTO, {
        headers: {
          Authorization: openPlatformToken
        }
      })

      return result
    } catch (error) {
      await this.loggerService.error(null, '开放平台媒体号数据同步任务概览和作品失败', {
        error: error.message
      })
    }
  }

  /**
   * 获取平台账号Cookie
   * @param platformAccountId
   * @returns
   */
  async getAccountCookie(platformAccountId: string) {
    const accountCookie = await this.platformAccountCookieModel
      .findOne({ platformAccountId: new Types.ObjectId(platformAccountId) })
      .select('cookie platformName localStorage')
      .lean()

    let localStorageStr = null
    if (accountCookie?.cookie) {
      const bufferData = buffer.Buffer.from(accountCookie.cookie, 'base64')
      const cookieStr = pako.inflate(bufferData, { to: 'string' })

      if (accountCookie.platformName === '抖音' && accountCookie.localStorage) {
        // 抖音的 localStorage 需要特殊处理
        const localBufferData = buffer.Buffer.from(accountCookie.localStorage, 'base64')
        localStorageStr = pako.inflate(localBufferData, { to: 'string' })
      }
      return {
        cookie: cookieStr,
        localStorage: accountCookie.localStorage ? JSON.parse(localStorageStr) : null
      }
    }
    return null
  }

  // 动态替换路径参数，如 {taskId}，允许 params 为空
  buildRequestPath(name: string, params?: Record<string, string | number>): string | undefined {
    const route = OPEN_PLATFORM_ROUTES.find((route) => route.name === name)
    if (!route) throw new Error(`接口 ${name} 不存在`)
    let path = route.path
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        path = path.replace(new RegExp(`\\{${key}\\}`, 'g'), String(value))
      })
    }
    return `${process.env.OPEN_PLATFORM_BASE_ADDRESS}${path}`
  }
}
