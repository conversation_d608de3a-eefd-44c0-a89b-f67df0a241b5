export interface OpenPlatformRoute {
  name: string; // 接口名称
  path: string; // 路径
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  description?: string;
}

export const OPEN_PLATFORM_ROUTES: OpenPlatformRoute[] = [
  {
    name: 'authenticate',
    path: '/open/auth/authenticate',
    method: 'POST',
    description: '获取访问令牌',
  },
  {
    name: 'verify',
    path: '/open/auth/verify',
    method: 'GET',
    description: '验证访问令牌',
  },
  {
    name: 'appInfo',
    path: '/open/auth/app-info',
    method: 'GET',
    description: '获取应用信息',
  },
  {
    name: 'sessionToken',
    path: '/open/auth/session-token',
    method: 'GET',
    description: '获取session_token',
  },
  {
    name: 'taskCancel',
    path: '/web/task/{taskId}',
    method: 'PATCH',
    description: '取消发布中任务',
  },
  {
    name: 'contentDelete',
    path: '/web/task/content',
    method: 'DELETE',
    description: '删除媒体平台内容',
  },
  {
    name: 'taskPush',
    path: '/web/task/push',
    method: 'POST',
    description: '推送发布任务',
  },
  {
    name: 'taskStatus',
    path: '/web/task/queryAuditStatus',
    method: 'POST',
    description: '推送审核状态查询任务',
  },
  {
    name: 'taskNum',
    path: '/web/task/publishTaskNum',
    method: 'GET',
    description: '获取发布中的任务数',
  },
  {
    name: 'searchMusic',
    path: '/web/configData/searchMusic',
    method: 'POST',
    description: '获取音乐',
  },
  {
    name: 'getMusicCategory',
    path: '/web/configData/getMusicCategory',
    method: 'POST',
    description: '获取音乐分类',
  },
  {
    name: 'getMusicByCategory',
    path: '/web/configData/getMusicByCategory',
    method: 'POST',
    description: '根据分类获取音乐',
  },
  {
    name: 'location',
    path: '/web/configData/searchLocation',
    method: 'POST',
    description: '获取地理位置',
  },
  {
    name: 'checkStatus',
    path: '/web/platformAccount/check-status',
    method: 'POST',
    description: '检测登录有效性',
  },
  {
    name: 'accountOverview',
    path: '/web/platformAccount/account-data-overview',
    method: 'POST',
    description: '媒体号概览数据同步任务',
  },
  {
    name: 'accountData',
    path: '/web/platformAccount/account-data',
    method: 'POST',
    description: '执行媒体号数据同步任务(概览+作品)',
  },
  {
    name: 'accountContentList',
    path: '/web/platformAccount/account-data-contentlist',
    method: 'POST',
    description: '媒体号作品列表数据同步任务',
  },
]; 