import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
  OpenPlatformUserEntity,
  OpenPlatformApplicationEntity,
  OpenPlatformUserRoleEntity,
  TeamEntity,
  PlatformAccountEntity,
  TrafficBillingEntity,
  OrderEntity,
  OpenPlatformApplicationBalanceEntity
} from '@yxr/mongo'
import { OpenPlatformStatus, OrderStatus } from '@yxr/common'
import {
  GetOpenPlatformUsersRequestDto,
  UpdateUserRemarkRequestDto,
  GetUserApplicationsRequestDto,
  OpenPlatformUserInfoDto,
  UserApplicationDetailDto,
  OpenPlatformUsersListDto,
  UserApplicationsListDto,
  UpdateApplicationPriceRequestDto,
  GetApplicationsRequestDto
} from '../dto/user-admin.dto'

/**
 * 开放平台用户管理服务（管理员专用）
 * 提供用户列表查询、用户应用详情查询、用户备注管理等功能
 */
@Injectable()
export class UserAdminService {
  private readonly logger = new Logger(UserAdminService.name)

  constructor(
    @InjectModel(OpenPlatformUserEntity.name)
    private userModel: Model<OpenPlatformUserEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(OpenPlatformApplicationBalanceEntity.name)
    private balanceModel: Model<OpenPlatformApplicationBalanceEntity>
  ) {}

  /**
   * 获取开放平台用户列表
   */
  async getOpenPlatformUsers(
    queryDto: GetOpenPlatformUsersRequestDto
  ): Promise<OpenPlatformUsersListDto> {
    this.logger.log(
      `获取开放平台用户列表: page=${queryDto.page}, limit=${queryDto.size}, ` +
      `phone=${queryDto.phone || 'all'}`
    )

    // 构建查询条件
    const filter: any = {}
    
    if (queryDto.phone) {
      filter.phone = { $regex: queryDto.phone, $options: 'i' }
    }

    // 注册时间范围查询

    if (queryDto.registeredStartTime && queryDto.registeredEndTime) {
      filter.createdAt = {}
      if (queryDto.registeredStartTime) {
        filter.createdAt.$gte = new Date(Number(queryDto.registeredStartTime))
      }
      if (queryDto.registeredEndTime) {
        filter.createdAt.$lte = new Date(Number(queryDto.registeredEndTime))
      }
    }

    // 计算分页参数
    const skip = (queryDto.page - 1) * queryDto.size

    try {
      // 并行查询总数和用户列表
      const [total, users] = await Promise.all([
        this.userModel.countDocuments(filter),
        this.userModel
          .find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(queryDto.size)
          .lean()
          .exec()
      ])

      // 获取每个用户的应用数量
      const userIds = users.map(user => user._id)
      const applicationCounts = await this.getApplicationCountsForUsers(userIds)

      // 构建响应数据
      const list: OpenPlatformUserInfoDto[] = users.map(user => ({
        id: user._id.toString(),
        phone: user.phone,
        nickname: user.nickname,
        status: user.status,
        applicationCount: applicationCounts[user._id.toString()] || 0,
        remark: user.remark,
        createdAt: user.createdAt?.getTime() || 0,
        updatedAt: user.updatedAt?.getTime() || 0
      }))

      const totalPage = Math.ceil(total / queryDto.size)

      const result: OpenPlatformUsersListDto = {
        data: list,
        totalSize: total,
        page: queryDto.page,
        size: queryDto.size,
        totalPage
      }

      this.logger.log(
        `用户列表查询完成: 总数=${total}, 当前页=${queryDto.page}, ` +
        `返回${list.length}条记录`
      )

      return result
    } catch (error) {
      this.logger.error(`获取用户列表失败: ${error.message}`, error.stack)
      throw error
    }
  }

  async getApplicationsByPhone(
    queryDto: GetApplicationsRequestDto
  ): Promise<UserApplicationsListDto> {
    this.logger.log(`获取用户应用详情: phone=${queryDto.phone}`)

    // 验证用户是否存在
    const user = await this.userModel.findOne({ phone: queryDto.phone })
    if (!user) {
      return {
        userId: '',
        userPhone: queryDto.phone,
        applications: [],
        totalApplications: 0
      }
    }

    try {
      // 查找用户的所有应用
      const applications = await this.applicationModel
        .find({ userId: user._id })
        .sort({ createdAt: -1 })
        .lean()
        .exec()

      // 为每个应用获取详细统计信息
      const applicationDetails: UserApplicationDetailDto[] = []
      
      for (const app of applications) {
        const detail = await this.getApplicationStatistics(app)
        applicationDetails.push(detail)
      }

      const result: UserApplicationsListDto = {
        userId: user._id.toString(),
        userPhone: user.phone,
        applications: applicationDetails,
        totalApplications: applications.length
      }

      this.logger.log(
        `用户应用详情查询完成: userId=${user._id.toString()}, ` +
        `应用数量=${applications.length}`
      )

      return result
    } catch (error) {
      this.logger.error(`获取用户应用详情失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 获取用户应用详情
   */
  async getUserApplications(
    queryDto: GetUserApplicationsRequestDto
  ): Promise<UserApplicationsListDto> {
    this.logger.log(`获取用户应用详情: userId=${queryDto.userId}`)

    // 验证用户是否存在
    const user = await this.userModel.findById(queryDto.userId)
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    try {
      // 查找用户的所有应用
      const applications = await this.applicationModel
        .find({ userId: new Types.ObjectId(queryDto.userId) })
        .sort({ createdAt: -1 })
        .lean()
        .exec()

      // 为每个应用获取详细统计信息
      const applicationDetails: UserApplicationDetailDto[] = []
      
      for (const app of applications) {
        const detail = await this.getApplicationStatistics(app)
        applicationDetails.push(detail)
      }

      const result: UserApplicationsListDto = {
        userId: queryDto.userId,
        userPhone: user.phone,
        applications: applicationDetails,
        totalApplications: applications.length
      }

      this.logger.log(
        `用户应用详情查询完成: userId=${queryDto.userId}, ` +
        `应用数量=${applications.length}`
      )

      return result
    } catch (error) {
      this.logger.error(`获取用户应用详情失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 修改用户备注
   */
  async updateUserRemark(
    updateDto: UpdateUserRemarkRequestDto
  ) {
    this.logger.log(`修改用户备注: userId=${updateDto.userId}`)

    // 验证用户是否存在
    const user = await this.userModel.findById(updateDto.userId)
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    try {
      // 更新用户备注
      const updatedUser = await this.userModel.findByIdAndUpdate(
        updateDto.userId,
        {
          remark: updateDto.remark,
          updatedAt: new Date()
        },
        { new: true }
      )

      if (!updatedUser) {
        throw new BadRequestException('用户备注更新失败')
      }

      const result = {
        userId: updateDto.userId,
        remark: updateDto.remark,
        updatedAt: updatedUser.updatedAt?.getTime() || Date.now()
      }

      this.logger.log(
        `用户备注修改成功: userId=${updateDto.userId}, ` +
        `备注="${updateDto.remark || ''}"`
      )

      return result
    } catch (error) {
      this.logger.error(`修改用户备注失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 修改应用价格
   */
  async updateApplicationPrice(
    applicationId: string,
    updateDto: UpdateApplicationPriceRequestDto
  ) {
    this.logger.log(`修改应用价格: applicationId=${applicationId}`)

    // 验证应用是否存在
    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    try {
      // 更新应用价格
      const updatedApplication = await this.applicationModel.findByIdAndUpdate(
        applicationId,
        {
          accountPrice: updateDto.accountPrice,
          trafficPrice: updateDto.trafficPrice,
          packagePrice: updateDto.packagePrice,
          updatedAt: new Date()
        },
        { new: true }
      )

      if (!updatedApplication) {
        throw new BadRequestException('应用价格更新失败')
      }

      const result = {
        applicationId: updatedApplication._id.toString(),
        accountPrice: updatedApplication.accountPrice,
        trafficPrice: updatedApplication.trafficPrice,
        packagePrice: updatedApplication.packagePrice,
        updatedAt: updatedApplication.updatedAt?.getTime() || Date.now()
      }

      this.logger.log(
        `应用价格修改成功: applicationId=${applicationId}, ` +
        `accountPrice=${updateDto.accountPrice}, trafficPrice=${updateDto.trafficPrice}`
      )

      return result
    } catch (error) {
      this.logger.error(`修改应用价格失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 获取多个用户的应用数量
   */
  private async getApplicationCountsForUsers(
    userIds: Types.ObjectId[]
  ): Promise<Record<string, number>> {
    if (userIds.length === 0) return {}

    const counts = await this.applicationModel.aggregate([
      {
        $match: {
          userId: { $in: userIds }
        }
      },
      {
        $group: {
          _id: '$userId',
          count: { $sum: 1 }
        }
      }
    ])

    const result: Record<string, number> = {}
    counts.forEach(item => {
      result[item._id.toString()] = item.count
    })

    return result
  }

  /**
   * 获取应用的详细统计信息
   */
  private async getApplicationStatistics(
    application: any
  ): Promise<UserApplicationDetailDto> {
    const appId = application._id

    try {
      // 并行查询各种统计数据
      const [
        userCount,
        teamCount,
        totalAccountCount,
        trafficStats,
        availableBalance
      ] = await Promise.all([
        this.getUserCountForApplication(appId),
        this.getTeamCountForApplication(appId),
        this.getAccountCountForApplication(appId),
        this.getTrafficStatsForApplication(appId),
        this.getPayAmountForApplication(appId)
      ])

      const totalTraffic = trafficStats.totalTraffic || 0
      const totalTrafficMB = Number((totalTraffic / (1024 * 1024)).toFixed(2))

      const usedTraffic = trafficStats.usedTraffic || 0
      const usedTrafficMB = Number((usedTraffic / (1024 * 1024)).toFixed(2))

      return {
        id: application._id.toString(),
        appId: application.appId,
        name: application.name,
        userCount,
        teamCount,
        totalAccountCount,
        totalTraffic,
        totalTrafficMB,
        usedTraffic,
        usedTrafficMB,
        availableBalance,
        trafficPrice: application.trafficPrice,
        accountPrice: application.accountPrice,
        packagePrice: application.packagePrice,
        status: application.status,
        createdAt: application.createdAt?.getTime() || 0,
        updatedAt: application.updatedAt?.getTime() || 0
      }
    } catch (error) {
      this.logger.error(
        `获取应用统计信息失败: appId=${appId}, error=${error.message}`
      )
      
      // 返回默认值，避免整个查询失败
      return {
        id: application._id.toString(),
        appId: application.appId,
        name: application.name,
        userCount: 0,
        teamCount: 0,
        totalAccountCount: 0,
        totalTraffic: 0,
        totalTrafficMB: 0,
        usedTraffic: 0,
        usedTrafficMB: 0,
        availableBalance: 0,
        trafficPrice: application.trafficPrice,
        accountPrice: application.accountPrice,
        packagePrice: application.packagePrice,
        status: application.status,
        createdAt: application.createdAt?.getTime() || 0,
        updatedAt: application.updatedAt?.getTime() || 0
      }
    }
  }

  /**
   * 获取应用的关联用户数
   */
  private async getUserCountForApplication(appId: Types.ObjectId): Promise<number> {
    return await this.userRoleModel.countDocuments({
      applicationId: appId
    })
  }

  /**
   * 获取应用的关联团队数
   */
  private async getTeamCountForApplication(appId: Types.ObjectId): Promise<number> {
    return await this.teamModel.countDocuments({
      sourceAppId: appId
    })
  }

  /**
   * 获取应用的账号总数
   */
  private async getAccountCountForApplication(appId: Types.ObjectId): Promise<number> {
    // 通过团队关联查询账号总数
    const teams = await this.teamModel.find(
      { sourceAppId: appId },
      { _id: 1 }
    ).lean()

    if (teams.length === 0) return 0

    const teamIds = teams.map(team => team._id)
    return await this.platformAccountModel.countDocuments({
      teamId: { $in: teamIds }
    })
  }

  /**
   * 获取应用的流量统计
   */
  private async getTrafficStatsForApplication(
    appId: Types.ObjectId
  ): Promise<{ totalTraffic: number, usedTraffic: number }> {
    // 通过团队关联查询流量统计
    const teams = await this.teamModel.find(
      { sourceAppId: appId },
      { _id: 1 }
    ).lean()

    if (teams.length === 0) return { totalTraffic: 0, usedTraffic: 0 }

    // 通过团队列表中的总流量字段与已使用流量字段统计
    const totalTraffic = teams.reduce((acc, team) => {
      return acc + (team.networkTraffic || 0)
    }, 0)

    const usedTraffic = teams.reduce((acc, team) => {
      return acc + (team.useNetworkTraffic || 0)
    }, 0)


    return {
      totalTraffic,
      usedTraffic
    }
  }

  private async getPayAmountForApplication(
    appId: Types.ObjectId
  ): Promise<number> {
    // 通过团队关联查询流量统计
    const balance = await this.balanceModel.findOne({
      applicationId: appId
    })

    if (balance) {
      return balance.availableBalance
    } else {
      return 0
    }
  }
}
