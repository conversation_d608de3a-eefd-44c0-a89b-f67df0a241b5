import { Module } from '@nestjs/common'
import { TeamController } from './team.controller'
import { TeamService } from './team.service'
import {
  MemberMongoose,
  PlatformAccountMongoose,
  RefundMongoose,
  TeamMongoose,
  UserMongoose,
  AdminMongoose,
  TeamExpiredLogMongoose,
  PlatformAccountOverviewMongoose} from '@yxr/mongo'
import { WebhookModule } from '../webhook/webhook.module'
import { OrderManagerModule } from '@yxr/order'
import { HuoshanModule } from '@yxr/huoshan'

@Module({
  imports: [
    TeamMongoose,
    PlatformAccountMongoose,
    MemberMongoose,
    UserMongoose,
    AdminMongoose,
    RefundMongoose,
    TeamExpiredLogMongoose,
    PlatformAccountOverviewMongoose,
    WebhookModule,
    OrderManagerModule,
    HuoshanModule
  ],
  controllers: [TeamController],
  providers: [
    TeamService,
  ],
  exports:[TeamService]
})
export class TeamModule {}
