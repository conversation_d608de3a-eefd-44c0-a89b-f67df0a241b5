import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common'
import { SDK } from 'wechatify-sdk'
import {
  WxAssistantResponse,
  WxQrCodeResponse
} from '../wx-third-platform/wx-ipad/wx-ipad.dto'
import { DeviceDataDto } from './wechat-sdk.dto'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class WechatIpadSdkService {
  private sdk: SDK

  constructor(
    private readonly loggerService: TlsService
  ) {
    this.sdk = new SDK({
      app_id: process.env.BAIZHUN_APP_ID,
      app_secret: process.env.BAIZHUN_APP_SECRET,
      host: process.env.BAIZHUN_HOST
    })
  }

  /**
   * 第三方微信获取二维码
   */
  async qrcode(data: DeviceDataDto): Promise<WxQrCodeResponse> {
    try {
      const result = await this.sdk.qrcode(data)
      return {
        base64: result.base64,
        uuid: result.uuid
      }
    } catch (e) {
      await this.loggerService.error(null, '获取第三方微信二维码失败', { error: e.message })
      throw new ForbiddenException('获取微信二维码失败')
    }
  }

  /**
   * 删除微信账号
   * @param platformAuthorId
   */
  async deleteWechatAccount(platformAuthorId: string) {
    try {
      await this.sdk.logout(platformAuthorId)
    } catch (error) {
      await this.loggerService.error(null, '退出微信错误', { error: error.message })
    }
    try {
      await this.sdk.delete(platformAuthorId)
    } catch (error) {
      await this.loggerService.error(null, '删除微信错误', { error: error.message })
    }
  }

  /**
   * 获取微信视频号账号列表
   * @param platformAccountId
   * @returns
   */
  async assistantMembers(
    platformAuthorId: string,
    platformAccountId: string
  ): Promise<WxAssistantResponse[]> {
    try {
      const token = platformAccountId
      const result = await this.sdk.assistant.members(platformAuthorId, token)
      return result.map((item) => {
        return {
          finderUsername: item.finderUsername,
          nickname: item.nickname,
          headImgUrl: item.headImgUrl,
          uniqId: item.uniqId
        }
      })
    } catch (e) {
      await this.loggerService.error(null, '获取第三方微信视频号账号列表失败：', { error: e.message })
      throw new ForbiddenException('获取微信视频号账号列表失败')
    }
  }

  /**
   * 更新第三方视频号的登录凭证
   * @param platformAuthorId wxid
   * @param parentId 父级账号id
   * @param finderUsername 视频号账号
   * @returns
   */
  async assistantMemberToken(
    platformAuthorId: string,
    parentId: string,
    finderUsername: string
  ): Promise<string> {
    try {
      return await this.sdk.assistant.scan(platformAuthorId, parentId, finderUsername)
    } catch (e) {
      await this.loggerService.error(null, '更新微信视频号登录状态失败：', { error: e.message })
      throw new NotFoundException('微信视频号状态失效')
    }
  }

  /**
   * 检测账号登陆状态
   * @param uuid
   * @param currentTeamId
   * @param currentUserId
   */
  async checkLogin(uuid: string) {
    const res = await this.sdk.checkLogin(uuid)
    if (res) {
      const [status, data] = res
      return {
        status: status,
        data: data
      }
    }
  }
}
