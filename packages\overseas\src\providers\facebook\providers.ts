import { Provider } from '@nestjs/common'
import { OverseasPlatformNames } from '../../constants'
import { AccountAuthProvider } from '../account-auth.provider'
import { ContentPublishProvider } from '../content-publish.provider'
import { FacebookAccountAuthProvider } from './facebook-account-auth.provider'
import { FacebookContentPublishProvider } from './facebook-content-publish.provider'
import { FacebookApi } from './facebook-api'

export const providers = [
  FacebookApi,
  {
    provide: AccountAuthProvider.register_token(OverseasPlatformNames.Facebook),
    useClass: FacebookAccountAuthProvider
  },
  {
    provide: ContentPublishProvider.register_token(OverseasPlatformNames.Facebook),
    useClass: FacebookContentPublishProvider
  },
  // {
  //   provide: UserinfoService.token(Tiktok),
  //   useClass: TiktokBusinessUserinfoService
  // },
  // {
  //   provide: ContentService.token(Tiktok),
  //   useClass: TiktokBusinessContentService
  // },
  // {
  //   provide: CommentService.token(Tiktok),
  //   useClass: TiktokBusinessCommentService
  // },
  // {
  //   provide: MessageService.token(Tiktok),
  //   useClass: TiktokBusinessMessageService
  // },
  // {
  //   provide: DataRetrievalService.token(Tiktok),
  //   useClass: TiktokBusinessDataRetrievalService
  // },
  // {
  //   provide: PermissionProvider.token(Tiktok),
  //   useClass: TiktokBusinessPermissionProvider
  // },
  // {
  //   provide: WebhookProvider.token(Tiktok),
  //   useClass: TiktokBusinessWebhookProvider
  // }
] as Provider[]
