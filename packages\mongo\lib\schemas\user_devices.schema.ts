import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class UserDevicesEntity {
  /**
   * 用户id
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: String,
    required: false,
    default: null
  })
  version?: string

  //数字版本号
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  numberVersion?: number

  /**
   * 设备ID
   */
  @Prop({
    type: String,
    index: true,
    required: true
  })
  deviceId: string

  /**
   * 操作系统类型
   */
  @Prop({
    type: String,
    required: false
  })
  osType?: string

  /**
   * 登录时间
   */
  @Prop({
    type: Date,
    index: true
  })
  loginTime?: Date

  /**
   * 是否活跃设备
   */
  @Prop({
    type: Boolean,
    default: false
  })
  isActive: boolean

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const UserDevicesSchema: ModelDefinition = {
  name: UserDevicesEntity.name,
  schema: SchemaFactory.createForClass(UserDevicesEntity)
}

export const UserDevicesMongoose = MongooseModule.forFeature([UserDevicesSchema])
