import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { ContentEntity, PlatformAccountEntity, TaskEntity } from '@yxr/mongo'
import { Model, Types, UpdateQuery } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { WxBasicService } from './wx-basic.service'
import { WxArticle, WxMediaType } from './wx-publish.dto'
import {
  EventNames,
  StageStatus,
  TaskAuditStatusEvent,
  TaskStages,
  WeChatErrorCode
} from '@yxr/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { WxCommonService } from './wx-common.service'
import { TlsService } from '@yxr/huoshan'

export const eventKey = 'wechat-publish'
export const eventName = 'wechat-publish-event'

export const wechatPublishEventEmitter = new EventEmitter()

/**
 * 注意此服务有调用事件不能引入 FastifyRequest 微信相关事件功能会失效
 */
@Injectable()
export class WxPublishEventService implements OnModuleInit {
  logger = new Logger('WxPublishEventService')

  constructor(
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    private readonly wxBasicService: WxBasicService,
    private readonly wxCommonService: WxCommonService,
    private readonly loggerService: TlsService,
    private eventEmitter: EventEmitter2
  ) {}

  wxTaskQueue: Queue

  wxTaskWorker: Worker

  onModuleInit() {
    this.logger.log('WxPublishEventService init')

    wechatPublishEventEmitter.on(eventKey, this.createWxPublishQueue.bind(this))

    this.wxTaskQueue = new Queue(eventName, {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.wxTaskWorker = new Worker(
      eventName,
      async (job) => {
        const { taskIdentityId, contentId } = job.data
        await this.wxPublishWorker(taskIdentityId, contentId)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  /**
   * 创建微信发布队列
   * @param param0
   */
  async createWxPublishQueue({
    taskIdentityId,
    contentIds
  }: {
    taskIdentityId: string
    contentIds: [string]
  }) {
    this.logger.log('createWxPublishQueue')
    if (contentIds.length > 0) {
      for (let index = 0; index < contentIds.length; index++) {
        const contentId = contentIds[index]
        // await this.wxPublishWorker(taskIdentityId, contentId)
        await this.wxTaskQueue.add(
          eventName,
          { taskIdentityId, contentId },
          {
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${contentId}-${eventName}`
          }
        )
      }
    }
  }

  /**
   * 消费队列微信发布任务
   * @param contentId
   * @returns
   */
  async wxPublishWorker(taskIdentityId: string, contentId: string) {
    const content = await this.contentModel.findOne({
      _id: new Types.ObjectId(contentId)
    })

    if (!content) {
      return
    }

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(content.platformAccountId)
    })
    const accessToken = await this.wxCommonService.getAuthorizerAccessToken(
      platformAccount.platformAuthorId,
      platformAccount.token
    )
    const contentObjectId = new Types.ObjectId(content._id)
    const publishContent: WxArticle[] = content.publishArgs as WxArticle[]
    // 转换内容图片标签
    for (let index = 0; index < publishContent.length; index++) {
      const element = publishContent[index]
      //封面处理
      if (element.thumbUrl) {
        const thumbResult = await this.wxCommonService.uploadWxMaterial(
          accessToken,
          element.thumbUrl,
          WxMediaType.image
        )
        if (thumbResult && thumbResult.media_id) {
          publishContent[index].thumbUrl = thumbResult.media_id
        }
      }
      publishContent[index].content = await this.wxCommonService.imgTagReplace(
        accessToken,
        element.content
      )
    }

    const result = await this.wxBasicService.addWxDraft(accessToken, publishContent)
    if (result?.errcode && result.errcode !== 0) {
      const faliedMessage = WeChatErrorCode.getErrorMessage(result.errcode)
      await this.taskModel.updateOne(
        {
          contentId: contentObjectId
        },
        {
          stages: TaskStages.Push,
          stageStatus: StageStatus.Fail,
          errorMessage: faliedMessage
        }
      )

      // 触发任务状态变更事件
      await this.eventEmitter.emitAsync(
        EventNames.TaskAuditStatusChangedEvent,
        new TaskAuditStatusEvent(content.teamId.toString(), taskIdentityId)
      )
      await this.loggerService.error(null, '微信公众号草稿创建失败', {
        platformAccountId: platformAccount._id,
        error: JSON.stringify(result)
      })
      return
    }

    //更新
    await this.contentModel.updateOne(
      {
        _id: contentObjectId
      },
      {
        mediaId: result.media_id
      }
    )
    let updateData: UpdateQuery<TaskEntity> = {}
    let submitResult: any
    let publishId: string
    if (content.sendAll > 0) {
      //群发
      submitResult = await this.wxBasicService.sendAll(
        accessToken,
        result.media_id,
        content.sendIgnoreReprint
      )
      if (submitResult.errcode === 0) {
        publishId = submitResult.msg_id
      }
    } else {
      submitResult = await this.wxBasicService.publishSubmit(accessToken, result.media_id)
      if (submitResult.errcode === 0) {
        publishId = submitResult.publish_id
      }
    }

    if (submitResult.errcode === 0) {
      //提交成功
      updateData = {
        publishId: publishId,
        stages: TaskStages.Push,
        stageStatus: StageStatus.Success
      }
    } else {
      //提交失败
      updateData = {
        errorMessage: '发布失败',
        stages: TaskStages.Push,
        stageStatus: StageStatus.Fail
      }
      await this.loggerService.error(null, '微信公众号发布失败', {
        platformAccountId: platformAccount._id,
        error: JSON.stringify(submitResult)
      })
    }
    await this.taskModel.updateOne(
      {
        contentId: contentObjectId
      },
      updateData
    )

    // 触发任务状态变更事件
    await this.eventEmitter.emitAsync(
      EventNames.TaskAuditStatusChangedEvent,
      new TaskAuditStatusEvent(content.teamId.toString(), taskIdentityId)
    )
  }
}
