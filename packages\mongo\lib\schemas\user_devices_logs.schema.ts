import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class UserDeviceLogsEntity {
  /**
   * 用户id
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: String,
    index: true
  })
  phone?: string

  @Prop({
    type: String,
    required: false
  })
  avatar?: string

  @Prop({
    type: String,
    index: true
  })
  nickName?: string

  @Prop({
    type: String,
    required: false
  })
  version?: string

  //数字版本号
  @Prop({
    type: Number,
    required: false
  })
  numberVersion?: number

  /**
   * 设备ID
   */
  @Prop({
    type: String,
    index: true,
    required: false
  })
  deviceId?: string

  @Prop({
    type: String,
    required: false
  })
  downloadLink?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const UserDeviceLogsSchema: ModelDefinition = {
  name: UserDeviceLogsEntity.name,
  schema: SchemaFactory.createForClass(UserDeviceLogsEntity)
}

export const UserDeviceLogsMongoose = MongooseModule.forFeature([UserDeviceLogsSchema])
