# @yxr/open-platform-order

开放平台订单管理服务包，提供开放平台订单的核心业务逻辑。

## 功能特性

### 订单类型支持

1. **主订单（开通订单）**
   - 团队的VIP服务主订单，支持首次开通和后续主订单
   - 订单开始时间：不能小于当天（可指定未来时间）
   - 可配置参数：账号数量、流量数量、时长（按月计算）、开始时间（可选）
   - 时间不重叠原则：同一团队在任何时间点只能有一个有效的主订单

2. **增购订单（独立订单）**
   - 团队的额外资源订单，独立于主订单时间范围
   - 订单开始时间：不能小于当天（可指定未来时间）
   - 可配置参数：账号数量、流量数量、时长（可自定义数字）、开始时间（必填）
   - 存在性依赖：团队必须至少有一个有效的主订单才能创建增购订单
   - 时间独立：增购订单的时间范围可以完全独立于主订单
   - 可重叠：同一团队可以有多个增购订单，时间可以重叠

### 业务规则

- **主订单时间不重叠原则**：主订单之间的生效时间永远不会重叠，创建新主订单时会验证时间冲突
- **订单生效时间限制**：所有订单（主订单和增购订单）的生效时间都不能小于当天
- **增购订单独立性**：增购订单的时间范围完全独立于主订单，不受主订单时间限制
- **增购订单存在性验证**：创建增购订单时只需验证团队至少有一个有效的主订单（任何状态为Paid的主订单）
- **增购订单当日生效判断**：判断增购订单是否包含当前日期（今天），如果包含则该增购订单的完整权益可以累加到团队总权益中
- **完整权益累加**：不使用比例计算，如果增购订单在当日有效，则其完整的账号数量和流量数量都应该被累加
- **权益计算公式**：团队的总权益 = 当前有效主订单的权益 + 所有包含当前日期的增购订单的完整权益之和
- **增购订单灵活性**：同一团队可以有多个增购订单，时间可以重叠，完全独立管理
- **定时任务管理**：通过定时任务处理订单状态管理
  - 每小时自动检查过期订单
  - 批量更新团队VIP状态
  - 自动失效无效的增购订单
- **VIP状态兼容性**：保持team表中现有的VIP标记字段逻辑
- **数据库兼容性**：与现有订单表结构完全兼容

## 安装使用

### 安装依赖

```bash
# 在需要使用的项目中添加依赖
npm install @yxr/open-platform-order
```

### 导入模块

```typescript
import { OpenPlatformOrderManagerModule } from '@yxr/open-platform-order'

@Module({
  imports: [
    OpenPlatformOrderManagerModule,
    // 其他模块...
  ],
  // ...
})
export class YourModule {}
```

### 使用服务

```typescript
import { OpenPlatformOrderManagerService } from '@yxr/open-platform-order'

@Injectable()
export class YourService {
  constructor(
    private readonly orderManagerService: OpenPlatformOrderManagerService
  ) {}

  async createOrder() {
    const result = await this.orderManagerService.createOpenOrder({
      teamId: 'team-id',
      userId: 'user-id',
      accountCount: 10,
      trafficCount: 100,
      duration: 3,
      remark: '开通VIP服务'
    })
    return result
  }
}
```

## API 接口

### 核心方法

#### createOpenOrder(createDto)
创建开通订单（主订单）

**参数：**
- `teamId`: 团队ID
- `userId`: 用户ID
- `accountCount`: 账号数量
- `trafficCount`: 流量数量（GB）
- `duration`: 时长（月）
- `startTime`: 订单开始时间（可选，默认为当前时间）
- `sourceAppId`: 来源应用ID（可选）
- `remark`: 备注（可选）



#### createAddonOrder(createDto)
创建增购订单（附属订单）

**参数：**
- `teamId`: 团队ID
- `userId`: 用户ID
- `accountCount`: 账号数量
- `trafficCount`: 流量数量（GB）
- `duration`: 时长（可自定义数字）
- `startTime`: 订单开始时间（独立于主订单时间范围）
- `sourceAppId`: 来源应用ID（可选）
- `remark`: 备注（可选）

**验证规则：**
- 团队必须至少有一个有效的主订单（orderStatus为Paid）
- 增购订单时间范围可以完全独立于主订单
- 不再设置parentOrderId字段，与主订单无直接关联

#### getTeamVipStatus(teamId)
获取团队VIP状态

**参数：**
- `teamId`: 团队ID

**返回：**
- 团队VIP状态信息，包括当前权益、过期时间、增购订单列表等

#### updateTeamVipStatus(teamId, session?)
更新团队VIP状态

**参数：**
- `teamId`: 团队ID
- `session`: MongoDB事务会话（可选）

#### invalidateAddonOrdersWhenMainOrderExpires(teamId)
检查主订单失效时自动失效增购订单

**参数：**
- `teamId`: 团队ID

### 定时任务服务

#### OpenPlatformOrderSchedulerService

**主要功能：**
- 每小时自动检查过期订单和更新VIP状态
- 批量处理团队VIP状态更新
- 提供手动触发和调试接口

**核心方法：**

#### handleExpiredOrdersAndUpdateVipStatus()
定时任务主方法，每小时执行一次

#### manualUpdateAllTeamVipStatus()
手动更新所有团队VIP状态

**返回：**
```typescript
{
  totalTeams: number
  successCount: number
  failureCount: number
  errors: string[]
}
```

#### getOrderStatistics()
获取订单统计信息

**返回：**
```typescript
{
  totalOrders: number
  activeMainOrders: number
  activeAddonOrders: number
  expiredOrders: number
  cancelledOrders: number
  teamsWithActiveOrders: number
}
```

#### validateTeamVipBenefits(teamId)
验证团队VIP权益计算准确性（调试用）

**参数：**
- `teamId`: 团队ID

**返回：**
```typescript
{
  teamId: string
  currentVipStatus: any
  calculatedBenefits: any
  isConsistent: boolean
  differences?: any
}
```

## 数据模型

### 订单响应DTO
```typescript
interface OpenPlatformOrderResponseDto {
  id: string
  orderNo: string
  teamId: string
  teamName: string
  userId: string
  orderStatus: OrderStatus
  orderType: OrderType
  mainType: OpenPlatformOrderMainType
  accountCount: number
  trafficCount: number
  duration: number
  startTime: number
  endTime: number
  parentOrderId?: string
  remark: string
  createdAt: number
  updatedAt: number
}
```

### 团队VIP状态DTO
```typescript
interface TeamVipStatusResponseDto {
  teamId: string
  teamName: string
  isVip: boolean
  expiredAt: number
  accountCountLimit: number
  networkTraffic: number
  currentMainOrderId?: string
  addonOrders: OpenPlatformOrderResponseDto[]
}
```

## 常量配置

### 订单配置
- 默认账号数量：1
- 默认流量数量：10GB
- 默认时长：1个月
- 最小时长：0.1个月
- 最大时长：120个月

### 错误消息
- `TEAM_NOT_FOUND`: 团队不存在
- `EXISTING_MAIN_ORDER`: 团队已有有效的主订单，请使用续费功能
- `ADDON_ORDER_OUT_OF_RANGE`: 增购订单必须在有效的主订单时间范围内

## 依赖项

- `@yxr/common`: 公共枚举和类型
- `@yxr/mongo`: MongoDB数据模型
- `@yxr/config`: 配置管理
- `@yxr/redis`: Redis缓存
- `@yxr/utils`: 工具函数

## 权益计算算法

### 简化的当日生效判断

增购订单的权益叠加采用简化的当日生效判断：

1. **检查当日生效**：
   ```typescript
   const now = new Date()
   const isActiveToday = addonOrder.startTime <= now && addonOrder.endTime > now
   ```

2. **完整权益累加**：
   ```typescript
   if (isActiveToday) {
     totalAccountCount += addonOrder.accountCount || 0
     totalTrafficCount += addonOrder.trafficCount || 0
   }
   ```

3. **权益计算公式**：
   ```typescript
   团队总权益 = 主订单权益 + 所有当日生效增购订单权益之和
   ```

### 定时任务优化

采用定时任务处理订单状态管理：

- **每小时执行**：检查过期订单和更新VIP状态
- **批量处理**：分批更新团队状态，避免数据库压力
- **错误恢复**：单个团队更新失败不影响其他团队
- **手动触发**：提供管理接口用于调试和紧急修复

### 💰 金额计算算法

订单金额计算采用以下公式：

```typescript
订单总金额（分） = (账号数量 × 账号单价 + 流量数量 × 流量单价) × 时长（月）
```

**价格配置（可在constant.ts中调整）：**
- 主订单账号单价：1000分/个/月（10.00元）
- 主订单流量单价：50分/GB/月（0.50元）
- 增购订单账号单价：1000分/个/月（10.00元）
- 增购订单流量单价：50分/GB/月（0.50元）

**金额精度处理：**
- 所有金额计算使用分为单位，避免浮点数精度问题
- 前端显示时转换为元，保留2位小数
- 支持最小订单金额1分，最大订单金额100万元

**余额扣费机制：**
- 订单创建前检查应用可用余额
- 余额不足时禁止下单，返回详细错误信息
- 成功创建订单后自动扣除相应金额
- 支持事务回滚，确保数据一致性

### 📊 算法示例

**权益计算场景：** 当前日期为2024年2月15日

```
主订单：    |------ 2024年1月1日 到 2024年4月1日 ------|  (账号:5, 流量:50GB)
增购订单1：      |-- 2024年2月1日 到 2024年3月1日 --|  (账号:3, 流量:30GB) ✓ 当日生效
增购订单2：                    |-- 2024年3月1日 到 2024年5月1日 --|  (账号:2, 流量:20GB) ✗ 当日未生效
增购订单3：  |-- 2024年1月15日 到 2024年2月20日 --|  (账号:1, 流量:10GB) ✓ 当日生效

当前日期：           ↑ 2024年2月15日

注意：增购订单2和3的时间范围可以超出或独立于主订单时间范围

团队总权益 = 主订单权益 + 增购订单1权益 + 增购订单3权益
          = (5账号 + 50GB) + (3账号 + 30GB) + (1账号 + 10GB)
          = 9账号 + 90GB流量
```

**金额计算场景：** 创建3个月主订单

```
订单配置：
- 账号数量：10个
- 流量数量：100GB
- 时长：3个月

金额计算：
账号费用 = 10个 × 1000分/个/月 × 3月 = 30,000分
流量费用 = 100GB × 50分/GB/月 × 3月 = 15,000分
订单总金额 = 30,000分 + 15,000分 = 45,000分 = 450.00元

余额检查：
- 应用当前可用余额：50,000分（500.00元）
- 订单需要金额：45,000分（450.00元）
- 余额充足，允许创建订单
- 创建成功后剩余余额：5,000分（50.00元）
```

## 管理接口

### 管理员专用接口

提供了完整的管理员接口用于监控和调试：

- `GET /admin/open-platform/orders/statistics` - 获取订单统计
- `POST /admin/open-platform/orders/manual-update-vip-status` - 手动更新所有团队VIP状态
- `GET /admin/open-platform/orders/teams/:teamId/vip-validation` - 验证权益计算准确性
- `POST /admin/open-platform/orders/teams/:teamId/update-vip-status` - 更新指定团队VIP状态
- `POST /admin/open-platform/orders/scheduler/run-now` - 立即执行定时任务

## 版本历史

### v1.0.0
- 初始版本
- 支持开通、续费、增购三种订单类型
- 实现主订单唯一性约束
- 实现增购订单叠加逻辑
- 支持团队VIP状态管理

### v1.1.0
- 修复增购订单权益叠加逻辑
- 简化为当日生效判断，移除复杂的时间重叠计算
- 实现完整权益累加逻辑
- 添加定时任务服务
- 提供管理员调试接口
- 优化批量处理性能

### v1.2.0
- 简化订单类型，移除续费订单类型
- 统一主订单创建逻辑，支持首次开通和后续主订单
- 添加订单生效时间验证（不能小于当天）
- 优化主订单时间重叠检查逻辑
- 增购订单支持多个重叠订单
- 更新API接口和文档

### v1.3.0
- 添加订单金额计算功能
- 实现应用余额扣费机制
- 支持可配置的账号和流量单价
- 添加余额不足验证和错误提示
- 优化金额精度处理（使用分为单位）
- 在订单响应中包含金额信息（分和元）
- 完善订单创建日志，包含金额信息

### v1.4.0
- 调整增购订单与主订单的关系规则
- 移除增购订单时间必须在主订单范围内的限制
- 增购订单时间范围完全独立，可自由设置
- 简化验证逻辑：只需团队有任何有效主订单即可
- 移除parentOrderId字段，增购订单与主订单无直接关联
- 优化自动失效逻辑：仅在团队完全无主订单时才失效增购订单
- 更新API文档和错误消息
