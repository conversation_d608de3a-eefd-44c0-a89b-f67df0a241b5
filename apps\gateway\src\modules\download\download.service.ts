import { ForbiddenException, Inject, Injectable } from '@nestjs/common'
import { FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import captcha20230305, * as $captcha20230305 from '@alicloud/captcha20230305'
import * as $OpenApi from '@alicloud/openapi-client'
import * as $Util from '@alicloud/tea-util'
import { captchaVerifyRequestDTO, OssResourceUrlResponse } from './download.dto'
import { TlsService, TosService } from '@yxr/huoshan'

@Injectable()
export class DownloadService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly ossService: TosService,
    private readonly loggerService: TlsService
  ) {}

  async postDesktopUrl(body: captchaVerifyRequestDTO): Promise<OssResourceUrlResponse> {
    const checkResult = await this.VerifyAliyunIntelligentCaptchaAsync(
      '5aun0gpl',
      body.captchaVerifyParam
    )
    let downloadUrl = null
    if (checkResult) {
      let key = body.isChannel ? 'latest/director/' : 'latest/'
      switch (body.type) {
        case 'windows-x86':
          key += 'yixiaoer-lite-latest-win-ia32.exe'
          break
        case 'windows-x64':
          key += 'yixiaoer-lite-latest-win-x64.exe'
          break
        case 'macos-x64':
          key += 'yixiaoer-lite-latest-mac-x64.dmg'
          break
        case 'macos-arm64':
          key += 'yixiaoer-lite-latest-mac-arm64.dmg'
          break
        case 'android':
          key = 'latest/app/app-release.apk'
          break
        default:
          throw new ForbiddenException('下载失败，请稍后再试')
      }
      downloadUrl = await this.ossService.getDesktopDownloadUrl('lite-desktop', key, 10)
    }
    return {
      downloadUrl: downloadUrl,
      captchaVerifyResult: checkResult
    }
  }

  /**
   * @remarks
   * 使用AK&SK初始化账号Client
   * @returns Client
   *
   * @throws Exception
   */
  static createClient(): captcha20230305 {
    let config = new $OpenApi.Config({
      accessKeyId: process.env.CAPTCHA_ACCESS_KEY_ID,
      accessKeySecret: process.env.CAPTCHA_ACCESS_KEY_SECRET
    })
    config.endpoint = `captcha.cn-shanghai.aliyuncs.com`
    return new captcha20230305(config)
  }

  async VerifyAliyunIntelligentCaptchaAsync(
    sceneId: string,
    captchaVerifyParam: string
  ): Promise<boolean> {
    let client = DownloadService.createClient()
    let verifyCaptchaRequest = new $captcha20230305.VerifyCaptchaRequest({
      sceneId: sceneId,
      captchaVerifyParam: captchaVerifyParam
    })

    try {
      const response = await client.verifyCaptchaWithOptions(
        verifyCaptchaRequest,
        new $Util.RuntimeOptions({
          ConnectTimeout: 5000,
          ReadTimeout: 5000
        })
      )
      return response.body.result.verifyResult
    } catch (error) {
      await this.loggerService.error(null, '阿里云验证错误', { error: error.message })
      return false
    }
  }
}
