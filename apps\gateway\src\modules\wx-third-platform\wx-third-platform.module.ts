import { forwardR<PERSON>, Module } from '@nestjs/common'
import { AuthorizationService } from '../../common/security/authorization.service'
import { CommonModule } from '@yxr/common'
import { WxPublishService } from './wx-publish.service'
import { WxCallBackController } from './wx-call-back.controller'
import { WxThirdAuthController } from './wx-third-auth.controller'
import {
  ContentMongoose,
  ContentStatisticMongoose,
  GroupMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  PlatformAccountOverviewMongoose,
  PlatformAccountSummaryMongoose,
  TaskMongoose,
  TaskSetMongoose,
  TeamMongoose,
  UserMongoose
} from '@yxr/mongo'
import { WxThirdAuthService } from './wx-third-auth.service'
import { WxPublishController } from './wx-publish.controller'
import { WxBasicService } from './wx-basic.service'
import { TeamModule } from '../team/team.module'
import { WebhookModule } from '../webhook/webhook.module'
import { WxPublishEventService } from './wx-publish.event'
import { PlatformAccountModule } from '../platform/platform-account.model'
import { WxCommonService } from './wx-common.service'
import { WxIpadController } from './wx-ipad/wx-ipad.controller'
import { WxIpadService } from './wx-ipad/wx-ipad.service'
import { BrowserModule } from '../browser/browser.module'
import { KuaidailiModule } from '../kuaidaili/kuaidaiali.module'
import { WechatIpadModule } from '../wechat-ipad/wechat-ipad.module'
import { HuoshanModule } from '@yxr/huoshan'

@Module({
  imports: [
    MemberMongoose,
    TeamMongoose,
    TaskMongoose,
    TaskSetMongoose,
    ContentMongoose,
    UserMongoose,
    GroupMongoose,
    PlatformAccountMongoose,
    PlatformAccountOverviewMongoose,
    ContentStatisticMongoose,
    PlatformAccountSummaryMongoose,
    BrowserModule,
    TeamModule,
    WebhookModule,
    KuaidailiModule,
    CommonModule,
    WechatIpadModule,
    HuoshanModule,
    forwardRef(() => PlatformAccountModule)
  ],
  controllers: [WxThirdAuthController, WxPublishController, WxCallBackController, WxIpadController],
  providers: [
    WxPublishService,
    WxThirdAuthService,
    WxBasicService,
    AuthorizationService,
    WxPublishEventService,
    WxCommonService,
    WxIpadService
  ],
  exports: [WxPublishService, WxPublishService]
})
export class WxThirdPlatformModule {}
