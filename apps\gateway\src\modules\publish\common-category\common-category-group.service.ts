import { ConflictException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { CommonCategorysEntity, CommonCategorysGroupEntity } from '@yxr/mongo'
import { CategorysGroupDTO, PostCategoryGroupsRequest } from './common-category.dto'

@Injectable()
export class CommonCategorysGroupService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(CommonCategorysGroupEntity.name)
    private categorysGroupModel: Model<CommonCategorysGroupEntity>,
    @InjectModel(CommonCategorysEntity.name)
    private categorysModel: Model<CommonCategorysEntity>
  ) {}

  async postCategoryGroups(body: PostCategoryGroupsRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const group = await this.categorysGroupModel.findOne({
      name: body.name,
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    if (group) {
      throw new ConflictException('分组已存在')
    }

    const data = await this.categorysGroupModel.create({
      name: body.name,
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    return {
      id: data._id.toString(),
      name: data.name,
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 分组列表
   */
  async getCategoryGroupsAsync(): Promise<CategorysGroupDTO[]> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const data = await this.categorysGroupModel
      .find({
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(currentUserId)
      })
      .sort({ createdAt: 'desc' })

    return data.map((item) => ({
      id: item._id.toString(),
      name: item.name
    }))
  }

  /**
   * 修改分组
   * @param groupId
   * @param body
   */
  async putCategoryGroupsAsync(
    groupId: string,
    body: PostCategoryGroupsRequest
  ): Promise<CategorysGroupDTO> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    let group = await this.categorysGroupModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId),
      _id: new Types.ObjectId(groupId)
    })

    if (!group) {
      throw new NotFoundException('分组不存在')
    }

    const hasGroupName = await this.categorysGroupModel.findOne({
      name: body.name,
      _id: { $ne: groupId },
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (hasGroupName) {
      throw new ConflictException('分组名称已存在')
    }

    const data = await this.categorysGroupModel.findOneAndUpdate(
      { _id: group._id },
      {
        $set: {
          name: body.name
        }
      }, // 使用 $set 只更新传入的字段
      { returnDocument: 'after', new: true } // 返回更新后的文档
    )

    return {
      id: data._id.toString(),
      name: data.name
    }
  }

  /**
   * 删除分组
   * @param groupId
   */
  async deleteCategoryGroupsAsync(groupId: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const group = await this.categorysGroupModel.findOne({
      _id: new Types.ObjectId(groupId),
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    await this.categorysGroupModel.deleteOne({
      _id: new Types.ObjectId(group._id)
    })

    await this.categorysModel.deleteMany({
      groupId: new Types.ObjectId(group._id)
    })
  }
}
