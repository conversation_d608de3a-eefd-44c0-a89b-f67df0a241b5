import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { PlatformAccountSummaryEntity, PlatformAccountTrendEntity, RobotEntity } from '@yxr/mongo'
import { FilterQuery, Model, Types } from 'mongoose'
import { Cron } from '@nestjs/schedule'
import dayjs from 'dayjs'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { ReportTypeEnum, SendingTimeTypeEnum } from '@yxr/common'
import { RobotsService } from './robots.service'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class RobotsCornService implements OnModuleInit {
  private lockValue: string = 'handleRobots'
  private lockFridayWeeklyValue: string = 'handleFridayWeeklyRobots'
  private lockMondayWeeklyValue: string = 'handleMondayWeeklyRobots'
  private readonly lockPrefix = 'lock:'

  constructor(
    @InjectModel(RobotEntity.name)
    private robotModel: Model<RobotEntity>,
    @InjectModel(PlatformAccountTrendEntity.name)
    private platformAccountTrendModel: Model<PlatformAccountTrendEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly loggerService: TlsService,
    private readonly robotsService: RobotsService
  ) {}

  async onModuleInit() {
    // await this.mondayWeeklyCronTask()
    // await this.fridayWeeklyCronTask()
    // await this.dayReportCronTask()
  }

  /**
   * 日报发送
   * 每天9:00 执行前一天数据
   */
  @Cron('0 0 9 * * *', {
    name: 'dayReportCron',
    timeZone: 'Asia/Shanghai'
  })
  async dayReportCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        await this.createReportTask(ReportTypeEnum.Day)
      } catch (e) {
        await this.loggerService.error(null, '日报定时处理失败', { error: e })
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  /**
   * 周五周报发送
   * 每周五18:00执行本周数据
   */
  @Cron('0 0 18 * * 5', {
    name: 'fridayWeeklyCron',
    timeZone: 'Asia/Shanghai'
  })
  async fridayWeeklyCronTask() {
    if (await this.acquireLock(this.lockFridayWeeklyValue, 60)) {
      try {
        await this.createReportTask(ReportTypeEnum.Weekly, SendingTimeTypeEnum.Friday)
      } catch (e) {
        await this.loggerService.error(null, '周五周报定时处理失败', { error: e })
      } finally {
        await this.releaseLock(this.lockFridayWeeklyValue)
      }
    }
  }

  /**
   * 周一周报发送
   * 每周一9:00执行上周数据
   */
  @Cron('0 0 9 * * 1', {
    name: 'mondayWeeklyCron',
    timeZone: 'Asia/Shanghai'
  })
  async mondayWeeklyCronTask() {
    if (await this.acquireLock(this.lockMondayWeeklyValue, 60)) {
      try {
        await this.createReportTask(ReportTypeEnum.Weekly, SendingTimeTypeEnum.Monday)
      } catch (e) {
        await this.loggerService.error(null, '周一周报定时处理失败', { error: e })
      } finally {
        await this.releaseLock(this.lockMondayWeeklyValue)
      }
    }
  }

  async createReportTask(reportType: ReportTypeEnum, sendingTimeType?: SendingTimeTypeEnum) {
    const batchSize = 5 //每批处理5个机器人，防止平台限流
    let query: FilterQuery<RobotEntity> = {
      reportType: reportType
    }
    const totalRobots = await this.robotModel.countDocuments(query)
    const totalBatches = Math.ceil(totalRobots / batchSize)

    const fields = [
      'fansTotal',
      'readTotal',
      'playTotal',
      'commentsTotal',
      'likesTotal',
      'favoritesTotal'
    ]
    // 动态构建 $group 语句
    const groupStage: any = {
      _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
    }
    fields.forEach((item) => {
      groupStage[item] = { $sum: `$${item}` }
    })

    // 前一天时间
    let startTime = dayjs().tz('Asia/Shanghai').subtract(2, 'day').format('YYYY-MM-DD')
    let endTime = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
    // 默认上一周
    let weekStart = dayjs().tz('Asia/Shanghai').subtract(1, 'week').startOf('week').add(1, 'day')
    if (reportType === ReportTypeEnum.Day) {
      query.reportType = reportType
    } else if (reportType === ReportTypeEnum.Weekly) {
      query.reportType = reportType
      query.sendTimeType = sendingTimeType
      if (sendingTimeType === SendingTimeTypeEnum.Friday) {
        // 上周六
        startTime = weekStart.add(5, 'day').format('YYYY-MM-DD')
        // 本周五
        endTime = weekStart.add(11, 'day').format('YYYY-MM-DD')
      } else {
        // 上周日
        endTime = weekStart.add(6, 'day').format('YYYY-MM-DD')
      }
    }
    await this.loggerService.info(null, '机器人定时任务执行', {
      reportType,
      sendingTimeType,
      startTime,
      endTime
    })

    try {
      for (let i = 0; i < totalBatches; i++) {
        const robots = await this.robotModel
          .find(query)
          .skip(i * batchSize)
          .limit(batchSize)
          .lean()
        for (const robot of robots) {
          try {
            const current = {}
            const increments = {}
            let res = []
            if (
              reportType === ReportTypeEnum.Weekly &&
              sendingTimeType === SendingTimeTypeEnum.Friday
            ) {
              res = await this.platformAccountTrendModel.aggregate([
                {
                  $match: {
                    teamId: new Types.ObjectId(robot.teamId),
                    createTime: startTime
                  }
                },
                {
                  $group: groupStage
                }
              ])

              const todayGroupStage: any = {
                _id: null
              }
              fields.forEach((ff) => {
                todayGroupStage[ff] = { $sum: `$${ff}` }
              })
              //周五数据还未归档，实时统计
              const todayStatistic = await this.platformAccountSummaryModel.aggregate([
                {
                  $match: {
                    teamId: new Types.ObjectId(robot.teamId)
                  }
                },
                { $group: todayGroupStage }
              ])
              const statResult = todayStatistic[0] || {}
              delete statResult._id // 移除原_id
              res.push({
                _id: endTime,
                ...statResult
              })
            } else {
              res = await this.platformAccountTrendModel.aggregate([
                {
                  $match: {
                    teamId: new Types.ObjectId(robot.teamId),
                    $or: [{ createTime: startTime }, { createTime: endTime }]
                  }
                },
                {
                  $group: groupStage
                }
              ])
            }

            const dataInfo = res.find((item) => item._id === endTime)
            const incInfo = res.find((item) => item._id === startTime) ?? {}
            fields.forEach((item) => {
              current[item] = dataInfo?.[item] ?? 0
              incInfo[item] = incInfo?.[item] ?? current[item]
              increments[item] = current[item] - incInfo[item]
            })
            await this.robotsService.sendRobotMessage(
              robot.teamId.toString(),
              robot.robotType,
              reportType,
              robot.sendTimeType,
              robot.webhookUrl,
              {
                current,
                increments
              }
            )
          } catch (error) {
            await this.loggerService.error(null, '机器人消息发送失败', {
              robotId: robot._id,
              teamId: robot.teamId,
              error: error.message
            })
            continue
          }
        }

        // 分页间延迟 - 每处理完一页后等待
        if (i < totalBatches - 1) {
          await this.sleep(2000) // 分页间间隔2秒
        }
      }
    } catch (error) {
      await this.loggerService.error(null, '机器人定时任务执行异常', {
        error: error.message
      })
    }
  }

  // 添加 sleep 方法
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
