import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { ContentType, HistoryStatisticTypeEnum } from 'packages/common'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class HistoryStatisticEntity {
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  userId?: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  platformName?: string

  @Prop({
    type: String,
    enum: ContentType,
    required: false
  })
  type?: ContentType

  @Prop({
    type: String,
    enum: HistoryStatisticTypeEnum,
    required: true
  })
  statisticType: HistoryStatisticTypeEnum

  @Prop({
    type: Types.Map
  })
  historyStatistic?: unknown

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const HistoryStatisticSchema: ModelDefinition = {
  name: HistoryStatisticEntity.name,
  schema: SchemaFactory.createForClass(HistoryStatisticEntity)
}

export const HistoryStatisticMongoose = MongooseModule.forFeature([HistoryStatisticSchema])
