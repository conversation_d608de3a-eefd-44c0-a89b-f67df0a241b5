import { AxiosError, AxiosResponse } from 'axios'
import { OverseasContext } from '../providers/types'

/**
 * 海外平台API错误代码枚举
 */
export enum RemoteApiErrorCodes {
  // 网络相关错误
  NetworkError = 'NETWORK_ERROR',
  Timeout = 'TIMEOUT',
  ConnectionRefused = 'CONNECTION_REFUSED',
  ProxyError = 'PROXY_ERROR',

  // HTTP状态码错误
  RequestInvalid = 'REQUEST_INVALID',
  Unauthorized = 'UNAUTHORIZED',
  Forbidden = 'FORBIDDEN',
  NotFound = 'NOT_FOUND',
  ServerError = 'SERVER_ERROR',

  // API限流错误
  RateLimitExceeded = 'RATE_LIMIT_EXCEEDED',
  QuotaExceeded = 'QUOTA_EXCEEDED',

  // 认证授权错误
  AccessTokenInvalid = 'ACCESS_TOKEN_INVALID',
  AccessTokenExpired = 'ACCESS_TOKEN_EXPIRED',
  ScopeNotAuthorized = 'SCOPE_NOT_AUTHORIZED',
  RefreshTokenInvalid = 'REFRESH_TOKEN_INVALID',

  // 平台特定错误
  ContentViolation = 'CONTENT_VIOLATION',
  MediaUploadFailed = 'MEDIA_UPLOAD_FAILED',
  AccountSuspended = 'ACCOUNT_SUSPENDED',
  FeatureNotAvailable = 'FEATURE_NOT_AVAILABLE',

  // 业务逻辑错误
  RequestParametersIncorrect = 'REQUEST_PARAMETERS_INCORRECT',
  ResourceNotFound = 'RESOURCE_NOT_FOUND',
  DuplicateContent = 'DUPLICATE_CONTENT',

  // 未知错误
  Unknown = 'UNKNOWN',
  UnknownRemoteApiError = 'UNKNOWN_REMOTE_API_ERROR'
}

/**
 * 错误严重程度
 */
export enum ErrorSeverity {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  Critical = 'critical'
}

/**
 * 错误分类
 */
export enum ErrorCategory {
  Network = 'network',
  Authentication = 'authentication',
  Authorization = 'authorization',
  RateLimit = 'rate_limit',
  Business = 'business',
  Platform = 'platform',
  Unknown = 'unknown'
}

/**
 * 重试策略配置
 */
export interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  retryableErrors: RemoteApiErrorCodes[]
}

/**
 * 错误信息本地化映射
 */
export const ERROR_MESSAGES: Record<RemoteApiErrorCodes, string> = {
  [RemoteApiErrorCodes.NetworkError]: '网络连接异常，请检查网络设置',
  [RemoteApiErrorCodes.Timeout]: '请求超时，请稍后重试',
  [RemoteApiErrorCodes.ConnectionRefused]: '连接被拒绝，请检查网络或代理设置',
  [RemoteApiErrorCodes.ProxyError]: '代理服务器连接失败',

  [RemoteApiErrorCodes.RequestInvalid]: '请求参数无效',
  [RemoteApiErrorCodes.Unauthorized]: '认证失败，请重新登录',
  [RemoteApiErrorCodes.Forbidden]: '权限不足，无法执行此操作',
  [RemoteApiErrorCodes.NotFound]: '请求的资源不存在',
  [RemoteApiErrorCodes.ServerError]: '服务器内部错误，请稍后重试',

  [RemoteApiErrorCodes.RateLimitExceeded]: '请求频率过高，请稍后重试',
  [RemoteApiErrorCodes.QuotaExceeded]: '配额已用完，请稍后重试',

  [RemoteApiErrorCodes.AccessTokenInvalid]: '访问令牌无效，请重新授权',
  [RemoteApiErrorCodes.AccessTokenExpired]: '访问令牌已过期，请重新授权',
  [RemoteApiErrorCodes.ScopeNotAuthorized]: '权限范围不足，请重新授权',
  [RemoteApiErrorCodes.RefreshTokenInvalid]: '刷新令牌无效，请重新授权',

  [RemoteApiErrorCodes.ContentViolation]: '内容违反平台规定',
  [RemoteApiErrorCodes.MediaUploadFailed]: '媒体文件上传失败',
  [RemoteApiErrorCodes.AccountSuspended]: '账号已被暂停',
  [RemoteApiErrorCodes.FeatureNotAvailable]: '功能暂不可用',

  [RemoteApiErrorCodes.RequestParametersIncorrect]: '请求参数错误',
  [RemoteApiErrorCodes.ResourceNotFound]: '资源未找到',
  [RemoteApiErrorCodes.DuplicateContent]: '内容重复',

  [RemoteApiErrorCodes.Unknown]: '未知错误',
  [RemoteApiErrorCodes.UnknownRemoteApiError]: '未知的远程API错误'
}

/**
 * 错误分类映射
 */
export const ERROR_CATEGORIES: Record<RemoteApiErrorCodes, ErrorCategory> = {
  [RemoteApiErrorCodes.NetworkError]: ErrorCategory.Network,
  [RemoteApiErrorCodes.Timeout]: ErrorCategory.Network,
  [RemoteApiErrorCodes.ConnectionRefused]: ErrorCategory.Network,
  [RemoteApiErrorCodes.ProxyError]: ErrorCategory.Network,

  [RemoteApiErrorCodes.RequestInvalid]: ErrorCategory.Business,
  [RemoteApiErrorCodes.Unauthorized]: ErrorCategory.Authentication,
  [RemoteApiErrorCodes.Forbidden]: ErrorCategory.Authorization,
  [RemoteApiErrorCodes.NotFound]: ErrorCategory.Business,
  [RemoteApiErrorCodes.ServerError]: ErrorCategory.Platform,

  [RemoteApiErrorCodes.RateLimitExceeded]: ErrorCategory.RateLimit,
  [RemoteApiErrorCodes.QuotaExceeded]: ErrorCategory.RateLimit,

  [RemoteApiErrorCodes.AccessTokenInvalid]: ErrorCategory.Authentication,
  [RemoteApiErrorCodes.AccessTokenExpired]: ErrorCategory.Authentication,
  [RemoteApiErrorCodes.ScopeNotAuthorized]: ErrorCategory.Authorization,
  [RemoteApiErrorCodes.RefreshTokenInvalid]: ErrorCategory.Authentication,

  [RemoteApiErrorCodes.ContentViolation]: ErrorCategory.Platform,
  [RemoteApiErrorCodes.MediaUploadFailed]: ErrorCategory.Platform,
  [RemoteApiErrorCodes.AccountSuspended]: ErrorCategory.Platform,
  [RemoteApiErrorCodes.FeatureNotAvailable]: ErrorCategory.Platform,

  [RemoteApiErrorCodes.RequestParametersIncorrect]: ErrorCategory.Business,
  [RemoteApiErrorCodes.ResourceNotFound]: ErrorCategory.Business,
  [RemoteApiErrorCodes.DuplicateContent]: ErrorCategory.Business,

  [RemoteApiErrorCodes.Unknown]: ErrorCategory.Unknown,
  [RemoteApiErrorCodes.UnknownRemoteApiError]: ErrorCategory.Unknown
}

/**
 * 错误严重程度映射
 */
export const ERROR_SEVERITIES: Record<RemoteApiErrorCodes, ErrorSeverity> = {
  [RemoteApiErrorCodes.NetworkError]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.Timeout]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.ConnectionRefused]: ErrorSeverity.High,
  [RemoteApiErrorCodes.ProxyError]: ErrorSeverity.High,

  [RemoteApiErrorCodes.RequestInvalid]: ErrorSeverity.Low,
  [RemoteApiErrorCodes.Unauthorized]: ErrorSeverity.High,
  [RemoteApiErrorCodes.Forbidden]: ErrorSeverity.High,
  [RemoteApiErrorCodes.NotFound]: ErrorSeverity.Low,
  [RemoteApiErrorCodes.ServerError]: ErrorSeverity.Critical,

  [RemoteApiErrorCodes.RateLimitExceeded]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.QuotaExceeded]: ErrorSeverity.High,

  [RemoteApiErrorCodes.AccessTokenInvalid]: ErrorSeverity.High,
  [RemoteApiErrorCodes.AccessTokenExpired]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.ScopeNotAuthorized]: ErrorSeverity.High,
  [RemoteApiErrorCodes.RefreshTokenInvalid]: ErrorSeverity.High,

  [RemoteApiErrorCodes.ContentViolation]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.MediaUploadFailed]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.AccountSuspended]: ErrorSeverity.Critical,
  [RemoteApiErrorCodes.FeatureNotAvailable]: ErrorSeverity.Low,

  [RemoteApiErrorCodes.RequestParametersIncorrect]: ErrorSeverity.Low,
  [RemoteApiErrorCodes.ResourceNotFound]: ErrorSeverity.Low,
  [RemoteApiErrorCodes.DuplicateContent]: ErrorSeverity.Low,

  [RemoteApiErrorCodes.Unknown]: ErrorSeverity.Medium,
  [RemoteApiErrorCodes.UnknownRemoteApiError]: ErrorSeverity.Medium
}

/**
 * 默认重试配置
 */
export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffMultiplier: 2,
  retryableErrors: [
    RemoteApiErrorCodes.NetworkError,
    RemoteApiErrorCodes.Timeout,
    RemoteApiErrorCodes.ServerError,
    RemoteApiErrorCodes.RateLimitExceeded,
    RemoteApiErrorCodes.AccessTokenExpired
  ]
}

/**
 * 扩展的海外平台上下文，包含平台信息
 */
export interface ExtendedOverseasContext extends OverseasContext {
  platform: string
}

/**
 * 请求信息接口
 */
export interface RequestInfo {
  url: string
  method?: string
  headers?: Record<string, any>
  data?: any
}

/**
 * 响应信息接口
 */
export interface ResponseInfo {
  status: number
  statusText: string
  headers?: Record<string, any>
  data?: any
}

/**
 * 海外平台API错误类
 */
export class RemoteApiError extends Error {
  public readonly platform: string
  public readonly errorCode: RemoteApiErrorCodes
  public readonly category: ErrorCategory
  public readonly severity: ErrorSeverity
  public readonly isRetryable: boolean
  public readonly localizedMessage: string
  public readonly requestInfo: RequestInfo
  public readonly responseInfo: ResponseInfo
  public readonly context: ExtendedOverseasContext
  public readonly timestamp: Date
  public readonly details: Record<string, any>

  constructor(
    platform: string,
    errorCode: RemoteApiErrorCodes,
    details: Record<string, any>,
    requestInfo: RequestInfo,
    responseInfo: ResponseInfo,
    context: ExtendedOverseasContext,
    customMessage?: string
  ) {
    const localizedMessage = customMessage || ERROR_MESSAGES[errorCode] || details.message || '未知错误'
    super(localizedMessage)

    this.name = 'RemoteApiError'
    this.platform = platform
    this.errorCode = errorCode
    this.category = ERROR_CATEGORIES[errorCode] || ErrorCategory.Unknown
    this.severity = ERROR_SEVERITIES[errorCode] || ErrorSeverity.Medium
    this.isRetryable = DEFAULT_RETRY_CONFIG.retryableErrors.includes(errorCode)
    this.localizedMessage = localizedMessage
    this.requestInfo = requestInfo
    this.responseInfo = responseInfo
    this.context = context
    this.timestamp = new Date()
    this.details = details

    // 确保错误堆栈正确
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, RemoteApiError)
    }
  }

  /**
   * 获取错误的完整信息
   */
  getFullErrorInfo() {
    return {
      name: this.name,
      message: this.message,
      platform: this.platform,
      errorCode: this.errorCode,
      category: this.category,
      severity: this.severity,
      isRetryable: this.isRetryable,
      localizedMessage: this.localizedMessage,
      timestamp: this.timestamp,
      context: this.context,
      requestInfo: this.requestInfo,
      responseInfo: this.responseInfo,
      details: this.details,
      stack: this.stack
    }
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return this.getFullErrorInfo()
  }
}

/**
 * 从Axios响应中提取请求信息
 */
export function extractRequestInfo(response: AxiosResponse): RequestInfo {
  const config = response.config
  return {
    url: config.url || '',
    method: config.method?.toUpperCase(),
    headers: config.headers,
    data: config.data
  }
}

/**
 * 从Axios响应中提取响应信息
 */
export function extractResponseInfo(response: AxiosResponse): ResponseInfo {
  return {
    status: response.status,
    statusText: response.statusText,
    headers: response.headers,
    data: response.data
  }
}

/**
 * 海外平台API错误处理器接口
 */
export interface OverseasApiErrorHandler {
  /**
   * 处理网络错误
   */
  handleNetworkError(error: AxiosError, context: ExtendedOverseasContext): Promise<never>

  /**
   * 处理HTTP状态码错误
   */
  handleHttpError(error: AxiosError, context: ExtendedOverseasContext): Promise<never>

  /**
   * 处理平台特定的业务错误
   */
  handleBusinessError(response: AxiosResponse, context: ExtendedOverseasContext): Promise<never>

  /**
   * 记录错误日志
   */
  logError(error: RemoteApiError): void
}

/**
 * 默认的海外平台API错误处理器实现
 */
export class DefaultOverseasApiErrorHandler implements OverseasApiErrorHandler {
  /**
   * 处理网络错误
   */
  async handleNetworkError(error: AxiosError, context: ExtendedOverseasContext): Promise<never> {
    let errorCode: RemoteApiErrorCodes
    let message: string

    if (error.code === 'ECONNABORTED') {
      errorCode = RemoteApiErrorCodes.Timeout
      message = '请求超时'
    } else if (error.code === 'ECONNREFUSED') {
      errorCode = RemoteApiErrorCodes.ConnectionRefused
      message = '连接被拒绝'
    } else if (error.code === 'ENOTFOUND') {
      errorCode = RemoteApiErrorCodes.NetworkError
      message = 'DNS解析失败'
    } else if (error.message.includes('proxy')) {
      errorCode = RemoteApiErrorCodes.ProxyError
      message = '代理连接失败'
    } else {
      errorCode = RemoteApiErrorCodes.NetworkError
      message = error.message || '网络连接异常'
    }

    const requestInfo: RequestInfo = {
      url: error.config?.url || '',
      method: error.config?.method?.toUpperCase(),
      headers: error.config?.headers,
      data: error.config?.data
    }

    const responseInfo: ResponseInfo = {
      status: 0,
      statusText: 'Network Error',
      data: null
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      { message, originalError: error.message },
      requestInfo,
      responseInfo,
      context
    )

    this.logError(apiError)
    throw apiError
  }

  /**
   * 处理HTTP状态码错误
   */
  async handleHttpError(error: AxiosError, context: ExtendedOverseasContext): Promise<never> {
    const response = error.response!
    const status = response.status
    let errorCode: RemoteApiErrorCodes

    // 根据HTTP状态码映射错误类型
    if (status === 400) {
      errorCode = RemoteApiErrorCodes.RequestInvalid
    } else if (status === 401) {
      errorCode = RemoteApiErrorCodes.Unauthorized
    } else if (status === 403) {
      errorCode = RemoteApiErrorCodes.Forbidden
    } else if (status === 404) {
      errorCode = RemoteApiErrorCodes.NotFound
    } else if (status === 429) {
      errorCode = RemoteApiErrorCodes.RateLimitExceeded
    } else if (status >= 500) {
      errorCode = RemoteApiErrorCodes.ServerError
    } else {
      errorCode = RemoteApiErrorCodes.Unknown
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      {
        message: error.message,
        httpStatus: status,
        responseData: response.data
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    this.logError(apiError)
    throw apiError
  }

  /**
   * 处理平台特定的业务错误
   */
  async handleBusinessError(response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> {
    // 这个方法需要在具体的平台实现中重写
    // 因为每个平台的业务错误格式不同
    const apiError = new RemoteApiError(
      context.platform,
      RemoteApiErrorCodes.UnknownRemoteApiError,
      {
        message: '平台业务错误',
        responseData: response.data
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    this.logError(apiError)
    throw apiError
  }

  /**
   * 记录错误日志
   */
  logError(error: RemoteApiError): void {
    const logLevel = this.getLogLevel(error.severity)
    const logMessage = `[${error.platform.toUpperCase()}] ${error.localizedMessage}`

    console[logLevel](`${logMessage}`, {
      errorCode: error.errorCode,
      category: error.category,
      severity: error.severity,
      platform: error.platform,
      context: error.context,
      requestInfo: error.requestInfo,
      responseInfo: error.responseInfo,
      details: error.details,
      timestamp: error.timestamp
    })
  }

  /**
   * 根据错误严重程度获取日志级别
   */
  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.Low:
        return 'log'
      case ErrorSeverity.Medium:
        return 'warn'
      case ErrorSeverity.High:
      case ErrorSeverity.Critical:
        return 'error'
      default:
        return 'warn'
    }
  }
}

/**
 * 创建增强的错误处理器
 */
export function createEnhancedErrorHandler(
  context: ExtendedOverseasContext,
  customHandler?: Partial<OverseasApiErrorHandler>
): { onResponseError: (error: AxiosError) => Promise<never> } {
  const handler = new DefaultOverseasApiErrorHandler()

  // 合并自定义处理器
  if (customHandler) {
    Object.assign(handler, customHandler)
  }

  return {
    onResponseError: async (error: AxiosError) => {
      if (error.response) {
        // HTTP错误（有响应）
        return handler.handleHttpError(error, context)
      } else {
        // 网络错误（无响应）
        return handler.handleNetworkError(error, context)
      }
    }
  }
}

/**
 * 重试机制装饰器
 */
export function withRetry<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  config: Partial<RetryConfig> = {}
): (...args: T) => Promise<R> {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config }

  return async (...args: T): Promise<R> => {
    let lastError: Error
    let attempt = 0

    while (attempt <= retryConfig.maxRetries) {
      try {
        return await fn(...args)
      } catch (error) {
        lastError = error as Error
        attempt++

        // 检查是否是可重试的错误
        if (error instanceof RemoteApiError) {
          if (!error.isRetryable || attempt > retryConfig.maxRetries) {
            throw error
          }

          // 计算延迟时间（指数退避）
          const delay = Math.min(
            retryConfig.baseDelay * Math.pow(retryConfig.backoffMultiplier, attempt - 1),
            retryConfig.maxDelay
          )

          console.warn(`[Retry] 第${attempt}次重试失败，${delay}ms后重试: ${error.localizedMessage}`)
          await sleep(delay)
        } else {
          // 非RemoteApiError，不重试
          throw error
        }
      }
    }

    throw lastError!
  }
}

/**
 * 睡眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 平台特定错误处理器工厂
 */
export class PlatformErrorHandlerFactory {
  /**
   * 创建Facebook错误处理器
   */
  static createFacebookHandler(): Partial<OverseasApiErrorHandler> {
    return {
      handleBusinessError: async (response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> => {
        const data = response.data

        if (data.error) {
          let errorCode: RemoteApiErrorCodes

          switch (data.error.code) {
            case 190: // Invalid access token
              errorCode = RemoteApiErrorCodes.AccessTokenInvalid
              break
            case 102: // Session key invalid
              errorCode = RemoteApiErrorCodes.AccessTokenExpired
              break
            case 4: // Application request limit reached
            case 17: // User request limit reached
              errorCode = RemoteApiErrorCodes.RateLimitExceeded
              break
            case 200: // Permissions error
              errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
              break
            default:
              errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
          }

          const apiError = new RemoteApiError(
            context.platform,
            errorCode,
            {
              message: data.error.message,
              facebookErrorCode: data.error.code,
              facebookErrorType: data.error.type
            },
            extractRequestInfo(response),
            extractResponseInfo(response),
            context
          )

          throw apiError
        }

        // 如果没有错误，抛出一个通用错误（这个方法应该只在有错误时调用）
        throw new RemoteApiError(
          context.platform,
          RemoteApiErrorCodes.UnknownRemoteApiError,
          { message: '未知业务错误' },
          extractRequestInfo(response),
          extractResponseInfo(response),
          context
        )
      }
    }
  }

  /**
   * 创建Instagram错误处理器
   */
  static createInstagramHandler(): Partial<OverseasApiErrorHandler> {
    return {
      handleBusinessError: async (response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> => {
        const data = response.data

        if (data.error) {
          let errorCode: RemoteApiErrorCodes

          switch (data.error.code) {
            case 190:
              errorCode = RemoteApiErrorCodes.AccessTokenInvalid
              break
            case 100:
              errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
              break
            case 368:
              errorCode = RemoteApiErrorCodes.ContentViolation
              break
            default:
              errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
          }

          const apiError = new RemoteApiError(
            context.platform,
            errorCode,
            {
              message: data.error.message,
              instagramErrorCode: data.error.code,
              instagramErrorType: data.error.type
            },
            extractRequestInfo(response),
            extractResponseInfo(response),
            context
          )

          throw apiError
        }

        throw new RemoteApiError(
          context.platform,
          RemoteApiErrorCodes.UnknownRemoteApiError,
          { message: '未知业务错误' },
          extractRequestInfo(response),
          extractResponseInfo(response),
          context
        )
      }
    }
  }

  /**
   * 创建Twitter错误处理器
   */
  static createTwitterHandler(): Partial<OverseasApiErrorHandler> {
    return {
      handleBusinessError: async (response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> => {
        const data = response.data

        if (data.errors && data.errors.length > 0) {
          const error = data.errors[0]
          let errorCode: RemoteApiErrorCodes

          switch (error.code) {
            case 89: // Invalid or expired token
              errorCode = RemoteApiErrorCodes.AccessTokenInvalid
              break
            case 88: // Rate limit exceeded
              errorCode = RemoteApiErrorCodes.RateLimitExceeded
              break
            case 187: // Status is a duplicate
              errorCode = RemoteApiErrorCodes.DuplicateContent
              break
            case 326: // Account temporarily locked
              errorCode = RemoteApiErrorCodes.AccountSuspended
              break
            default:
              errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
          }

          const apiError = new RemoteApiError(
            context.platform,
            errorCode,
            {
              message: error.message,
              twitterErrorCode: error.code
            },
            extractRequestInfo(response),
            extractResponseInfo(response),
            context
          )

          throw apiError
        }

        throw new RemoteApiError(
          context.platform,
          RemoteApiErrorCodes.UnknownRemoteApiError,
          { message: '未知业务错误' },
          extractRequestInfo(response),
          extractResponseInfo(response),
          context
        )
      }
    }
  }

  /**
   * 创建TikTok错误处理器
   */
  static createTikTokHandler(): Partial<OverseasApiErrorHandler> {
    return {
      handleBusinessError: async (response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> => {
        const data = response.data

        if (data.code !== 0) {
          let errorCode: RemoteApiErrorCodes

          switch (data.code) {
            case 40001:
              errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
              break
            case 40102:
            case 40104:
            case 40105:
              errorCode = RemoteApiErrorCodes.AccessTokenInvalid
              break
            case 40000:
            case 40002:
            case 40006:
            case 40010:
            case 40011:
            case 40053:
            case 40051:
              errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
              break
            default:
              errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
          }

          const apiError = new RemoteApiError(
            context.platform,
            errorCode,
            {
              message: data.message,
              tiktokErrorCode: data.code,
              logId: data.request_id
            },
            extractRequestInfo(response),
            extractResponseInfo(response),
            context
          )

          throw apiError
        }

        throw new RemoteApiError(
          context.platform,
          RemoteApiErrorCodes.UnknownRemoteApiError,
          { message: '未知业务错误' },
          extractRequestInfo(response),
          extractResponseInfo(response),
          context
        )
      }
    }
  }

  /**
   * 创建YouTube错误处理器
   */
  static createYouTubeHandler(): Partial<OverseasApiErrorHandler> {
    return {
      handleBusinessError: async (response: AxiosResponse, context: ExtendedOverseasContext): Promise<never> => {
        const data = response.data

        if (data.error) {
          let errorCode: RemoteApiErrorCodes

          switch (data.error.code) {
            case 401:
              errorCode = RemoteApiErrorCodes.AccessTokenInvalid
              break
            case 403:
              if (data.error.message.includes('quota')) {
                errorCode = RemoteApiErrorCodes.QuotaExceeded
              } else {
                errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
              }
              break
            case 400:
              errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
              break
            default:
              errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
          }

          const apiError = new RemoteApiError(
            context.platform,
            errorCode,
            {
              message: data.error.message,
              youtubeErrorCode: data.error.code,
              youtubeErrors: data.error.errors
            },
            extractRequestInfo(response),
            extractResponseInfo(response),
            context
          )

          throw apiError
        }

        throw new RemoteApiError(
          context.platform,
          RemoteApiErrorCodes.UnknownRemoteApiError,
          { message: '未知业务错误' },
          extractRequestInfo(response),
          extractResponseInfo(response),
          context
        )
      }
    }
  }
}
