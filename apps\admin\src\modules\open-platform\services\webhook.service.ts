import {
  Injectable,
  Logger,
} from '@nestjs/common'
import { firstValueFrom, timeout, catchError } from 'rxjs'
import { AxiosError } from 'axios'
import { HttpService } from '@nestjs/axios'
import { WebhookVerificationRequestDto, WebhookVerificationResponseDto } from '../dto/application.dto'

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name)
  private readonly VERIFICATION_TIMEOUT = 5000 // 5秒超时
  private readonly MAX_RETRIES = 3 // 最大重试次数

  constructor(private readonly httpService: HttpService) {}

  /**
   * 验证Webhook URL的可访问性
   */
  async verifyWebhookUrl(webhookUrl: string): Promise<{
    success: boolean
    message: string
    statusCode?: number
  }> {
    const challenge = this.generateChallenge()
    const verificationData: WebhookVerificationRequestDto = {
      type: 'webhook_verification',
      timestamp: Date.now(),
      challenge
    }

    this.logger.log(`开始验证Webhook URL: ${webhookUrl}, challenge: ${challenge}`)

    let lastError: any
    
    // 重试机制
    for (let attempt = 1; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        this.logger.log(`第${attempt}次尝试验证Webhook URL: ${webhookUrl}`)
        
        const response = await firstValueFrom(
          this.httpService.post(webhookUrl, verificationData, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'YiXiaoEr-Webhook-Verifier/1.0'
            },
            timeout: this.VERIFICATION_TIMEOUT
          }).pipe(
            timeout(this.VERIFICATION_TIMEOUT),
            catchError((error: AxiosError) => {
              throw error
            })
          )
        )

        // 验证响应格式
        const responseData = (response as { data: WebhookVerificationResponseDto }).data
        
        if (!this.isValidVerificationResponse(responseData)) {
          const message = '响应格式不正确，期望格式: { success: true, challenge: "原challenge值" }'
          this.logger.warn(`Webhook验证失败 - ${message}, 响应数据: ${JSON.stringify(responseData)}`)
          return {
            success: false,
            message,
            statusCode: (response as { status: number }).status
          }
        }

        // 验证challenge值
        if (responseData.challenge !== challenge) {
          const message = `Challenge值不匹配，期望: ${challenge}, 实际: ${responseData.challenge}`
          this.logger.warn(`Webhook验证失败 - ${message}`)
          return {
            success: false,
            message,
            statusCode: (response as { status: number }).status
          }
        }

        // 验证成功标志
        if (!responseData.success) {
          const message = '响应中success字段为false'
          this.logger.warn(`Webhook验证失败 - ${message}`)
          return {
            success: false,
            message,
            statusCode: (response as { status: number }).status
          }
        }

        this.logger.log(`Webhook验证成功: ${webhookUrl}`)
        return {
          success: true,
          message: '验证成功',
          statusCode: (response as { status: number }).status
        }

      } catch (error) {
        lastError = error
        this.logger.warn(`第${attempt}次Webhook验证失败: ${error.message}`)
        
        // 如果不是最后一次尝试，等待一段时间后重试
        if (attempt < this.MAX_RETRIES) {
          await this.sleep(1000 * attempt) // 递增等待时间
        }
      }
    }

    // 所有重试都失败了，返回最后的错误信息
    return this.handleVerificationError(lastError, webhookUrl)
  }

  /**
   * 验证响应数据格式是否正确
   */
  private isValidVerificationResponse(data: any): data is WebhookVerificationResponseDto {
    return (
      data &&
      typeof data === 'object' &&
      typeof data.success === 'boolean' &&
      typeof data.challenge === 'string'
    )
  }

  /**
   * 生成验证挑战值
   */
  private generateChallenge(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  /**
   * 处理验证错误
   */
  private handleVerificationError(error: any, webhookUrl: string): {
    success: boolean
    message: string
    statusCode?: number
  } {
    this.logger.error(`Webhook验证最终失败: ${webhookUrl}, 错误: ${error.message}`, error.stack)

    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
      return {
        success: false,
        message: `网络连接失败: ${error.message}`,
        statusCode: 0
      }
    }

    if (error.code === 'ETIMEDOUT' || error.name === 'TimeoutError') {
      return {
        success: false,
        message: `请求超时，服务器未在${this.VERIFICATION_TIMEOUT / 1000}秒内响应`,
        statusCode: 0
      }
    }

    if (error.response) {
      const status = error.response.status
      const statusText = error.response.statusText || '未知错误'
      
      if (status >= 400 && status < 500) {
        return {
          success: false,
          message: `客户端错误: ${status} ${statusText}`,
          statusCode: status
        }
      }
      
      if (status >= 500) {
        return {
          success: false,
          message: `服务器错误: ${status} ${statusText}`,
          statusCode: status
        }
      }
      
      return {
        success: false,
        message: `HTTP错误: ${status} ${statusText}`,
        statusCode: status
      }
    }

    return {
      success: false,
      message: `未知错误: ${error.message}`,
      statusCode: 0
    }
  }

  /**
   * 等待指定毫秒数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
