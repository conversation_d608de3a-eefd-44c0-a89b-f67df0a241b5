import { Injectable } from '@nestjs/common'
import { CreateTaskInputDto } from './dto/create-task.dto'
import { LookupCategoriesInputDto } from './dto/lookup-categories.dto'
import { LookupTagsInputDto } from './dto/lookup-tags.dto'
import { Douyin, Shipinhao } from '@yixiaoer/platform-service'

@Injectable()
export class CreatorService {
  async createTask(input: CreateTaskInputDto) {
    // TODO: 实现创建发布任务的业务逻辑
    console.log('Creating task with data:', input)
    return { success: true }
  }

  async lookupCategories(input: LookupCategoriesInputDto) {
    throw new Error('Method not implemented.')
  }

  async lookupTags(input: LookupTagsInputDto) {

    throw new Error('Method not implemented.')

    try {
      // const douyintopic = await Douyin.getDouyinTopics('cookiea=123;cookie2=234asdas', '搞笑')
      // console.log('抖音话题', douyintopic)
      //
      // // const eventcallBack = new EventEmitter()
      // // eventcallBack.on('publishing', (percent, msg, taskId) => {
      // // })
      //
      // const pubresult = await Douyin.publishVideo('cookiea=123;cookie2=234asdas', {
      //   video: { localPath: 'd://data//123.mp4' },
      //   cover: { pathOrUrl: 'http://www.baidu.com/123.png' }
      // }, eventcallBack)
      //
      // if (pubresult.code === 0) {
      //   console.log('发布成功', pubresult.publishId)
      // }

      //   new PublishService(params)
      //     .GetMediaTopicInfo(params)
      //     .then((data) => {
      //       console.debug('返回全名小视频话题数据', data);
      //
      //       data.dataBody &&
      //         data.dataBody.forEach((item) => {
      //           item.poi_name = item.title;
      //           item.poi_id = item.id;
      //           item.topicId = item.id;
      //           item.topicName = item.title;
      //           item.text = item.title;
      //           item.value = item.id;
      //         });
      //
      //       this.positionList = data.dataBody || [];
      //     })
      //     .catch((error) => {
      //       console.debug('获取全名小视频视频话题数据失败', error);
      //     });
    } catch (e) {
      console.debug(e)
    }
  }
}
