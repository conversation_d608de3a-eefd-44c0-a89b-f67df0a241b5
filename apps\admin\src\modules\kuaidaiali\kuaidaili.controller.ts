import { Body, Controller, Get, Param, Put } from '@nestjs/common'
import { KuaidailiService } from './kuaidaili.service'
import { ApiOperation } from '@nestjs/swagger'
import { UpdateTeamAreasInputDto } from './kuaidaili.dtos'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('kuaidaili')
@AdminOnly()
export class KuaidailiController {
  constructor(private readonly kuaidailiService: KuaidailiService) {}

  @Get('areas')
  @ApiOperation({ summary: '获取地区列表树' })
  getAreas() {
    return this.kuaidailiService.getAreas()
  }

  @Put(':teamId/areas')
  @ApiOperation({ summary: '更新团队代理区域' })
  updateTeamAreas(@Param('teamId') teamId: string, @Body() input: UpdateTeamAreasInputDto[]) {
    return this.kuaidailiService.updateTeamAreas(teamId, input)
  }

  @Get(':teamId/areas')
  @ApiOperation({ summary: ' 获取团队代理区域' })
  getTeamAreas(@Param('teamId') teamId: string) {
    return this.kuaidailiService.getTeamAreas(teamId)
  }
}
