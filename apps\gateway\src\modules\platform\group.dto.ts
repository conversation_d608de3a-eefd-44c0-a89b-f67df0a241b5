import { ApiProperty, ApiResponseProperty } from "@nestjs/swagger"
import { IsOptional, IsString } from "class-validator"
import { BaseResponseDTO } from "../../common/dto/BaseResponseDTO"

export class GroupsDTO{
    @ApiProperty({
        type: String,
        example: '********-da27-42cb-9413-3941b7c06fa2'
    })
    id: string

    @ApiProperty({
        type: String,
        example: '娱乐分组'
    })
    name: string

    @ApiProperty({
        type: [String]
    })
    accounts: string[]

    @ApiProperty({
        type: Number
    })
    createdAt?: number
}

export class CreateOrUpdateGroupRequest {

    @ApiProperty({
        description: '分组名称',
        default: '张三组',
        required: false,
        type: String
    })
    @IsOptional()
    @IsString()
    name: string

    @ApiProperty({
        description: '绑定账号ID',
        default: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a'],
        required: false,
        type: [String]
    })
    @IsOptional()
    account: string[]
}


/**
 * 分组
 */
export class PostGroupsRequest {

    @ApiProperty({
        type: String,
        example: '分组',
        required: true
    })
    @IsString()
    name: string

}

/**
 * 分组
 */
export class PatchGroupsRequest {

    @ApiProperty({
        type: String,
        example: '山口组',
        description: '分组名称',
        required: false
    })
    @IsString()
    @IsOptional()
    name: string

    @ApiProperty({
        type: [String],
        description:'绑定到分组的媒体账号IDs',
        example: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a'],
        required: false
    })
    @IsOptional()
    accounts: string[]
}

/**
 * 分组详情
 */
export class GroupsDetailResponse {

    @ApiResponseProperty({
        type: String,
        example: '********-da27-42cb-9413-3941b7c06fa2'
    })
    id: string

    @ApiResponseProperty({
        type: String,
        example: '分组'
    })
    name: string

    @ApiResponseProperty({
        type: [String],
        example: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a'],
    })
    accounts: string[]

    @ApiResponseProperty({
        type: Number,
        example: '**********'
    })
    createdAt: number

}

export class GroupsListResponse {
    @ApiResponseProperty({
        type: [GroupsDTO]
    })
    data: GroupsDTO[]

    @ApiResponseProperty({
        type: Number,
        example: 1
    })
    page: number

    @ApiResponseProperty({
        type: Number,
        example: 10
    })
    size: number

    @ApiResponseProperty({
        type: Number,
        example: 100
    })
    totalSize: number

    @ApiResponseProperty({
        type: Number,
        example: 100
    })
    totalPage: number
}

/**
 * 分组详情响应体
 */
export class GroupsDetailResponseDTO extends BaseResponseDTO {

    @ApiResponseProperty({
        type: GroupsDetailResponse
    })
    data: GroupsDetailResponse
}

/**
 * 分组列表响应体
 */
export class GroupsListResponseDTO extends BaseResponseDTO {
    @ApiResponseProperty({
        type: GroupsListResponse
    })
    data: GroupsListResponse
}
