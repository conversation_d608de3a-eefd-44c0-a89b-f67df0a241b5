import { Body, Controller, Param, <PERSON>, Post, Put } from '@nestjs/common'
import { ApiHeader, ApiOkResponse, ApiOperation, ApiTags, ApiUnauthorizedResponse } from '@nestjs/swagger'

import { BaseResponseDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { DeviceLogsRequestBodyDTO } from './user-devices.dto'
import { UserDevicesService } from './user-devices.service'

@Controller('users/devices')
@ApiTags('用户管理/设备管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class UserDevicesController {
  constructor(private readonly userDevicesService: UserDevicesService) {}

  /**
   * 生成设备日志
   * @param data
   */
  @Patch(':logId')
  @ApiOperation({ summary: '更新设备日志信息' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  async postDeviceLogs(@Param('logId') logId: string,@Body() data: DeviceLogsRequestBodyDTO) {
    return this.userDevicesService.postDeviceLogs(logId,data)
  }
}
