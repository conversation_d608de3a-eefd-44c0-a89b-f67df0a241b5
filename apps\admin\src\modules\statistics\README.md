# 团队统计数据系统

## 概述

团队统计数据系统用于跟踪和记录团队的日常使用情况，包括流量使用量和账号新增数量。系统支持团队级别和开放平台应用级别的统计数据，并提供定时任务自动计算和手动补充功能。

## 核心功能

### 1. 数据实体

#### 团队日统计数据（TeamDailyStatsEntity）
- `teamId`: 团队ID
- `date`: 统计日期（按天）
- `trafficUsage`: 当日流量使用量（字节）
- `accountsAdded`: 当日新增账号数量
- `createdAt/updatedAt`: 创建和更新时间

#### 开放平台应用日统计数据（OpenPlatformAppDailyStatsEntity）
- `applicationId`: 开放平台应用ID
- `date`: 统计日期
- `totalTrafficUsage`: 该应用下所有团队的流量使用总和（字节）
- `totalAccountsAdded`: 该应用下所有团队的新增账号总数
- `teamCount`: 该应用下的团队数量
- `createdAt/updatedAt`: 创建和更新时间

### 2. 数据来源

- **流量使用情况**: 从`TrafficBillingEntity`实体中按团队和日期聚合统计
- **账号添加数量**: 从`PlatformAccountEntity`实体中按团队和创建日期统计当日新增数量

### 3. 定时任务

#### 每日统计任务
- **执行时间**: 每天凌晨00:30（Asia/Shanghai时区）
- **统计内容**: 前一天的团队和应用数据
- **执行顺序**: 先统计团队数据，再基于团队数据汇总应用数据

#### 数据清理任务
- **执行时间**: 每月1日凌晨02:00
- **清理策略**: 删除90天前的历史统计数据
- **保留期限**: 90天

## API接口

### 1. 获取团队统计数据

```http
GET /statistics/team?teamId={teamId}&startDate={startDate}&endDate={endDate}
```

**参数:**
- `teamId`: 团队ID
- `startDate`: 开始日期（YYYY-MM-DD）
- `endDate`: 结束日期（YYYY-MM-DD）

**响应:**
```json
{
  "code": 200,
  "message": "获取团队统计数据成功",
  "data": [
    {
      "teamId": "507f1f77bcf86cd799439011",
      "date": "2024-01-01",
      "trafficUsage": 1048576,
      "accountsAdded": 5,
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

### 2. 获取应用统计数据

```http
GET /statistics/app?applicationId={applicationId}&startDate={startDate}&endDate={endDate}
```

**参数:**
- `applicationId`: 应用ID
- `startDate`: 开始日期（YYYY-MM-DD）
- `endDate`: 结束日期（YYYY-MM-DD）

**响应:**
```json
{
  "code": 200,
  "message": "获取应用统计数据成功",
  "data": [
    {
      "applicationId": "507f1f77bcf86cd799439011",
      "date": "2024-01-01",
      "totalTrafficUsage": ********,
      "totalAccountsAdded": 50,
      "teamCount": 10,
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

### 3. 获取统计概览

```http
GET /statistics/overview?date={date}
```

**参数:**
- `date`: 统计日期（可选，默认今天）

**响应:**
```json
{
  "code": 200,
  "message": "获取统计概览成功",
  "data": {
    "totalTeams": 100,
    "totalApps": 20,
    "totalTrafficUsage": *********,
    "totalAccountsAdded": 500
  }
}
```

### 4. 获取统计状态

```http
GET /statistics/status
```

**响应:**
```json
{
  "code": 200,
  "message": "获取统计状态成功",
  "data": {
    "lastCalculatedDate": "2024-01-01",
    "totalTeamRecords": 1000,
    "totalAppRecords": 200,
    "todayOverview": {
      "totalTeams": 100,
      "totalApps": 20,
      "totalTrafficUsage": *********,
      "totalAccountsAdded": 500
    }
  }
}
```

### 5. 手动执行统计

```http
POST /statistics/manual
Content-Type: application/json

{
  "date": "2024-01-01"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "手动统计任务已执行",
  "data": {
    "success": true,
    "message": "统计任务执行成功",
    "date": "2024-01-01"
  }
}
```

### 6. 批量补充统计数据

```http
POST /statistics/backfill
Content-Type: application/json

{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "批量补充任务已执行",
  "data": {
    "success": true,
    "message": "批量补充完成: 成功30天, 失败0天",
    "processedDays": 30,
    "failedDays": 0
  }
}
```

## 数据库设计

### 索引策略

#### TeamDailyStatsEntity
- 复合唯一索引: `{ teamId: 1, date: 1 }`
- 日期索引: `{ date: -1 }`
- 团队查询索引: `{ teamId: 1, date: -1 }`

#### OpenPlatformAppDailyStatsEntity
- 复合唯一索引: `{ applicationId: 1, date: 1 }`
- 日期索引: `{ date: -1 }`
- 应用查询索引: `{ applicationId: 1, date: -1 }`

### 数据一致性

- 使用`upsert`操作确保每个团队每天只有一条记录
- 使用`upsert`操作确保每个应用每天只有一条记录
- 自动更新时间戳字段

## 错误处理

### 统计任务错误处理
- 单个团队统计失败不影响其他团队
- 详细的错误日志记录
- 失败统计和成功统计分别计数

### API错误处理
- 参数验证错误返回400状态码
- 数据库查询错误返回500状态码
- 统一的错误响应格式

## 性能优化

### 聚合查询优化
- 使用MongoDB聚合管道进行数据统计
- 利用索引加速查询性能
- 分批处理大量数据

### 定时任务优化
- 批量补充时添加延迟，避免数据库压力
- 异步处理，不阻塞主线程
- 详细的进度日志

## 监控和维护

### 日志记录
- 统计任务执行状态
- 处理成功和失败的数量
- 详细的错误信息

### 数据清理
- 自动清理90天前的历史数据
- 定期执行，保持数据库性能

### 状态监控
- 提供统计状态查询接口
- 显示最后统计日期和记录总数
- 实时概览数据

## 使用示例

### 查询团队最近7天的统计数据

```bash
curl -X GET "http://localhost:3000/statistics/team?teamId=507f1f77bcf86cd799439011&startDate=2024-01-01&endDate=2024-01-07" \
  -H "Authorization: Bearer {token}"
```

### 手动补充缺失的统计数据

```bash
curl -X POST "http://localhost:3000/statistics/backfill" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31"
  }'
```

## 注意事项

1. **时区处理**: 所有定时任务使用Asia/Shanghai时区
2. **数据精度**: 流量使用量以字节为单位存储
3. **权限控制**: 所有接口需要管理员权限
4. **数据关联**: 应用统计基于团队的`latestTeamId`字段关联
5. **性能考虑**: 大量历史数据补充时建议分批执行
