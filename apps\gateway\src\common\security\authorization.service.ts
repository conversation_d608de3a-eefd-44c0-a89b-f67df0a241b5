import { ForbiddenException, Injectable, Scope } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { MemberEntity } from '@yxr/mongo'
import { MemberStatusEnum, TeamRoleNames } from '@yxr/common'

/**
 * 授权服务
 * 用户检查用户是否拥有某个角色
 */
@Injectable({ scope: Scope.REQUEST })
export class AuthorizationService {
  constructor(@InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>) {}

  /**
   * 检查用户是否拥有某个角色, 在不具备任何角色时抛出异常
   * @param teamId
   * @param userId
   * @param roleNames 需要检查的角色名
   * @param message 异常信息, 默认: 您没有权限执行该操作
   *
   * @returns boolean 当用户拥有 roleNames 其中一个角色时返回 true
   *
   * @throws ForbiddenException 当用户没有 roleNames 其中一个角色时抛出异常
   */
  async checkRoleNames(
    teamId: string,
    userId: string,
    roleNames: string[],
    message: string = '您没有权限执行该操作'
  ): Promise<void> {
    // TODO: 先看缓存中有没有, 缓存中没有的话就从数据库中读取并更新至缓存
    const member = await this.findMember(teamId, userId)

    if (!member || member.status !== MemberStatusEnum.Joined) {
      throw new ForbiddenException('您没有权限执行该操作')
    }

    if (member.roles.filter((item) => roleNames.includes(item)).length === 0) {
      throw new ForbiddenException('您没有权限执行该操作')
    }
  }

  /**
   * 设置用户角色
   * @param teamId
   * @param userId
   * @param roleNames 需要设置的角色名
   */
  async setRoleNames(teamId: string, userId: string, roleNames: string[]) {}

  private async findMember(teamId: string | Types.ObjectId, userId: string) {
    return await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        userId: new Types.ObjectId(userId)
      })
      .exec()
  }
}
