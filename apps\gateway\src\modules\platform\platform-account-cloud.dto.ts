import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class AccountCloudBaseRequst {
  @ApiProperty({
    type: String,
    description: '搜索关键词,可空，不分平台为空时没有默认数据'
  })
  @IsOptional()
  @IsString()
  keyWord: string

  @ApiProperty({
    type: String,
    description: '下一页参数，会在返回结果中给出，如果返回结果没有，则没有下一页'
  })
  @IsOptional()
  @IsString()
  nextPage: string
}

export class AccountMusicBodyRequest extends AccountCloudBaseRequst {
  @ApiProperty({
    type: String,
    description: '分类查询时必填'
  })
  @IsOptional()
  @IsString()
  categoryId: string

  @ApiProperty({
    type: String,
    description: '分类查询时必填'
  })
  @IsOptional()
  @IsString()
  categoryName: string
}

export class CloudBaseDTO {
  authToken: string
  platform: string
  platformAccountId: string
  platformAccountSpaceId: string
  parentId?: string
  wxKey?: string
  token?: string
}

export class AccountMusicDTO extends CloudBaseDTO {
  keyWord: string
  nextPage: string
  categoryId?: string
  categoryName?: string
}

export class AccountLocationDTO extends CloudBaseDTO {
  keyWord: string
  nextPage: string
}

export class rawDTO {
  @ApiProperty({
    type: String
  })
  author: string
  @ApiProperty({
    type: String
  })
  id_str: string

  @ApiProperty({
    type: Number
  })
  duration: number

  @ApiProperty({
    type: String
  })
  title: string

  @ApiProperty({
    type: String
  })
  play_url: string
}

export class AccountMusicRetrunDTO {
  @ApiProperty({
    type: String
  })
  yixiaoerId: string

  @ApiProperty({
    type: String
  })
  yixiaoerName: string

  @ApiProperty({
    type: String
  })
  authorName: string

  @ApiProperty({
    type: String
  })
  playUrl: string

  @ApiProperty({
    type: Number
  })
  duration: number

  @ApiProperty({
    type: rawDTO
  })
  raw: rawDTO
}

export class AccountMusicResponse {
  @ApiProperty({
    type: [AccountMusicRetrunDTO],
    description: '音乐数据列表'
  })
  dataList: AccountMusicRetrunDTO[]

  @ApiProperty({
    type: String,
    description: '下一页参数，为空或者null时，表示没有下一页'
  })
  nextPage: string
}

export class AccountMusicResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountMusicResponse
  })
  data: AccountMusicResponse
}

export class musicCategoryRawDTO {
  @ApiProperty({
    type: String
  })
  id: string
  @ApiProperty({
    type: String
  })
  name: string
}

export class AccountMusicCategoryRetrunDTO {
  @ApiProperty({
    type: String
  })
  yixiaoerId: string

  @ApiProperty({
    type: String
  })
  yixiaoerName: string

  @ApiProperty({
    type: musicCategoryRawDTO
  })
  raw: musicCategoryRawDTO
}

export class AccountMusicCategoryResponse {
  @ApiProperty({
    type: [AccountMusicCategoryRetrunDTO],
    description: '音乐分类数据列表'
  })
  dataList: AccountMusicCategoryRetrunDTO[]
}

export class AccountMusicCategoryResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountMusicCategoryResponse
  })
  data: AccountMusicCategoryResponse
}

export class AccountLocationRetrunDTO {
  @ApiProperty({
    type: String
  })
  yixiaoerId: string

  @ApiProperty({
    type: String
  })
  yixiaoerName: string

  @ApiProperty({
    description: '地理位置对象结构',
    required: false
  })
  raw: unknown
}

export class AccountLocationResponse {
  @ApiProperty({
    type: [AccountLocationRetrunDTO],
    description: '地理位置数据列表'
  })
  dataList: AccountLocationRetrunDTO[]
}

export class AccountLocationResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountLocationResponse
  })
  data: AccountLocationResponse
}
export class AccountValidationResponse {
  @ApiProperty({
    description: "信息",
    type: String
  })
  message: string

  @ApiProperty({
    description: "0:失效，1:有效,-1:不确定,-2:不支持的平台，-3：获取cookie失败",
    type: Number 
  })
  loginStatus: number 
}
export class AccountValidationResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountValidationResponse
  })
  data: AccountValidationResponse
}
