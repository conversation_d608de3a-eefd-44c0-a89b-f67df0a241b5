import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  collection: 'open_platform_application_balances',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformApplicationBalanceEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    unique: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  totalBalance: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  frozenBalance: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  availableBalance: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  totalRecharge: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  totalConsumption: number

  @Prop({
    type: Date,
    required: false
  })
  lastRechargeTime?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const OpenPlatformApplicationBalanceSchema: ModelDefinition = {
  name: OpenPlatformApplicationBalanceEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformApplicationBalanceEntity)
}

export const OpenPlatformApplicationBalanceMongoose = MongooseModule.forFeature([OpenPlatformApplicationBalanceSchema])
