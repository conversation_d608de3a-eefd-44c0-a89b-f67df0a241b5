# 海外平台内容发布API文档

## 概述

本文档描述了海外平台内容发布系统的API接口，包括国内服务端和香港服务端的接口定义。

## 国内服务端API (Gateway)

### 基础信息
- **Base URL**: `https://api.yixiaoer.com`
- **认证方式**: Bearer <PERSON>ken
- **Content-Type**: `application/json`

### 1. 创建发布任务

创建海外平台内容发布任务。

**接口地址**
```
POST /overseas-platform/publish
```

**请求头**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**请求参数**
```json
{
  "platformAccountIds": ["64f1a2b3c4d5e6f7g8h9i0j1", "64f1a2b3c4d5e6f7g8h9i0j2"],
  "content": {
    "type": "text",
    "text": "这是一条测试发布内容",
    "title": "测试标题",
    "description": "测试描述",
    "images": [
      "https://cdn.example.com/image1.jpg",
      "https://cdn.example.com/image2.jpg"
    ],
    "videoUrl": "https://cdn.example.com/video.mp4",
    "videoCover": "https://cdn.example.com/cover.jpg",
    "tags": ["测试", "发布"],
    "location": {
      "name": "北京市",
      "latitude": 39.9042,
      "longitude": 116.4074
    },
    "platformSpecific": {
      "facebook": {
        "targeting": {
          "age_min": 18,
          "age_max": 65
        }
      }
    }
  },
  "publishAt": "2024-01-01T10:00:00Z",
  "maxRetries": 3
}
```

**参数说明**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| platformAccountIds | string[] | 是 | 平台账号ID列表 |
| content | object | 是 | 发布内容 |
| content.type | string | 是 | 内容类型: text/image/video/mixed |
| content.text | string | 否 | 文本内容 |
| content.title | string | 否 | 标题 |
| content.description | string | 否 | 描述 |
| content.images | string[] | 否 | 图片URL列表 |
| content.videoUrl | string | 否 | 视频URL |
| content.videoCover | string | 否 | 视频封面URL |
| content.tags | string[] | 否 | 标签列表 |
| content.location | object | 否 | 位置信息 |
| content.platformSpecific | object | 否 | 平台特定参数 |
| publishAt | string | 否 | 定时发布时间(ISO 8601格式) |
| maxRetries | number | 否 | 最大重试次数(0-5，默认3) |

**响应示例**
```json
{
  "code": 200,
  "message": "OK",
  "data": [
    {
      "taskId": "task_64f1a2b3c4d5e6f7g8h9i0j1",
      "taskSetId": "taskset_64f1a2b3c4d5e6f7g8h9i0j1",
      "platformAccountId": "64f1a2b3c4d5e6f7g8h9i0j1",
      "platformName": "facebook",
      "platformAccountName": "测试页面",
      "platformAvatar": "https://cdn.example.com/avatar.jpg",
      "status": "pending",
      "createdAt": "2024-01-01T09:00:00Z"
    }
  ]
}
```

### 2. 查询发布状态

查询指定任务集的发布状态。

**接口地址**
```
GET /overseas-platform/publish/status/{taskSetId}
```

**请求头**
```
Authorization: Bearer {access_token}
```

**路径参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| taskSetId | string | 是 | 任务集ID |

**响应示例**
```json
{
  "code": 200,
  "message": "OK",
  "data": [
    {
      "taskId": "task_64f1a2b3c4d5e6f7g8h9i0j1",
      "taskSetId": "taskset_64f1a2b3c4d5e6f7g8h9i0j1",
      "platformAccountId": "64f1a2b3c4d5e6f7g8h9i0j1",
      "platformName": "facebook",
      "platformAccountName": "测试页面",
      "platformAvatar": "https://cdn.example.com/avatar.jpg",
      "status": "success",
      "platformContentId": "fb_post_123456",
      "platformContentUrl": "https://facebook.com/post/123456",
      "createdAt": "2024-01-01T09:00:00Z",
      "updatedAt": "2024-01-01T09:05:00Z"
    }
  ]
}
```

### 3. 发布结果回调

接收香港服务端的发布结果回调。

**接口地址**
```
POST /overseas-platform/publish/callback
```

**请求参数**
```json
{
  "taskId": "task_64f1a2b3c4d5e6f7g8h9i0j1",
  "status": "success",
  "platformContentId": "fb_post_123456",
  "platformContentUrl": "https://facebook.com/post/123456",
  "errorMessage": null,
  "errorCode": null,
  "rawResponse": {
    "id": "fb_post_123456",
    "created_time": "2024-01-01T09:05:00Z"
  },
  "completedAt": "2024-01-01T09:05:00Z"
}
```

## 香港服务端API (Overseavice)

### 基础信息
- **Base URL**: `https://overseas.yixiaoer.com`
- **认证方式**: API Key
- **Content-Type**: `application/json`

### 1. 执行发布任务

执行单个发布任务。

**接口地址**
```
POST /content-publish/tasks
```

**请求参数**
```json
{
  "taskId": "task_64f1a2b3c4d5e6f7g8h9i0j1",
  "taskSetId": "taskset_64f1a2b3c4d5e6f7g8h9i0j1",
  "teamId": "team_64f1a2b3c4d5e6f7g8h9i0j1",
  "userId": "user_64f1a2b3c4d5e6f7g8h9i0j1",
  "accountOpenId": "fb_page_123456",
  "platform": "facebook",
  "content": {
    "type": "text",
    "text": "这是一条测试发布内容"
  },
  "callbackUrl": "https://api.yixiaoer.com/overseas-platform/publish/callback",
  "maxRetries": 3
}
```

### 2. 批量执行发布任务

批量执行多个发布任务。

**接口地址**
```
POST /content-publish/tasks/batch
```

**请求参数**
```json
{
  "tasks": [
    {
      "taskId": "task_64f1a2b3c4d5e6f7g8h9i0j1",
      "taskSetId": "taskset_64f1a2b3c4d5e6f7g8h9i0j1",
      "teamId": "team_64f1a2b3c4d5e6f7g8h9i0j1",
      "userId": "user_64f1a2b3c4d5e6f7g8h9i0j1",
      "accountOpenId": "fb_page_123456",
      "platform": "facebook",
      "content": {
        "type": "text",
        "text": "这是一条测试发布内容"
      },
      "callbackUrl": "https://api.yixiaoer.com/overseas-platform/publish/callback"
    }
  ]
}
```

### 3. 查询发布状态

查询平台侧的发布状态。

**接口地址**
```
GET /content-publish/tasks/{taskId}/status
```

**查询参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| platform | string | 是 | 平台名称 |
| accountOpenId | string | 是 | 账号OpenID |
| platformContentId | string | 是 | 平台内容ID |

## 状态码说明

### 发布状态
- `pending`: 待发布
- `publishing`: 发布中
- `success`: 发布成功
- `failed`: 发布失败
- `reviewing`: 审核中
- `approved`: 审核通过
- `rejected`: 审核拒绝

### 错误码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率超限
- `500`: 服务器内部错误

## 平台特定参数

### Facebook
```json
{
  "platformSpecific": {
    "facebook": {
      "targeting": {
        "age_min": 18,
        "age_max": 65,
        "genders": [1, 2],
        "locales": ["zh_CN"]
      },
      "call_to_action": {
        "type": "LEARN_MORE",
        "value": {
          "link": "https://example.com"
        }
      }
    }
  }
}
```

### Instagram
```json
{
  "platformSpecific": {
    "instagram": {
      "caption": "Instagram专用描述",
      "location_id": "123456789",
      "user_tags": [
        {
          "username": "example_user",
          "x": 0.5,
          "y": 0.5
        }
      ]
    }
  }
}
```

## 内容限制

### Facebook
- 文本最大长度: 63,206字符
- 图片最大数量: 10张
- 视频最大大小: 4GB
- 视频最大时长: 240分钟
- 支持的图片格式: jpg, jpeg, png, gif, bmp, webp
- 支持的视频格式: mp4, mov, avi, mkv, webm, 3gp

### Instagram
- 文本最大长度: 2,200字符
- 图片最大数量: 10张
- 视频最大大小: 4GB
- 视频最大时长: 60秒(Feed)/15分钟(IGTV)
- 支持的图片格式: jpg, jpeg, png
- 支持的视频格式: mp4, mov

## SDK示例

### JavaScript/TypeScript
```typescript
import axios from 'axios'

class OverseasPublishClient {
  constructor(private baseUrl: string, private accessToken: string) {}

  async createPublishTask(params: CreatePublishTaskParams) {
    const response = await axios.post(
      `${this.baseUrl}/overseas-platform/publish`,
      params,
      {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  }

  async getPublishStatus(taskSetId: string) {
    const response = await axios.get(
      `${this.baseUrl}/overseas-platform/publish/status/${taskSetId}`,
      {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      }
    )
    return response.data
  }
}
```
