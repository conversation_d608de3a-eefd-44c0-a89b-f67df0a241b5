import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common'
import { FilterQuery, Model, Types } from 'mongoose'
import { ChannelEntity, OrderEntity, UserEntity } from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import {
  ChannelCreateRequestDTO,
  ChannelDetail,
  ChannelListRequestDTO,
  ChannelListResponse,
  ChannelPatchRequestDTO,
  PutChannelEnabledRequestDTO
} from './channel.dto'
import crypto from 'crypto'

@Injectable()
export class ChannelService {
  constructor(
    @InjectModel(ChannelEntity.name) private channelModel: Model<ChannelEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>
  ) {}

  verifyPassword(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  /**
   * 获取渠道列表
   * @param query
   * @returns
   */
  async getChannels(query: ChannelListRequestDTO): Promise<ChannelListResponse> {
    const filter: FilterQuery<ChannelEntity> = {}

    if (query.channelName) {
      filter.channelName = { $regex: query.channelName, $options: 'i' }
    }
    if (query.channelCode) {
      filter.channelCode = { $regex: query.channelCode, $options: 'i' }
    }

    const channels = await this.channelModel
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.channelModel.find(filter).countDocuments()

    const result = await Promise.all(
      channels.map(async (item) => {
        const userCount = await this.userModel
          .find({ channelCode: item.channelCode })
          .countDocuments()

        const orderCount = await this.orderModel
          .find({ channelCode: item.channelCode })
          .countDocuments()

        const totalAmount = await this.orderModel.aggregate([
          {
            $match: {
              channelCode: item.channelCode
            }
          },
          {
            $group: {
              _id: null,
              total: { $sum: '$amount' }
            }
          }
        ])

        return {
          id: item.id.toString(),
          channelName: item.channelName,
          channelCode: item.channelCode,
          userCount: userCount,
          orderCount: orderCount,
          orderTotalAmount: totalAmount[0]?.total ?? 0,
          enabled: item.enabled,
          giftDays: item.giftDays,
          createdAt: item.createdAt ? item.createdAt.getTime() : 0
        }
      })
    )

    return {
      page: query.page,
      size: query.size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / query.size),
      data: result
    }
  }

  /**
   * 创建渠道
   * @param data
   * @returns
   */
  async createChannel(data: ChannelCreateRequestDTO): Promise<ChannelDetail> {
    const channelCode = await this.channelModel.findOne({ channelCode: data.channelCode })
    if (channelCode) {
      throw new ForbiddenException('该渠道码已存在')
    }

    const channelUserName = await this.channelModel.findOne({ username: data.username })
    if (channelUserName) {
      throw new ForbiddenException('该渠道账号已存在')
    }

    const { salt, hash } = this.hashPassword(data.password)
    const createChannel: ChannelEntity = {
      username: data.username,
      channelName: data.channelName,
      channelCode: data.channelCode,
      password: hash,
      salt: salt,
      enabled: true,
      giftDays: data.giftDays
    }

    const channelInfo = await this.channelModel.create(createChannel)

    return {
      id: channelInfo.id.toString(),
      username: channelInfo.username,
      channelName: channelInfo.channelName,
      channelCode: channelInfo.channelCode,
      createdAt: channelInfo.createdAt.getTime(),
      enabled: channelInfo.enabled,
      giftDays: channelInfo.giftDays
    }
  }

  /**
   * 渠道详情
   * @param channelId
   * @returns
   */
  async getChannelDetail(channelId: string): Promise<ChannelDetail> {
    const channelInfo = await this.channelModel.findById(new Types.ObjectId(channelId))

    if (!channelInfo) {
      throw new NotFoundException('数据不存在')
    }

    return {
      id: channelInfo.id,
      username: channelInfo.username,
      channelCode: channelInfo.channelCode,
      channelName: channelInfo.channelName,
      createdAt: channelInfo.createdAt.getTime(),
      enabled: channelInfo.enabled,
      giftDays: channelInfo.giftDays
    }
  }

  /**
   * 渠道上下架
   * @param channelId
   * @param body
   */
  async putChannelEnabled(channelId: string, body: PutChannelEnabledRequestDTO) {
    const channel = await this.channelModel.findOne({
      _id: new Types.ObjectId(channelId)
    })

    if (!channel) {
      throw new NotFoundException('渠道未找到')
    }

    channel.enabled = body.enabled
    await channel.save()
  }

  /**
   * 更新渠道
   * @param channelId
   * @param body
   */
  async patchChannelDetail(
    channelId: string,
    body: ChannelPatchRequestDTO
  ): Promise<ChannelDetail> {
    const channel = await this.channelModel.findOne({ _id: new Types.ObjectId(channelId) })
    if (!channel) {
      throw new NotFoundException('渠道不存在')
    }

    if (body.channelName) {
      channel.channelName = body.channelName
    }
    if (body.password) {
      const { salt, hash } = this.hashPassword(body.password)
      channel.password = hash
      channel.salt = salt
    }
    if (body.giftDays >= 0) {
      channel.giftDays = body.giftDays
    }

    await channel.save()

    return {
      id: channel.id.toString(),
      username: channel.username,
      channelName: channel.channelName,
      channelCode: channel.channelCode,
      createdAt: channel.createdAt.getTime(),
      enabled: channel.enabled,
      giftDays: channel.giftDays
    }
  }

  /**
   * 删除渠道
   * @param channelId
   * @returns
   */
  async deleteChannel(channelId: string) {
    return this.channelModel.deleteOne({ _id: new Types.ObjectId(channelId) })
  }
}
