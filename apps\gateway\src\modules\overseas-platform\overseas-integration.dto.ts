import { ApiProperty } from "@nestjs/swagger"
import { <PERSON><PERSON><PERSON><PERSON>, IsNotEmpty, IsString } from "class-validator"

class IntegrationAuthorizationCallbackAccountInput{

  @ApiProperty({
    description: '授权的openId',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  openId: string

  @ApiProperty({
    description: '账号昵称',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  nick_name: string

  @ApiProperty({
    description: '账号头像',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  avatar: string

  @ApiProperty({
    description: '授权凭证, 各平台的授权凭证结构不同',
    required: true
  })
  credentials: unknown
}

// =======================================================================================================================
// Input
// =======================================================================================================================
export class IntegrationAuthorizationCallbackInput {

  @ApiProperty({
    description: '授权状态码',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  state:string

  @ApiProperty({
    type: [IntegrationAuthorizationCallbackAccountInput],
    description: '授权的账号',
    required: true
  })
  @IsArray()
  @IsNotEmpty()
  accounts: IntegrationAuthorizationCallbackAccountInput[]
}
