import { Body, Controller, Get, Param, Post } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { OverseasPlatformAuthService } from './overseas-platform-auth.service'
import {
  OverseasGetAuthorizationUrlOutputDto,
  OverseasGetAuthorizationUrlOutputResponse,
  SelectAccountsInput
} from './overseas-platform-auth.dto'

@Controller('overseas')
@ApiTags('媒体账号管理/海外平台/授权管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class OverseasPlatformAuthController {
  constructor(private readonly service: OverseasPlatformAuthService) {
  }

  @Get(':platform/auth')
  @ApiOperation({ summary: '获得账号授权地址(用于绑定账号)' })
  @ApiOkResponse({ type: OverseasGetAuthorizationUrlOutputResponse })
  async getAuthorizationUrl(@Param('platform') platform: string): Promise<OverseasGetAuthorizationUrlOutputDto> {
    return await this.service.getAuthorizationUrl(platform)
  }

  @Post('select-accounts')
  @ApiOperation({ summary: '用户选择账号' })
  @ApiBody({ type: SelectAccountsInput })
  async selectAccounts(@Body() input: SelectAccountsInput) {
    return await this.service.selectAccounts(input)
  }
}
