import { Provider } from '@nestjs/common'
import { OverseasPlatformNames } from '../../constants'
import { AccountAuthProvider } from '../account-auth.provider'
import { ContentPublishProvider } from '../content-publish.provider'
import { YoutubeAccountAuthProvider } from './youtube-account-auth.provider'
import { YoutubeContentPublishProvider } from './youtube-content-publish.provider'
import { YoutubeApi } from './youtube-api'

export const providers = [
  YoutubeApi,
  {
    provide: AccountAuthProvider.register_token(OverseasPlatformNames.Youtube),
    useClass: YoutubeAccountAuthProvider
  },
  {
    provide: ContentPublishProvider.register_token(OverseasPlatformNames.Youtube),
    useClass: YoutubeContentPublishProvider
  },
  // {
  //   provide: UserinfoService.token(Tiktok),
  //   useClass: TiktokBusinessUserinfoService
  // },
  // {
  //   provide: ContentService.token(Tiktok),
  //   useClass: TiktokBusinessContentService
  // },
  // {
  //   provide: CommentService.token(Tiktok),
  //   useClass: TiktokBusinessCommentService
  // },
  // {
  //   provide: MessageService.token(Tiktok),
  //   useClass: TiktokBusinessMessageService
  // },
  // {
  //   provide: DataRetrievalService.token(Tiktok),
  //   useClass: TiktokBusinessDataRetrievalService
  // },
  // {
  //   provide: PermissionProvider.token(Tiktok),
  //   useClass: TiktokBusinessPermissionProvider
  // },
  // {
  //   provide: WebhookProvider.token(Tiktok),
  //   useClass: TiktokBusinessWebhookProvider
  // }
] as Provider[]
