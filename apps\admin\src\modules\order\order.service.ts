import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
  AdminEntity,
  ContractEntity,
  InterestEntity,
  MemberEntity,
  OrderEntity,
  TeamEntity
} from '@yxr/mongo'
import { OrderManagerService, VIPOften } from '@yxr/order'
import {
  OrderDetailResponse,
  OrderInterestData,
  OrderListRequestDTO,
  orderPriceRequestDTO,
  OrderPriceResponseDto,
  OrderRequestCreateOrderDTO,
  orderResponseCreateOrder,
  orderResponseListOrder,
  OrderStatusRequestDTO,
  UpgradeOrderRequest,
  VipRenewDTO
} from './order.dto'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { AdminRole, OrderStatus, PayType, TeamRoleNames } from '@yxr/common'
import { eventKey, orderEventEmitter } from './order.event'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'

@Injectable()
export class OrderService {
  constructor(
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(ContractEntity.name) private contractModel: Model<ContractEntity>,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(InterestEntity.name) private interestModel: Model<InterestEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManagerService: OrderManagerService,
    private readonly webhookService: WebhookService
  ) {}

  /**
   * 获取订单列表
   * @param query
   * @returns
   */
  async getOrders(query: OrderListRequestDTO): Promise<orderResponseListOrder> {
    const page = query.page
    const size = query.size
    const orderWhere: any = { $match: {} }
    const where: any = { $match: {} }

    if (query.orderNo) {
      orderWhere.$match.orderNo = query.orderNo
    }
    if (query.payType) {
      orderWhere.$match.payType = query.payType
    }
    if (query.orderType) {
      orderWhere.$match.orderType = query.orderType
    }
    if (query.orderStatus) {
      orderWhere.$match.orderStatus = query.orderStatus
    }
    if (query.createStartTime && query.createEndTime) {
      orderWhere.$match.createdAt = {
        $gt: new Date(Number(query.createStartTime)),
        $lte: new Date(Number(query.createEndTime))
      }
    }
    if (query.payStartTime && query.payEndTime) {
      orderWhere.$match.payTime = {
        $gt: new Date(Number(query.payStartTime)),
        $lte: new Date(Number(query.payEndTime))
      }
    }
    if (query.customerId) {
      orderWhere.$match.customerId = new Types.ObjectId(query.customerId)
    }
    if (query.payAmount) {
      orderWhere.$match.payAmount = { $gte: query.payAmount }
    }
    if (query.channelCode) {
      orderWhere.$match.channelCode = query.channelCode
    }
    if (query.phone) {
      orderWhere.$match['phone'] = { $regex: query.phone, $options: 'i' }
    }
    if (query.teamName) {
      where.$match.$or = [
        { 'teams.name': { $regex: query.teamName, $options: 'i' } },
        { 'teams.code': { $regex: query.teamName, $options: 'i' } }
      ]
    }

    if (query.salesType !== undefined) {
      //传4查询新购和复购
      orderWhere.$match.salesType = query.salesType !== 4 ? query.salesType : { $gt: 0 }
    }

    const result = await this.orderModel.aggregate([
      orderWhere,
      {
        $lookup: {
          from: 'teamentities',
          localField: 'teamId',
          foreignField: '_id',
          as: 'teams'
        }
      },
      {
        $unwind: { path: '$teams' }
      },
      where,
      {
        $facet: {
          counts: [{ $count: 'total' }],
          totalAmount: [
            {
              $group: {
                _id: null,
                total: { $sum: '$payAmount' }
              }
            }
          ],
          items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
        }
      }
    ])

    const customers = await this.adminModel
      .find({
        role: AdminRole.Customer
      })
      .select('_id name')

    const data = result[0]?.items.map((item) => {
      return {
        id: item.id,
        orderNo: item.orderNo,
        phone: item.phone,
        teamName: item.teams.name,
        code: item.teams.code,
        orderStatus: item.orderStatus,
        orderType: item.orderType,
        payType: item.payType,
        payAmount: item.payAmount,
        createdAt: item.createdAt ? item.createdAt.getTime() : 0,
        payTime: item?.payTime ? item.payTime.getTime() : 0,
        salesType: item.salesType,
        customerName: item.customerId
          ? customers.find((cs) => cs._id.toString() === item.customerId.toString())?.name
          : ''
      }
    })
    const totalSize = result[0]?.counts[0]?.total ?? 0
    const totalAmount = result[0]?.totalAmount[0]?.total ?? 0

    return {
      totalSize,
      totalAmount: totalAmount,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  /**
   * 获取订单详情
   * @param query
   * @returns
   */
  async getOrderDetail(orderNo: string): Promise<OrderDetailResponse> {
    const order = await this.orderModel.findOne({
      orderNo: orderNo
    })

    if (!order) {
      throw new NotFoundException('订单未找到')
    }
    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(order.teamId)
    })

    const contract = await this.contractModel.findOne({ orderNo: orderNo }).sort({ endTime: -1 })
    return {
      orderNo: order.orderNo,
      teamName: team.name,
      code: team.code,
      orderStatus: order.orderStatus,
      orderType: order.orderType,
      orderSource: order.orderSource,
      payType: order.payType,
      totalAmount: order.totalAmount,
      payableAmount: order.payableAmount,
      payAmount: order.payAmount,
      createdAt: order?.createdAt.getTime(),
      payTime: order?.payTime?.getTime(),
      expiredAt: contract ? contract.endTime.getTime() : 0,
      interestCount: order.interestCount,
      vipMonth: order.vipMonth,
      freeMonth: order.freeMonth,
      days: order.days,
      isFree: order.isFree,
      creatorName: order.creatorName,
      remark: order.remark
    }
  }

  /**
   * 创建订单
   * @param query
   * @returns
   */
  async createOrder({
    interestId,
    interestCount,
    month,
    days,
    teamId,
    isPay,
    payAmount,
    remark
  }: OrderRequestCreateOrderDTO): Promise<orderResponseCreateOrder> {
    const { user } = this.request
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))

    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    if (!member) {
      throw new NotFoundException('团队创建者信息无法找到')
    }

    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const admin = await this.adminModel.findOne({
      username: user.username
    })

    const orderNo = await this.orderManagerService.createOrder({
      teamId: teamId,
      interestCount: interestCount,
      interestId: interestId,
      isCorporateTransfer: false,
      month: month,
      days: days,
      userId: member.userId.toString(),
      remark: remark,
      creatorId: admin.id,
      creatorName: user.username,
      payAmount: payAmount,
      isPay:isPay
    })

    // 对公转账没有支付，2小时自动取消订单
    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!isPay) {
      await this.orderModel.updateOne(
        {
          orderNo
        },
        {
          orderStatus: OrderStatus.Paid,
          payAmount: payAmount,
          payTime: new Date(),
          payType: PayType.Other
        }
      )

      await this.orderManagerService.handleCompletedOrder(orderNo)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
      await this.webhookService.grpchook(null, teamId, {
        event: WebhookEvents.TeamVersionUpgrade
      })
    }

    return { orderNo }
  }

  /**
   * 订单升级
   * @param data
   * @returns
   */
  async createUpgradeOrder(data: UpgradeOrderRequest): Promise<orderResponseCreateOrder> {
    const { user } = this.request

    const team = await this.teamModel.findById(new Types.ObjectId(data.teamId))

    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(data.teamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(data.teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    if (!member) {
      throw new NotFoundException('团队创建者信息无法找到')
    }

    const { interestId, interestCount, payAmount, isPay } = data

    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const admin = await this.adminModel.findOne({
      username: user.username
    })

    const orderNo = await this.orderManagerService.upgradeOrder({
      teamId: data.teamId,
      interestCount,
      interestId,
      isCorporateTransfer: false,
      userId: member.userId.toString(),
      remark: data.remark,
      creatorId: admin.id,
      creatorName: user.username,
      payAmount: data.payAmount,
      isPay: isPay
    })

    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!data.isPay) {
      await this.orderModel.updateOne(
        {
          orderNo
        },
        {
          orderStatus: OrderStatus.Paid,
          payAmount: data.payAmount,
          payTime: new Date(),
          payType: PayType.Other
        }
      )

      await this.orderManagerService.handleCompletedOrder(orderNo)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
      await this.webhookService.grpchook(null, data.teamId, {
        event: WebhookEvents.TeamVersionUpgrade
      })
    }

    return {
      orderNo
    }
  }

  /**
   * 续费订单
   * @param data
   * @returns
   */
  async renewOrder(data: VipRenewDTO): Promise<orderResponseCreateOrder> {
    const { user } = this.request

    const team = await this.teamModel.findById(new Types.ObjectId(data.teamId))

    if (!team) {
      throw new NotFoundException('团队信息无法找到')
    }

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(data.teamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(data.teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    if (!member) {
      throw new NotFoundException('团队创建者信息无法找到')
    }

    const { interestId, payAmount, isPay } = data

    if (isPay && payAmount <= 0) {
      throw new ForbiddenException('需要支付时折扣价必须大于0')
    }

    const admin = await this.adminModel.findOne({
      username: user.username
    })

    const orderNo = await this.orderManagerService.renewOrder({
      teamId: data.teamId,
      interestId: interestId,
      isCorporateTransfer: false,
      month: data.month,
      days: data.days,
      userId: member.userId.toString(),
      remark: data.remark,
      creatorId: admin.id,
      creatorName: user.username,
      payAmount: data.payAmount,
      isPay: isPay
    })

    orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })

    if (!data.isPay) {
      await this.orderModel.updateOne(
        {
          orderNo
        },
        {
          orderStatus: OrderStatus.Paid,
          payAmount: data.payAmount,
          payTime: new Date(),
          payType: PayType.Other
        }
      )

      await this.orderManagerService.handleCompletedOrder(orderNo)
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
      await this.webhookService.grpchook(null, data.teamId, {
        event: WebhookEvents.TeamVersionUpgrade
      })
    }

    return { orderNo }
  }

  /**
   * 开通对公转账
   * @param orderNo
   * @param body
   */
  async putOrderStatus(orderNo: string, body: OrderStatusRequestDTO) {
    const order = await this.orderModel.findOne({
      orderNo
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.orderStatus !== OrderStatus.Pending) {
      throw new ForbiddenException('订单不是待支付状态，无法对公账户开通')
    }

    try {
      const gmtPayment = new Date()

      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })

      await this.orderModel.findByIdAndUpdate(order.id, {
        payTime: gmtPayment,
        orderStatus: OrderStatus.Paid,
        payAmount: order.payableAmount,
        remark: body.remark,
        payType: PayType.CorporateTransfer
      })

      await this.orderManagerService.handleCompletedOrder(order.orderNo)
      await this.webhookService.grpchook(null, order.teamId.toString(), {
        event: WebhookEvents.TeamVersionUpgrade
      })
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }

  /**
   * 取消订单
   * @param orderNo
   */
  async putOrderCancel(orderNo: string) {
    const order = await this.orderModel.findOne({
      orderNo
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.orderStatus !== OrderStatus.Pending) {
      throw new ForbiddenException('订单不是待支付状态，无法订单取消')
    }

    await this.orderModel.findByIdAndUpdate(order.id, {
      orderStatus: OrderStatus.Cancelled
    })
    orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
  }

  /**
   * 获取权益包信息
   * @returns
   */
  async getInterest(): Promise<OrderInterestData> {
    const res = await this.interestModel.findOne()

    return {
      id: res._id.toString(),
      platformAccountCount: res.accountCountLimit,  // 变更为账号点数
      platformAccountCapacity: res.accountCapacityLimit,
      capacityLimit: res.capacityLimit,
      appPublish: res.appPublish,
      memberCount: res.memberCountLimit,
      price: res.price,
      vipOften: VIPOften
    }
  }

  /**
   * 计算订单价格
   * @param body
   * @returns
   */
  async calculateOrderPrice(body: orderPriceRequestDTO): Promise<OrderPriceResponseDto> {
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(body.teamId),
      roles: { $in: [TeamRoleNames.MASTER] }
    })

    return await this.orderManagerService.calculateOrderPrice({
      userId: member.userId.toString(),
      teamId: body.teamId.toString(),
      orderType: body.orderType,
      interestId: body.interestId.toString(),
      interestCount: body.interestCount,
      month: body.month,
      days: body.days
    })
  }
}
