import { Injectable, Logger, Scope } from '@nestjs/common'
import { AccountAuthProvider } from '../account-auth.provider'
import { OverseasContext, PlatformAccountInfo } from '../types'
import { Tik<PERSON><PERSON><PERSON> } from './tiktok-api'

@Injectable({ scope: Scope.TRANSIENT })
export class TiktokA<PERSON>untAuthProvider implements AccountAuthProvider {
  logger = new Logger(TiktokAccountAuthProvider.name)
  private readonly clientKey = process.env.TIKTOK_CLIENT_KEY || ''
  private readonly oauthCallbackUri = `${process.env.OVERSEAS_BASE_ADDRESS}auth/callback`

  constructor(private readonly api: Tiktok<PERSON>pi) {
  }

  async generateAuthorizationUrl(state: string): Promise<string> {
    const scope = [
      'user.info.basic',
      'user.info.username',
      'user.info.stats',
      'user.info.profile',
      'user.account.type',
      'user.insights',
      'video.list',
      'video.insights',
      'comment.list',
      'comment.list.manage',
      'video.publish',
      'video.upload',
      'biz.spark.auth',
      'biz.brand.insights'
    ].join(',')

    const url = new URL('https://www.tiktok.com/v2/auth/authorize')
    url.searchParams.set('client_key', this.clientKey)
    url.searchParams.set('response_type', 'code')
    url.searchParams.set('scope', scope)
    url.searchParams.set('redirect_uri', this.oauthCallbackUri)
    url.searchParams.set('state', state)
    url.searchParams.set('disable_auto_auth', '1')

    return url.toString()
  }

  async exchangeAuthCodeForAccounts(context: OverseasContext, code: string, state: string): Promise<PlatformAccountInfo[]> {
    const response = await this.api.oauth2_token(context, {
      auth_code: code,
      redirect_uri: this.oauthCallbackUri
    })

    const account = await this.api.getAccountOverview(context, response.data.access_token, response.data.open_id, {
      fields: ['is_verified', 'profile_image', 'display_name', 'username']
    })

    return [
      {
        openId: response.data.open_id,
        credentials: {
          token_type: response.data.token_type,
          scope: response.data.scope,
          access_token: response.data.access_token,
          expire_at: new Date(response.data.expires_in * 1000 + new Date().getTime()),
          refresh_token: response.data.refresh_token,
          refresh_token_expire_at: new Date(response.data.refresh_token_expires_in * 1000 + new Date().getTime())
        },
        avatar: account.data.profile_image,
        name: account.data.display_name,
        username: account.data.username
      } as PlatformAccountInfo
    ]
  }
}
