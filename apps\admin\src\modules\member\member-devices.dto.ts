import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'

export class DeviceLogsQueryDTO {
  @ApiProperty({
    type: String,
    description: '手机号',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  phone: string

  @ApiProperty({
    type: String,
    example: '蚁小二team',
    description: '用户名称',
    required: false
  })
  @IsOptional()
  @IsString()
  nickName: string

  @ApiProperty({
    type: Number,
    description: '注册开始时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  createStartTime: number

  @ApiProperty({
    type: Number,
    description: '注册结束时间',
    example: '18800000888',
    required: false
  })
  @IsOptional()
  createEndTime: number

  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class MemberDevicesResponse {
  @ApiProperty({
    type: String,
    description: '设备id',
    example: '680f6c4892c7e010a3feccda'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '设备id',
    example: '680f6c4892c7e010a3feccda'
  })
  userId: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0'
  })
  version: string

  @ApiProperty({
    type: String,
    description: '设备号',
    example: 'windows 10家庭中文版'
  })
  deviceId: string

  @ApiProperty({
    type: Number,
    description: '最后登录时间',
    example: 100
  })
  loginTime: number

  @ApiProperty({
    type: Boolean,
    description: '设备状态',
    example: true
  })
  isActive: boolean
}

export class MemberDevicesResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [MemberDevicesResponse]
  })
  data: MemberDevicesResponse[]
}

export class MemberDeviceLogDTO {
  @ApiProperty({
    type: String,
    description: '设备日志id',
    example: '13011112222'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '手机号',
    example: '13011112222'
  })
  phone: string

  @ApiProperty({
    type: String,
    description: '昵称',
    example: '张三'
  })
  nickName: string

  @ApiProperty({
    type: String,
    description: '头像',
    example: '头像'
  })
  avatar?: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0'
  })
  version: string

  @ApiProperty({
    type: String,
    description: '设备号',
    example: 'windows 10家庭中文版'
  })
  deviceId: string

  @ApiProperty({
    type: String,
    description: '文件下载OSS地址',
    example: ''
  })
  downloadLink: string

  @ApiProperty({
    type: Number,
    description: '创建时间',
    example: 1231094801
  })
  createdAt: number
}

export class MemberDeviceLogsResponse {
  @ApiResponseProperty({
    type: [MemberDeviceLogDTO]
  })
  data: MemberDeviceLogDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class MemberDeviceLogsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [MemberDeviceLogsResponse]
  })
  data: MemberDeviceLogsResponse[]
}
