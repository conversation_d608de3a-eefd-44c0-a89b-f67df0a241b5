import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsOptional, IsString } from 'class-validator'
import {  Type } from 'class-transformer'
import { LoginStatus } from '@yxr/mongo'
export class PlatformAccountDTO {
  @ApiProperty({
    type: String,
    description: '媒体账号名称',
    example: '抖音瓜子'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    description: '媒体账号备注',
    example: '抖音账号1'
  })
  remark: string

  @ApiResponseProperty({
    type: String,
    example: '团队ID'
  })
  teamCode: string


  @ApiResponseProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiProperty({
    type: String,
    example: '抖音'
  })
  platformName: string


  @ApiProperty({
    description:'创建时间戳',
    type: Number,
    example: '**********'
  })
  createdAt: number


  @ApiProperty({
    description:'最后登录时间戳',
    type: Number,
    example: '**********'
  })
  updatedAt: number

  @ApiProperty({
    default: LoginStatus.Succesed,
    enum: LoginStatus,
    description: '账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败, 4：取消授权>',
    required: true
  })
  status: LoginStatus

  @ApiProperty({
    description:'云检测登录有效性时间戳',
    type: Number,
    example: '**********'
  })
  cloudCheckTime: number

  @ApiProperty({
    description:'账号数据同步时间戳',
    type: Number,
    example: '**********'
  })
  cloudUpdatedAt: number

  @ApiProperty({
    description:'在线天数',
    type: Number,
    example: '12'
  })
  onLineDays: number

  @ApiProperty({
    type: String,
    description:'所属微信id，该字段有值表示是微信授权后添加的视频号',
    example: 'sfaajkdasd'
  })
  parentId: string
}

export class PlatformAccountListResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [PlatformAccountDTO]
  })
  data: PlatformAccountDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}


export class PlatformAccountListRequest {
  @ApiProperty({
    type: Number,
    description: '累加页码 <默认 1>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10


  @ApiProperty({
    type: String,
    description: '根据媒体账号名称/备注名称模糊查询',
    example: '糊涂的倒霉蛋',
    required: false
  })
  @IsOptional()
  @IsString()
  platformAccountName: string

  @ApiProperty({
    type: String,
    description: '按所属团队名称或者id查询',
    example: '团队名称或者id',
    required: false
  })
  @IsOptional()
  @IsString()
  teamName: string

  /**
   * 平台名称
   */
  @ApiProperty({
    type: String,
    description: '平台查询',
    example: '抖音',
    required: false
  })
  @IsOptional()
  @IsString()
  platformName: string


  @ApiProperty({
    type: Number,
    enum: LoginStatus,
    description: '账号状态 0未登录 1成功 2过期失效 3失败',
    example: LoginStatus.Succesed,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  loginStatus?: LoginStatus

  @ApiProperty({
    type: Number,
    required: false,
    default:'***********',
    description: '创建开始时间'
  })
  @IsOptional()
  @Type(() => Number)
  createStartTime: number

  @ApiProperty({
    type: Number,
    required: false,
    default:'***********',
    description: '创建结束时间'
  })
  @IsOptional()
  @Type(() => Number)
  createEndTime: number
}

