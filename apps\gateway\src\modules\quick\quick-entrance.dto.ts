import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class QuickEntrancesCreateRequest {
  @ApiProperty({
    required: true,
    description: '快捷入口数据'
  })
  quickArgs: unknown
}

export class QuickEntrance {
  @ApiProperty({
    type: String,
    description: '快捷入口ID',
    example: '6756b3547ca09fc8fa29342e'
  })
  id: string

  @ApiProperty({
    description: '快捷入口数据'
  })
  quickArgs: unknown
}

export class QuickEntrancesResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [QuickEntrance]
  })
  data: QuickEntrance[]
}
