import { Injectable, Inject, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { AdminEntity, OpenPlatformUserEntity } from '@yxr/mongo'
import { UserType } from '@yxr/common'
import { 
  IAuthService, 
  TokenValidationResult, 
  AuthenticatedUser, 
  SessionInfo 
} from '../interfaces/auth.interface'

/**
 * 优化后的统一认证服务
 * 
 * 将所有认证逻辑集中在这个服务中，减少守卫的直接依赖
 */
@Injectable()
export class OptimizedUnifiedAuthService implements IAuthService {
  private readonly logger = new Logger(OptimizedUnifiedAuthService.name)

  constructor(
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(OpenPlatformUserEntity.name) private openPlatformUserModel: Model<OpenPlatformUserEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  /**
   * 验证管理员Token
   */
  async validateAdminToken(token: string): Promise<TokenValidationResult> {
    try {
      // 尝试从缓存获取管理员信息（保持原有逻辑）
      const user = await this.cacheManager.get<AdminEntity>(token)
      
      if (user) {
        return {
          isValid: true,
          user: this.formatAdminUser(user),
          session: {
            userId: (user as any)._id?.toString() || (user as any).id?.toString(),
            userType: UserType.ADMIN,
            teamId: undefined,
            applicationId: undefined
          }
        }
      }

      return { isValid: false, error: 'Admin token not found in cache' }
    } catch (error) {
      this.logger.warn(`Admin token validation failed: ${error.message}`)
      return { isValid: false, error: error.message }
    }
  }

  /**
   * 验证开放平台用户Token
   */
  async validateOpenPlatformToken(token: string): Promise<TokenValidationResult> {
    try {
      // 尝试开放平台用户的缓存格式
      const user = await this.cacheManager.get<OpenPlatformUserEntity>(`open_platform:${token}`)
      
      if (user) {
        return {
          isValid: true,
          user: this.formatOpenPlatformUser(user),
          session: {
            userId: (user as any)._id.toString(),
            userType: UserType.OPEN_PLATFORM,
            teamId: undefined,
            applicationId: undefined
          }
        }
      }

      return { isValid: false, error: 'Open platform token not found in cache' }
    } catch (error) {
      this.logger.warn(`Open platform token validation failed: ${error.message}`)
      return { isValid: false, error: error.message }
    }
  }

  /**
   * 验证应用Token（这个方法在这里主要是为了接口完整性，实际验证在ApplicationTokenService中）
   */
  async validateApplicationToken(token: string): Promise<TokenValidationResult> {
    // 这个方法主要是为了接口完整性
    // 实际的应用Token验证在ApplicationTokenService中进行
    return { isValid: false, error: 'Use ApplicationTokenService for application token validation' }
  }

  /**
   * 开发环境模拟Token验证
   */
  async validateDevMockToken(userId: string): Promise<TokenValidationResult> {
    try {
      // 首先尝试作为管理员ID
      const adminUser = await this.adminModel.findById<AdminEntity>(new Types.ObjectId(userId))
      if (adminUser) {
        this.logger.debug(`模拟的管理员会话: userId: ${userId}`)
        return {
          isValid: true,
          user: this.formatAdminUser(adminUser),
          session: {
            userId: (adminUser as any)._id.toString(),
            userType: UserType.ADMIN,
            teamId: undefined,
            applicationId: undefined
          }
        }
      }

      // 然后尝试作为开放平台用户ID
      const openPlatformUser = await this.openPlatformUserModel.findById<OpenPlatformUserEntity>(
        new Types.ObjectId(userId)
      )
      if (openPlatformUser) {
        this.logger.debug(`模拟的开放平台会话: userId: ${userId}`)
        return {
          isValid: true,
          user: this.formatOpenPlatformUser(openPlatformUser),
          session: {
            userId: (openPlatformUser as any)._id.toString(),
            userType: UserType.OPEN_PLATFORM,
            teamId: undefined,
            applicationId: undefined
          }
        }
      }

      return { isValid: false, error: 'User not found for dev mock' }
    } catch (error) {
      this.logger.warn(`Dev mock token validation failed: ${error.message}`)
      return { isValid: false, error: error.message }
    }
  }

  /**
   * 生成管理员Token（保持原有逻辑）
   */
  async generateAdminToken(user: any): Promise<string> {
    // 这里保持原有的token生成逻辑
    // 具体实现可以参考原有的UnifiedAuthService
    throw new Error('Method not implemented - use original UnifiedAuthService')
  }

  /**
   * 生成开放平台用户Token（保持原有逻辑）
   */
  async generateOpenPlatformToken(user: any): Promise<string> {
    // 这里保持原有的token生成逻辑
    // 具体实现可以参考原有的UnifiedAuthService
    throw new Error('Method not implemented - use original UnifiedAuthService')
  }

  /**
   * 格式化管理员用户信息
   */
  private formatAdminUser(user: AdminEntity): AuthenticatedUser {
    return {
      id: (user as any)._id?.toString() || (user as any).id?.toString(),
      userType: UserType.ADMIN,
      username: user.username,
      name: user.name,
      role: user.role
    }
  }

  /**
   * 格式化开放平台用户信息
   */
  private formatOpenPlatformUser(user: OpenPlatformUserEntity): AuthenticatedUser {
    return {
      id: (user as any)._id.toString(),
      userType: UserType.OPEN_PLATFORM,
      phone: user.phone,
      nickname: user.nickname,
      status: user.status
    }
  }
}
