import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { OpenPlatformStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_users',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformUserEntity {
  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true
  })
  phone: string

  @Prop({
    type: String,
    required: false
  })
  password?: string

  @Prop({
    type: String,
    required: false
  })
  salt?: string

  @Prop({
    type: String,
    required: false,
    maxlength: 50
  })
  nickname?: string

  @Prop({
    type: Number,
    enum: OpenPlatformStatus,
    required: true,
    default: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  remark?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const OpenPlatformUserSchema: ModelDefinition = {
  name: OpenPlatformUserEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformUserEntity)
}

export const OpenPlatformUserMongoose = MongooseModule.forFeature([OpenPlatformUserSchema])
