import { Body, Controller, Get, Param, Post, Put, Query, Req } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { OrderService } from './order.service'
import {
  ChannelGiftRequestDTO,
  ChannelGiftResponseDTO,
  OrderInfoResponseDTO,
  OrderInterestResponseDTO,
  OrderListRequest,
  OrderPriceDetailResponseDTO,
  orderPriceRequestDTO,
  OrderRequestCreateOrderDTO,
  OrderResponseCreateOrderDTO,
  OrderResponseQueryOrderDTO,
  OrdersListResponseDTO,
  OrderStatusRequestDTO,
  PayInfoResponseDTO,
  PeddingOrderResponseDTO,
  RenewOrderRequest,
  UpgradeOrderRequest
} from './order.dto'
import { BaseResponseDTO, BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'

@Controller('orders')
@ApiTags('订单管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  @ApiOperation({ summary: '获取订单列表' })
  @ApiOkResponse({ description: '操作成功', type: OrdersListResponseDTO })
  @ApiQuery({ type: OrderListRequest })
  async getOrders(@Query() query: OrderListRequest) {
    return this.orderService.getOrders(query)
  }

  @Post()
  @ApiOperation({ summary: '创建订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  createOrder(@Body() body: OrderRequestCreateOrderDTO) {
    return this.orderService.createOrder(body)
  }

  @Post('price')
  @ApiOperation({ summary: '订单计算金额' })
  @ApiOkResponse({ type: OrderPriceDetailResponseDTO })
  calculateOrderPrice(@Body() body: orderPriceRequestDTO) {
    return this.orderService.calculateOrderPrice(body)
  }

  @Post('upgrade')
  @ApiOperation({ summary: '升级订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  createUpgradeOrder(@Body() body: UpgradeOrderRequest) {
    return this.orderService.createUpgradeOrder(body)
  }

  @Post('renew')
  @ApiOperation({ summary: '续费订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  createRenewOrder(@Body() body: RenewOrderRequest) {
    return this.orderService.createRenewOrder(body)
  }

  @Get(':orderNo/status')
  @ApiOperation({ summary: '查询支付状态' })
  @ApiOkResponse({ type: OrderResponseQueryOrderDTO })
  getOrderStatus(@Param('orderNo') orderNo: string) {
    return this.orderService.getOrderStatus(orderNo)
  }

  @Put(':orderNo/status')
  @ApiOperation({ summary: '取消订单' })
  @ApiOkResponse({ type: BaseResponseDTO })
  putOrderStatus(@Param('orderNo') orderNo: string, @Body() body: OrderStatusRequestDTO) {
    return this.orderService.putOrderStatus(orderNo, body.orderStatus)
  }

  @Get(':orderNo')
  @ApiOperation({ summary: '订单详情' })
  @ApiOkResponse({ type: OrderInfoResponseDTO })
  getOrder(@Param('orderNo') orderNo: string) {
    return this.orderService.getOrderInfo(orderNo)
  }

  @Get(':orderNo/payinfo')
  @ApiOperation({ summary: '支付渠道信息' })
  @ApiOkResponse({ type: PayInfoResponseDTO })
  getPayInfo(@Param('orderNo') orderNo: string) {
    return this.orderService.getPayInfo(orderNo)
  }

  @Get('pending')
  @ApiOperation({ summary: '是否有待付款订单' })
  @ApiOkResponse({ type: PeddingOrderResponseDTO })
  getPendingOrder() {
    return this.orderService.getPeddingOrders()
  }

  @Get('interest')
  @ApiOperation({ summary: '获取订单VIP规格' })
  @ApiOkResponse({ type: OrderInterestResponseDTO })
  getInterest() {
    return this.orderService.getInterest()
  }

  @Post('channel/gift')
  @ApiOperation({ summary: '渠道赠送' })
  @ApiOkResponse({ type: ChannelGiftResponseDTO })
  async channelGiftOrder(@Body() body: ChannelGiftRequestDTO) {
    return this.orderService.channelGiftOrder(body)
  }
}
