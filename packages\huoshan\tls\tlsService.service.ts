import { Injectable, Logger } from '@nestjs/common'
import { tlsOpenapi } from '@volcengine/openapi'
import { GenerateSLSDto, TlsDTO } from './tls.dto'
import { FastifyRequest } from 'fastify'
import crypto from 'crypto'

@Injectable()
export class TlsService {
  logger = new Logger('TlsService')

  private tlsOpenapiService = tlsOpenapi.defaultService

  constructor() {
    this.tlsOpenapiService.setSecretKey(process.env.TLS_SECRET_ACCESS_KEY)
    this.tlsOpenapiService.setAccessKeyId(process.env.TLS_ACCESS_KEY_ID)
    this.tlsOpenapiService.setHost(process.env.TLS_HOST)
    this.tlsOpenapiService.setRegion(process.env.TLS_REGION)
  }

  private generateRequestId(): string {
    return crypto.randomBytes(8).toString('hex')
  }

  async info(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'info', message, extra)
  }

  async error(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'error', message, extra)
  }

  async warn(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'warn', message, extra)
  }

  async debug(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'debug', message, extra)
  }

  private async log(
    req: FastifyRequest | null,
    level: 'info' | 'error' | 'warn' | 'debug',
    message: string,
    extra: Record<string, any> = {}
  ) {
    const logEntry = {
      logLevel: level,
      requestId: req?.headers['x-request-id'] || this.generateRequestId(),
      host: req?.headers['host'] || 'unknown',
      userAgent: req?.headers['user-agent'] || 'unknown',
      authorization: req?.headers['authorization'] || 'unknown',
      message: message,
      ...extra
    }

    try {
      const logsBuffer = await tlsOpenapi.TlsService.objToProtoBuffer({
        LogGroups: [
          {
            Logs: [this.dtoToGenerateSLSDto(logEntry)],
            Source: '',
            LogTags: [],
            FileName: '',
            ContextFlow: ''
          }
        ]
      })

      const param = {
        TopicId: process.env.TLS_LOG_TOPIC_ID,
        LogGroupList: Buffer.from(logsBuffer)
      }

      this.tlsOpenapiService.PutLogs(param)
    } catch (error) {
      console.log(error)
    }
  }

  private dtoToGenerateSLSDto(slsDto: Record<string, any> = {}): GenerateSLSDto {
    return {
      Time: Math.floor(Date.now() / 1000),
      Contents: Object.entries(slsDto)
        .filter(([_, value]) => value !== undefined && value !== null) // 过滤无效值
        .map(([key, value]) => ({ Key:key, Value: String(value) })) // 统一转换为字符串
    }
  }

  async putLogs(context: TlsDTO) {
    const logsBuffer = await tlsOpenapi.TlsService.objToProtoBuffer({
      LogGroups: [
        {
          Logs: [
            {
              Time: Math.floor(Date.now() / 1000),
              Contents: [
                { Key: 'timestamp', Value: new Date().toISOString() },
                ...(context
                  ? Object.entries(context).map(([k, v]) => ({ Key: k, Value: JSON.stringify(v) }))
                  : [])
              ]
            }
          ],
          Source: '',
          LogTags: [],
          FileName: '',
          ContextFlow: ''
        }
      ]
    })
    try {
      const result = await this.tlsOpenapiService.PutLogs({
        TopicId: process.env.TLS_LOG_TOPIC_ID,
        LogGroupList: Buffer.from(logsBuffer)
      })
    } catch (error) {
      console.log(error, 'error')
    }
  }
}
