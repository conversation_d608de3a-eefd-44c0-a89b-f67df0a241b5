import { Module } from '@nestjs/common'
import { AccountAuthController } from './account-auth.controller'
import { AccountAuthService } from './account-auth.service'
import { OverseasModule } from '@yxr/overseas'

@Module({
  imports: [
    // MemberMongoose,
    // TeamMongoose,
    // TaskMongoose,
    // TaskSetMongoose,
    // ContentMongoose,
    // UserMongoose,
    // GroupMongoose,
    // PlatformAccountMongoose,
    // PlatformAccountOverviewMongoose,
    // ContentStatisticMongoose,
    // PlatformAccountSummaryMongoose,
    // PlatformAccountModule,
    // BrowserModule,
    // TeamModule,
    // TaskModule,
    // WebhookModule,
    // KuaidailiModule,
    // CommonModule,
    // WechatIpadModule,
    // SlSLoggerModule,
    // OverseasModule，
    OverseasModule
  ],
  controllers: [AccountAuthController],
  providers: [
    AccountAuthService
  ]
})
export class AccountAuthModule {
}
