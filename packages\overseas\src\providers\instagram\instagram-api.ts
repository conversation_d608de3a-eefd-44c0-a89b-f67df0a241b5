import { Injectable, Lo<PERSON>, Scope } from '@nestjs/common'
import { OverseasContext } from '../types'
import { _ensureSuccessfulInvoke } from '../utils'
import { createAxiosInstance } from '../../utils/axios-config'

@Injectable({ scope: Scope.TRANSIENT })
export class InstagramApi {
  logger = new Logger(InstagramApi.name)

  private readonly clientId = process.env.INSTAGRAM_CLIENT_ID

  private readonly clientSecret = process.env.INSTAGRAM_CLIENT_SECRET

  private readonly api_version = 'v22.0'

  /**
   * 获取 Instagram 访问令牌(短效的)
   *
   * https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/business-login
   *
   * @param context
   * @param params
   */
  async get_short_live_oauth_access_token(context: OverseasContext, params: { code: string; redirect_uri: any }) {
    return await _ensureSuccessfulInvoke<{
      access_token: string
      permissions: string[]
      user_id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://api.instagram.com', {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
      })
      return axios.post(
        '/oauth/access_token',
        {
          ...params,
          grant_type: 'authorization_code',
          client_id: this.clientId,
          client_secret: this.clientSecret
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )
    })
  }

  /**
   * 获取 Instagram 访问令牌(长效的)
   *
   * https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/business-login
   *
   * @param context
   * @param params
   */
  async get_long_live_oauth_access_token(context: OverseasContext, params: { access_token: string }) {
    return await _ensureSuccessfulInvoke<{
      access_token: string
      expires_in: number
      token_type: string
      user_id: string
      scopes: string[]
    }>({}, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.get('/access_token', {
        params: {
          ...params,
          grant_type: 'ig_exchange_token',
          client_secret: this.clientSecret
        }
      })
    })
  }

  /**
   * 获取当前登录用户信息
   *
   * https://developers.facebook.com/docs/instagram-platform/reference/me
   *
   * @param context
   * @param accessToken
   */
  async get_me(context: OverseasContext, accessToken: string) {
    const response = await _ensureSuccessfulInvoke<{
      id: string
      user_id: string
      username: string
      name: string
      account_type: string
      profile_picture_url: string
      followers_count: number
      follows_count: number
      media_count: number
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.get('/v22.0/me', {
        params: {
          fields: 'id,user_id,username,name,account_type,profile_picture_url,followers_count,follows_count,media_count'
        },
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })

    this.logger.debug(`[instagram.api.get_me]: ${JSON.stringify(response, null, 2)}`)
    return response
  }

  /**
   * 创建图片帖子
   */
  async createImagePost(context: OverseasContext, params: {
    image_url: string
    caption?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const userId = this.getUserIdFromContext(context)

    // 第一步：创建媒体容器
    const container = await _ensureSuccessfulInvoke<{
      id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.post(`/${this.api_version}/${userId}/media`, {
        image_url: params.image_url,
        caption: params.caption
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })

    // 第二步：发布媒体
    return await this.publishMedia(context, container.id, accessToken)
  }

  /**
   * 创建视频帖子
   */
  async createVideoPost(context: OverseasContext, params: {
    video_url: string
    caption?: string
    cover_url?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const userId = this.getUserIdFromContext(context)

    // 第一步：创建视频媒体容器
    const container = await _ensureSuccessfulInvoke<{
      id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.post(`/${this.api_version}/${userId}/media`, {
        media_type: 'VIDEO',
        video_url: params.video_url,
        caption: params.caption,
        cover_url: params.cover_url
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })

    // 第二步：等待视频处理完成
    await this.waitForVideoProcessing(context, container.id, accessToken)

    // 第三步：发布媒体
    return await this.publishMedia(context, container.id, accessToken)
  }

  /**
   * 创建轮播帖子（多张图片）
   */
  async createCarouselPost(context: OverseasContext, params: {
    children: Array<{ media_type: 'IMAGE' | 'VIDEO'; image_url?: string; video_url?: string }>
    caption?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const userId = this.getUserIdFromContext(context)

    // 第一步：为每个媒体创建容器
    const childContainers = []
    for (const child of params.children) {
      const container = await _ensureSuccessfulInvoke<{
        id: string
      }>(context, () => {
        const axios = createAxiosInstance('https://graph.instagram.com')
        return axios.post(`/${this.api_version}/${userId}/media`, {
          media_type: child.media_type,
          image_url: child.image_url,
          video_url: child.video_url,
          is_carousel_item: true
        }, {
          headers: { Authorization: `Bearer ${accessToken}` }
        })
      })
      childContainers.push(container.id)
    }

    // 第二步：创建轮播容器
    const carouselContainer = await _ensureSuccessfulInvoke<{
      id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.post(`/${this.api_version}/${userId}/media`, {
        media_type: 'CAROUSEL',
        children: childContainers.join(','),
        caption: params.caption
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })

    // 第三步：发布轮播
    return await this.publishMedia(context, carouselContainer.id, accessToken)
  }

  /**
   * 发布媒体
   */
  private async publishMedia(context: OverseasContext, containerId: string, accessToken: string) {
    const userId = this.getUserIdFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.post(`/${this.api_version}/${userId}/media_publish`, {
        creation_id: containerId
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 等待视频处理完成
   */
  private async waitForVideoProcessing(context: OverseasContext, containerId: string, accessToken: string, maxWaitTime = 300000) {
    const startTime = Date.now()

    while (Date.now() - startTime < maxWaitTime) {
      const status = await _ensureSuccessfulInvoke<{
        status_code: string
      }>(context, () => {
        const axios = createAxiosInstance('https://graph.instagram.com')
        return axios.get(`/${this.api_version}/${containerId}`, {
          params: { fields: 'status_code' },
          headers: { Authorization: `Bearer ${accessToken}` }
        })
      })

      if (status.status_code === 'FINISHED') {
        return
      } else if (status.status_code === 'ERROR') {
        throw new Error('视频处理失败')
      }

      // 等待5秒后重试
      await new Promise(resolve => setTimeout(resolve, 5000))
    }

    throw new Error('视频处理超时')
  }

  /**
   * 获取媒体信息
   */
  async getMedia(context: OverseasContext, mediaId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
      media_type: string
      media_url: string
      permalink: string
      caption: string
      timestamp: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.instagram.com')
      return axios.get(`/${this.api_version}/${mediaId}`, {
        params: {
          fields: 'id,media_type,media_url,permalink,caption,timestamp'
        },
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 删除媒体
   */
  async deleteMedia(context: OverseasContext, mediaId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      success: boolean
    }>(context, () => {
      return axios.delete(`https://graph.instagram.com/${this.api_version}/${mediaId}`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 从上下文中获取访问令牌
   */
  private getAccessTokenFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.access_token) {
      throw new Error('缺少Instagram访问令牌')
    }
    return credentials.access_token
  }

  /**
   * 从上下文中获取用户ID
   */
  private getUserIdFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.user_id) {
      throw new Error('缺少Instagram用户ID')
    }
    return credentials.user_id
  }
}
