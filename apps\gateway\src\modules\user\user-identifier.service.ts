import { Injectable, BadRequestException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { UserEntity } from '@yxr/mongo'
import { UserValidationUtils, UserValidationError } from '@yxr/common'

/**
 * 用户标识符管理服务
 * 处理用户标识符的唯一性验证和管理
 */
@Injectable()
export class UserIdentifierService {
  constructor(
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>
  ) {}

  /**
   * 验证手机号是否已被使用
   */
  async validatePhoneUniqueness(phone: string, excludeUserId?: string): Promise<void> {
    if (!phone) return

    const query: any = { phone }
    if (excludeUserId) {
      query._id = { $ne: excludeUserId }
    }

    const existingUser = await this.userModel.findOne(query).lean()
    if (existingUser) {
      throw new BadRequestException(
        UserValidationUtils.getErrorMessage(UserValidationError.PHONE_ALREADY_EXISTS)
      )
    }
  }

  /**
   * 验证账号是否已被使用
   */
  async validateAccountUniqueness(account: string, excludeUserId?: string): Promise<void> {
    if (!account) return

    const query: any = { account }
    if (excludeUserId) {
      query._id = { $ne: excludeUserId }
    }

    const existingUser = await this.userModel.findOne(query).lean()
    if (existingUser) {
      throw new BadRequestException(
        UserValidationUtils.getErrorMessage(UserValidationError.ACCOUNT_ALREADY_EXISTS)
      )
    }
  }

  /**
   * 验证用户标识符的完整性（格式+唯一性）
   */
  async validateUserIdentifiers(
    phone?: string,
    account?: string,
    excludeUserId?: string
  ): Promise<void> {
    // 验证格式
    const formatValidation = UserValidationUtils.validateUserIdentifiers(phone, account)
    if (!formatValidation.isValid) {
      throw new BadRequestException(formatValidation.message)
    }

    // 验证唯一性
    await Promise.all([
      this.validatePhoneUniqueness(phone, excludeUserId),
      this.validateAccountUniqueness(account, excludeUserId)
    ])
  }

  /**
   * 根据手机号或账号查找用户
   */
  async findUserByIdentifier(phone?: string, account?: string): Promise<UserEntity | null> {
    if (phone) {
      return await this.userModel.findOne({ phone }).lean()
    }
    
    if (account) {
      return await this.userModel.findOne({ account }).lean()
    }

    return null
  }

  /**
   * 检查用户标识符是否存在
   */
  async checkIdentifierExists(phone?: string, account?: string): Promise<boolean> {
    const user = await this.findUserByIdentifier(phone, account)
    return !!user
  }

  /**
   * 获取用户的所有标识符
   */
  async getUserIdentifiers(userId: string): Promise<{ phone?: string; account?: string }> {
    const user = await this.userModel.findById(userId).select('phone account').lean()
    if (!user) {
      throw new BadRequestException('用户不存在')
    }

    return {
      phone: user.phone,
      account: user.account
    }
  }

  /**
   * 更新用户标识符
   */
  async updateUserIdentifiers(
    userId: string,
    updates: { phone?: string; account?: string }
  ): Promise<void> {
    // 验证新的标识符
    await this.validateUserIdentifiers(updates.phone, updates.account, userId)

    // 构建更新对象
    const updateData: any = {}
    if (updates.phone !== undefined) {
      updateData.phone = updates.phone
    }
    if (updates.account !== undefined) {
      updateData.account = updates.account
    }

    // 执行更新
    await this.userModel.updateOne({ _id: userId }, updateData)
  }

  /**
   * 生成建议的账号名称（基于手机号）
   */
  generateSuggestedAccount(phone: string): string {
    if (!phone || !UserValidationUtils.isPhoneNumber(phone)) {
      return ''
    }

    // 基于手机号生成账号建议
    const prefix = 'user'
    const suffix = phone.slice(-6) // 取手机号后6位
    return `${prefix}_${suffix}`
  }

  /**
   * 批量检查账号名称的可用性
   */
  async checkAccountAvailability(accounts: string[]): Promise<{ [account: string]: boolean }> {
    const result: { [account: string]: boolean } = {}

    for (const account of accounts) {
      if (!UserValidationUtils.validateAccountFormat(account)) {
        result[account] = false
        continue
      }

      const exists = await this.userModel.findOne({ account }).lean()
      result[account] = !exists
    }

    return result
  }

  /**
   * 生成可用的账号名称建议
   */
  async generateAvailableAccountSuggestions(baseAccount: string, count: number = 5): Promise<string[]> {
    const suggestions: string[] = []
    let currentBase = baseAccount

    // 如果基础账号不符合格式，先修正
    if (!UserValidationUtils.validateAccountFormat(currentBase)) {
      currentBase = 'user_account'
    }

    for (let i = 0; i < count; i++) {
      let suggestion = i === 0 ? currentBase : `${currentBase}_${i}`
      
      // 检查是否可用
      const exists = await this.userModel.findOne({ account: suggestion }).lean()
      if (!exists) {
        suggestions.push(suggestion)
      } else {
        // 如果已存在，尝试添加随机数字
        const randomSuffix = Math.floor(Math.random() * 9999).toString().padStart(4, '0')
        suggestion = `${currentBase}_${randomSuffix}`
        
        const existsWithRandom = await this.userModel.findOne({ account: suggestion }).lean()
        if (!existsWithRandom) {
          suggestions.push(suggestion)
        }
      }
    }

    return suggestions
  }

  /**
   * 验证登录标识符（自动识别是手机号还是账号）
   */
  async validateLoginIdentifier(identifier: string): Promise<{
    type: 'phone' | 'account'
    isValid: boolean
    user?: UserEntity
  }> {
    const identifierInfo = UserValidationUtils.identifyUserInput(identifier)
    
    if (!identifierInfo.isValid) {
      return {
        type: identifierInfo.type === 'phone' ? 'phone' : 'account',
        isValid: false
      }
    }

    // 查找用户
    const user = await this.findUserByIdentifier(
      identifierInfo.type === 'phone' ? identifier : undefined,
      identifierInfo.type === 'account' ? identifier : undefined
    )

    return {
      type: identifierInfo.type === 'phone' ? 'phone' : 'account',
      isValid: true,
      user: user || undefined
    }
  }
}
