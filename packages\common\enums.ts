export enum MemberStatusEnum {
  /**
   * 未加入
   */
  NotJoined = 'notJoined',

  /**
   * 待处理
   */
  Pending = 'pending',

  /**
   * 已加入
   */
  Joined = 'joined'
}

export enum ProposalStatusEnum {
  /**
   * 待处理
   */
  Pending = 'pending',

  /**
   * 已通过
   */
  Approved = 'approved',

  /**
   * 已拒绝
   */
  Rejected = 'rejected'
}

export enum InvitationStatusEnum {
  /**
   * 待处理
   */
  Pending = 'pending',

  /**
   * 已同意
   */
  Approved = 'approved',

  /**
   * 已拒绝
   */
  Rejected = 'rejected'
}

/**
 * 消息类型
 */
export enum NoticeTypesEnum {
  /**
   * 常规消息
   */
  Regular = 'regular',

  /**
   * 用户申请加入团队
   */
  Proposal = 'proposal',

  /**
   * 邀请用户加入团队
   */
  Invitation = 'invitation',

  /**
   * 系统消息
   */
  System = 'system'
}

/**
 * 团队存储桶名称枚举
 */
export enum TeamBucketNamesEnum {
  /**
   * 团队资产存储桶
   * 存储团队内作品相关资源(作品封面, 作品视频, 作品音频, 作品图片, 作品文档, etc...)
   */
  Assets = 'assets',

  /**
   * 团队附件存储桶
   * 存储团队内相关附件(Logo, etc...)
   */
  Attachments = 'attachments',

  /**
   *
   */
  SiteSpaces = 'site-spaces',

  /**
   * 素材库
   */
  MaterialLibrary = 'material-library',

  /**
   * 微信发布
   */
  WechatPublish = 'wechat-pubish',

  /**
   * 云发布
   */
  CloudPublish = 'cloud-publish',

  /**
   * 云发布
   */
  Logs = 'logs'
}

export enum TaskSetStatusEnum {
  /**
   * 发布中(子任务未全部结束，都属于发布中)
   */
  Publishing = 'publishing',

  /**
   * 全部发布成功(子任务全部结束时，全部成功)
   */
  AllSuccess = 'allsuccessful',

  /**
   * 部分发布成功(子任务全部结束时，有成功任务)
   */
  PartialSuccess = 'partialsuccessful',

  /**
   * 全部发布失败(子任务全部结束时，全部失败)
   */
  AllFailed = 'allfailed'
}

export enum UploadStatusEnum {
  /**
   * 等待中
   */
  Waiting = 'waiting',
  /**
   * 上传中
   */
  Doing = 'doing',

  /**
   * 上传成功
   */
  Successful = 'successful',

  /**
   * 上传失败
   */
  Failed = 'failed'
}

export enum MaterialTypeEnum {
  /**
   * 系统
   */
  System = 'system',

  /**
   * 视频
   */
  Video = 'video',

  /**
   * 图片
   */
  Image = 'image'
}

/**
 * 素材库容量
 */
export enum StorageSize {
  M_500 = 500 * 1024 * 1024, // 500MB
  G_1 = 1 * 1024 * 1024 * 1024, // 1GB
  G_5 = 5 * 1024 * 1024 * 1024, // 5GB
  G_20 = 20 * 1024 * 1024 * 1024, // 20GB
  G_50 = 50 * 1024 * 1024 * 1024 // 50GB
}

export enum PlatformType {
  '其他' = 0, //爬虫账号
  '开放平台' = 1,
  '海外平台' = 2
}

/**
 * 特殊平台名称枚举
 */
export enum PlatformNameEnum {
  微信公众号 = '微信公众号', //微信官方开放平台
  视频号 = '视频号',
  微信 = '微信', //第三方微信
  其他 = '其他账号' //网站空间账号
}

export enum OrderStatus {
  Pending = 'pending', // 待支付
  Paid = 'paid', // 已支付
  Cancelled = 'cancelled', // 已取消
  Refunded = 'refunded' // 已退费
}

export enum OrderType {
  Create = 'create', // 开通
  Renew = 'renew', // 续费
  Upgrade = 'upgrade', // 升级
  Gift = 'gift', // 赠送
  // 开放平台专用订单类型
  OpenPlatformCreate = 'open_platform_create', // 开放平台开通订单
  OpenPlatformRenew = 'open_platform_renew', // 开放平台续费订单
  OpenPlatformAddon = 'open_platform_addon', // 开放平台增购订单
  // 新的开放平台订单类型
  OpenPlatformAccountPoints = 'open_platform_account_points', // 开放平台账号点数订单
  OpenPlatformTraffic = 'open_platform_traffic' // 开放平台流量订单
}

export enum OrderSource {
  Online = 'online',
  System = 'system',
  OpenPlatform = 'open_platform' // 开放平台来源
}

export enum PayType {
  WechatPay = 'wechatPay', //微信支付
  Alipay = 'alipay', // 阿里支付
  CorporateTransfer = 'corporateTransfer', // 对公转账
  Other = 'other' // 其他
}

/**
 * 开放平台订单主类型
 */
export enum OpenPlatformOrderMainType {
  Main = 'main', // 主订单（开通/续费）
  Addon = 'addon' // 增购订单（附属订单）
}

/**
 * 开放平台订单资源类型
 */
export enum OpenPlatformOrderResourceType {
  AccountPoints = 'account_points', // 账号点数订单
  Traffic = 'traffic', // 流量订单,
  Package = 'package'
}
/**
 * 作品内容类型
 */
export enum ContentType {
  all = 'all', //不需要区分类型
  video = 'video',
  miniVideo = 'miniVideo',
  dynamic = 'dynamic',
  article = 'article'
}

/**
 * 发布渠道类型
 */
export enum PublishChannel {
  local = 'local', //本机发布
  cloud = 'cloud' //云端发布
}

// 微信错误码信息
export const WeChatErrorCode = {
  0: '成功',
  47001: '数据格式错误',
  88000: '没有留言权限',

  getErrorMessage(code: number) {
    return this[code] || code
  }
}

export enum AdminRole {
  SuperAdmin = 0, // 超级管理员
  Admin = 1, // 管理员
  Customer = 2 // 客服
}

export enum SalesType {
  NotBuy = 0, //未购买
  FirstBuy = 1, //新购
  ReBuy = 2 //复购
}

export enum LiteSystemErrorCode {
  IsNotVip = 1001, //没有VIP权益
  AccountLock = 1002, //账号已锁定
  AccountLockDestroy = 1003, //账号锁定已销毁
  SecretLockError = 1004 //密钥错误
}

/**
 * 子任务阶段状态
 */
export enum TaskStages {
  Upload = 'upload', //上传
  Push = 'push', //推送
  Transcoding = 'transcoding', //转码
  Review = 'review', //审核
  Scheduled = 'scheduled', //定时
  Success = 'success', // 成功
  NotFount = 'notfound' //未找到作品
}

export enum StageStatus {
  /**
   * 进行中
   */
  Doing = 'doing',

  /**
   * 成功
   */
  Success = 'success',

  /**
   * 失败
   */
  Fail = 'fail'
}

export enum OverviewDateTypeEnum {
  Day = 'day',
  Seven = 'seven',
  Thirty = 'thirty'
}

/**
 * 历史概览统计维度枚举
 */
export enum HistoryStatisticTypeEnum {
  home = 'home',
  account = 'account',
  content = 'content',
  platformAccount = 'platformAccount'
}

// 一级平台
export const FirstLevelPlatform = [
  '抖音',
  '快手',
  '小红书',
  '视频号',
  '哔哩哔哩',
  '新浪微博',
  '头条号',
  '百家号'
]

// 二级平台
export const SecondaryPlatform = [
  '知乎',
  '企鹅号',
  '搜狐号',
  '一点号',
  '大鱼号',
  '网易号',
  '爱奇艺',
  '腾讯微视'
]

export const AllPlatforms = [
  '抖音',
  '快手',
  '视频号',
  '小红书',
  '哔哩哔哩',
  '百家号',
  '头条号',
  '新浪微博',
  '知乎',
  '企鹅号',
  '搜狐号',
  '一点号',
  '大鱼号',
  '网易号',
  '爱奇艺',
  '腾讯微视',
  '微信',
  '微信公众号'
]

/**
 * 机器人类型
 */
export enum RobotTypeEnum {
  FeiShu = 'feishu',
  DingDing = 'dingding',
  QiyeWx = 'qiyewx'
}

export enum ReportTypeEnum {
  Day = 'day', //日报
  Weekly = 'weekly' //周报
}

export enum SendingTimeTypeEnum {
  Day = 'day', //每天
  Monday = 'monday', //星期一
  Friday = 'friday' //星期五
}

export enum AdTypeEmun {
  Banner = 'banner', //banner
  Popup = 'popup' //弹框
}

export enum PopupTypeEmun {
  EveryDay = 'everyday', //每天一次
  ThreeDay = 'threeday' //三天一次
}

export enum TaskStatusEmun {
  Cancel = 'cancel', //已取消
  Deleted = 'deleted' //已删除
}
/**
 * 数据传webhook的时候增加event字段
 * 回传事件类型
 */
export enum CallBackDataEvent {
  /**
   * 发布进度
   */
  publishProcess = 'publishProcess',
  /**
   * 发布结果
   */
  publishResult = 'publishResult',
  /**
   * 审核状态
   */
  auditStatus = 'auditStatus',
  /**
   * 账号概览数据
   */
  accountOverview = 'accountOverview',
  /**
   * 账号内容列表
   */
  contentList = 'contentList',
}