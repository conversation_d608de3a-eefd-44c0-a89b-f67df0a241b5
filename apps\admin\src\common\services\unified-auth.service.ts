import { Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { ConfigService } from '@nestjs/config'
import { nanoid } from 'nanoid'
import { AdminEntity, OpenPlatformUserEntity } from '@yxr/mongo'
import { UserType } from '@yxr/common'
import { RootConfigMap } from '@yxr/config'

/**
 * 统一Token缓存结构
 */
interface UnifiedTokenCache {
  userType: UserType
  user: AdminEntity | OpenPlatformUserEntity
  userId: string
  applicationId?: string // 开放平台用户特有
  teamId?: string // admin用户可能有
}

@Injectable()
export class UnifiedAuthService {
  private readonly logger = new Logger(UnifiedAuthService.name)

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService
  ) {}

  /**
   * 为Admin用户生成统一格式的Token
   */
  async generateAdminToken(adminUser: AdminEntity): Promise<string> {
    const authorization = nanoid()
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    // 清理旧的token
    const oldAuthorization = await this.cacheManager.get<string>(adminUser.username)
    if (oldAuthorization) {
      await Promise.all([
        this.cacheManager.del(adminUser.username),
        this.cacheManager.del(oldAuthorization),
        this.cacheManager.del(`unified:${oldAuthorization}`)
      ])
    }

    const tokenCache: UnifiedTokenCache = {
      userType: UserType.ADMIN,
      user: adminUser,
      userId: (adminUser as any)._id?.toString() || (adminUser as any).id?.toString(),
      teamId: undefined
    }

    // 存储新的token（同时保持旧格式兼容性）
    await Promise.all([
      this.cacheManager.set(adminUser.username, authorization, overdueToken),
      this.cacheManager.set(authorization, adminUser, overdueToken), // 旧格式兼容
      this.cacheManager.set(`unified:${authorization}`, tokenCache, overdueToken) // 新统一格式
    ])

    return authorization
  }

  /**
   * 为开放平台用户生成统一格式的Token
   * 使用与UnifiedTokenGuard兼容的缓存格式
   */
  async generateOpenPlatformToken(
    openPlatformUser: OpenPlatformUserEntity,
    _applicationId?: string
  ): Promise<string> {
    const authorization = nanoid()
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    // 清理旧的token
    const userIdKey = `open_platform_user:${(openPlatformUser as any)._id}`
    const oldAuthorization = await this.cacheManager.get<string>(userIdKey)
    if (oldAuthorization) {
      await Promise.all([
        this.cacheManager.del(userIdKey),
        this.cacheManager.del(`open_platform:${oldAuthorization}`)
      ])
    }

    // 存储新的token - 使用与UnifiedTokenGuard兼容的格式
    await Promise.all([
      this.cacheManager.set(userIdKey, authorization, overdueToken),
      this.cacheManager.set(`open_platform:${authorization}`, openPlatformUser, overdueToken)
    ])

    return authorization
  }

  /**
   * 删除用户的认证Token
   */
  async deleteToken(authorization: string, userType: UserType, userIdentifier: string): Promise<void> {
    if (userType === UserType.ADMIN) {
      await Promise.all([
        this.cacheManager.del(userIdentifier), // username
        this.cacheManager.del(authorization),
        this.cacheManager.del(`unified:${authorization}`)
      ])
    } else if (userType === UserType.OPEN_PLATFORM) {
      const userIdKey = `open_platform_user:${userIdentifier}`
      await Promise.all([
        this.cacheManager.del(userIdKey),
        this.cacheManager.del(`unified:${authorization}`)
      ])
    }
  }

  /**
   * 验证Token并获取用户信息
   */
  async validateToken(authorization: string): Promise<UnifiedTokenCache | null> {
    try {
      // 首先尝试新的统一格式
      const unifiedCache = await this.cacheManager.get<UnifiedTokenCache>(`unified:${authorization}`)
      if (unifiedCache) {
        return unifiedCache
      }

      // 兼容旧的admin格式
      const adminUser = await this.cacheManager.get<AdminEntity>(authorization)
      if (adminUser) {
        return {
          userType: UserType.ADMIN,
          user: adminUser,
          userId: (adminUser as any)._id?.toString() || (adminUser as any).id?.toString(),
          teamId: undefined
        }
      }

      return null
    } catch (error) {
      this.logger.error('Token验证失败:', error)
      return null
    }
  }

  /**
   * 刷新Token过期时间
   */
  async refreshToken(authorization: string): Promise<void> {
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
    
    const tokenCache = await this.cacheManager.get<UnifiedTokenCache>(`unified:${authorization}`)
    if (tokenCache) {
      await this.cacheManager.set(`unified:${authorization}`, tokenCache, overdueToken)
      
      // 同时刷新相关的缓存
      if (tokenCache.userType === UserType.ADMIN) {
        const adminUser = tokenCache.user as AdminEntity
        await Promise.all([
          this.cacheManager.set(adminUser.username, authorization, overdueToken),
          this.cacheManager.set(authorization, adminUser, overdueToken)
        ])
      } else if (tokenCache.userType === UserType.OPEN_PLATFORM) {
        const userIdKey = `open_platform_user:${tokenCache.userId}`
        await this.cacheManager.set(userIdKey, authorization, overdueToken)
      }
    }
  }
}
