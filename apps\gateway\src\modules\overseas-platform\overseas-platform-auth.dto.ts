import { ApiProperty } from '@nestjs/swagger'
import { <PERSON><PERSON><PERSON>y, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export enum AuthorizationStatus {
  /**
   * 用户操作超时
   */
  Expired = 'expired',

  /**
   * 已授权成功
   */
  Authorized = 'authorized',

  /**
   * 用户已授权, 但账号点数超出限额, 需要等待用户确认选择账号
   */
  CapacityExceed = 'capacity_exceed',

  /**
   * 等待用户操作
   */
  Waiting = 'waiting',

  /**
   * 未知错误
   */
  Error = 'error'
}

// =======================================================================================================================
// Input
// =======================================================================================================================
export class OverseasAuthorizationCallbackInputDto {
  @IsString()
  @IsNotEmpty()
  code: string

  @IsString()
  @IsNotEmpty()
  state: string

  // @IsString()
  // @IsOptional()
  // oauth_token?: string
  //
  // @IsString()
  // @IsOptional()
  // oauth_verifier?: string
}

export class OverseasGetAuthorizationStatusInputDto {
  @ApiProperty({
    type: String,
    title: '状态码',
    description: '通过此状态码查询授权状态',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  state!: string
}

export class SelectAccountsInput {
  @ApiProperty({
    description: '授权状态码',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  state: string

  @ApiProperty({
    type: [String],
    description: '用户选择的账号openId列表',
    required: true
  })
  @IsArray()
  @IsNotEmpty()
  selectedAccountIds: string[]
}

// =======================================================================================================================
// Output
// =======================================================================================================================
export class OverseasGetAuthorizationUrlOutputDto {
  @ApiProperty({
    type: String,
    title: '授权地址',
    description: '通过浏览器打开此地址, 获得用户授权',
    example: 'https://www.tiktok.com/v2/auth/authorize?client_key=xxxx&response_type=code&scope=xxxx&redirect_uri=xxxx&state=xxxx',
    required: true
  })
  authorizationUrl!: string

  @ApiProperty({
    type: String,
    title: '授权状态码',
    description: '通过此状态码查询授权状态',
    required: true
  })
  state!: string
}

export class OverseasGetAuthorizationStatusOutputDto {
  @ApiProperty({
    title: '授权状态',
    description: '授权状态: wating(等待操作), authorized(已授权), expired(操作超时)',
    type: String,
    enum: AuthorizationStatus,
    example: AuthorizationStatus.Authorized
  })
  status!: AuthorizationStatus

  @ApiProperty({
    title: '授权失败时的提示消息',
    type: String,
    required: false,
  })
  message?: string
}


// =======================================================================================================================
// Response
// =======================================================================================================================
export class OverseasGetAuthorizationUrlOutputResponse extends BaseResponseDTO {
  @ApiProperty({ type: OverseasGetAuthorizationUrlOutputDto })
  data!: OverseasGetAuthorizationUrlOutputDto
}

export class OverseasGetAuthorizationStatusOutputResponse extends BaseResponseDTO {
  @ApiProperty({
    type: OverseasGetAuthorizationStatusOutputDto
  })
  data!: OverseasGetAuthorizationStatusOutputDto
}
