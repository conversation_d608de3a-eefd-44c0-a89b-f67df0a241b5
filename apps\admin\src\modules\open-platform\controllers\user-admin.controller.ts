import {
  Controller,
  Get,
  Put,
  Query,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiQuery
} from '@nestjs/swagger'
import { UserAdminService } from '../services/user-admin.service'
import {
  GetOpenPlatformUsersRequestDto,
  UpdateUserRemarkRequestDto,
  GetUserApplicationsRequestDto,
  GetOpenPlatformUsersResponseDto,
  GetUserApplicationsResponseDto,
  UpdateUserRemarkResponseDto,
  OpenPlatformUsersListDto,
  UserApplicationsListDto,
  UpdateApplicationPriceRequestDto,
  GetApplicationsRequestDto
} from '../dto/user-admin.dto'
import { OpenPlatformStatus } from '@yxr/common'
import { AdminOnly } from 'apps/admin/src/common/decorators/access-control.decorator'

@ApiTags('开放平台用户管理（管理员）')
@Controller('admin/open-platform/users')
@AdminOnly()
@ApiBearerAuth()
export class UserAdminController {
  constructor(private readonly userAdminService: UserAdminService) {}

  @Get()
  @ApiOperation({
    summary: '获取开放平台用户列表',
    description: '管理员获取开放平台用户列表，支持分页和搜索功能'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: '页码',
    example: 1
  })
  @ApiQuery({
    name: 'size',
    required: false,
    description: '每页数量',
    example: 20
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    description: '手机号码搜索（支持模糊搜索）',
    example: '138'
  })
  @ApiQuery({
    name: 'registeredStartTime',
    required: false,
    description: '注册开始时间'
  })
  @ApiQuery({
    name: 'registeredEndTime',
    required: false,
    description: '注册开始时间'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetOpenPlatformUsersResponseDto
  })
  async getOpenPlatformUsers(
    @Query() query: GetOpenPlatformUsersRequestDto
  ): Promise<OpenPlatformUsersListDto> {
    return await this.userAdminService.getOpenPlatformUsers(query)
  }

  @Get('applications')
  @ApiOperation({
    summary: '获取应用列表（根据手机号码）',
    description: '管理员获取指定用户下所有应用的详细信息，包括统计数据'
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    description: '手机号码搜索',
    example: '138'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetUserApplicationsResponseDto
  })
  async getApplications(
    @Query() query: GetApplicationsRequestDto
  ): Promise<UserApplicationsListDto> {
    return this.userAdminService.getApplicationsByPhone(query)
  }


  @Get(':userId/applications')
  @ApiOperation({
    summary: '获取用户应用详情',
    description: '管理员获取指定用户下所有应用的详细信息，包括统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetUserApplicationsResponseDto
  })
  async getUserApplications(
    @Param('userId') userId: string
  ): Promise<UserApplicationsListDto> {
    const queryDto: GetUserApplicationsRequestDto = { userId }
    return this.userAdminService.getUserApplications(queryDto)
  }

  @Put('remark')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '修改用户备注',
    description: '管理员修改开放平台用户的备注信息'
  })
  @ApiResponse({
    status: 200,
    description: '修改成功',
    type: UpdateUserRemarkResponseDto
  })
  async updateUserRemark(
    @Body() updateDto: UpdateUserRemarkRequestDto
  ) {
    return this.userAdminService.updateUserRemark(updateDto)
  }

  @Put('application/:id/price')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '修改应用价格',
    description: '管理员修改应用的账号和流量价格'
  })
  @ApiResponse({
    status: 200,
    description: '修改成功',
  })
  async updateApplicationPrice(
    @Param('id') id: string,
    @Body() updateDto: UpdateApplicationPriceRequestDto
  ) {
    return this.userAdminService.updateApplicationPrice(id, updateDto)
  }
}
