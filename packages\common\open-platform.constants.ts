/**
 * 开放平台角色定义
 */
export class OpenPlatformRoleNames {
  public static readonly ADMIN = 'admin'        // 管理员（应用创建者）
  public static readonly CHANNEL = 'channel'    // 渠道商
  
  public static readonly All = [OpenPlatformRoleNames.ADMIN, OpenPlatformRoleNames.CHANNEL]
}

/**
 * 开放平台权限定义
 */
export class OpenPlatformPermissions {
  public static readonly VIEW_APPLICATION = 'view_application'
  public static readonly MANAGE_APPLICATION = 'manage_application'
  public static readonly GENERATE_TOKEN = 'generate_token'
  public static readonly INVITE_CHANNEL = 'invite_channel'
  public static readonly MANAGE_AUTHORIZATION = 'manage_authorization'
  
  public static readonly All = [
    OpenPlatformPermissions.VIEW_APPLICATION,
    OpenPlatformPermissions.MANAGE_APPLICATION,
    OpenPlatformPermissions.GENERATE_TOKEN,
    OpenPlatformPermissions.INVITE_CHANNEL,
    OpenPlatformPermissions.MANAGE_AUTHORIZATION
  ]
}


/**
 * 开放平台状态枚举
 */
export enum OpenPlatformStatus {
  ACTIVE = 0,
  DISABLED = 1
}

/**
 * 邀请状态枚举
 */
export enum InvitationStatus {
  PENDING = 0,
  ACCEPTED = 1,
  REJECTED = 2
}

/**
 * 授权状态枚举
 */
export enum AuthorizationStatus {
  ACTIVE = 0,
  DISABLED = 1
}


/**
 * 开放平台用户类型
 */
export enum UserType {
  ADMIN = 'admin',
  OPEN_PLATFORM = 'open-platform',
  APPLICATION = 'application'
}

/**
 * 充值类型枚举
 */
export enum RechargeType {
  BANK_TRANSFER = 'BANK_TRANSFER',  // 对公转账
  GIFT = 'GIFT'                     // 赠送
}

/**
 * 充值状态枚举
 */
export enum RechargeStatus {
  PENDING = 'PENDING',     // 待处理
  SUCCESS = 'SUCCESS',     // 成功
  FAILED = 'FAILED',       // 失败
  CANCELLED = 'CANCELLED'  // 已取消
}

/**
 * 应用类型枚举
 */
export enum ApplicationType {
  TECHNICAL_SERVICE = 'TECHNICAL_SERVICE',  // 技术服务商
  CHANNEL_PARTNER = 'CHANNEL_PARTNER'       // 渠道商
}

/**
 * Webhook状态枚举
 */
export enum WebhookStatus {
  NOT_SET = 'NOT_SET',           // 未设置
  VERIFYING = 'VERIFYING',       // 验证中
  VERIFIED = 'VERIFIED',         // 验证成功
  FAILED = 'FAILED'              // 验证失败
}