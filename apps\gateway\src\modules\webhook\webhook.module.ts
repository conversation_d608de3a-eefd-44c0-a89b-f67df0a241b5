import { Module } from '@nestjs/common'
import { WebhookController } from './webhook.controller'
import { WebhookService } from './webhook.service'
import { OrderManagerModule } from '@yxr/order'
import { OrderMongoose } from '@yxr/mongo'
import { HuoshanModule } from '@yxr/huoshan'

@Module({
  imports: [OrderMongoose, OrderManagerModule, HuoshanModule],
  controllers: [WebhookController],
  providers: [WebhookService],
  exports: [WebhookService]
})
export class WebhookModule {}
