import { Model<PERSON><PERSON>inition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { PublishChannel, StageStatus, TaskStages, TaskStatusEmun } from '@yxr/common'

@Schema({
  timestamps: true,
  optimisticConcurrency: true,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class TaskEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  contentId: Types.ObjectId

  /**
   * 使用content._id 作为任务id
   */
  @Prop({
    type: String,
    unique: true,
    required: true
  })
  taskId: string

  @Prop({
    type: String,
    unique: true,
    required: true
  })
  taskSetId: string

  @Prop({
    type: String,
    required: false
  })
  errorMessage?: string

  @Prop({
    type: String,
    enum: {
      values: Object.values(TaskStages),
      message: '{VALUE} is not a valid state'
    },
    default: null
  })
  stages?: TaskStages

  @Prop({
    type: String,
    enum: StageStatus,
    required: false,
    default: null
  })
  stageStatus?: StageStatus

  @Prop({
    type: Date,
    required: false
  })
  stageTime?: Date

  @Prop({
    type: String,
    enum: PublishChannel,
    default: PublishChannel.local
  })
  publishChannel: PublishChannel

  @Prop({
    type: String,
    default: null
  })
  openUrl?: string

  @Prop({
    type: String,
    default: null,
    index: true
  })
  documentId?: string

  @Prop({
    type: String,
    default: null,
    index: true
  })
  publishId?: string

  @Prop({
    type: String,
    default: null
  })
  progressToken?: string

  @Prop({
    type: String,
    index: true,
    default: null
  })
  publishType: string

  @Prop({
    type: String,
    required: false,
    default: null
  })
  mediaType?: string

  /**
   * 是否定时发布
   */
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  isTimed?: number

  /**
   * 任务状态
   */
  @Prop({
    type: String,
    enum: {
      values: Object.values(TaskStatusEmun),
      message: '{VALUE} is not a valid state'
    },
    required: false,
    default: null
  })
  taskStatus?: TaskStatusEmun

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  taskStatusCode?: number

  /**
   * @deprecated 已废弃
   */
  @Prop({
    example: '1',
    required: false,
    default: 0
  })
  progress?: number

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  deliveryStatus?: string

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  deliveryFailedMessage?: string

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: Date,
    required: false
  })
  deliveryTime?: Date

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  auditStatus?: string

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    required: false,
    default: null
  })
  auditFailedMessage?: string

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: Date,
    required: false
  })
  auditTime?: Date

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: String,
    default: null
  })
  reviewStatus?: string
}

export const TaskSchema: ModelDefinition = {
  name: TaskEntity.name,
  schema: SchemaFactory.createForClass(TaskEntity)
}

export const TaskMongoose = MongooseModule.forFeature([TaskSchema])
