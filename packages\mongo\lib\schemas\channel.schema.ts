import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import mongoose, { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class ChannelEntity {
  @Prop({
    type: String,
    required: true
  })
  channelName: string

  @Prop({
    type: String,
    required: true
  })
  channelCode: string

  /**
   * 是否有效
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  enabled: boolean

  /**
   * 赠送天数
   */
  @Prop({
    type: Number,
    default: 0
  })
  giftDays: number

  @Prop({
    type: String,
    required: true,
    unique: true
  })
  username: string

  @Prop({
    type: String,
    required: true
  })
  password: string

  @Prop({
    type: String,
    required: true
  })
  salt: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const ChannelSchema: ModelDefinition = {
  name: ChannelEntity.name,
  schema: SchemaFactory.createForClass(ChannelEntity)
}

export const ChannelMongoose = MongooseModule.forFeature([ChannelSchema])
