import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { SalesType } from '@yxr/common'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class TeamEntity {
  @Prop({
    type: String,
    index: true,
    unique: false,
    required: [true, '团队名称不能为空'],
    maxlength: 64
  })
  name: string

  @Prop({
    type: String,
    required: [true, '团队LOGO不能为空'],
    maxlength: 256
  })
  logo: string

  @Prop({
    type: String,
    required: true,
    minlength: 6,
    maxlength: 6,
    unique: true,
    index: true
  })
  code: string

  /**
   * 团队账号数量限制 0时使用默认
   *
   * @deprecated 请使用 accountCapacityLimit
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCountLimit: number // 变更为账号点数

  /**
   * 团队账号点数限制 0时使用默认
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCapacityLimit: number

  /**
   * 团队账号数量
   *
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCount: number  // 变更为账号点数(此字段需保留)

  /**
   * 团队账号点数
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCapacity: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  memberCountLimit: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  memberCount: number

  //素材库容量 0时使用默认容量
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  capacity?: number

  //已使用容量
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  usedCapacity?: number

  //团队流量限制 0时使用默认
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  networkTraffic?: number

  //已使用网络流量
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  useNetworkTraffic?: number

  //素材库容量类型
  @Prop({
    type: String,
    required: false
  })
  capacityType?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  /**
   * VIP 到期时间
   */
  @Prop({
    type: Date
  })
  expiredAt?: Date

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isDeleted: boolean

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  isVip: boolean

  /**
   * @deprecated 已废弃
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  enabled: boolean

  /**
   * 权益包数量
   */
  @Prop({
    type: Number,
    required: true,
    default: 0
  })
  interestCount?: number

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  appPublish?: boolean

  /**
   * @deprecated 是否开通了数据白名单
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  cloudDataEnabled?: boolean

  /**
   * 云端数据最后更新时间
   */
  @Prop({
    type: Date
  })
  cloudDataUpdatedAt?: Date

  /**
   * 是否开通了WebApp体验权限
   */
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  webappEnabled?: boolean

  /**
   * 是否显示快捷入口
   */
  @Prop({
    type: Boolean,
    required: true,
    default: true
  })
  quickEnabled?: boolean

  /**
   * 团队代理地区数组
   */
  @Prop({
    type: [Object],
    required: false,
    default: null
  })
  kuaidailiAreas?: {
    province: { name: string; code: string }
    city: { name?: string; code?: string }
  }[]

  /**
   * 销售类型
   */
  @Prop({
    type: Number,
    enum: SalesType,
    required: false,
    default: SalesType.NotBuy
  })
  salesType?: SalesType

  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  customerId?: Types.ObjectId

  /**
   * 团队来源：gateway原生团队 或 开放平台应用创建的团队
   */
  @Prop({
    type: String,
    enum: ['gateway', 'open_platform_app'],
    required: false,
    default: 'gateway'
  })
  source?: 'gateway' | 'open_platform_app'

  /**
   * 创建团队的应用appId（仅当source为open_platform_app时有值）
   */
  @Prop({
    type: String,
    required: false,
    index: true
  })
  sourceAppId?: string

  /**
   * 创建者的开放平台用户ID（仅当source为open_platform_app时有值）
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  openPlatformUserId?: Types.ObjectId

  /**
   * 累积流量总量（新订单系统，单位：KB）
   * 流量订单会直接增加此字段，有效期1年
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  accumulatedTraffic?: number

  /**
   * 流量过期记录（用于管理不同批次的流量过期时间）
   */
  @Prop({
    type: [
      {
        amount: { type: Number, required: true }, // 流量数量（KB）
        expiredAt: { type: Date, required: true }, // 过期时间
        createdAt: { type: Date, default: Date.now } // 创建时间
      }
    ],
    required: false,
    default: []
  })
  trafficRecords?: Array<{
    amount: number
    expiredAt: Date
    createdAt: Date
  }>
}

export const TeamSchema: ModelDefinition = {
  name: TeamEntity.name,
  schema: SchemaFactory.createForClass(TeamEntity)
}

export const TeamMongoose = MongooseModule.forFeature([TeamSchema])
