import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { Anonymous } from '../../common/decorators/Anonymous'
import {
  createAdminUserRequestDto,
  AdminUserDto,
  getAdminUsersQueryDto,
  GetAdminUsersResponse,
  AdminUserResponseDTO,
  patchAdminUserRequestDto,
  UserDeleteResponseDTO,
  UserLoginOkResponseDTO,
  UserLoginRequestDto,
  UserLoginResphoneDTO,
  resetPasswordRequestDto,
  putStatusRequestDto,
  BindMfaRequestDTO,
  CustomerResponseDTO
} from './user.dto'
import {
  BaseBadRequestDTO,
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { UserService } from './user.service'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('users')
@AdminOnly()
@ApiTags('后台用户')
export class UserController {
  constructor(private readonly userService: UserService,
  ) {}

  @Post('auth')
  @ApiOperation({ summary: '用户登录' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '参数错误', type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '验证码无效', type: BaseBadRequestDTO })
  @ApiForbiddenResponse({ description: '该账号已被禁用', type: BaseForbiddenResponseDTO })
  @Anonymous()
  async registerUser(@Body() data: UserLoginRequestDto): Promise<UserLoginResphoneDTO> {
    return await this.userService.putLoginUser(data)
  }

  @Post(`two/factor/auth`)
  @ApiOperation({ summary: '二次验证mfa' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @Anonymous()
  async verifyMfa(@Body() data: BindMfaRequestDTO) {
    return this.userService.verifyMfa(data)
  }

  @Delete('auth')
  @ApiOperation({ summary: '退出登录' })
  @ApiOkResponse({ type: UserDeleteResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async loginOut() {
    return this.userService.deleteAuthorization()
  }

  @Post()
  @ApiOperation({ summary: '创建后台用户' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async createAdminUser(@Body() data: createAdminUserRequestDto): Promise<AdminUserDto> {
    return await this.userService.createAdminUser(data)
  }

  @Get()
  @ApiOperation({ summary: '获取后台用户列表' })
  @ApiOkResponse({ description: '操作成功', type: AdminUserResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiQuery({ type: getAdminUsersQueryDto })
  @ApiHeader({ name: 'authorization', required: true })
  async getAdminUsers(@Query() query: getAdminUsersQueryDto): Promise<GetAdminUsersResponse> {
    return await this.userService.getAdminUsers(query)
  }

  @Get('customers')
  @ApiOperation({ summary: '获取客服列表' })
  @ApiOkResponse({ description: '操作成功', type: CustomerResponseDTO })
  @ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getCustomers() {
    return await this.userService.getCustomers()
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除后台用户' })
  @ApiOkResponse({ description: '操作成功', type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async deleteAdminUser(@Param('id') id: string): Promise<void> {
    return await this.userService.deleteAdminUser(id)
  }

  @Get(':id')
  @ApiOperation({ summary: '获取后台用户详情' })
  @ApiOkResponse({ description: '操作成功', type: AdminUserResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getAdminUser(@Param('id') id: string): Promise<AdminUserDto> {
    return await this.userService.getAdminUser(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '修改后台用户详情' })
  @ApiOkResponse({ description: '操作成功', type: AdminUserResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async patchAdminUser(@Param('id') id: string, @Body() data: patchAdminUserRequestDto) {
    await this.userService.patchAdminUser(id, data)
  }

  @Put(':id/password')
  @ApiOperation({ summary: '重置密码' })
  @ApiOkResponse({ description: '操作成功', type: AdminUserResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async resetPassword(@Param('id') id: string, @Body() data: resetPasswordRequestDto) {
    await this.userService.resetPassword(id, data)
  }

  @Put(':id/status')
  @ApiOperation({ summary: '更新用户状态' })
  @ApiOkResponse({ description: '操作成功', type: AdminUserResponseDTO })
  @ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async putStatus(@Param('id') id: string, @Body() data: putStatusRequestDto) {
    await this.userService.putStatus(id, data)
  }
}
