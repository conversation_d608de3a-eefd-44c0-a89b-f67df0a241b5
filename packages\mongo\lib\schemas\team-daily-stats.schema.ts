import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document, Types } from 'mongoose'
import { MongooseModule } from '@nestjs/mongoose'

export type TeamDailyStatsDocument = TeamDailyStatsEntity & Document

@Schema({
  collection: 'team_daily_stats',
  timestamps: true,
  versionKey: false
})
export class TeamDailyStatsEntity {
  @Prop({
    type: Types.ObjectId,
    ref: 'TeamEntity',
    required: true,
    index: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Date,
    required: true,
    index: true
  })
  date: Date

  @Prop({
    type: Number,
    default: 0,
    min: 0
  })
  trafficUsage: number // 当日流量使用量（字节）

  @Prop({
    type: Number,
    default: 0,
    min: 0
  })
  accountsAdded: number // 当日新增账号数量

  @Prop({
    type: Date,
    default: Date.now
  })
  createdAt: Date

  @Prop({
    type: Date,
    default: Date.now
  })
  updatedAt: Date
}

export const TeamDailyStatsSchema = SchemaFactory.createForClass(TeamDailyStatsEntity)

// 创建复合索引，确保每个团队每天只有一条记录
TeamDailyStatsSchema.index({ teamId: 1, date: 1 }, { unique: true })

// 创建日期索引，便于按时间范围查询
TeamDailyStatsSchema.index({ date: -1 })

// 创建团队索引，便于查询特定团队的统计数据
TeamDailyStatsSchema.index({ teamId: 1, date: -1 })

// 更新时间戳
TeamDailyStatsSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

TeamDailyStatsSchema.pre(['updateOne', 'findOneAndUpdate'], function(next) {
  this.set({ updatedAt: new Date() })
  next()
})

// 导出Mongoose模块
export const TeamDailyStatsMongoose = MongooseModule.forFeature([
  { name: TeamDailyStatsEntity.name, schema: TeamDailyStatsSchema }
])
