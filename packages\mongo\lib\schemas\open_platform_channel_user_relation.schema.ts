import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { OpenPlatformStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_channel_user_relations',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformChannelUserRelationEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  channelUserId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  relatedUserId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  createdBy: Types.ObjectId

  @Prop({
    type: Number,
    enum: OpenPlatformStatus,
    required: true,
    default: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  remark?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

// 创建复合索引确保同一应用下渠道商和用户的关联关系唯一
const schema = SchemaFactory.createForClass(OpenPlatformChannelUserRelationEntity)
schema.index({ applicationId: 1, channelUserId: 1, relatedUserId: 1 }, { unique: true })

export const OpenPlatformChannelUserRelationSchema: ModelDefinition = {
  name: OpenPlatformChannelUserRelationEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformChannelUserRelationEntity)
}

export const OpenPlatformChannelUserRelationMongoose = MongooseModule.forFeature([OpenPlatformChannelUserRelationSchema])
