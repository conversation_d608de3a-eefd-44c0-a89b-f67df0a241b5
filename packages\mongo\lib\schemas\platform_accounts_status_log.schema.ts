import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformAccountStatusLogEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const PlatformAccountStatusLogSchema: ModelDefinition = {
  name: PlatformAccountStatusLogEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountStatusLogEntity)
}

export const PlatformAccountStatusLogMongoose = MongooseModule.forFeature([
  PlatformAccountStatusLogSchema
])
