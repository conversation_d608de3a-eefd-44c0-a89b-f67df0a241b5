import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { REQUEST } from '@nestjs/core'
import { Model, Types } from 'mongoose'
import { type FastifyRequest } from 'fastify'
import { MemberEntity, InvitationEntity, TeamEntity, UserEntity, NoticeEntity } from '@yxr/mongo'
import {
  MemberStatusEnum,
  InvitationStatusEnum,
  TeamRoleNames,
  NoticeTypesEnum,
  TeamFeatures
} from '@yxr/common'
import { InvitationUserStatusDTO } from './invitation.dto'
import { AuthorizationService } from '../../common/security/authorization.service'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { TeamService } from './team.service'
import { TosService } from '@yxr/huoshan'

@Injectable()
export class InvitationService {
  logger = new Logger('InvitationService')

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(InvitationEntity.name) private invitationModel: Model<InvitationEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(NoticeEntity.name) private noticeModel: Model<NoticeEntity>,
    private readonly ossService: TosService,
    private readonly authorizationService: AuthorizationService,
    private readonly webhookService: WebhookService,
    private readonly teamService: TeamService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getUserStatus(phone: string): Promise<InvitationUserStatusDTO> {
    // 查询用户是否已注册
    const user = await this.userModel.findOne({ phone })
    if (!user) {
      throw new NotFoundException('该用户暂未注册')
    }

    // 返回用户是否已经加入团队
    const { teamId: currentTeamId } = this.request.session
    const status = await this.findMemberStatus(currentTeamId, user.id)

    return {
      id: user.id,
      nickName: user.nickName,
      avatarUrl: await this.ossService.getAccessSignatureUrl(user.avatar),
      avatarKey: user.avatar,
      status: status
    }
  }

  /**
   * 邀请账号加入团队
   * @param phone
   */
  async create(phone: string) {
    // 当前用户身份是否可以执行操作
    const { nickName } = this.request.user
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    await this.authorizationService.checkRoleNames(
      currentTeamId,
      currentUserId,
      [TeamRoleNames.MASTER, TeamRoleNames.ADMIN],
      '您不是团队管理员, 无法邀请他人加入团队'
    )

    // 判断人数是否超限
    const team = await this.teamModel.findOne({ _id: new Types.ObjectId(currentTeamId) })
    if (team.memberCount >= team.memberCountLimit) {
      throw new ForbiddenException('团队人数已满, 无法邀请他人加入')
    }

    // 受邀者是否注册
    const invitee = await this.userModel.findOne({ phone })
    if (!invitee) {
      throw new NotFoundException('该用户暂未注册')
    }

    // 查询受邀者是否已加入团队
    const status = await this.findMemberStatus(currentTeamId, invitee.id)
    if (status == MemberStatusEnum.Joined) {
      throw new ForbiddenException('该用户已加入团队, 请勿重复邀请')
    }

    // 查询受邀用户团队数量是否超限
    const teamCount = await this.teamService.teamCount(invitee.id)
    if (teamCount >= TeamFeatures.MaximumTeamCount) {
      throw new ForbiddenException('受邀用户团队数量已达上限, 无法邀请')
    }

    let invitationId = ''
    const session = await this.memberModel.db.startSession()
    session.startTransaction()

    try {
      const user = await this.userModel.findOne({
        _id: new Types.ObjectId(invitee._id)
      })
      // 创建成员关系
      await this.memberModel.create<MemberEntity>(
        [
          {
            userId: new Types.ObjectId(invitee._id),
            teamId: new Types.ObjectId(currentTeamId),
            roles: [TeamRoleNames.NotJoined],
            accounts: [],
            remark: user.nickName,
            status: MemberStatusEnum.Pending
          }
        ],
        { session }
      )

      // 创建邀请函
      const invitations = await this.invitationModel.create<InvitationEntity>(
        [
          {
            userId: new Types.ObjectId(invitee._id),
            invitedBy: new Types.ObjectId(currentUserId),
            teamId: new Types.ObjectId(currentTeamId),
            roles: [TeamRoleNames.MEMBER], // 邀请成为成员
            status: InvitationStatusEnum.Pending // 状态: 未批准, 已批准, 已拒绝
          }
        ],
        { session }
      )

      // 更新团队成员数
      const memberCount = await this.memberModel.countDocuments(
        { teamId: new Types.ObjectId(currentTeamId) },
        { session }
      )
      await this.teamModel.updateOne(
        { _id: new Types.ObjectId(currentTeamId) },
        { memberCount: memberCount },
        { session }
      )

      await session.commitTransaction()

      invitationId = invitations[0].id
    } catch (error) {
      console.log(error)
      await session.abortTransaction()
      throw new HttpException('申请加入团队失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }

    // 确保事务成功后再执行的收尾操作
    // 创建受邀者用户的消息通知
    await this.noticeModel.create<NoticeEntity>({
      title: `${nickName} 邀请您加入 [${team.name}] 团队`,
      content: `${nickName} 邀请您加入 [${team.name}] 团队`,
      type: NoticeTypesEnum.Invitation,
      teamId: new Types.ObjectId(team.id),
      senderId: new Types.ObjectId(currentUserId),
      receiverIds: [invitee.id],
      bizArgs: {
        invitationId: invitationId
      }
    })

    await this.webhookService.grpchook([invitee.id], null, { event: WebhookEvents.NoticeCreate })
  }

  /**
   * 处理加入团队的邀请函
   * @param param
   */
  async handle({ approved, invitationId }: { approved: boolean; invitationId: string }) {
    const { _id: currentUserId } = this.request.user

    const invitation = await this.invitationModel.findById<InvitationEntity>(
      new Types.ObjectId(invitationId)
    )

    if (invitation === null) {
      throw new NotFoundException('申请不存在')
    }

    if (invitation.status !== InvitationStatusEnum.Pending) {
      throw new BadRequestException('邀请已经处理, 请勿重复处理')
    }

    if (invitation.userId.toString() !== currentUserId) {
      throw new ForbiddenException('您不是被邀请人, 无权处理邀请')
    }

    // 当同意加入团队时，需要检查团队实际生效的成员数量是否超限
    if (approved) {
      const memberCount = await this.memberModel.countDocuments({
        teamId: invitation.teamId, // 只检查受邀团队
        status: MemberStatusEnum.Joined, // 只检查已加入的
        isFreeze: false // 只检查未冻结的
      })

      const team = await this.teamModel.findById(invitation.teamId)
      if (memberCount >= team.memberCountLimit) {
        throw new ForbiddenException(`该团队成员已达上限！`)
      }
    }

    const session = await this.invitationModel.db.startSession()
    session.startTransaction()

    const invitationUser = await this.userModel.findById(new Types.ObjectId(invitation.userId))
    try {
      // 处理申请
      await this.invitationModel.updateOne(
        {
          _id: new Types.ObjectId(invitationId)
        },
        {
          handledAt: new Date(),
          status: approved ? InvitationStatusEnum.Approved : InvitationStatusEnum.Rejected
        },
        { session }
      )

      // 处理成员关系
      if (approved) {
        await this.memberModel.updateOne(
          {
            teamId: new Types.ObjectId(invitation.teamId),
            userId: new Types.ObjectId(currentUserId)
          },
          {
            status: MemberStatusEnum.Joined,
            roles: invitation.roles
          },
          { session }
        )

        await this.noticeModel.create<NoticeEntity>({
          title: `你邀请的[${invitationUser.nickName}] 已加入团队`,
          content: `你邀请的 [${invitationUser.nickName}] 已加入团队`,
          type: NoticeTypesEnum.Regular,
          teamId: new Types.ObjectId(invitation.teamId),
          senderId: new Types.ObjectId(currentUserId),
          receiverIds: [invitation.invitedBy.toString()],
          bizArgs: {
            invitationId: invitationId
          }
        })
      } else {
        await this.memberModel.deleteOne(
          {
            teamId: new Types.ObjectId(invitation.teamId),
            userId: new Types.ObjectId(currentUserId)
          },
          { session }
        )

        // 拒绝时更新团队成员数
        const memberCount = await this.memberModel.countDocuments(
          { teamId: new Types.ObjectId(invitation.teamId) },
          { session }
        )
        await this.teamModel.updateOne(
          { _id: new Types.ObjectId(invitation.teamId) },
          { memberCount: memberCount },
          { session }
        )

        await this.noticeModel.create<NoticeEntity>({
          title: `你邀请的 [${invitationUser.nickName}] 拒绝已加入团队`,
          content: `你邀请的 [${invitationUser.nickName}] 拒绝已加入团队`,
          type: NoticeTypesEnum.Regular,
          teamId: new Types.ObjectId(invitation.teamId),
          senderId: new Types.ObjectId(currentUserId),
          receiverIds: [invitation.invitedBy.toString()],
          bizArgs: {
            invitationId: invitationId
          }
        })
      }

      await session.commitTransaction()
    } catch (error) {
      console.log(error)
      await session.abortTransaction()
      throw new HttpException('处理加入申请失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }

    // 处理关联消息的业务状态
    await this.noticeModel.updateOne(
      { 'bizArgs.invitationId': invitationId },
      { bizState: approved ? 'approved' : 'rejected' }
    )

    if (approved) {
      await this.webhookService.grpchook([currentUserId], null, { event: WebhookEvents.TeamJoined })
    }
  }

  private async findMemberStatus(teamId: string, userId: string) {
    const member = await this.findMember(teamId, userId)
    return member === null ? MemberStatusEnum.NotJoined : member.status
  }

  private async findMember(teamId: string | Types.ObjectId, userId: string) {
    return await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        userId: new Types.ObjectId(userId)
      })
      .exec()
  }
}
