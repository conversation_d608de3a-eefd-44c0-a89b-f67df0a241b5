import { ApiProperty, ApiQuery, ApiResponseProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsOptional, IsString, Length } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { MemberStatusEnum } from '@yxr/common'

export class MemberListItemDTO {
  @ApiProperty({
    description: '用户Id',
    example: '{objectId}',
    required: true
  })
  id: string

  @ApiProperty({
    description: '手机号',
    example: '13888888888',
    required: true
  })
  phone: string

  @ApiProperty({
    description: '昵称',
    example: 'admin',
    required: true
  })
  nickName: string

  @ApiProperty({
    description: '成员备注',
    example: '团队成员备注',
    required: true
  })
  remark: string

  @ApiProperty({
    description: '头像地址',
    example: 'https://xxx.com/xxx.png',
    required: true
  })
  avatarUrl: string

  @ApiProperty({
    description: '头像图片存储KEY',
    example: 'xxx.png',
    required: true
  })
  avatarKey: string

  @ApiProperty({
    type: String,
    description: '状态',
    example: MemberStatusEnum.Joined,
    required: true,
    enum: MemberStatusEnum
  })
  status: MemberStatusEnum

  @ApiProperty({
    description: '角色名数组',
    type: [String],
    example: ['admin', 'member'],
    required: true
  })
  roles: string[]

  @ApiProperty({
    description: '加入团队时间',
    example: '2021-01-01 12:00:00',
    required: true
  })
  createdAt: Date

  @ApiProperty({
    type: Number,
    description: '运营账号数量',
    example: 10,
    required: true
  })
  accountCount: number

  @ApiProperty({
    type: Number,
    deprecated: true,
    description: '运营网站空间数量',
    example: 10,
    required: true
  })
  browserCount: number

  @ApiProperty({
    type: Boolean,
    description: '是否冻结',
    required: true,
    example: false
  })
  isFreeze: boolean
}

export class MemberDetailsDto extends MemberListItemDTO {}

/**
 * 账号概览上报
 */
export class PutMemberRemarkRequest {
  @ApiProperty({
    type: String
  })
  @IsString()
  remark: string
}

export class MemberResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MemberDetailsDto
  })
  data: MemberDetailsDto
}

export class MemberPagedListResponse {
  @ApiResponseProperty({
    type: [MemberListItemDTO]
  })
  data: MemberListItemDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class getMemberListOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MemberPagedListResponse
  })
  data: MemberPagedListResponse
}

export class putMemberRolesRequestBodyDTO {
  @ApiProperty({
    description: '角色名数组',
    type: [String],
    example: ['admin', 'member'],
    required: true
  })
  roles: string[]
}

export class putMemberAccountsRequestBodyDTO {
  @ApiProperty({
    description: '账号Id数组',
    type: [String],
    example: ['{objectId}', '{objectId}'],
    required: true
  })
  accountIds: string[]
}
