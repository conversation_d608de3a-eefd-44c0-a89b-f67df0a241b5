import { BadRequestException, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { InjectModel } from '@nestjs/mongoose'
import {
  LoginStatus,
  PlatformAccountCookieEntity,
  PlatformAccountEntity,
  PlatformAccountOverviewEntity,
  PlatformAccountSummaryEntity,
  TeamEntity
} from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import { Cron } from '@nestjs/schedule'
import { PlatformAccountListResponse } from '../platform/platform-account.dto'
import {
  createHttpInstance,
  DataService,
  Platforms,
  AccountService
} from '@yixiaoer/platform-service'
import pako from 'pako'
import { StatisticCommonService, TeamBucketNamesEnum } from '@yxr/common'
import { contentStatisticEventEmitter, eventKey } from '../platform/content.event'
import { nanoid } from 'nanoid'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService, TosService } from '@yxr/huoshan'
import * as buffer from 'buffer'

const $http = createHttpInstance()
const platformNamesRe = {
  抖音: 'DouYin',
  快手: 'KuaiShou',
  视频号: 'ShiPinHao',
  哔哩哔哩: 'BiLiBiLi',
  小红书: 'XiaoHongShu',
  百家号: 'BaiJiaHao',
  头条号: 'TouTiaoHao',
  新浪微博: 'XinLangWeiBo',
  知乎: 'ZhiHu',
  企鹅号: 'QiEHao',
  搜狐号: 'SouHuHao',
  一点号: 'YiDianHao',
  大鱼号: 'DaYuHao',
  网易号: 'WangYiHao',
  爱奇艺: 'AiQiYi',
  腾讯微视: 'TengXunWeiShi',
};
@Injectable()
export class DataSyncCornService implements OnModuleInit {
  private readonly logger = new Logger(DataSyncCornService.name)
  private readonly lockPrefix = 'lock:'
  private lockValue: string = 'syncservice'

  private teamBucketDirs = {
    [TeamBucketNamesEnum.Assets]: 'as',
    [TeamBucketNamesEnum.Attachments]: 'at',
    [TeamBucketNamesEnum.SiteSpaces]: 'sp',
    [TeamBucketNamesEnum.MaterialLibrary]: 'ml',
    [TeamBucketNamesEnum.WechatPublish]: 'wxp'
  }
  dataSyncQueue: Queue

  dataSyncWorker: Worker

  constructor(
    @InjectModel(PlatformAccountCookieEntity.name)
    private platformAccountCookieModel: Model<PlatformAccountCookieEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformAccountOverviewEntity.name)
    private platformAccountOverviewModel: Model<PlatformAccountOverviewEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    private readonly ossService: TosService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private newLogger: TlsService,
    private readonly statisticCommonService: StatisticCommonService
  ) {}

  onModuleInit() {
    this.dataSyncQueue = new Queue('platformData-sync', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT, 10),
        db: parseInt(process.env.REDIS_SYNC_DB, 10),
        password: process.env.REDIS_PASSWORD
      }
    })

    this.dataSyncWorker = new Worker(
      'platformData-sync',
      async (job) => {
        await this.GetPlatformData(job.data)
      },
      {
        concurrency: 2,
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT, 10),
          db: parseInt(process.env.REDIS_SYNC_DB, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
    this.dataSyncWorker.on('failed', (job, error) => {
      this.newLogger.error(null,`任务 ${job.data?.teamId} 处理失败:`, error)
    })

    this.logger.log('data-sync-corn-service init')
    //this.SyncAccountDataCronTask()
    //this.getPlatformAccountCookie('67da90fe3cb791fb4c9ce4c6','683e9d0b34b0e3b29e706d95');
  }

  /**
   * 每日用户注册数统计
   */
  @Cron('0 30 0 * * *', {
    name: 'syncPlatformMediaData',
    timeZone: 'Asia/Shanghai'
  })
  async SyncAccountDataCronTask() {
    if (await this.acquireLock(this.lockValue, 600)) {
      try {
        const accountList = await this.getTeamListAsync();
        this.newLogger.info(
          null,
          `云端数据任务开始，vip团队数-${accountList.length}`,
          {
            taskDate: new Date().toISOString(),
          }
        )
        if (accountList?.length > 0) {
          for (let item of accountList) {
            try {
              await this.dataSyncQueue.add(
                'platformData-sync',
                {
                  data: item,
                  teamId: item.id
                },
                {
                  delay: 0, // 立即执行
                  removeOnComplete: true,
                  removeOnFail: true,
                  jobId: `platformData-sync-move-${item.id}`
                }
              )
            } catch (e) {
              this.newLogger.error(
                null,
                `云端数据任务添加失败-${JSON.stringify(item)}:${e?.toString()}`
              )
            }
          }
        }
      } catch (e) {
        this.newLogger.error(null, '数据同步任务获取白名单团队列表失败', e)
      } finally {
      }
    } else {
      console.log('未获取到锁')
    }
  }

  async GetPlatformData(team: {
    teamId: string
    data: { id: string; name: string; isVip: boolean }
  }) {
    try {
      let page = 1
      let totalPage = 2
      while (page < totalPage) {
        const mediaAccountList = await this.getPlatformAccountListAsync(team.teamId, page, 20)
        page++
        if (mediaAccountList?.data?.length > 0) {
          totalPage = mediaAccountList.totalPage
          for (let platformAccount of mediaAccountList?.data) {
            if (platformAccount.parentId) {
              continue
            }
            const cookieObj = await this.getPlatformAccountCookie(
              team.teamId,
              platformAccount.spaceId,
              platformAccount.id,
              platformAccount.platformName
            )

            if (!cookieObj) {
              continue
            }

            //做有效性校验
            const loggedStatus = await new AccountService(
              JSON.stringify(cookieObj),
              platformNamesRe[platformAccount.platformName] as Platforms
            ).checkAccountAlive()
            if (loggedStatus == 0) {
              //过期账号，更新状态
              await this.updatePlatformAccoutStatusByIdAsync(
                platformAccount.id,
                LoginStatus.Expired
              )
              this.newLogger.error(
                null,
                `账号检测失效 ${platformAccount.platformName}-${platformAccount.platformAccountName}-${team.data?.name}` +
                  JSON.stringify(cookieObj),
                {
                  platformName: platformAccount.platformName
                }
              )
              //过期账号，不再进行后续数据同步
              continue
            } else {
              await this.updatePlatformAccoutStatusByIdAsync(
                platformAccount.id,
                LoginStatus.Succesed
              )
              if (loggedStatus == -1) {
                this.newLogger.error(
                  null,
                  `有效性未知 平台:${platformAccount.platformName} 账号:${platformAccount.platformAccountName} 团队:${team?.data?.name} cookie:${JSON.stringify(cookieObj)}\n`,
                  {
                    platformName: platformAccount.platformName
                  }
                )
              }
            }

            //非vip不同步数据
            if (team.data.isVip !== true) {
              continue
            }
            const cookie = cookieObj?.map((m) => `${m.name}=${m.value}`).join(';')
            const accountViewData = await DataService.SyncDataService.getAccountData(
              platformNamesRe[platformAccount.platformName] as Platforms,
              cookie,
              platformAccount.id
            )

            if (accountViewData.code === 0 && accountViewData?.overviewData?.length > 2) {
              //入库
              await this.putPlatformAccountOverviews(
                team.teamId,
                platformAccount.id,
                accountViewData,
                platformAccount.platformName
              )
            }

            const accountContentData = await DataService.SyncDataService.getAccountContentData(
              platformNamesRe[platformAccount.platformName] as Platforms,
              cookie,
              platformAccount.id,
              null,
              7,
              700
            )
            if (accountContentData?.contentStatisticsData?.length > 0) {
              //入库
              const dataStatistic = accountContentData.contentStatisticsData.map((obj) => ({
                ...obj,
                date: new Date(obj.date)
              }))
              if (dataStatistic.length > 0) {
                contentStatisticEventEmitter.emit(eventKey, {
                  teamId: team.teamId,
                  platformAccountId: platformAccount.id,
                  platformName: platformAccount.platformName,
                  contentData: dataStatistic
                })
              }
            }
          }
        }
      }
      //更新团队数据更新时间
    } catch (e) {
      this.logger.error('syncPlatformData', e)
    }
  }

  async getPlatformAccountListAsync(
    teamId: string,
    page: number = 1,
    size: number = 10
  ): Promise<PlatformAccountListResponse> {
    const where: any = {
      $match: {
        status: LoginStatus.Succesed,
        teamId: new Types.ObjectId(teamId),
        platformName: { $in: Object.keys(platformNamesRe) }
      }
    }

    const result = await this.platformAccountModel.aggregate([
      where,
      {
        $facet: {
          counts: [{ $count: 'total' }],
          items: [
            {
              $project: {
                _id: 0,
                id: '$_id',
                favorites: '$browserfavorites',
                createdAt: { $toLong: '$createdAt' },
                spaceId: 1,
                platformAvatar: 1,
                platformAccountName: 1,
                platformAuthorId: 1,
                platformName: 1,
                remark: 1,
                groups: 1,
                status: 1,
                isFreeze: 1,
                platformType: 1,
                serviceTypeId: 1,
                accountStatus: 1,
                verifyTypeInfo: 1,
                parentId: 1,
                teamId: { $toString: '$teamId' }
              }
            },
            { $sort: { createdAt: -1 } },
            { $skip: (page - 1) * size },
            { $limit: size }
          ]
        }
      }
    ])

    const data = result[0]?.items.map((item: any) => ({
      ...item
    }))

    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data.map((item) => ({
        ...item,
        spaceId: item.spaceId?.toString(),
        isOperate: 0,
        isFreeze: item.isFreeze ?? false
      }))
    }
  }

  /**
   * 更新媒体号的登录状态
   * @param id
   * @param newStatus
   */
  async updatePlatformAccoutStatusByIdAsync(id: string, newStatus: LoginStatus): Promise<void> {
    try {
      const objectId = new Types.ObjectId(id)
      const now = new Date()
      const updateObject = {
        $set: {
          cloudCheckTime: now.getTime()
        }
      }
      if (newStatus == LoginStatus.Expired) {
        updateObject.$set['status'] = newStatus
        updateObject.$set['loginStatusUpdatedAt'] = now
      }

      await this.platformAccountModel.updateOne({ _id: objectId }, updateObject)
    } catch (error) {
      this.logger.error('更新媒体号登录状态时出错:', error)
      // throw error;
    }
  }
  /**
   * 查询开通白名单的团队团队列表
   */
  async getTeamListAsync(): Promise<Array<{ id: string; name: string; isVip: boolean }>> {
    const result = await this.teamModel.aggregate([
      // 首先进行投影操作，转换 _id 为字符串，并包含需要的字段
      {
        $project: {
          _id: { $toString: '$_id' },
          name: 1,
          isVip: 1
        }
      },
      // 按照 cloudDataEnabled 字段进行排序，true 在前，false 在后
      {
        $sort: { isVip: -1 }
      }
    ])

    return result.map((item) => ({
      id: item._id,
      name: item.name,
      isVip: item.isVip
    }))
  }

  /**
   * 获取账号cookie
   * @param teamId
   * @param platformSpaceId
   * @param platformAccountId
   */
  async getPlatformAccountCookie(
    teamId: string,
    platformSpaceId: string,
    platformAccountId: string,
    platformName: string
  ): Promise<Array<{ name: string; value: string }> | undefined> {
    try {
      const cookieInfo = await this.platformAccountCookieModel.findOne({
        teamId: new Types.ObjectId(teamId),
        platformAccountId: new Types.ObjectId(platformAccountId)
      })
      //const cookieInfo={cookie:'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'};
      if (cookieInfo?.cookie) {
        const bufferData = buffer.Buffer.from(cookieInfo.cookie, 'base64')
        const cookieStr = pako.inflate(bufferData, { to: 'string' })
        return JSON.parse(cookieStr)
      }

      if (!platformSpaceId) {
        return undefined
      }
      const startsWith = this.calculateDir(teamId, TeamBucketNamesEnum.SiteSpaces)
      const fileKey = platformSpaceId

      const key = `${startsWith}${fileKey}_account_session`

      const ossUrl = await this.ossService.getAccessSignatureUrl(key)

      const cookieCompressDataRes = await $http.get(ossUrl, {
        responseType: 'arraybuffer'
        // proxy:{
        //  host: '127.0.0.1',
        //  port: 8888,
        //  protocol: 'http:',
        // }
      })
      if (cookieCompressDataRes.status > 204) {
        this.newLogger.error(
          null,
          `syncPlatformData-get-cookie ${ossUrl}  http状态码:${cookieCompressDataRes.status}`,
          {
            platformName: platformName
          }
        )
        return undefined
      }
      const jsonString = pako.inflate(cookieCompressDataRes.data, { to: 'string' })
      return JSON.parse(jsonString).cookies
    } catch (e) {
      this.logger.error('syncPlatformData-get-cookie', e)
    }
    return undefined
  }

  private calculateDir(teamId: string, bucket: TeamBucketNamesEnum) {
    const bucketDir = this.teamBucketDirs[bucket]
    if (!bucketDir) throw new BadRequestException(`bucket \'${bucket}\' is not valid`)

    // 根据不同环境添加不同的前缀, 生产环境不添加前缀
    const prefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}/`
    return `${prefix}t-${teamId}/${bucketDir}/`
  }

  /**
   * 入库账号数据
   * @param teamId
   * @param platformAccountId
   * @param body
   * @param platformName
   */
  async putPlatformAccountOverviews(
    teamId: string,
    platformAccountId: string,
    body: { overviewData: string },
    platformName:string
  ) {
    const platformAccountOverview = await this.platformAccountOverviewModel.findOne({
      platformAccountId: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(teamId)
    })

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      return
    }

    if (platformAccountOverview) {
      //更新
      await this.platformAccountOverviewModel.updateOne(
        {
          _id: platformAccountOverview._id
        },
        {
          overviewData: body.overviewData,
          cloudUpdatedAt: Date.now(),
          platformName:platformName
        }
      )
    } else {
      //新增
      await this.platformAccountOverviewModel.create({
        teamId: new Types.ObjectId(teamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        overviewData: body.overviewData,
        cloudUpdatedAt: Date.now(),
        platformName:platformName
      })
    }

    const overview = await this.statisticCommonService.getAccountOverview(
      platformAccount.platformName,
      body.overviewData
    )
    const summary = await this.platformAccountSummaryModel.findOne({
      teamId: new Types.ObjectId(teamId),
      platformAccountId: new Types.ObjectId(platformAccountId)
    })
    if (summary) {
      //更新
      await this.platformAccountSummaryModel.updateOne(
        {
          teamId: new Types.ObjectId(teamId),
          platformAccountId: new Types.ObjectId(platformAccountId)
        },
        {
          $set: {
            platformName: platformAccount.platformName,
            ...overview
          }
        }
      )
    } else {
      //新增
      await this.platformAccountSummaryModel.create({
        teamId: new Types.ObjectId(teamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        platformName: platformAccount.platformName,
        ...overview
      })
    }
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(): Promise<boolean> {
    const result = await this.cacheManager.store.client.del(this.lockPrefix)
    return result === 1
  }
}
