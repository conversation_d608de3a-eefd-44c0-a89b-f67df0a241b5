import { Controller, Get, Post, Query, Body, UseGuards, HttpCode, HttpStatus } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBadRequestResponse
} from '@nestjs/swagger'
import { TeamStatsService } from '../services/team-stats.service'
import { StatsSchedulerService } from '../services/stats-scheduler.service'
import { AdminOnly } from '../../../common/decorators/access-control.decorator'
import {
  GetTeamStatsRequestDto,
  GetAppStatsRequestDto,
  ManualStatsRequestDto,
  BackfillStatsRequestDto,
  GetTeamStatsResponseDto,
  GetAppStatsResponseDto,
  GetStatsOverviewResponseDto,
  GetStatsStatusResponseDto,
  ManualStatsResponseDto,
  BackfillStatsResponseDto,
  TeamDailyStatsResponseDto,
  AppDailyStatsResponseDto
} from '../dto/stats.dto'
import { BaseBadRequestDTO } from '../../../common/dto/BaseResponseDTO'

@ApiTags('统计数据管理')
@Controller('statistics')
@AdminOnly()
@ApiBearerAuth()
export class StatsController {
  constructor(
    private readonly teamStatsService: TeamStatsService,
    private readonly statsSchedulerService: StatsSchedulerService
  ) {}

  @Get('team')
  @ApiOperation({
    summary: '获取团队统计数据',
    description: '获取指定团队在指定时间范围内的日统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetTeamStatsResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async getTeamStats(@Query() query: GetTeamStatsRequestDto): Promise<GetTeamStatsResponseDto> {
    const startDate = new Date(query.startDate)
    const endDate = new Date(query.endDate)

    const stats = await this.teamStatsService.getTeamStats(query.teamId, startDate, endDate)

    const data: TeamDailyStatsResponseDto[] = stats.map((stat) => ({
      teamId: stat.teamId.toString(),
      date: stat.date.toISOString().split('T')[0],
      trafficUsage: stat.trafficUsage,
      accountsAdded: stat.accountsAdded,
      createdAt: stat.createdAt.getTime(),
      updatedAt: stat.updatedAt.getTime()
    }))

    return { data }
  }

  @Get('app')
  @ApiOperation({
    summary: '获取应用统计数据',
    description: '获取指定开放平台应用在指定时间范围内的日统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetAppStatsResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async getAppStats(@Query() query: GetAppStatsRequestDto): Promise<GetAppStatsResponseDto> {
    const startDate = new Date(query.startDate)
    const endDate = new Date(query.endDate)

    const stats = await this.teamStatsService.getAppStats(query.applicationId, startDate, endDate)

    const data: AppDailyStatsResponseDto[] = stats.map((stat) => ({
      applicationId: stat.applicationId.toString(),
      date: stat.date.toISOString().split('T')[0],
      totalTrafficUsage: stat.totalTrafficUsage,
      totalAccountsAdded: stat.totalAccountsAdded,
      teamCount: stat.teamCount,
      createdAt: stat.createdAt.getTime(),
      updatedAt: stat.updatedAt.getTime()
    }))

    return { data }
  }

  @Get('overview')
  @ApiOperation({
    summary: '获取统计概览',
    description: '获取指定日期的统计概览数据'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetStatsOverviewResponseDto
  })
  async getStatsOverview(@Query('date') date?: string): Promise<GetStatsOverviewResponseDto> {
    const targetDate = date ? new Date(date) : new Date()
    const overview = await this.teamStatsService.getStatsOverview(targetDate)

    return { data: overview }
  }

  @Get('status')
  @ApiOperation({
    summary: '获取统计状态',
    description: '获取统计系统的运行状态和基本信息'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetStatsStatusResponseDto
  })
  async getStatsStatus(): Promise<GetStatsStatusResponseDto> {
    const status = await this.statsSchedulerService.getStatsStatus()

    return { data: status }
  }

  @Post('manual')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '手动执行统计',
    description: '手动执行指定日期的统计任务'
  })
  @ApiResponse({
    status: 200,
    description: '执行成功',
    type: ManualStatsResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async manualCalculateStats(@Body() body: ManualStatsRequestDto): Promise<ManualStatsResponseDto> {
    const date = new Date(body.date)
    const result = await this.statsSchedulerService.manualCalculateStats(date)

    return { data: result }
  }

  @Post('backfill')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '批量补充统计数据',
    description: '批量补充指定时间范围内的历史统计数据'
  })
  @ApiResponse({
    status: 200,
    description: '执行成功',
    type: BackfillStatsResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async backfillStats(@Body() body: BackfillStatsRequestDto): Promise<BackfillStatsResponseDto> {
    const startDate = new Date(body.startDate)
    const endDate = new Date(body.endDate)

    const result = await this.statsSchedulerService.backfillStats(startDate, endDate)

    return { data: result }
  }
}
