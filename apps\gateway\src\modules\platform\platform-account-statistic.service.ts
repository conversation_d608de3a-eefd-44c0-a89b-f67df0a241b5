import { Inject, Injectable } from '@nestjs/common'
import {
  StatisticCommonService,
  TeamRoleNames
} from '@yxr/common'
import { Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import {
  MemberEntity,
  PlatformAccountEntity,
  PlatformAccountSummaryEntity} from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'

@Injectable()
export class PlatformAccountStatisticService {
  constructor(
    private readonly statisticCommonService: StatisticCommonService,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 账号概览数据统计
   * @param platformName
   * @returns
   */
  async getAccountOverviewSummary(platformName: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    const where: any = {
      $match: {
        teamId: new Types.ObjectId(currentTeamId),
        platformName: platformName
      }
    }

    const platformAccountWhere: { teamId: Types.ObjectId; platformName: string; members?: string } =
      {
        teamId: new Types.ObjectId(currentTeamId),
        platformName: platformName
      }
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      platformAccountWhere.members = currentUserId
    }
    const platformAccounts = await this.platformAccountModel
      .find(platformAccountWhere)
      .select('_id')
      .lean()
    const platformAccountIds = platformAccounts.map((item) => item._id) // 获取当前用户的所有平台账号ID

    where.$match.platformAccountId = {
      $in: platformAccountIds
    }
    const fields = await this.statisticCommonService.getAccountOverviewField(platformName)
    // 动态构建 $group 语句
    const groupStage: any = {
      _id: '$platformName'
    }
    fields.forEach((item) => {
      groupStage[item.value] = { $sum: `$${item.key}` }
    })
    // 进行聚合查询
    const result = await this.platformAccountSummaryModel.aggregate([where, { $group: groupStage }])

    return result[0] ?? {}
  }
}
