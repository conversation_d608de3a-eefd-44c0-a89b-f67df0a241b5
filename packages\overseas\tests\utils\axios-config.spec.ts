/**
 * 海外平台连接器axios配置测试
 */

import { AxiosError, AxiosResponse } from 'axios'
import { 
  createOverseasAxiosInstance, 
  InterceptorConfig, 
  getProxyConfig,
  BusinessErrorChecker
} from '../../src/utils/axios-config'
import { createOverseasContext } from '../../src/providers/utils'
import { 
  RemoteApiError, 
  RemoteApiErrorCodes,
  ErrorCategory,
  ErrorSeverity
} from '../../src/utils/error-handler'

// Mock axios
jest.mock('axios', () => {
  const originalAxios = jest.requireActual('axios')
  return {
    ...originalAxios,
    create: jest.fn(() => ({
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      },
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      patch: jest.fn(),
      delete: jest.fn(),
      request: jest.fn()
    }))
  }
})

describe('海外平台连接器axios配置', () => {
  const mockContext = createOverseasContext('tiktok', {
    accountOpenId: 'test-account',
    teamId: 'test-team',
    userId: 'test-user'
  })

  beforeEach(() => {
    jest.clearAllMocks()
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('getProxyConfig', () => {
    it('应该根据环境变量返回正确的代理配置', () => {
      // 测试启用代理
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      process.env.OVERSEAS_USE_PROXY = 'true'
      process.env.OVERSEAS_PROXY_URL = 'http://test-proxy:8080'
      process.env.OVERSEAS_REQUEST_TIMEOUT = '60000'

      const config = getProxyConfig()
      
      expect(config.enabled).toBe(true)
      expect(config.url).toBe('http://test-proxy:8080')
      expect(config.timeout).toBe(60000)

      // 恢复环境变量
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })

    it('应该在未设置环境变量时使用默认配置', () => {
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      delete process.env.OVERSEAS_USE_PROXY
      delete process.env.OVERSEAS_PROXY_URL
      delete process.env.OVERSEAS_REQUEST_TIMEOUT

      const config = getProxyConfig()
      
      expect(config.enabled).toBe(false)
      expect(config.url).toBe('http://127.0.0.1:7897')
      expect(config.timeout).toBe(30000)

      // 恢复环境变量
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })

    it('应该在禁用代理时返回正确配置', () => {
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      process.env.OVERSEAS_USE_PROXY = 'false'

      const config = getProxyConfig()
      
      expect(config.enabled).toBe(false)

      // 恢复环境变量
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })
  })

  describe('createOverseasAxiosInstance', () => {
    it('应该创建带有拦截器的axios实例', () => {
      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: true,
        enableBusinessErrorCheck: true
      }

      const instance = createOverseasAxiosInstance(
        'https://business-api.tiktok.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
      expect(instance.interceptors.request.use).toHaveBeenCalled()
      expect(instance.interceptors.response.use).toHaveBeenCalled()
    })

    it('应该正确配置代理', () => {
      // Mock environment variable
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      process.env.OVERSEAS_USE_PROXY = 'true'

      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: false,
        enableBusinessErrorCheck: false
      }

      const instance = createOverseasAxiosInstance(
        'https://business-api.tiktok.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()

      // Restore environment variable
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })

    it('应该支持自定义重试配置', () => {
      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: true,
        retryConfig: {
          maxRetries: 5,
          baseDelay: 2000,
          maxDelay: 60000,
          backoffMultiplier: 3
        }
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
      expect(instance.interceptors.response.use).toHaveBeenCalled()
    })
  })

  describe('BusinessErrorChecker', () => {
    it('应该正确检测业务错误', () => {
      const mockChecker: BusinessErrorChecker = {
        hasBusinessError: jest.fn((response) => response.data.error !== undefined),
        handleBusinessError: jest.fn()
      }

      const responseWithError = {
        data: {
          error: 'Some business error',
          code: 400
        }
      } as AxiosResponse

      const responseWithoutError = {
        data: {
          result: 'success',
          code: 200
        }
      } as AxiosResponse

      expect(mockChecker.hasBusinessError(responseWithError)).toBe(true)
      expect(mockChecker.hasBusinessError(responseWithoutError)).toBe(false)
    })

    it('应该正确处理业务错误', async () => {
      const mockChecker: BusinessErrorChecker = {
        hasBusinessError: (response) => response.data.error !== undefined,
        handleBusinessError: jest.fn().mockRejectedValue(
          new RemoteApiError(
            'test',
            RemoteApiErrorCodes.UnknownRemoteApiError,
            { message: 'Business error' },
            { url: '/test', method: 'GET' },
            { status: 200, statusText: 'OK' },
            mockContext
          )
        )
      }

      const response = {
        data: {
          error: 'Business error occurred'
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: { url: '/test' }
      } as AxiosResponse

      await expect(
        mockChecker.handleBusinessError(response, mockContext)
      ).rejects.toThrow(RemoteApiError)

      expect(mockChecker.handleBusinessError).toHaveBeenCalledWith(response, mockContext)
    })
  })

  describe('拦截器配置选项', () => {
    it('应该支持禁用重试机制', () => {
      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: false,
        enableBusinessErrorCheck: true
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
    })

    it('应该支持禁用业务错误检查', () => {
      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: true,
        enableBusinessErrorCheck: false
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
    })

    it('应该支持自定义业务错误检查器', () => {
      const customChecker: BusinessErrorChecker = {
        hasBusinessError: (response) => response.data.status === 'error',
        handleBusinessError: async (response, context) => {
          throw new RemoteApiError(
            context.platform,
            RemoteApiErrorCodes.UnknownRemoteApiError,
            { customError: response.data.error },
            { url: '/test', method: 'GET' },
            { status: 200, statusText: 'OK' },
            context
          )
        }
      }

      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        businessErrorChecker: customChecker,
        enableRetry: true,
        enableBusinessErrorCheck: true
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
    })
  })

  describe('错误处理集成', () => {
    it('应该将axios错误转换为RemoteApiError', () => {
      // 这个测试需要模拟实际的拦截器行为
      // 由于拦截器是在axios实例创建时设置的，我们主要验证配置是否正确
      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: true,
        enableBusinessErrorCheck: true
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      // 验证拦截器已被设置
      expect(instance.interceptors.response.use).toHaveBeenCalled()
      
      // 获取拦截器参数
      const interceptorCall = (instance.interceptors.response.use as jest.Mock).mock.calls[0]
      expect(interceptorCall).toHaveLength(2) // onFulfilled, onRejected
      expect(typeof interceptorCall[0]).toBe('function') // onFulfilled
      expect(typeof interceptorCall[1]).toBe('function') // onRejected
    })
  })

  describe('代理配置集成', () => {
    it('应该在启用代理时正确配置axios', () => {
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      process.env.OVERSEAS_USE_PROXY = 'true'
      process.env.OVERSEAS_PROXY_URL = 'http://proxy.example.com:8080'

      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: false,
        enableBusinessErrorCheck: false
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
      
      // 恢复环境变量
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })

    it('应该在禁用代理时直连访问', () => {
      const originalEnv = process.env.OVERSEAS_USE_PROXY
      process.env.OVERSEAS_USE_PROXY = 'false'

      const interceptorConfig: InterceptorConfig = {
        context: mockContext,
        enableRetry: false,
        enableBusinessErrorCheck: false
      }

      const instance = createOverseasAxiosInstance(
        'https://api.example.com',
        interceptorConfig
      )

      expect(instance).toBeDefined()
      
      // 恢复环境变量
      process.env.OVERSEAS_USE_PROXY = originalEnv
    })
  })
})
