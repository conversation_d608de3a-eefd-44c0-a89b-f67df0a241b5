import { Body, Controller, Get, Param, Post, Put, Query, Request } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger'
import { RechargeService } from '../services/recharge.service'
import { UserType } from '@yxr/common'
import { AdminAndOpenPlatformAccess, AdminOnly } from '../../../common/decorators/access-control.decorator'
import {
  CreateRechargeRequestDto,
  UpdateRechargeStatusRequestDto,
  GetRechargeListRequestDto,
  RechargeRecordResponseDto,
  RechargeListResponseDto,
  ApplicationBalanceResponseDto,
  RechargeListDto
} from '../dto/recharge.dto'
import type { FastifyRequest } from 'fastify'

@ApiTags('开放平台-应用充值')
@Controller('open-platform/recharge')
@AdminAndOpenPlatformAccess()
@ApiBearerAuth()
export class RechargeController {
  constructor(private readonly rechargeService: RechargeService) {}

  @Post()
  @AdminOnly()
  @ApiOperation({ summary: '创建充值订单' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: RechargeRecordResponseDto
  })
  async createRecharge(
    @Body() createRechargeDto: CreateRechargeRequestDto,
    @Request() request: FastifyRequest
  ): Promise<RechargeRecordResponseDto> {
    // 获取操作员ID（@AdminOnly装饰器已确保只有管理员可以访问）
    const operatorId = request.session?.userId

    return await this.rechargeService.createRecharge(createRechargeDto, operatorId)
  }

  @Put(':rechargeOrderNo/status')
  @AdminOnly()
  @ApiOperation({ summary: '更新充值状态' })
  @ApiResponse({
    description: '更新成功',
    type: RechargeRecordResponseDto
  })
  async updateRechargeStatus(
    @Param('rechargeOrderNo') rechargeOrderNo: string,
    @Body() updateStatusDto: UpdateRechargeStatusRequestDto,
    @Request() request: FastifyRequest
  ): Promise<RechargeRecordResponseDto> {
    // 获取操作员ID（@AdminOnly装饰器已确保只有管理员可以访问）
    const operatorId = request.session?.userId

    return await this.rechargeService.updateRechargeStatus(
      rechargeOrderNo,
      updateStatusDto,
      operatorId
    )
  }

  @Get()
  @ApiOperation({ summary: '查询充值记录列表' })
  @ApiResponse({
    description: '查询成功',
    type: RechargeListDto
  })
  async getRechargeList(
    @Query() query: GetRechargeListRequestDto,
    @Request() request: FastifyRequest
  ): Promise<RechargeListResponseDto> {
    // 数据隔离逻辑：开放平台用户只能查看自己有权限的应用充值记录
    // 这里可以通过服务层实现更细粒度的权限控制
    // if (request.session?.userType === UserType.OPEN_PLATFORM) {
    //   if (!query.applicationId) {
    //     throw new Error('开放平台用户查询充值记录必须传应用ID')
    //   }
    // }

    return await this.rechargeService.getRechargeList(query)
  }

  @Get('applications/:applicationId/balance')
  @ApiOperation({ summary: '查询应用余额' })
  @ApiResponse({
    description: '查询成功',
    type: ApplicationBalanceResponseDto
  })
  async getApplicationBalance(
    @Param('applicationId') applicationId: string
  ): Promise<ApplicationBalanceResponseDto> {
    // 权限验证由装饰器处理，这里可以根据业务需求添加更细粒度的权限控制
    return await this.rechargeService.getApplicationBalance(applicationId)
  }
}
