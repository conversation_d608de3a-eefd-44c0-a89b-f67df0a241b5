import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { CreatorService } from './creator.service';
import { CreateTaskInputDto } from './dto/create-task.dto';
import { LookupCategoriesInputDto } from './dto/lookup-categories.dto';
import { LookupTagsInputDto } from './dto/lookup-tags.dto';

@Controller('creator')
export class CreatorController {
  constructor(private readonly creatorService: CreatorService) {}

  @Post()
  async createTask(@Body() input: CreateTaskInputDto) {
    return this.creatorService.createTask(input);
  }

  @Get('lookup/categories')
  async lookupCategories(@Query() input: LookupCategoriesInputDto) {
    return this.creatorService.lookupCategories(input);
  }

  @Get('lookup/tags')
  async lookupTags(@Query() input: LookupTagsInputDto) {
    return this.creatorService.lookupTags(input);
  }
}
