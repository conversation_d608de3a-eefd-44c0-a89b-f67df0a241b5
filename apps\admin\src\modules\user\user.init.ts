import { Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { AdminEntity, InterestEntity } from '@yxr/mongo'
import { Model } from 'mongoose'
import crypto from 'crypto'

@Injectable()
export class UserInitService implements OnModuleInit {
  constructor(
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(InterestEntity.name) private interestModel: Model<InterestEntity>
  ) {}

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  async onModuleInit() {
    const initUsername = 'admin'

    const admin = await this.adminModel.findOne({
      username: initUsername
    })

    if (!admin) {
      if (process.env.NODE_ENV == 'prod') {
        const { salt, hash } = this.hashPassword('RFAWqB$U9J36YdJX')
        await this.adminModel.create({
          username: initUsername,
          name: initUsername,
          password: hash,
          salt: salt,
          role: 0
        })
      } else {
        const { salt, hash } = this.hashPassword('admin1234')
        await this.adminModel.create({
          username: initUsername,
          name: initUsername,
          password: hash,
          salt: salt,
          role: 0
        })
      }
    }

    const interest = await this.interestModel.findOne()
    if (!interest) {
      await this.interestModel.create({
        memberCountLimit: 2,
        accountCountLimit: 20, // 变更为账号点数
        accountCapacityLimit: 20,
        capacityLimit: **********,
        appPublish: true,
        price: 59,
        networkTrafficLimit: ***********
      })
    }
  }
}
