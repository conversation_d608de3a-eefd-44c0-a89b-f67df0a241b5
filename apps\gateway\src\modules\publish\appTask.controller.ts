import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { AppTaskService } from './appTask.service'
import {
  getAppPublishTaskDetailsResponseDTO,
  AppPublishTaskDetailsDto,
  AppTaskListResponse,
  AppTaskSetCreateRequest,
  AppTaskSetPagedListResponse,
  getAppTaskPagedListRequest,
  getAppTaskSetPagedListOkResponseDTO,
  AppTaskPagedListResponse,
  AppTaskDetailsDto
} from './appTask.dto'
import { TaskCloudPushResponseDTO } from './taskSet.dto'

@Controller('app-tasks')
@ApiTags('任务管理/移动端分发')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true, description: 'token' })
export class AppTaskController {
  constructor(private readonly appTaskService: AppTaskService) {}

  @Get('onlines')
  @ApiOperation({ summary: '获取移动端分发任务可用节点数' })
  @ApiOkResponse({ type: Number })
  getTaskNodeCount() {
    return this.appTaskService.getTaskNodeCount()
  }

  @Get()
  @ApiOperation({ summary: '获取移动端分发任务集列表', deprecated: true })
  @ApiOkResponse({ type: getAppTaskSetPagedListOkResponseDTO })
  @ApiQuery({ type: getAppTaskPagedListRequest })
  getPublishTaskSets(
    @Query() query: getAppTaskPagedListRequest
  ): Promise<AppTaskSetPagedListResponse> {
    return this.appTaskService.getPublishTaskSets(query)
  }

  @Get(':taskSetId/tasks')
  @ApiOperation({ summary: '获取移动端分发任务列表', deprecated: true })
  @ApiOkResponse({ type: AppTaskPagedListResponse })
  async getPublishTasks(@Param('taskSetId') taskSetId: string): Promise<AppTaskDetailsDto[]> {
    return await this.appTaskService.getPublishTasks(taskSetId)
  }

  @Post()
  @ApiOperation({ summary: '创建移动端分发任务', deprecated: true })
  @ApiOkResponse({ type: AppTaskListResponse })
  @ApiBody({ type: AppTaskSetCreateRequest })
  createTasks(@Body() body: AppTaskSetCreateRequest) {
    return this.appTaskService.createTasks(body)
  }

  @Get(':taskSetId/allocation')
  @ApiOperation({ summary: '获取可发布的任务集对象详情(云发布后会废弃)', deprecated: true })
  @ApiQuery({ name: 'version', required: true, description: '任务版本', type: Number })
  @ApiOkResponse({ type: getAppPublishTaskDetailsResponseDTO })
  getPublishTaskSetDetails(
    @Param('taskSetId') taskSetId: string,
    @Query('version') version: number
  ): Promise<AppPublishTaskDetailsDto[]> {
    return this.appTaskService.getPublishTaskSetDetails(taskSetId, version)
  }

  @Get(':taskSetId')
  @ApiOperation({ summary: '获取可发布的任务集对象, 云发布版本', deprecated: true })
  @ApiOkResponse({ type: TaskCloudPushResponseDTO })
  getPublishTaskSet(@Param('taskSetId') taskSetId: string) {
    return this.appTaskService.getPublishTaskSet(taskSetId)
  }
}
