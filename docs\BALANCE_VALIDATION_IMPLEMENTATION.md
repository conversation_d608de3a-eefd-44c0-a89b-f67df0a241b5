# 开放平台订单余额验证和扣除功能实现

## 概述

为 `createAccountPointsOrder` 和 `createTrafficOrder` 方法添加了完整的余额验证和扣除功能，确保订单创建前验证应用余额充足，订单创建成功后自动扣除相应金额，并使用数据库事务保证操作的原子性。

## 功能特性

### 1. **余额验证逻辑**
- ✅ 在创建订单之前，查询当前应用的可用余额
- ✅ 根据订单类型和应用价格配置计算所需金额
- ✅ 余额不足时抛出详细的错误信息

### 2. **订单创建流程**
- ✅ 验证余额充足后，调用现有的订单创建逻辑
- ✅ 订单创建成功后，立即扣除相应的应用余额
- ✅ 使用数据库事务确保余额扣除和订单创建的原子性

### 3. **错误处理**
- ✅ 余额不足时，提供详细的错误信息（当前余额、所需金额、差额）
- ✅ 订单创建失败时，确保不扣除余额
- ✅ 余额扣除失败时，回滚订单创建

## 技术实现

### 1. 账号点数订单余额验证

**文件**: `apps/admin/src/modules/open-platform/services/open-platform-order.service.ts`

#### 1.1 金额计算逻辑
```typescript
// 计算订单所需金额：账号数量 × 应用账号单价 × 时长（月）
const orderAmount = data.accountCapacityLimit * application.accountPrice * data.duration
const orderAmountInCents = Math.round(orderAmount * 100) // 转换为分

this.logger.log(
  `账号点数订单金额计算: 账号数量=${data.accountCapacityLimit}, 单价=${application.accountPrice}元, ` +
  `时长=${data.duration}月, 总金额=${orderAmount}元(${orderAmountInCents}分)`
)
```

#### 1.2 余额验证
```typescript
// 验证应用余额是否足够
const balance = await this.getApplicationBalance(session.applicationId)
if (balance.availableBalance < orderAmountInCents) {
  const shortfall = orderAmountInCents - balance.availableBalance
  throw new BadRequestException(
    `应用余额不足，当前可用余额：${(balance.availableBalance / 100).toFixed(2)}元，` +
    `订单所需金额：${(orderAmountInCents / 100).toFixed(2)}元，` +
    `差额：${(shortfall / 100).toFixed(2)}元`
  )
}
```

#### 1.3 事务处理
```typescript
await dbSession.withTransaction(async () => {
  // 创建订单
  await this.orderManagerService.createAccountPointsOrder({
    ...data,
    userId: teamMember.userId.toString(),
    sourceAppId: session?.applicationId,
    startTime: new Date(data.startTime)
  })

  // 扣除应用余额
  await this.deductApplicationBalance(session.applicationId, orderAmountInCents, dbSession)

  this.logger.log(
    `账号点数订单创建成功: 团队ID=${data.teamId}, 账号数量=${data.accountCapacityLimit}, ` +
    `时长=${data.duration}月, 扣除金额=${(orderAmountInCents / 100).toFixed(2)}元`
  )
})
```

### 2. 流量订单余额验证

#### 2.1 金额计算逻辑
```typescript
// 计算订单所需金额：流量数量(GB) × 应用流量单价
// 注意：流量订单通常是一次性购买，有效期1年，不按月计费
const orderAmount = data.trafficCount * application.trafficPrice
const orderAmountInCents = Math.round(orderAmount * 100) // 转换为分

this.logger.log(
  `流量订单金额计算: 流量数量=${data.trafficCount}GB, 单价=${application.trafficPrice}元/GB, ` +
  `总金额=${orderAmount}元(${orderAmountInCents}分)`
)
```

#### 2.2 余额验证和事务处理
```typescript
// 验证应用余额是否足够
const balance = await this.getApplicationBalance(session.applicationId)
if (balance.availableBalance < orderAmountInCents) {
  const shortfall = orderAmountInCents - balance.availableBalance
  throw new BadRequestException(
    `应用余额不足，当前可用余额：${(balance.availableBalance / 100).toFixed(2)}元，` +
    `订单所需金额：${(orderAmountInCents / 100).toFixed(2)}元，` +
    `差额：${(shortfall / 100).toFixed(2)}元`
  )
}

await dbSession.withTransaction(async () => {
  // 创建订单
  await this.orderManagerService.createTrafficOrder({
    ...data,
    userId: teamMember.userId.toString(),
    sourceAppId: session?.applicationId
  })

  // 扣除应用余额
  await this.deductApplicationBalance(session.applicationId, orderAmountInCents, dbSession)

  this.logger.log(
    `流量订单创建成功: 团队ID=${data.teamId}, 流量数量=${data.trafficCount}GB, ` +
    `扣除金额=${(orderAmountInCents / 100).toFixed(2)}元`
  )
})
```

### 3. 现有余额管理方法

#### 3.1 获取应用余额
```typescript
private async getApplicationBalance(applicationId: string): Promise<{
  totalBalance: number
  availableBalance: number
  frozenBalance: number
}> {
  let balance = await this.balanceModel.findOne({
    applicationId: new Types.ObjectId(applicationId)
  })

  // 如果余额记录不存在，创建一个初始记录
  if (!balance) {
    balance = await this.balanceModel.create({
      applicationId: new Types.ObjectId(applicationId),
      totalBalance: 0,
      frozenBalance: 0,
      availableBalance: 0,
      totalRecharge: 0,
      totalConsumption: 0
    })
  }

  return {
    totalBalance: balance.totalBalance,
    availableBalance: balance.availableBalance,
    frozenBalance: balance.frozenBalance
  }
}
```

#### 3.2 扣除应用余额
```typescript
private async deductApplicationBalance(
  applicationId: string,
  amount: number, // 单位：分
  session?: any
): Promise<void> {
  const options = session ? { session } : {}

  const result = await this.balanceModel.updateOne(
    {
      applicationId: new Types.ObjectId(applicationId),
      availableBalance: { $gte: amount } // 确保余额足够
    },
    {
      $inc: {
        availableBalance: -amount,
        totalConsumption: amount
      },
      $set: {
        updatedAt: new Date()
      }
    },
    options
  )

  if (result.matchedCount === 0) {
    throw new BadRequestException('余额不足或应用不存在')
  }

  this.logger.log(
    `应用余额扣除成功: applicationId=${applicationId}, amount=${amount}元`
  )
}
```

## 价格配置

### 1. **应用价格字段**
- **accountPrice**: 账号单价（元），默认值为1元
- **trafficPrice**: 流量单价（元/GB），默认值为0.5元

### 2. **金额计算公式**

#### 账号点数订单
```
订单金额（元） = 账号数量 × 应用账号单价 × 时长（月）
订单金额（分） = 订单金额（元） × 100
```

#### 流量订单
```
订单金额（元） = 流量数量（GB） × 应用流量单价
订单金额（分） = 订单金额（元） × 100
```

### 3. **计算示例**

#### 账号点数订单示例
- 账号数量：10个
- 应用账号单价：1元/个/月
- 时长：3个月
- **订单金额**：10 × 1 × 3 = 30元 = 3000分

#### 流量订单示例
- 流量数量：100GB
- 应用流量单价：0.5元/GB
- **订单金额**：100 × 0.5 = 50元 = 5000分

## 错误处理机制

### 1. **余额不足错误**
```json
{
  "statusCode": 400,
  "message": "应用余额不足，当前可用余额：25.00元，订单所需金额：30.00元，差额：5.00元",
  "error": "Bad Request"
}
```

### 2. **应用不存在错误**
```json
{
  "statusCode": 404,
  "message": "应用不存在",
  "error": "Not Found"
}
```

### 3. **团队权限错误**
```json
{
  "statusCode": 404,
  "message": "该团队不属于当前应用",
  "error": "Not Found"
}
```

## 事务保证

### 1. **原子性操作**
- ✅ 使用MongoDB事务确保订单创建和余额扣除的原子性
- ✅ 任一操作失败时，整个事务回滚
- ✅ 避免订单创建成功但余额未扣除的情况

### 2. **事务流程**
```typescript
const dbSession = await this.connection.startSession()

try {
  await dbSession.withTransaction(async () => {
    // 1. 创建订单
    await this.orderManagerService.createAccountPointsOrder(...)
    
    // 2. 扣除余额
    await this.deductApplicationBalance(...)
    
    // 3. 记录日志
    this.logger.log(...)
  })
} catch (error) {
  // 错误处理
} finally {
  await dbSession.endSession()
}
```

## 日志记录

### 1. **金额计算日志**
```
账号点数订单金额计算: 账号数量=10, 单价=1元, 时长=3月, 总金额=30元(3000分)
流量订单金额计算: 流量数量=100GB, 单价=0.5元/GB, 总金额=50元(5000分)
```

### 2. **订单创建成功日志**
```
账号点数订单创建成功: 团队ID=507f1f77bcf86cd799439011, 账号数量=10, 时长=3月, 扣除金额=30.00元
流量订单创建成功: 团队ID=507f1f77bcf86cd799439011, 流量数量=100GB, 扣除金额=50.00元
```

### 3. **余额扣除日志**
```
应用余额扣除成功: applicationId=app_123456, amount=3000元
```

## 兼容性保证

### 1. **方法签名保持不变**
- ✅ 保持现有方法签名和返回值不变
- ✅ 向后兼容现有调用方式
- ✅ 不影响现有的API接口

### 2. **权限控制兼容**
- ✅ 保持现有的 `@OpenPlatformAccess()` 权限控制
- ✅ 继续验证用户类型为 `APPLICATION`
- ✅ 维持团队权限验证逻辑

### 3. **错误处理兼容**
- ✅ 保持现有的异常类型和错误消息格式
- ✅ 新增的余额验证错误使用标准的 `BadRequestException`
- ✅ 维持现有的日志记录格式

## 使用示例

### 1. **创建账号点数订单**
```bash
curl -X POST "https://api.example.com/open-platform/orders/account-points" \
  -H "Authorization: Bearer app-token" \
  -H "Content-Type: application/json" \
  -d '{
    "teamId": "507f1f77bcf86cd799439011",
    "accountCapacityLimit": 10,
    "duration": 3,
    "startTime": "2024-01-01",
    "remark": "账号点数订单"
  }'
```

### 2. **创建流量订单**
```bash
curl -X POST "https://api.example.com/open-platform/orders/traffic" \
  -H "Authorization: Bearer app-token" \
  -H "Content-Type: application/json" \
  -d '{
    "teamId": "507f1f77bcf86cd799439011",
    "trafficCount": 100,
    "remark": "流量订单"
  }'
```

### 3. **余额不足响应示例**
```json
{
  "success": false,
  "statusCode": 400,
  "message": "应用余额不足，当前可用余额：25.00元，订单所需金额：30.00元，差额：5.00元",
  "error": "Bad Request"
}
```

## 总结

通过为 `createAccountPointsOrder` 和 `createTrafficOrder` 方法添加余额验证和扣除功能，实现了：

1. **完整的余额管理**：订单创建前验证余额，创建后自动扣除
2. **精确的金额计算**：基于应用价格配置和订单参数计算准确金额
3. **可靠的事务保证**：使用数据库事务确保操作原子性
4. **详细的错误处理**：提供具体的余额不足信息和错误提示
5. **完善的日志记录**：记录金额计算、订单创建和余额扣除的详细信息
6. **良好的兼容性**：保持现有接口和行为不变，无缝集成

这个实现确保了开放平台订单系统的财务安全性和数据一致性，为用户提供了可靠的订单创建体验。
