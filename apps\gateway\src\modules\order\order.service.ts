import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import {
  ChannelEntity,
  InterestEntity,
  MemberEntity,
  OrderEntity,
  TeamEntity,
  UserEntity
} from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import {
  ChannelGiftRequestDTO,
  ChannelGiftResponseDto,
  OrderInfoResponse,
  OrderInterestData,
  OrderListRequest,
  orderPriceRequestDTO,
  OrderRequestCreateOrderDTO,
  orderResponseCreateOrder,
  OrdersListResponse,
  PayInfoResponse,
  PeddingOrderResponse,
  RenewOrderRequest,
  UpgradeOrderRequest
} from './order.dto'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { OrderStatus } from '@yxr/common'
import { OrderManagerService } from '@yxr/order'
import { eventKey, orderEventEmitter } from './order.event'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { VIPOften, OrderPriceResponseDto } from '@yxr/order'
import { WebhookEvents } from '../webhook/constant'
import { WebhookService } from '../webhook/webhook.service'

@Injectable()
export class OrderService {
  constructor(
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(InterestEntity.name) private interestModel: Model<InterestEntity>,
    @InjectModel(ChannelEntity.name) private channelModel: Model<ChannelEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly orderManagerService: OrderManagerService,
    private readonly webhookService: WebhookService
  ) {}

  async getOrders(query: OrderListRequest): Promise<OrdersListResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const skip = (query.page - 1) * query.size
    const size = query.size
    const page = query.page

    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(currentTeamId)
    })
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    const where: any = {}
    where.teamId = new Types.ObjectId(currentTeamId)
    const totalSize = await this.orderModel.countDocuments(where)
    const data = await this.orderModel
      .find(where)
      .sort({ createdAt: 'desc' })
      .skip(skip)
      .limit(size)

    //id去重
    const mergeIds = [...new Set(data.map((doc) => new Types.ObjectId(doc.userId)))]
    const users = await this.userModel.find(
      {
        _id: { $in: mergeIds }
      },
      { _id: 1, nickName: 1 }
    )

    const userToNameMap = users.reduce((ucc, uitem) => {
      ucc[uitem._id.toString()] = uitem.nickName
      return ucc
    }, {})
    const members = await this.memberModel.find(
      {
        userId: { $in: mergeIds }
      },
      { userId: 1, remark: 1 }
    )
    const memberToNameMap = members.reduce((mcc, mitem) => {
      mcc[mitem.userId.toString()] = mitem.remark
      return mcc
    }, {})

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data.map((item) => {

        let creatorName = '系统创建'
        if (!item.creatorId) {
          if (memberToNameMap[item.userId.toString()]) {
            creatorName = memberToNameMap[item.userId.toString()]
          } else {
            creatorName = userToNameMap[item.userId.toString()]
          }
        }
        return {
          id: item.id,
          orderNo: item.orderNo,
          teamName: team.name,
          orderStatus: item.orderStatus === OrderStatus.Pending && item.expiredAt.getTime() < Date.now()
          ? OrderStatus.Cancelled
          : item.orderStatus,
          orderType: item.orderType,
          type: item.orderSource,
          createTime: item.createdAt.getTime(),
          payTime: item.payTime ? item.payTime.getTime() : 0,
          price: item.totalAmount,
          dueAmount: item.payableAmount,
          payAmount: item.payAmount,
          payType: item.payType,
          creatorName: creatorName,
          expireTime: item.expiredAt.getTime(),
          remainingTimeInSeconds:
            item.expiredAt.getTime() > Date.now() ? item.expiredAt.getTime() - Date.now() : 0
        }
      })
    }
  }

  /**
   * 创建订单
   * @param param0
   * @returns
   */
  async createOrder({
    interestId,
    interestCount,
    month,
    isCorporateTransfer
  }: OrderRequestCreateOrderDTO): Promise<orderResponseCreateOrder> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(currentTeamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const orderNo = await this.orderManagerService.createOrder({
      teamId: currentTeamId,
      userId: currentUserId,
      interestCount: interestCount,
      interestId: interestId,
      month: month,
      days: 0,
      isCorporateTransfer: isCorporateTransfer,
      isPay: true
    })

    if (isCorporateTransfer === undefined || isCorporateTransfer === false) {
      // 对公转账没有15分钟自动取消订单
      orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })
    }

    // 对公转账没有支付，2小时自动取消订单

    return { orderNo }
  }

  /**
   * 订单计算金额
   * @param body
   * @returns
   */
  async calculateOrderPrice(body: orderPriceRequestDTO): Promise<OrderPriceResponseDto> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    return await this.orderManagerService.calculateOrderPrice({
      userId: currentUserId,
      teamId: currentTeamId,
      interestCount: body.interestCount,
      interestId: body.interestId.toString(),
      month: body.month,
      orderType: body.orderType,
      days: body.days
    })
  }

  /**
   * 升级订单
   * @param body
   * @returns
   */
  async createUpgradeOrder(body: UpgradeOrderRequest): Promise<orderResponseCreateOrder> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(currentTeamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const orderNo = await this.orderManagerService.upgradeOrder({
      teamId: currentTeamId,
      userId: currentUserId,
      interestCount: body.interestCount,
      interestId: body.interestId,
      isCorporateTransfer: body.isCorporateTransfer,
      isPay: true
    })

    return { orderNo }
  }

  /**
   * 续费订单
   * @param body
   * @returns
   */
  async createRenewOrder(body: RenewOrderRequest): Promise<orderResponseCreateOrder> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const unexpiredOrderCount = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(currentTeamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    if (unexpiredOrderCount > 0) {
      throw new ForbiddenException('还有未支付的订单')
    }

    const orderNo = await this.orderManagerService.renewOrder({
      teamId: currentTeamId,
      userId: currentUserId,
      interestId: body.interestId.toString(),
      isCorporateTransfer: body.isCorporateTransfer,
      month: body.month,
      days: 0,
      isPay: true
    })

    if (body.isCorporateTransfer === undefined || body.isCorporateTransfer === false) {
      orderEventEmitter.emit(eventKey, { orderNo, type: 'create' })
    }

    return { orderNo }
  }

  /**
   * 查询支付状态
   * @param orderNo
   * @returns
   */
  async getOrderStatus(orderNo: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(currentTeamId)
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }
    const order = await this.orderModel.findOne({
      orderNo: orderNo
    })
    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId.toString() !== currentTeamId) {
      throw new NotFoundException('没有权限查看此订单')
    }

    return {
      orderStatus: order.orderStatus
    }
  }

  async putOrderStatus(orderNo: string, orderStatus: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const order = await this.orderModel.findOne({
      orderNo: orderNo
    })

    if (orderStatus !== OrderStatus.Cancelled) {
      throw new NotFoundException('状态不正确')
    }

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId.toString() !== currentTeamId) {
      throw new ForbiddenException('当前订单不属于当前团队，无法取消')
    }

    if (order.orderStatus !== OrderStatus.Pending) {
      throw new ForbiddenException('订单不是待支付状态，无法取消')
    }

    try {
      await this.orderModel.updateOne(
        {
          orderNo: orderNo
        },
        { orderStatus: OrderStatus.Cancelled }
      )
      orderEventEmitter.emit(eventKey, { orderNo, type: 'close' })
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }

  async getOrderInfo(orderNo: string): Promise<OrderInfoResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(currentTeamId)
    })

    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    const order = await this.orderModel.findOne({
      orderNo: orderNo
    })

    if (!order) {
      throw new NotFoundException('订单不存在')
    }

    if (order.teamId.toString() !== currentTeamId) {
      throw new NotFoundException('没权获取当前订单信息')
    }

    const interest = await this.interestModel.findOne({
      _id: new Types.ObjectId(order.interestId)
    })

    return {
      vipInfo: {
        interestCount: order.interestCount,
        platformAccountCount: interest.accountCountLimit * order.interestCount,  // 变更为账号点数
        platformAccountCapacity: interest.accountCapacityLimit * order.interestCount,
        teamMemberCount: interest.memberCountLimit * order.interestCount,
        capacityLimit: interest.capacityLimit * order.interestCount,
        month: order.vipMonth,
        freeMonth: order.freeMonth,
        expirationTime: order.expiredAt.getTime() > Date.now() ? order.expiredAt.getTime() : 0
      },
      orderInfo: {
        orderNo: order.orderNo,
        teamName: team.name,
        code: team.code,
        createdAt: order.createdAt.getTime(),
        orderStatus:
          order.orderStatus === OrderStatus.Pending && order.expiredAt.getTime() < Date.now()
            ? OrderStatus.Cancelled
            : order.orderStatus,
        orderType: order.orderType,
        type: order.orderSource,
        totalAmount: order.totalAmount,
        dueAmount: order.payableAmount || 0,
        payAmount: order.payAmount || 0,
        payTime: order.payTime ? order.payTime.getTime() : 0,
        payType: order.payType
      }
    }
  }

  async getPayInfo(orderNo: string): Promise<PayInfoResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const { serviceCodeUrl } = this.configService.get<RootConfigMap['app']>('app')

    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(currentTeamId)
    })
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    const order = await this.orderModel.findOne({
      orderNo: orderNo
    })
    if (!order) {
      throw new NotFoundException('订单不存在')
    }
    if (order.teamId.toString() !== currentTeamId) {
      throw new NotFoundException('没权获取当前订单信息')
    }
    if (order.orderStatus !== OrderStatus.Pending) {
      throw new NotFoundException('当前订单已支付或取消，无法查询')
    }
    if (order.expiredAt.getTime() <= Date.now()) {
      throw new NotFoundException('当前订单已取消，无法查询')
    }

    return {
      corporateTransfer: serviceCodeUrl,
      alipayUrl: order?.alipayUrl || '',
      weixinUrl: order?.weixinUrl || '',
      orderType: order.orderType,
      price: order.totalAmount,
      dueAmount: order.payableAmount,
      orderNo: order.orderNo,
      remainingTimeInSeconds: order.expiredAt.getTime() - Date.now()
    }
  }

  async getPeddingOrders(): Promise<PeddingOrderResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session

    const count = await this.orderModel.countDocuments({
      teamId: new Types.ObjectId(currentTeamId),
      orderStatus: OrderStatus.Pending,
      expiredAt: {
        $gt: new Date()
      }
    })

    return {
      hasPendingOrder: count > 0,
      count
    }
  }

  /**
   * 获取订单VIP规格
   * @returns
   */
  async getInterest(): Promise<OrderInterestData> {
    const res = await this.interestModel.findOne()

    return {
      id: res._id.toString(),
      platformAccountCount: res.accountCountLimit,  // 变更为账号点数
      platformAccountCapacity: res.accountCapacityLimit,
      capacityLimit: res.capacityLimit,
      networkTrafficLimit: res.networkTrafficLimit,
      appPublish: res.appPublish,
      memberCount: res.memberCountLimit,
      price: res.price,
      vipOften: VIPOften
    }
  }

  async channelGiftOrder(body: ChannelGiftRequestDTO): Promise<ChannelGiftResponseDto> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const user = await this.userModel.findById(new Types.ObjectId(currentUserId))
    if (user.channelCode) {
      throw new ForbiddenException('已兑换过渠道vip,请联系客服')
    }
    const channel = await this.channelModel.findOne({
      channelCode: body.channelCode,
      enabled: true
    })

    if (channel === null) {
      throw new NotFoundException('渠道不存在')
    }

    if (channel.giftDays > 0) {
      const orderNo = await this.orderManagerService.channelGiftOrder(
        currentTeamId,
        currentUserId,
        body.channelCode,
        channel.giftDays,
        '渠道兑换赠送'
      )

      await this.orderManagerService.handleCompletedOrder(orderNo)

      await this.webhookService.grpchook(null, currentTeamId, {
        event: WebhookEvents.TeamVersionUpgrade
      })
    }

    await this.userModel.updateOne(
      {
        _id: new Types.ObjectId(currentUserId)
      },
      {
        channelCode: body.channelCode
      }
    )
    return {
      giftDays: channel.giftDays ?? 0,
      channelCode: channel.channelCode
    }
  }
}
