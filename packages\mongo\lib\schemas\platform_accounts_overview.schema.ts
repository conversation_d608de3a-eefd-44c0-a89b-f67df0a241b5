import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformAccountOverviewEntity {
  //账号ID
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  platformName: string

  //概览数据对象
  @Prop({
    type: String,
    required: false,
    default: 0
  })
  overviewData: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: Number,
    required: false
  })
  cloudUpdatedAt: number
}

export const PlatformAccountOverviewSchema: ModelDefinition = {
  name: PlatformAccountOverviewEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountOverviewEntity)
}

export const PlatformAccountOverviewMongoose = MongooseModule.forFeature([
  PlatformAccountOverviewSchema
])
