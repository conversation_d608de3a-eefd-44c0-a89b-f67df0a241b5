import axios, { AxiosInstance } from 'axios'

/**
 * 创建一个用于从天翼云下载文件的 axios 实例
 * 这个实例专门用于从存储服务下载媒体文件，不包含任何 API 相关的配置
 */
export function createMediaDownloadAxiosInstance(): AxiosInstance {
  return axios.create({
    timeout: 60000, // 60秒超时
    maxContentLength: Infinity, // 不限制响应内容大小
    maxBodyLength: Infinity, // 不限制请求体大小
    headers: {
      'Accept': '*/*',
      'User-Agent': 'yixiaoer-service/1.0'
    }
  })
}

/**
 * 下载媒体文件
 * @param url 文件URL
 * @returns Buffer
 */
export async function downloadMediaFile(url: string): Promise<Buffer> {
  const downloadAxios = createMediaDownloadAxiosInstance()

  try {
    const response = await downloadAxios.get(url, {
      responseType: 'arraybuffer'
    })

    return Buffer.from(response.data)
  } catch (error) {
    throw new Error(`文件下载失败: ${error.message}`)
  }
}
