/**
 * 开放平台Webhook事件类型
 */
export enum OpenPlatformWebhookEventType {
  // 媒体账号事件
  PLATFORM_ACCOUNT_CREATED = 'platform_account_created',
  PLATFORM_ACCOUNT_DELETED = 'platform_account_deleted',
  
  // 任务事件
  TASKSET_CREATED = 'taskset_created',
  TASK_STATUS_UPDATED = 'task_status_updated'
}

/**
 * Webhook事件数据接口
 */
export interface WebhookEventData {
  type: OpenPlatformWebhookEventType
  timestamp: number
  teamId: string
  applicationId: string
  data: any
}

/**
 * 媒体账号创建事件数据
 */
export interface PlatformAccountCreatedEventData {
  accountId: string
  platformName: string
  nickname: string
  teamId: string
  userId: string
  capacity: number
  createdAt: number
}

/**
 * 媒体账号删除事件数据
 */
export interface PlatformAccountDeletedEventData {
  accountId: string
  platformName: string
  nickname: string
  teamId: string
  userId: string
  deletedAt: number
}

/**
 * 任务集创建事件数据
 */
export interface TaskSetCreatedEventData {
  taskSetId: string
  taskSetName: string
  teamId: string
  userId: string
  taskCount: number
  createdAt: number
}

/**
 * 任务状态更新事件数据
 */
export interface TaskStatusUpdatedEventData {
  taskId: string
  taskSetId: string
  taskSetName: string
  teamId: string
  userId: string
  oldStatus: string
  newStatus: string
  platformName: string
  accountNickname: string
  updatedAt: number
}

/**
 * Webhook推送配置
 */
export interface WebhookPushConfig {
  enabled: boolean
  maxRetries: number
  retryIntervals: number[] // 重试间隔（毫秒）
  timeout: number // 超时时间（毫秒）
}

/**
 * Webhook推送结果
 */
export interface WebhookPushResult {
  success: boolean
  statusCode?: number
  message: string
  retryCount: number
  duration: number
}

/**
 * 事件监听器参数
 */
export interface EventListenerParams {
  teamId: string
  data: any
}
