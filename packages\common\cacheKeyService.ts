import { ContentType, HistoryStatisticTypeEnum } from './enums'

export class CacheKeyService {
  /**
   * 微信账号登录状态信息
   * @param uuid 获取二维码返回的ID
   * @returns
   */
  static getWxAccountLoginInfoKey(uuid: string): string {
    return `kuaidailiLoginInfo:${uuid}`
  }

  /**
   * 微信扫码登陆快代理的相关信息
   * @param uuid 获取二维码返回的ID
   * @returns
   */
  static getKuaidailiKey(uuid: string): string {
    return `kuaidaili:${uuid}`
  }

  static getAssistantMemberKey(teamId: string, parentId: string): string {
    return `shipinhao:${teamId}:${parentId}`
  }

  static getWeiXinAccountLockKey(teamId: string, platformAccountId: string): string {
    return `${teamId}:AccountLock:${platformAccountId}`
  }

  /**
   * 首页统计缓存key
   * @param teamId
   * @param userId
   * @returns
   */
  static getHomeOverviewKey(teamId: string, userId?: string): string {
    let overviewKey = `home:statistic-${teamId}`
    if (userId) {
      overviewKey += `:${userId}`
    }
    return overviewKey
  }

  static getPlatformAccountOverviewKey(teamId: string, userId?: string): string {
    let overviewKey = `platformAccount:statistic-${teamId}`
    if (userId) {
      overviewKey += `:${userId}`
    }
    return overviewKey
  }

  /**
   * 趋势缓存key
   * @param teamId
   * @param statisticType
   * @returns
   */
  static getTrendStatisticKey(
    teamId: string,
    statisticType: HistoryStatisticTypeEnum,
    platform?: string,
    contentType?: ContentType
  ) {
    let cacheKey = `trend:${teamId}:${statisticType}`
    if (platform) {
      cacheKey += `-${platform}`
    }
    if (contentType) {
      cacheKey += `-${contentType}`
    }
    return cacheKey
  }

  /**
   * 平台统计缓存key
   * @param teamId
   * @param userId
   * @returns
   */
  static getPlatformOverviewKey(
    teamId: string,
    userId?: string,
    platform?: string,
    contentType?: ContentType
  ): string {
    let overviewKey = `statistic:${teamId}`
    if (userId) {
      overviewKey += `-${userId}`
    }
    if (platform) {
      overviewKey += `-${platform}`
    }
    if (contentType) {
      overviewKey += `-${contentType}`
    }
    return overviewKey
  }

  /**
   * 发布次数key
   * @param currentTeamId 
   * @returns 
   */
  static genCounterKey(currentTeamId: string) {
    return `pushes:counter:${currentTeamId}`
  }
}
