export class EventNames {
  /**
   * 成员角色发生变更
   */
  public static readonly MemberRolesChangedEvent = 'member.rolesChanged'

  /**
   * 成员运营账号发生变更
   */
  public static readonly MemberAccountsChangedEvent = 'member.accountsChanged'

  /**
   * 账号运营人发生变更
   */
  public static readonly PlatformMembersChangedEvent = 'platform.membersChanged'

  /**
   * 账号运营人发生变更2
   */
  public static readonly PlatformMembersChangedEvent2 = 'platform.membersChanged2'

  /**
   * 浏览器空间删除
   */
  public static readonly BrowserDeleteEvent = 'browser.delete'

  /**
   * 任务的审核状态变更
   */
  public static readonly TaskAuditStatusChangedEvent = 'task.auditStatusChanged'

  /**
   * 云端的任务推送
   */
  public static readonly TaskCloudPushEvent = 'task.cloudPush'
}

/**
 * 成员角色发生变更
 */
export class MemberRolesChangedEvent {
  constructor(
    public readonly userId: string,
    public readonly teamId: string,
    public readonly oldRoles: string[],
    public readonly newRoles: string[]
  ) {}
}

/**
 * 成员运营账号发生变更
 */
export class MemberAccountsChangedEvent {
  constructor(
    public readonly userId: string,
    public readonly teamId: string,
    public readonly addAccountIds: string[],
    public readonly delAccountIds: string[]
  ) {}
}

/**
 * 媒体账号发生变更
 */
export class PlatformMembersChangedEvent {
  constructor(
    public readonly platformAccountId: string,
    public readonly teamId: string,
    public readonly addMemberIds: string[],
    public readonly delMemberIds: string[]
  ) {}
}

/**
 * 网站空间删除关联数据事件
 */
export class BrowserDeleteEvent {
  constructor(
    public readonly accountId: string,
    public readonly teamId: string,
    public readonly originalId: string,
  ) {}
}

/**
 * 任务的审核状态变更
 */
export class TaskAuditStatusEvent {
  constructor(
    public readonly teamId: string,
    public readonly taskIdentityId: string
  ) {}
}

/**
 * 任务云端推送
 */
export class TaskCloudPushEvent {
  constructor(
    public readonly teamId: string,
    public readonly taskIdentityId: string,
    public readonly authorization: string
  ) {}
}
