import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { ContentStatisticEntity, TaskEntity } from '@yxr/mongo'
import { Model, Types, UpdateQuery } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { ContentStatisticDataDTO } from './content.dto'
import { StatisticCommonService } from '@yxr/common'
import { TlsService } from '@yxr/huoshan'

export const eventKey = 'content-statistic'
export const eventName = 'content-statistic-event'
export const contentStatisticEventEmitter = new EventEmitter()

/**
 * 注意此服务有调用事件不能引入 FastifyRequest 内容上报相关事件功能会失效
 */
@Injectable()
export class ContentStatisticEventService implements OnModuleInit {
  logger = new Logger('ContentStatisticEventService')

  constructor(
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    private readonly loggerService: TlsService
  ) {}

  contentTaskQueue: Queue

  contentTaskWorker: Worker

  onModuleInit() {
    this.logger.log('ContentStatisticEventService init')

    contentStatisticEventEmitter.on(eventKey, this.createContentStatisticQueue.bind(this))

    this.contentTaskQueue = new Queue(eventName, {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.contentTaskWorker = new Worker(
      eventName,
      async (job) => {
        const { teamId, platformAccountId, element, platformName } = job.data
        await this.contentStatisticWorker(teamId, platformAccountId, element, platformName)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )
  }

  /**
   * 创建内容统计队列
   * @param param0
   */
  async createContentStatisticQueue({
    teamId,
    platformAccountId,
    platformName,
    contentData
  }: {
    teamId: string
    platformAccountId: string
    platformName: string
    contentData: ContentStatisticDataDTO[]
  }) {
    await this.contentStatisticModel.deleteMany({
      teamId: new Types.ObjectId(teamId),
      platformAccountId: new Types.ObjectId(platformAccountId)
    })
    for (let index = 0; index < contentData.length; index++) {
      const element = contentData[index]
      await this.contentTaskQueue.add(
        eventName,
        { teamId, platformAccountId, element, platformName },
        {
          removeOnComplete: true,
          removeOnFail: true,
          jobId: `${Date.now()}-${eventName}`
        }
      )
    }
  }

  /**
   * 消费内容统计任务
   * @param contentId
   * @returns
   */
  async contentStatisticWorker(
    teamId: string,
    platformAccountId: string,
    contentData: ContentStatisticDataDTO,
    platformName: string
  ) {
    try {
      let contentStatistic = null
      let task = null
      if (!contentData?.id || contentData?.id === '') {
        contentStatistic = await this.contentStatisticModel.findOne({
          title: contentData.title,
          publishTime: contentData.date,
          teamId: new Types.ObjectId(teamId)
        })
      } else {
        contentStatistic = await this.contentStatisticModel.findOne({
          publishId: contentData?.id,
          teamId: new Types.ObjectId(teamId)
        })
        task = await this.taskModel.findOne({
          teamId: new Types.ObjectId(teamId),
          documentId: contentData?.id
        })
      }

      const data: UpdateQuery<ContentStatisticEntity> = {
        platformName: platformName,
        publishId: contentData?.id ?? null,
        contentData: contentData,
        publishTime: new Date(contentData.date),
        contentType: contentData.type,
        title: contentData.title,
        desc: contentData.desc,
        publishUserId: task?.userId ? new Types.ObjectId(task?.userId) : null,
        reCommand: contentData.reCommand
          ? StatisticCommonService.convertToNumber(contentData.reCommand)
          : 0,
        play: contentData.play ? StatisticCommonService.convertToNumber(contentData.play) : 0,
        read: contentData.read ? StatisticCommonService.convertToNumber(contentData.read) : 0,
        great: contentData.great ? StatisticCommonService.convertToNumber(contentData.great) : 0,
        comment: contentData.comment
          ? StatisticCommonService.convertToNumber(contentData.comment)
          : 0,
        share: contentData.share ? StatisticCommonService.convertToNumber(contentData.share) : 0,
        collect: contentData.collect
          ? StatisticCommonService.convertToNumber(contentData.collect)
          : 0,
        addFans: contentData.addFans
          ? StatisticCommonService.convertToNumber(contentData.addFans)
          : 0,
        finishPlay: contentData.finishPlay
          ? parseFloat(contentData.finishPlay.replace('%', '')) / 100
          : 0,
        love: contentData.love ? StatisticCommonService.convertToNumber(contentData.love) : 0,
        danmu: contentData.danmu ? StatisticCommonService.convertToNumber(contentData.danmu) : 0,
        coin: contentData.coin ? StatisticCommonService.convertToNumber(contentData.coin) : 0,
        like: contentData.like ? StatisticCommonService.convertToNumber(contentData.like) : 0,
        vote: contentData.vote ? StatisticCommonService.convertToNumber(contentData.vote) : 0,
        playTime: contentData.playTime
          ? StatisticCommonService.convertToNumber(contentData.playTime)
          : 0
      }

      if (contentStatistic) {
        //存在就只更新统计数据
        await this.contentStatisticModel.updateOne(
          {
            _id: contentStatistic._id
          },
          data
        )
      } else {
        data.teamId = new Types.ObjectId(teamId)
        data.platformAccountId = new Types.ObjectId(platformAccountId)

        await this.contentStatisticModel.create(data)
      }
    } catch (error) {
      await this.loggerService.error(null, '每日团队销售数据定时处理失败', { error: error.message })
    }
  }
}
