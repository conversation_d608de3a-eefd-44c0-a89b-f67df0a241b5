/**
 * TikTok平台特定错误处理器
 */

import { AxiosResponse } from 'axios'
import { BusinessErrorChecker, OverseasContext } from '../../utils/axios-config'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  extractRequestInfo,
  extractResponseInfo
} from '../../utils/error-handler'

import { TikTokApiResponse } from './tiktok-api-types'

/**
 * TikTok业务错误检查器
 */
export class TikTokBusinessErrorChecker implements BusinessErrorChecker {
  /**
   * 检查TikTok响应是否包含业务错误
   * TikTok API的特点是即使出现业务错误，HTTP状态码仍然是200
   * 需要通过响应数据中的code字段来判断是否有错误
   */
  hasBusinessError(response: AxiosResponse<TikTokApiResponse>): boolean {
    return response.data && response.data.code !== 0
  }

  /**
   * 处理TikTok业务错误
   * 根据TikTok错误码文档映射到标准错误类型
   * 文档: https://business-api.tiktok.com/portal/docs?id=1737172488964097
   */
  async handleBusinessError(response: AxiosResponse<TikTokApiResponse>, context: OverseasContext): Promise<never> {
    const data = response.data

    if (!data || data.code === 0) {
      // 不应该调用这个方法，因为没有错误
      throw new RemoteApiError(
        context.platform,
        RemoteApiErrorCodes.UnknownRemoteApiError,
        { message: '意外调用了错误处理方法，但API返回成功' },
        extractRequestInfo(response),
        extractResponseInfo(response),
        context
      )
    }

    let errorCode: RemoteApiErrorCodes

    // 根据TikTok错误码映射到标准错误类型
    switch (data.code) {
      // 权限相关错误
      case 40001:
        errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
        break

      // 访问令牌相关错误
      case 40102:
      case 40104:
      case 40105:
        errorCode = RemoteApiErrorCodes.AccessTokenInvalid
        break
      case 40103:
        errorCode = RemoteApiErrorCodes.AccessTokenExpired
        break

      // 请求参数错误
      case 40000:
      case 40002:
      case 40006:
      case 40010:
      case 40011:
      case 40053:
      case 40051:
        errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
        break

      // 服务器错误
      case 50001:
      case 50002:
        errorCode = RemoteApiErrorCodes.ServerError
        break

      // 限流相关错误
      case 60001:
        errorCode = RemoteApiErrorCodes.RateLimitExceeded
        break
      case 60002:
        errorCode = RemoteApiErrorCodes.QuotaExceeded
        break

      // 内容相关错误
      case 70001:
        errorCode = RemoteApiErrorCodes.ContentViolation
        break
      case 70002:
        errorCode = RemoteApiErrorCodes.MediaUploadFailed
        break

      // 账号相关错误
      case 80001:
        errorCode = RemoteApiErrorCodes.AccountSuspended
        break

      // 功能不可用
      case 90001:
        errorCode = RemoteApiErrorCodes.FeatureNotAvailable
        break

      // 未知错误
      default:
        errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      {
        message: data.message,
        tiktokErrorCode: data.code,
        logId: data.request_id
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    // 调试中, 如果需要更详细的错误信息，可以打开下面的注释
    // console.warn(`[TikTok] API业务错误`, {
    //   errorCode: apiError.errorCode,
    //   tiktokCode: data.code,
    //   message: data.message,
    //   logId: data.request_id,
    //   context: context,
    //   requestInfo: apiError.requestInfo,
    //   responseInfo: apiError.responseInfo
    // })

    throw apiError
  }
}

/**
 * 创建TikTok业务错误检查器实例
 */
export function createTikTokBusinessErrorChecker(): BusinessErrorChecker {
  return new TikTokBusinessErrorChecker()
}

/**
 * TikTok错误码到错误类型的映射表（用于文档和调试）
 */
export const TIKTOK_ERROR_CODE_MAPPING = {
  // 权限错误
  40001: 'SCOPE_NOT_AUTHORIZED',

  // 令牌错误
  40102: 'ACCESS_TOKEN_INVALID',
  40103: 'ACCESS_TOKEN_EXPIRED',
  40104: 'ACCESS_TOKEN_INVALID',
  40105: 'ACCESS_TOKEN_INVALID',

  // 参数错误
  40000: 'REQUEST_PARAMETERS_INCORRECT',
  40002: 'REQUEST_PARAMETERS_INCORRECT',
  40006: 'REQUEST_PARAMETERS_INCORRECT',
  40010: 'REQUEST_PARAMETERS_INCORRECT',
  40011: 'REQUEST_PARAMETERS_INCORRECT',
  40051: 'REQUEST_PARAMETERS_INCORRECT',
  40053: 'REQUEST_PARAMETERS_INCORRECT',

  // 服务器错误
  50001: 'SERVER_ERROR',
  50002: 'SERVER_ERROR',

  // 限流错误
  60001: 'RATE_LIMIT_EXCEEDED',
  60002: 'QUOTA_EXCEEDED',

  // 内容错误
  70001: 'CONTENT_VIOLATION',
  70002: 'MEDIA_UPLOAD_FAILED',

  // 账号错误
  80001: 'ACCOUNT_SUSPENDED',

  // 功能错误
  90001: 'FEATURE_NOT_AVAILABLE'
} as const

/**
 * 获取TikTok错误码的描述信息
 */
export function getTikTokErrorDescription(code: number): string {
  const mapping = TIKTOK_ERROR_CODE_MAPPING as Record<number, string>
  return mapping[code] || 'UNKNOWN_ERROR'
}
