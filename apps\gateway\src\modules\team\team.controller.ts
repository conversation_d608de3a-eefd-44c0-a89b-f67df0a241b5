import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  createTeamRequestBodyDTO,
  getTeamListOkResponseDTO,
  patchTeamRequestBodyDTO,
  putTeamComponentRequestBodyDTO,
  TeamResponseDTO
} from './team.dto'
import { TeamService } from './team.service'
import { TeamAreaResponseDTO, UserLoginOkResponseDTO } from '../user/user.dto'

@Controller('teams')
@ApiTags('团队管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true, description: 'token' })
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  /**
   * 创建团队
   * @param data
   */
  @Post()
  @ApiOperation({ summary: '创建团队', description: '当前用户会成为新创建团队的超级管理员' })
  @ApiOkResponse({ type: TeamResponseDTO, description: '操作成功' })
  create(@Body() data: createTeamRequestBodyDTO) {
    return this.teamService.createTeam({ name: data.name, logoKey: data.logoKey })
  }

  /**
   * 获取团队列表
   * @param size
   * @param page
   */
  @Get()
  @ApiOperation({ summary: '获取团队列表' })
  @ApiOkResponse({ type: getTeamListOkResponseDTO, description: '操作成功' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  async getList(
    @Query('size', {
      transform: (value) => value || 10
    })
    size: number,
    @Query('page', {
      transform: (value) => value || 1
    })
    page: number
  ) {
    return this.teamService.getPagedTeams({
      page,
      size
    })
  }

  /**
   * 修改团队信息
   * @param teamId
   * @param data
   */
  @Patch(':teamId')
  @ApiOperation({ summary: '更新团队信息' })
  @ApiOkResponse({ type: TeamResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'teamId', required: true, description: '团队ID' })
  patch(@Param('teamId') teamId: string, @Body() data: patchTeamRequestBodyDTO) {
    return this.teamService.update(teamId, data)
  }

  /**
   * 解散团队
   * @param teamId
   */
  @Delete(':teamId')
  @ApiOperation({ summary: '解散团队' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'teamId', required: true, description: '团队ID' })
  delete(@Param('teamId') teamId: string) {
    return this.teamService.delete(teamId)
  }

  /**
   * 获取团队信息
   * @param teamId
   */
  @Get(':teamId')
  @ApiOperation({ summary: '获取团队信息' })
  @ApiOkResponse({ type: TeamResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'teamId', required: true, description: '团队ID' })
  async get(@Param('teamId') teamId: string) {
    return this.teamService.getTeam(teamId)
  }

  /**
   * 登录到团队(切换团队)
   * @param teamId
   */
  @Post(':teamId/auth')
  @ApiOperation({ summary: '登录到团队(切换团队)' })
  @ApiOkResponse({ type: UserLoginOkResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'teamId', required: true, description: '团队ID' })
  signin(@Param('teamId') teamId: string) {
    return this.teamService.signin(teamId)
  }

  @Get(':teamId/areas')
  @ApiOkResponse({ type: TeamAreaResponseDTO, description: '操作成功' })
  @ApiOperation({ summary: ' 获取团队代理区域' })
  getTeamAreas(@Param('teamId') teamId: string) {
    return this.teamService.getTeamAreas(teamId)
  }

  @Put(':teamId/components')
  @ApiOkResponse({ type: TeamAreaResponseDTO, description: '操作成功' })
  @ApiOperation({ summary: '新增插件或修改插件' })
  putTeamComponents(@Param('teamId') teamId: string, @Body() body: putTeamComponentRequestBodyDTO) {
    return this.teamService.putTeamComponents(teamId, body)
  }
}
