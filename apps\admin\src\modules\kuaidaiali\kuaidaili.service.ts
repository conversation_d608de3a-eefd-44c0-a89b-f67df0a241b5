import { Injectable, Logger, NotFoundException, OnModuleInit } from '@nestjs/common'
import { AllPlatforms, KUAIDIALI_AREAS, PlatformNameEnum } from '@yxr/common'
import { UpdateTeamAreasInputDto } from './kuaidaili.dtos'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformProxyEntity, TeamEntity } from '@yxr/mongo'

@Injectable()
export class KuaidailiService implements OnModuleInit {
  logger = new Logger(KuaidailiService.name)

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformProxyEntity.name) private platformProxyModel: Model<PlatformProxyEntity>
  ) {}
  async onModuleInit() {
    // await this.dailiInit()
  }

  async getAreas() {
    return KUAIDIALI_AREAS
  }

  async updateTeamAreas(teamId: string, input: UpdateTeamAreasInputDto[]) {
    const team = await this.teamModel.findOne({ _id: new Types.ObjectId(teamId) })

    if (!team) {
      throw new NotFoundException('团队未找到')
    }

    const areas = input.map((item) => ({
      province: { name: item.name, code: item.code },
      city: item.cities.length > 0 ? { name: item.cities[0].name, code: item.cities[0].code } : null
    }))

    team.kuaidailiAreas = areas
    await team.save()
  }

  async getTeamAreas(teamId: string) {
    const team = await this.teamModel.findOne({ _id: new Types.ObjectId(teamId) })

    if (!team) {
      throw new NotFoundException('团队未找到')
    }

    const areas = team.kuaidailiAreas

    if (Array.isArray(areas) && areas.length > 0) {
      return areas
        .filter((area) => area && typeof area === 'object') // 过滤掉无效值
        .map(
          (area) =>
            ({
              name: area.province.name,
              code: area.province.code,
              cities: area.city ? [{ name: area.city.name, code: area.city.code }] : []
            }) as UpdateTeamAreasInputDto
        )
    } else {
      return []
    }
  }

  /**
   * 代理初始化
   */
  async dailiInit() {
    const ips = [
      // { prot: 4843, code: 430100 },
      // { prot: 48431, code: 430100 },
      // { prot: 4844, code: 440100 },
      // { prot: 48441, code: 440100 },
      // { prot: 48442, code: 440100 },
      // { prot: 48443, code: 440100 },
      // { prot: 4845, code: 330100 },
      // { prot: 48451, code: 330100 },
      // { prot: 4846, code: 410100 },
      // { prot: 48461, code: 410100 },
      // { prot: 4847, code: 11 },
      // { prot: 48471, code: 11 },
      // { prot: 4848, code: 320100 },
      { prot: 48481, code: 320100 },
      // { prot: 4849, code: 510100 },
      // { prot: 4850, code: 31 },
      { prot: 48501, code: 31 },
      // { prot: 4851, code: 420100 },
      // { prot: 4852, code: 130100 },
      // { prot: 4853, code: 210100 },
      // { prot: 48531, code: 210100 },
      // { prot: 4854, code: 340100 },
      // { prot: 4855, code: 350100 },
      { prot: 4856, code: 370100 },
      { prot: 4857, code: 530100 },
      { prot: 4858, code: 360100 }
    ]

    console.log(ips)
    for (const ip of ips) {
      
      const addProxy: PlatformProxyEntity = {
        platformName: null,
        proxyIp: '********:' + ip.prot,
        proxyPwd: 'password',
        proxyUser: 'maojian',
        regionId: ip.code.toString(),
        currLogin: 0,
        maxLogin: 200,
        enabled: true
      }
      const proxys = await this.platformProxyModel.findOne({
        proxyIp: addProxy.proxyIp
      })
      if (!proxys) {
        const platforms = AllPlatforms
        const newPlatforms = platforms.filter(
          (platform) => platform !== PlatformNameEnum.微信公众号
        )
        for (const platform of newPlatforms) {
          const proxys = await this.platformProxyModel.findOne({
            proxyIp: addProxy.proxyIp,
            platformName: platform
          })
          if (proxys) {
            continue
          }
          addProxy.platformName = platform
          await this.platformProxyModel.create(addProxy)
        }
      }
    }
  }
}
