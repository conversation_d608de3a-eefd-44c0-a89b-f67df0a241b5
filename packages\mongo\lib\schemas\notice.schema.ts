import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { NoticeTypesEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class NoticeEntity {
  /**
   * 消息标题
   */
  @Prop({
    type: String,
    required: true
  })
  title: string

  /**
   * 消息内容
   */
  @Prop({
    type: String,
    required: true
  })
  content: string

  /**
   * 消息的接收者数组(用户Id)
   */
  @Prop({
    type: [String],
    required: true,
    index: true
  })
  receiverIds: string[]

  /**
   * 消息的发送者(用户Id)
   */
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  senderId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    enum: {
      values: [
        NoticeTypesEnum.Regular,
        NoticeTypesEnum.Proposal,
        NoticeTypesEnum.Invitation,
        NoticeTypesEnum.System
      ],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: NoticeTypesEnum.Regular
  })
  type: NoticeTypesEnum

  /**
   * 交互动作参数定义, 无固定结构
   */
  @Prop({
    type: Types.Map,
    required: false
  })
  bizArgs?: any

  /**
   * 交互状态结果定义, 无固定结构
   */
  @Prop({
    type: Types.Map,
    required: false
  })
  bizState?: any

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const NoticeSchema: ModelDefinition = {
  name: NoticeEntity.name,
  schema: SchemaFactory.createForClass(NoticeEntity)
}

export const NoticeMongoose = MongooseModule.forFeature([NoticeSchema])
