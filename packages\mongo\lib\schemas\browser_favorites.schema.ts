import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class BrowserFavoritesEntity {
  /**
   * @deprecated
   * 空间ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  browserId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  platformAccountId: Types.ObjectId

  /**
   * 网站地址
   */
  @Prop({
    type: String,
    required: false
  })
  websiteUrl: string

  /**
   * 收藏名称
   */
  @Prop({
    type: String,
    required: false
  })
  name: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const BrowserFavoritesSchema: ModelDefinition = {
  name: BrowserFavoritesEntity.name,
  schema: SchemaFactory.createForClass(BrowserFavoritesEntity)
}

export const BrowserFavoritesMongoose = MongooseModule.forFeature([BrowserFavoritesSchema])
