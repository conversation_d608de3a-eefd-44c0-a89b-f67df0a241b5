import { InjectModel } from '@nestjs/mongoose'
import {
  MessageCreateRequestDTO,
  MessageDetail,
  MessageListRequestDTO,
  MessageListResponse
} from './message.dto'
import { Model, Types } from 'mongoose'
import { MessageEntity } from '@yxr/mongo'
import { Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { NoticeTypesEnum } from '@yxr/common'

@Injectable()
export class MessageService {
  constructor(
    @InjectModel(MessageEntity.name) private messageModel: Model<MessageEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly webhookService: WebhookService
  ) {}

  /**
   * 获取广告列表
   * @param query
   * @returns
   */
  async getMessages(query: MessageListRequestDTO): Promise<MessageListResponse> {
    const messages = await this.messageModel
      .find({ type: NoticeTypesEnum.System })
      .sort({ updatedAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.messageModel.find().countDocuments()

    return {
      page: query.page,
      size: query.size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / query.size),
      data: messages.map((item) => ({
        id: item.id,
        title: item.title,
        content: item.content,
        type: item.type,
        isPopUp: item.isPopUp,
        createdAt: item.createdAt ? item.createdAt.getTime() : 0
      }))
    }
  }

  /**
   * 创建广告
   * @param body
   */
  async createMessage(body: MessageCreateRequestDTO) {
    const { user } = this.request

    const createData: MessageEntity = {
      title: body.title,
      content: body.content,
      isPopUp: body.isPopUp,
      type: NoticeTypesEnum.System
    }

    await this.messageModel.create(createData)

    await this.webhookService.sendToAll({
      event: WebhookEvents.SystemMessageUpdated
    })
  }

  /**
   * 获取消息详情
   * @param messageId
   * @returns
   */
  async getMessageDetail(messageId: string): Promise<MessageDetail> {
    const message = await this.messageModel.findOne({
      _id: new Types.ObjectId(messageId)
    })

    if (!message) {
      throw new NotFoundException('消息未找到')
    }

    return {
      id: message.id,
      title: message.title,
      content: message.content,
      isPopUp: message.isPopUp,
      type: message.type,
      createdAt: message.createdAt.getTime()
    }
  }

  /**
   * 更新广告
   * @param adId
   * @param body
   */
  async patchAdDetail(messageId: string, body: MessageCreateRequestDTO) {
    const message = await this.messageModel.findOne({
      _id: new Types.ObjectId(messageId)
    })

    if (!message) {
      throw new NotFoundException('消息未找到')
    }

    message.title = body.title
    message.content = body.content
    message.isPopUp = body.isPopUp

    await message.save()

    await this.webhookService.sendToAll({
      event: WebhookEvents.SystemMessageUpdated
    })
  }

  /**
   * 删除广告
   * @param messageId
   */
  async deleteMessage(messageId: string) {
    const ad = await this.messageModel.findOne({
      _id: new Types.ObjectId(messageId)
    })

    if (!ad) {
      throw new NotFoundException('广告未找到')
    }

    await this.messageModel.deleteOne({
      _id: ad._id
    })

    await this.webhookService.sendToAll({
      event: WebhookEvents.SystemMessageUpdated
    })
  }
}
