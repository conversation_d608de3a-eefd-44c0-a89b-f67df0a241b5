import { Controller } from '@nestjs/common'
import { GrpcMethod } from '@nestjs/microservices'
import { ToDeviceSocketParam, SocketParam, ToAllSocketParam } from '@yxr/proto'
import { EventsGateway } from './events.gateway'

@Controller()
export class SocketController {
  constructor(private readonly eventsGateway: EventsGateway) {}

  @GrpcMethod('Socket', 'send')
  async send({ list }: SocketParam) {
    await this.eventsGateway.send(JSON.parse(list))
  }

  @GrpcMethod('Socket', 'sendToAll')
  async sendToAll({ data }: ToAllSocketParam) {
    await this.eventsGateway.sendToAll(JSON.parse(data))
  }

  @GrpcMethod('Socket', 'sendToDevice')
  async sendToDevice(param: ToDeviceSocketParam) {
    await this.eventsGateway.sendToDevice(param.deviceType, JSON.parse(param.data))
  }
}
