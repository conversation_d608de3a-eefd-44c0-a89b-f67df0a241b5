// 定义一个角色名的字符串常量
export class TeamRoleNames {
  public static readonly MASTER = 'master'
  public static readonly ADMIN = 'admin'
  public static readonly MEMBER = 'member'
  public static readonly NotJoined = 'notJoined' // 不属于常规角色

  public static readonly All = [TeamRoleNames.MASTER, TeamRoleNames.ADMIN, TeamRoleNames.MEMBER]
}

export class TeamFeatures {
  /**
   * 默认账号数量限制
   *
   * @deprecated 请使用 DefaultAccountCapacityLimit
   */
  public static readonly DefaultAccountCountLimit = 5  // 变更为账号点数

  /**
   * 默认账号点数限制
   */
  public static readonly DefaultAccountCapacityLimit = 5

  /**
   * 默认成员数量限制
   */
  public static readonly DefaultMemberCountLimit = 1

  /**
   * 用户准许加入的团队数量上限
   */
  public static readonly MaximumTeamCount = 10
}

export class TopicFeatures {
  /**
   * 默认话题分组内最大话题数
   */
  public static readonly MixGroupTopicsCountLimit = 50
}

export class UserFeatures {
  /**
   * 密码登陆错误次数
   */
  public static readonly ErrorPasswordLoginCountLimit = 5
}

export * from './enums'
export * from './events'
// export * from './oss.service'
export * from './tianyiyun-oss.service'
export * from './statistic-common.service'
export * from './cacheKeyService'
export * from './version.service'
export * from './kuaidaili-areas'
export * from './common.module'
export * from './open-platform.constants'
export * from './user-utils'
