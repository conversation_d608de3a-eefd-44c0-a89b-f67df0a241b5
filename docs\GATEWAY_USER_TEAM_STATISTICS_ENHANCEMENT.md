# Gateway User 团队统计信息增强

## 概述

在 `gateway-user.service.ts` 的 `getAppTeams` 方法中增强了团队信息查询功能，添加了团队成员数、流量统计等关键信息，提供更完整的团队数据视图。

## 新增统计信息

### 1. 当前团队成员数 (`memberCount`)
- **数据来源**: `MemberEntity` 集合
- **统计逻辑**: 统计状态为 `joined` 的实际成员数量
- **数据隔离**: 只统计当前应用（`sourceAppId`）下的成员

### 2. 团队总流量数 (`totalTraffic`)
- **数据来源**: `TeamEntity.accumulatedTraffic` 字段
- **单位转换**: 从 KB 转换为 GB
- **精度**: 保留两位小数

### 3. 已使用流量数 (`usedTraffic`)
- **数据来源**: `TrafficBillingEntity` 集合
- **统计逻辑**: 汇总该团队所有流量计费记录的使用量
- **单位转换**: 从字节转换为 GB
- **精度**: 保留两位小数

## 实现方案

### 1. DTO 增强

**文件**: `apps/admin/src/modules/open-platform/dto/gateway-user.dto.ts`

```typescript
export class AppTeamResponseDto {
  // ... 现有字段
  
  @ApiProperty({
    description: '当前团队成员数',
    example: 5
  })
  memberCount: number

  @ApiProperty({
    description: '团队总流量数（GB）',
    example: 100
  })
  totalTraffic: number

  @ApiProperty({
    description: '已使用流量数（GB）',
    example: 25
  })
  usedTraffic: number
}
```

### 2. 聚合查询实现

**文件**: `apps/admin/src/modules/open-platform/services/gateway-user.service.ts`

#### 2.1 依赖注入
```typescript
constructor(
  // ... 现有依赖
  @InjectModel(TrafficBillingEntity.name) 
  private trafficBillingModel: Model<TrafficBillingEntity>,
  // ... 其他依赖
)
```

#### 2.2 聚合管道设计
```typescript
const aggregationPipeline = [
  { $match: filter },
  {
    // 关联成员信息
    $lookup: {
      from: 'memberentities',
      localField: '_id',
      foreignField: 'teamId',
      as: 'members'
    }
  },
  {
    // 关联流量计费信息
    $lookup: {
      from: 'trafficbillingentities',
      localField: '_id',
      foreignField: 'teamId',
      as: 'trafficBilling'
    }
  },
  {
    // 计算统计字段
    $addFields: {
      // 实际成员数（只统计已加入的成员）
      actualMemberCount: {
        $size: {
          $filter: {
            input: '$members',
            cond: { $eq: ['$$this.status', 'joined'] }
          }
        }
      },
      // 总流量数（KB转GB）
      totalTrafficGB: {
        $divide: [{ $ifNull: ['$accumulatedTraffic', 0] }, 1024 * 1024]
      },
      // 已使用流量数（字节转GB）
      usedTrafficGB: {
        $divide: [
          {
            $sum: {
              $map: {
                input: '$trafficBilling',
                as: 'billing',
                in: { $ifNull: ['$$billing.useNetworkTraffic', 0] }
              }
            }
          },
          1024 * 1024 * 1024
        ]
      }
    }
  }
]
```

### 3. 数据格式化

```typescript
private formatAppTeamResponse(team: any): AppTeamResponseDto {
  return {
    // ... 现有字段
    
    // 使用聚合查询计算的实际成员数
    memberCount: team.actualMemberCount !== undefined 
      ? team.actualMemberCount 
      : (team.memberCount || 0),
      
    // 流量统计，保留两位小数
    totalTraffic: Math.round((team.totalTrafficGB || 0) * 100) / 100,
    usedTraffic: Math.round((team.usedTrafficGB || 0) * 100) / 100,
    
    // ... 其他字段
  }
}
```

## 性能优化

### 1. 聚合查询优化
- **单次查询**: 使用聚合管道在一次数据库操作中获取所有统计信息
- **避免N+1问题**: 通过 `$lookup` 操作批量关联相关数据
- **字段投影**: 使用 `$project` 移除不需要的临时字段

### 2. 索引建议
```javascript
// 成员集合索引
db.memberentities.createIndex({ teamId: 1, status: 1 })

// 流量计费集合索引
db.trafficbillingentities.createIndex({ teamId: 1 })

// 团队集合索引
db.teamentities.createIndex({ sourceAppId: 1, source: 1, isDeleted: 1 })
```

### 3. 数据缓存策略
- 考虑对团队统计信息进行短期缓存（5-10分钟）
- 使用 Redis 缓存热点团队的统计数据
- 在团队成员或流量发生变化时清除相关缓存

## 数据隔离保证

### 1. 应用级隔离
- 通过 `sourceAppId` 确保只查询指定应用的团队
- 成员统计只包含该应用下的用户

### 2. 权限控制
- 使用 `@AdminAndOpenPlatformAccess()` 装饰器
- 开放平台用户只能查看自己应用的团队统计

### 3. 数据完整性
- 处理空值和缺失数据的情况
- 提供默认值确保响应数据的一致性

## 响应数据示例

```json
{
  "totalSize": 10,
  "page": 1,
  "size": 10,
  "totalPage": 1,
  "data": [
    {
      "id": "507f1f77bcf86cd799439013",
      "name": "测试团队",
      "code": "ABC123",
      "logo": "https://example.com/logo.jpg",
      "memberCount": 8,
      "totalTraffic": 150.75,
      "usedTraffic": 45.32,
      "isVip": true,
      "expiredAt": *************,
      "accountCountLimit": 100,
      "accountCount": 50,
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

## 监控和调试

### 1. 性能监控
- 监控聚合查询的执行时间
- 跟踪大团队数据查询的性能表现
- 设置查询超时告警

### 2. 数据准确性验证
- 定期验证统计数据的准确性
- 对比聚合结果与直接查询结果
- 监控数据异常情况

### 3. 错误处理
- 聚合查询失败时的降级策略
- 部分统计数据缺失时的处理方案
- 详细的错误日志记录

## 后续优化建议

1. **实时统计**: 考虑使用 MongoDB Change Streams 实现实时统计更新
2. **分页优化**: 对于大量团队的情况，考虑游标分页
3. **统计缓存**: 实现智能缓存策略，减少重复计算
4. **数据预聚合**: 考虑定期预计算团队统计数据
5. **API版本控制**: 为统计功能提供版本控制支持
