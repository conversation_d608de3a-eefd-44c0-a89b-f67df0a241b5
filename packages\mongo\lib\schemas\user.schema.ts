import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class UserEntity {
  @Prop({
    type: String,
    required: false,
    index: true,
    minlength: [11, '手机号长度不能小于11'],
    maxlength: [11, '手机号长度不能大于11']
  })
  phone?: string

  @Prop({
    type: String,
    required: false,
    index: true,
    minlength: [8, '账号长度不能小于8个字符'],
    maxlength: [20, '账号长度不能大于20个字符']
  })
  account?: string

  @Prop({
    type: String,
    required: false,
    maxlength: 64
  })
  avatar?: string

  @Prop({
    type: String,
    index: true,
    unique: false,
    required: [true, '昵称不能为空'],
    maxlength: [64, '昵称长度不能大于64']
  })
  nickName: string

  /**
   * 用户最后一次登录使用的团队ID
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  latestTeamId?: Types.ObjectId

  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  customerId?: Types.ObjectId

  /**
   * 渠道码
   */
  @Prop({
    type: String,
    required: false
  })
  channelCode?: string

  @Prop({
    type: String,
    required: false
  })
  registrationSource?: string

  @Prop({
    type: String,
    required: false
  })
  password?: string

  @Prop({
    type: String,
    required: false
  })
  salt?: string

  /**
   * 用户来源：gateway原生用户 或 开放平台应用创建的用户
   */
  @Prop({
    type: String,
    enum: ['gateway', 'open_platform_app'],
    required: false,
    default: 'gateway'
  })
  source?: 'gateway' | 'open_platform_app'

  /**
   * 创建用户的应用appId（仅当source为open_platform_app时有值）
   */
  @Prop({
    type: String,
    required: false,
    index: true
  })
  sourceAppId?: string

  /**
   * 关联的开放平台用户ID（冗余存储，便于查询）
   */
  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  openPlatformUserId?: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  openUniqueId?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const UserSchema: ModelDefinition = {
  name: UserEntity.name,
  schema: SchemaFactory.createForClass(UserEntity)
}

export const UserMongoose = MongooseModule.forFeature([UserSchema])
