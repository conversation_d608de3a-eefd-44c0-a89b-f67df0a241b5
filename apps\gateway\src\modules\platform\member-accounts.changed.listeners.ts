import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { EventNames, MemberAccountsChangedEvent } from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountEntity } from '@yxr/mongo'

@Injectable()
export class MemberAccountsChangedListener {
  logger = new Logger('MemberAccountsChangedListener')

  constructor(@InjectModel(PlatformAccountEntity.name) private platformAccountModel: Model<PlatformAccountEntity>) {
  }

  @OnEvent(EventNames.MemberAccountsChangedEvent, { async: true })
  async handleMemberAccountsChangedEvent(payload: MemberAccountsChangedEvent) {

    // 更新用户添加运营账号的运营人, 这些账号的运营人清单中添加用户
    if (payload.addAccountIds && payload.addAccountIds.length > 0) {
      const result = await this.platformAccountModel.updateMany(
        { teamId: new Types.ObjectId(payload.teamId), _id: { $in: payload.addAccountIds.map(x => (new Types.ObjectId(x))) } },
        { $addToSet: { members: payload.userId } }
      )

      this.logger.debug(`批量添加账号的运营人, teamId: ${payload.teamId} addAccountIds: ${payload.addAccountIds}, userId: ${payload.userId}, 更新记录数: ${result.modifiedCount}`)
    }

    // 更新用户移除运营账号的运营人, 这些账号的运营人清单中移除用户
    if(payload.delAccountIds && payload.delAccountIds.length > 0) {
      const result = await this.platformAccountModel.updateMany(
        { teamId: new Types.ObjectId(payload.teamId), _id: { $in: payload.delAccountIds.map(x => (new Types.ObjectId(x))) } },
        { $pull: { members: payload.userId } }
      )

      this.logger.debug(`批量删除账号的运营人, teamId: ${payload.teamId} delAccountIds: ${payload.delAccountIds}, userId: ${payload.userId}, 更新记录数: ${result.modifiedCount}`)
    }
  }
}
