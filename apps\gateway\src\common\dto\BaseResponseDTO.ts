import { ApiConflictResponse, ApiResponseProperty } from '@nestjs/swagger'

export class BaseResponseDTO {
  @ApiResponseProperty()
  statusCode: number
}

export class BaseBadRequestResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 400
  })
  statusCode: number

  @ApiResponseProperty({
    type: String,
    example: '操作失败'
  })
  message: string
}

export class BaseUnauthorizedResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 401
  })
  statusCode: number

  @ApiResponseProperty({
    type: String,
    example: '参数错误'
  })
  message: string
}

export class BaseForbiddenResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 403
  })
  statusCode: number

  @ApiResponseProperty({
    type: String,
    example: '该账号已被禁用'
  })
  message: string
}

export class BaseNotFoundResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 404
  })
  statusCode: number

  @ApiResponseProperty({
    type: String,
    example: 'XXX 未找到'
  })
  message: string
}

export class BaseConflictResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 409
  })
  statusCode: number

  @ApiResponseProperty({
    type: String,
    example: 'XXX 已失效'
  })
  message: string

}
