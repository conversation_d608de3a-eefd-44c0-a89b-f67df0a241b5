import { Controller, Get } from '@nestjs/common'
import { KuaidailiService } from './kuaidaili.service'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'

@Controller('daili')
@ApiTags('代理管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class KuaidailiController {
  constructor(private readonly kuaidailiService: KuaidailiService) {}

  @Get('areas')
  @ApiOperation({ summary: '获取地区列表树' })
  @ApiOkResponse({ type: BaseResponseDTO })
  getAreas() {
    return this.kuaidailiService.getAreas()
  }
}
