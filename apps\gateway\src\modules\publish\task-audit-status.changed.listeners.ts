import { Inject, Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  CacheKeyService,
  EventNames,
  PlatformNameEnum,
  PublishChannel,
  StageStatus,
  TaskAuditStatusEvent,
  TaskSetStatusEnum,
  TaskStages
} from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { ContentEntity, PlatformAccountEntity, TaskEntity, TaskSetEntity } from '@yxr/mongo'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'

@Injectable()
export class TaskAuditStatusChangedListener {
  logger = new Logger('TaskAuditStatusChangedListener')

  constructor(
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>
  ) {}

  @OnEvent(EventNames.TaskAuditStatusChangedEvent, { async: true })
  async handleTaskAuditStatusChangedEvent(payload: TaskAuditStatusEvent) {
    const taskSet = await this.taskSetModel.findOne({
      teamId: new Types.ObjectId(payload.teamId),
      taskIdentityId: payload.taskIdentityId
    })
    if (!taskSet) {
      return
    }

    const tasks = await this.taskModel
      .find({
        taskSetId: payload.taskIdentityId,
        teamId: new Types.ObjectId(payload.teamId)
      })
      .select('stages stageStatus platformAccountId')

    let successCount = 0
    let errorsCount = 0
    const totalCount = tasks.length
    let allFinish = true

    for (const task of tasks) {
      if (task.stageStatus === StageStatus.Fail) {
        errorsCount++
        continue // 跳过后续判断
      }

      // 检查成功条件
      if (
        ((task.stages !== TaskStages.Push && task.stages !== TaskStages.Upload) ||
          (task.stages === TaskStages.Push && task.stageStatus === StageStatus.Success)) &&
        task.stages !== null
      ) {
        successCount++
      } else {
        allFinish = false
      }
    }

    taskSet.taskSetStatus = TaskSetStatusEnum.Publishing
    if (successCount === totalCount) {
      taskSet.taskSetStatus = TaskSetStatusEnum.AllSuccess
    } else if (successCount > 0 && successCount < totalCount) {
      taskSet.taskSetStatus = TaskSetStatusEnum.PartialSuccess
    } else if (errorsCount === totalCount) {
      taskSet.taskSetStatus = TaskSetStatusEnum.AllFailed
    }

    await taskSet.save()

    if (
      allFinish &&
      (taskSet.publishChannel == PublishChannel.cloud || taskSet.isAppContent === true)
    ) {
      //云发布判断所有子任务都执行完成就释放微信锁
      const platformAccountIds = tasks.map((item) => new Types.ObjectId(item.platformAccountId))
      const platformAccounts = await this.platformAccountModel
        .find({
          _id: { $in: platformAccountIds },
          platformName: PlatformNameEnum.视频号,
          parentId: { $ne: null },
          teamId: new Types.ObjectId(taskSet.teamId)
        })
        .select('parentId')

      if (platformAccounts?.length > 0) {
        for (const account of platformAccounts) {
          await this.cacheManager.del(
            CacheKeyService.getWeiXinAccountLockKey(
              taskSet.teamId?.toString(),
              account.parentId?.toString()
            )
          )
        }
      }
    }
  }
}
