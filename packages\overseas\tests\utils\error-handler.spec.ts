/**
 * 海外平台连接器异常处理测试
 */

import { AxiosError, AxiosResponse } from 'axios'
import { 
  RemoteApiError, 
  RemoteApiErrorCodes, 
  ErrorCategory, 
  ErrorSeverity,
  DefaultOverseasApiErrorHandler,
  extractRequestInfo,
  extractResponseInfo
} from '../../src/utils/error-handler'

describe('海外平台连接器异常处理', () => {
  let errorHandler: DefaultOverseasApiErrorHandler
  
  const mockContext = {
    platform: 'tiktok',
    accountOpenId: 'test-account',
    teamId: 'test-team',
    userId: 'test-user'
  }

  beforeEach(() => {
    errorHandler = new DefaultOverseasApiErrorHandler()
    // Mock console methods
    jest.spyOn(console, 'error').mockImplementation()
    jest.spyOn(console, 'warn').mockImplementation()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('RemoteApiError', () => {
    it('应该正确创建RemoteApiError实例', () => {
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.AccessTokenInvalid,
        { message: 'Token invalid' },
        { url: '/test', method: 'GET' },
        { status: 401, statusText: 'Unauthorized' },
        mockContext
      )

      expect(error.platform).toBe('tiktok')
      expect(error.errorCode).toBe(RemoteApiErrorCodes.AccessTokenInvalid)
      expect(error.category).toBe(ErrorCategory.Authentication)
      expect(error.severity).toBe(ErrorSeverity.High)
      expect(error.isRetryable).toBe(false)
      expect(error.localizedMessage).toBe('访问令牌无效，请重新授权')
    })

    it('应该正确处理自定义错误消息', () => {
      const customMessage = '自定义错误消息'
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.UnknownRemoteApiError,
        { message: 'Original message' },
        { url: '/test', method: 'GET' },
        { status: 500, statusText: 'Internal Server Error' },
        mockContext,
        customMessage
      )

      expect(error.localizedMessage).toBe(customMessage)
      expect(error.message).toBe(customMessage)
    })

    it('应该提供完整的错误信息', () => {
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.RateLimitExceeded,
        { message: 'Rate limit exceeded', retryAfter: 60 },
        { url: '/test', method: 'POST', headers: { 'Content-Type': 'application/json' } },
        { status: 429, statusText: 'Too Many Requests', headers: { 'X-RateLimit-Remaining': '0' } },
        mockContext
      )

      const fullInfo = error.getFullErrorInfo()
      
      expect(fullInfo.platform).toBe('tiktok')
      expect(fullInfo.errorCode).toBe(RemoteApiErrorCodes.RateLimitExceeded)
      expect(fullInfo.category).toBe(ErrorCategory.RateLimit)
      expect(fullInfo.severity).toBe(ErrorSeverity.Medium)
      expect(fullInfo.isRetryable).toBe(true)
      expect(fullInfo.requestInfo.url).toBe('/test')
      expect(fullInfo.requestInfo.method).toBe('POST')
      expect(fullInfo.responseInfo.status).toBe(429)
      expect(fullInfo.details.retryAfter).toBe(60)
      expect(fullInfo.timestamp).toBeInstanceOf(Date)
    })
  })

  describe('DefaultOverseasApiErrorHandler', () => {
    it('应该正确处理网络错误', async () => {
      const networkError = new AxiosError('Network Error')
      networkError.code = 'ECONNREFUSED'

      await expect(
        errorHandler.handleNetworkError(networkError, mockContext)
      ).rejects.toThrow(RemoteApiError)

      try {
        await errorHandler.handleNetworkError(networkError, mockContext)
      } catch (error) {
        expect(error).toBeInstanceOf(RemoteApiError)
        expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.ConnectionRefused)
        expect((error as RemoteApiError).platform).toBe('tiktok')
        expect((error as RemoteApiError).category).toBe(ErrorCategory.Network)
        expect((error as RemoteApiError).isRetryable).toBe(true)
      }
    })

    it('应该正确处理HTTP错误', async () => {
      const httpError = new AxiosError('Request failed with status code 401')
      httpError.response = {
        status: 401,
        statusText: 'Unauthorized',
        data: { error: 'Invalid token' },
        headers: {},
        config: { url: '/test', method: 'GET' }
      } as AxiosResponse

      await expect(
        errorHandler.handleHttpError(httpError, mockContext)
      ).rejects.toThrow(RemoteApiError)

      try {
        await errorHandler.handleHttpError(httpError, mockContext)
      } catch (error) {
        expect(error).toBeInstanceOf(RemoteApiError)
        expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.Unauthorized)
        expect((error as RemoteApiError).category).toBe(ErrorCategory.Authentication)
        expect((error as RemoteApiError).severity).toBe(ErrorSeverity.High)
      }
    })

    it('应该正确处理不同的HTTP状态码', async () => {
      const testCases = [
        { status: 400, expectedError: RemoteApiErrorCodes.RequestParametersIncorrect },
        { status: 403, expectedError: RemoteApiErrorCodes.Forbidden },
        { status: 404, expectedError: RemoteApiErrorCodes.NotFound },
        { status: 429, expectedError: RemoteApiErrorCodes.RateLimitExceeded },
        { status: 500, expectedError: RemoteApiErrorCodes.ServerError },
        { status: 502, expectedError: RemoteApiErrorCodes.ServerError },
        { status: 503, expectedError: RemoteApiErrorCodes.ServerError }
      ]

      for (const testCase of testCases) {
        const httpError = new AxiosError(`Request failed with status code ${testCase.status}`)
        httpError.response = {
          status: testCase.status,
          statusText: 'Error',
          data: {},
          headers: {},
          config: { url: '/test', method: 'GET' }
        } as AxiosResponse

        try {
          await errorHandler.handleHttpError(httpError, mockContext)
          fail(`Expected error for status ${testCase.status}`)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(testCase.expectedError)
        }
      }
    })

    it('应该正确处理超时错误', async () => {
      const timeoutError = new AxiosError('timeout of 5000ms exceeded')
      timeoutError.code = 'ECONNABORTED'

      try {
        await errorHandler.handleNetworkError(timeoutError, mockContext)
      } catch (error) {
        expect(error).toBeInstanceOf(RemoteApiError)
        expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.Timeout)
        expect((error as RemoteApiError).isRetryable).toBe(true)
      }
    })
  })

  describe('错误分类和严重程度', () => {
    it('应该正确分类网络错误', () => {
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.NetworkError,
        {},
        { url: '/test', method: 'GET' },
        { status: 0, statusText: '' },
        mockContext
      )

      expect(error.category).toBe(ErrorCategory.Network)
      expect(error.severity).toBe(ErrorSeverity.Medium)
      expect(error.isRetryable).toBe(true)
    })

    it('应该正确分类认证错误', () => {
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.AccessTokenExpired,
        {},
        { url: '/test', method: 'GET' },
        { status: 401, statusText: 'Unauthorized' },
        mockContext
      )

      expect(error.category).toBe(ErrorCategory.Authentication)
      expect(error.severity).toBe(ErrorSeverity.High)
      expect(error.isRetryable).toBe(false)
    })

    it('应该正确分类限流错误', () => {
      const error = new RemoteApiError(
        'tiktok',
        RemoteApiErrorCodes.RateLimitExceeded,
        {},
        { url: '/test', method: 'GET' },
        { status: 429, statusText: 'Too Many Requests' },
        mockContext
      )

      expect(error.category).toBe(ErrorCategory.RateLimit)
      expect(error.severity).toBe(ErrorSeverity.Medium)
      expect(error.isRetryable).toBe(true)
    })
  })

  describe('错误信息本地化', () => {
    it('应该提供中文错误信息', () => {
      const testCases = [
        { code: RemoteApiErrorCodes.AccessTokenInvalid, expected: '访问令牌无效，请重新授权' },
        { code: RemoteApiErrorCodes.AccessTokenExpired, expected: '访问令牌已过期，请刷新令牌' },
        { code: RemoteApiErrorCodes.RateLimitExceeded, expected: '请求频率过高，请稍后重试' },
        { code: RemoteApiErrorCodes.ContentViolation, expected: '内容违反平台规定，请检查内容' },
        { code: RemoteApiErrorCodes.NetworkError, expected: '网络连接异常，请检查网络设置' }
      ]

      testCases.forEach(testCase => {
        const error = new RemoteApiError(
          'tiktok',
          testCase.code,
          {},
          { url: '/test', method: 'GET' },
          { status: 400, statusText: 'Bad Request' },
          mockContext
        )

        expect(error.localizedMessage).toBe(testCase.expected)
      })
    })
  })

  describe('工具函数', () => {
    it('应该正确提取请求信息', () => {
      const mockResponse = {
        config: {
          url: '/test/endpoint',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer token'
          },
          data: { key: 'value' }
        }
      } as AxiosResponse

      const requestInfo = extractRequestInfo(mockResponse)

      expect(requestInfo.url).toBe('/test/endpoint')
      expect(requestInfo.method).toBe('POST')
      expect(requestInfo.headers).toEqual({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer token'
      })
    })

    it('应该正确提取响应信息', () => {
      const mockResponse = {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Remaining': '100'
        },
        data: { result: 'success' }
      } as AxiosResponse

      const responseInfo = extractResponseInfo(mockResponse)

      expect(responseInfo.status).toBe(200)
      expect(responseInfo.statusText).toBe('OK')
      expect(responseInfo.headers).toEqual({
        'Content-Type': 'application/json',
        'X-RateLimit-Remaining': '100'
      })
    })

    it('应该处理缺失的请求/响应信息', () => {
      const emptyResponse = {} as AxiosResponse

      const requestInfo = extractRequestInfo(emptyResponse)
      const responseInfo = extractResponseInfo(emptyResponse)

      expect(requestInfo.url).toBe('')
      expect(requestInfo.method).toBe('')
      expect(requestInfo.headers).toEqual({})

      expect(responseInfo.status).toBe(0)
      expect(responseInfo.statusText).toBe('')
      expect(responseInfo.headers).toEqual({})
    })
  })
})
