# 开放平台应用类型分类功能实现

## 概述

在开放平台应用系统中添加了应用类型分类功能，支持技术服务商和渠道商两种应用类型，并实现了基于应用类型的敏感信息保护机制。

## 功能特性

### 1. 应用类型枚举
- **技术服务商** (`TECHNICAL_SERVICE`): 具有完整的应用信息访问权限
- **渠道商** (`CHANNEL_PARTNER`): 敏感信息受保护，appId和secretKey被隐藏

### 2. 安全保护机制
- 渠道商类型应用的敏感信息自动隐藏
- 确保数据安全和权限隔离
- 保持向后兼容性

## 技术实现

### 1. 数据模型增强

**文件**: `packages/mongo/lib/schemas/open_platform_application.schema.ts`

#### 1.1 枚举定义
```typescript
// packages/common/open-platform.constants.ts
export enum ApplicationType {
  TECHNICAL_SERVICE = 'TECHNICAL_SERVICE',  // 技术服务商
  CHANNEL_PARTNER = 'CHANNEL_PARTNER'       // 渠道商
}
```

#### 1.2 Schema字段
```typescript
@Prop({
  type: String,
  enum: ApplicationType,
  required: true,
  index: true,
  default: ApplicationType.TECHNICAL_SERVICE
})
applicationType: ApplicationType
```

**字段特性**:
- ✅ **必填字段**: `required: true`
- ✅ **索引优化**: `index: true` 
- ✅ **枚举验证**: 限制为有效的应用类型值
- ✅ **默认值**: 技术服务商类型

### 2. DTO增强

**文件**: `apps/admin/src/modules/open-platform/dto/application.dto.ts`

#### 2.1 创建应用请求DTO
```typescript
export class CreateApplicationRequestDto {
  @ApiProperty({
    description: '应用类型',
    enum: ApplicationType,
    example: ApplicationType.TECHNICAL_SERVICE
  })
  @IsNotEmpty()
  @IsEnum(ApplicationType, { message: '应用类型必须是有效的枚举值' })
  applicationType: ApplicationType
  
  // ... 其他字段
}
```

#### 2.2 应用响应DTO
```typescript
export class ApplicationDto {
  @ApiResponseProperty({
    enum: ApplicationType,
    example: ApplicationType.TECHNICAL_SERVICE
  })
  applicationType: ApplicationType
  
  // ... 其他字段
}
```

#### 2.3 应用列表查询DTO
```typescript
export class ApplicationListRequestDto {
  @ApiProperty({
    enum: ApplicationType,
    description: '应用类型筛选',
    example: ApplicationType.TECHNICAL_SERVICE,
    required: false
  })
  @IsEnum(ApplicationType)
  @IsOptional()
  applicationType?: ApplicationType
  
  // ... 其他字段
}
```

### 3. Service层实现

**文件**: `apps/admin/src/modules/open-platform/services/application.service.ts`

#### 3.1 创建应用功能
```typescript
async createApplication(createDto: CreateApplicationRequestDto): Promise<ApplicationDto> {
  // 验证用户权限
  if (session?.userType !== UserType.OPEN_PLATFORM) {
    throw new ForbiddenException('只有开放平台用户可以创建应用')
  }

  const application = await this.applicationModel.create({
    userId: new Types.ObjectId(session.userId),
    name: createDto.name,
    appId,
    secretKey,
    description: createDto.description || '',
    status: OpenPlatformStatus.ACTIVE,
    applicationType: createDto.applicationType  // 新增字段
  })
  
  // ... 其他逻辑
}
```

#### 3.2 应用列表查询功能
```typescript
async getApplicationList(queryDto: ApplicationListRequestDto): Promise<ApplicationListResponseDto> {
  const { page = 1, size = 10, keyword, status, applicationType } = queryDto
  
  // 构建查询条件
  const query: any = {
    _id: { $in: applicationIds }
  }

  if (applicationType !== undefined) {
    query.applicationType = applicationType  // 新增筛选条件
  }
  
  // ... 其他逻辑
}
```

#### 3.3 敏感信息保护机制
```typescript
private formatApplicationDto(
  application: OpenPlatformApplicationEntity,
  userRole: string,
  hideSecret: boolean = false,
  isOwner: boolean = false
): ApplicationDto {
  // 安全要求：当应用类型为渠道商时，隐藏敏感信息
  const shouldHideSensitiveInfo = application.applicationType === ApplicationType.CHANNEL_PARTNER
  
  return {
    id: (application as any)._id.toString(),
    name: application.name,
    appId: shouldHideSensitiveInfo ? '' : application.appId,           // 条件性隐藏
    secretKey: (hideSecret || shouldHideSensitiveInfo) ? undefined : application.secretKey,  // 条件性隐藏
    description: application.description || '',
    status: application.status,
    applicationType: application.applicationType,  // 新增字段
    // ... 其他字段
  }
}
```

### 4. 数据迁移

**文件**: `scripts/migrations/add-application-type-to-existing-applications.js`

#### 4.1 迁移功能
- ✅ 为现有应用添加 `applicationType` 字段
- ✅ 默认设置为 `TECHNICAL_SERVICE`
- ✅ 创建性能优化索引
- ✅ 完整的迁移验证

#### 4.2 索引创建
```javascript
// 应用类型索引
await collection.createIndex(
  { applicationType: 1 },
  { name: 'idx_application_type', background: true }
)

// 用户应用类型复合索引
await collection.createIndex(
  { userId: 1, applicationType: 1 },
  { name: 'idx_user_application_type', background: true }
)

// 状态应用类型复合索引
await collection.createIndex(
  { status: 1, applicationType: 1 },
  { name: 'idx_status_application_type', background: true }
)
```

## 安全特性

### 1. 敏感信息保护
- **渠道商应用**: `appId` 和 `secretKey` 字段返回空字符串或undefined
- **技术服务商应用**: 正常返回所有信息
- **动态判断**: 基于应用类型自动应用保护规则

### 2. 权限控制
- **创建权限**: 只有开放平台用户可以创建应用
- **访问权限**: 基于用户角色和授权关系控制访问
- **数据隔离**: 确保用户只能访问有权限的应用

### 3. 数据验证
- **枚举验证**: 应用类型必须是有效的枚举值
- **必填验证**: 创建应用时必须指定应用类型
- **类型安全**: TypeScript类型检查确保类型安全

## API接口变更

### 1. 创建应用接口
**请求示例**:
```json
POST /open-platform/applications
{
  "name": "我的应用",
  "applicationType": "TECHNICAL_SERVICE",
  "description": "这是一个技术服务商应用"
}
```

**响应示例**:
```json
{
  "id": "507f1f77bcf86cd799439011",
  "name": "我的应用",
  "appId": "app_1234567890abcdef",
  "secretKey": "sk_1234567890abcdef1234567890abcdef",
  "applicationType": "TECHNICAL_SERVICE",
  "status": 0,
  "description": "这是一个技术服务商应用",
  // ... 其他字段
}
```

### 2. 应用列表接口
**请求示例**:
```json
GET /open-platform/applications?applicationType=CHANNEL_PARTNER&page=1&size=10
```

**渠道商应用响应示例**:
```json
{
  "data": [
    {
      "id": "507f1f77bcf86cd799439012",
      "name": "渠道商应用",
      "appId": "",                    // 敏感信息被隐藏
      "secretKey": undefined,         // 敏感信息被隐藏
      "applicationType": "CHANNEL_PARTNER",
      "status": 0,
      // ... 其他字段
    }
  ],
  "totalSize": 1,
  "page": 1,
  "size": 10
}
```

## 性能优化

### 1. 数据库索引
- **单字段索引**: `applicationType`
- **复合索引**: `userId + applicationType`
- **复合索引**: `status + applicationType`

### 2. 查询优化
- 利用索引加速应用类型筛选
- 减少全表扫描
- 提升大数据量下的查询性能

## 兼容性保证

### 1. 向后兼容
- ✅ 现有API接口保持不变
- ✅ 新增字段不影响现有功能
- ✅ 数据迁移确保现有数据完整性

### 2. 渐进式部署
- ✅ 支持逐步部署，不影响现有服务
- ✅ 迁移脚本可重复执行
- ✅ 完整的回滚机制

## 部署指南

### 1. 数据迁移
```bash
# 执行迁移脚本
node scripts/migrations/add-application-type-to-existing-applications.js
```

### 2. 验证步骤
1. 检查所有应用记录都包含 `applicationType` 字段
2. 验证索引创建成功
3. 测试应用类型筛选功能
4. 验证敏感信息保护机制

### 3. 监控要点
- 应用创建成功率
- 应用列表查询性能
- 敏感信息保护效果
- 数据库索引使用情况

## 后续优化建议

1. **权限细化**: 基于应用类型实现更细粒度的权限控制
2. **审计日志**: 记录应用类型变更的操作日志
3. **批量操作**: 支持批量修改应用类型
4. **统计分析**: 提供应用类型分布统计
5. **配置管理**: 支持动态配置敏感信息保护规则
