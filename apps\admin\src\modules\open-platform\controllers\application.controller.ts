import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { ApplicationService } from '../services/application.service'
import {
  CreateApplicationRequestDto,
  UpdateApplicationRequestDto,
  ApplicationListRequestDto,
  CreateApplicationResponseDto,
  GetApplicationResponseDto,
  GetApplicationListResponseDto,
  RegenerateSecretResponseDto,
  ApplicationOverviewOkResponseDto,
  SetWebhookRequestDto,
  SetWebhookResponseDto,
  GetWebhookStatusResponseDto
} from '../dto/application.dto'
import {
  BaseBadRequestDTO,
  BaseNotFoundResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformAccess } from '../../../common/decorators/access-control.decorator'

@Controller('open-platform/applications')
@OpenPlatformAccess()
@ApiTags('开放平台应用管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class ApplicationController {
  constructor(private readonly applicationService: ApplicationService) {}

  @Post()
  @ApiOperation({ summary: '创建应用' })
  @ApiOkResponse({
    description: '创建成功',
    type: CreateApplicationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async createApplication(@Body() createDto: CreateApplicationRequestDto) {
    const result = await this.applicationService.createApplication(createDto)
    return result
  }

  @Get()
  @ApiOperation({ summary: '获取应用列表' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetApplicationListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getApplicationList(@Query() queryDto: ApplicationListRequestDto) {
    const result = await this.applicationService.getApplicationList(queryDto)
    return result
  }

  @Get(':id')
  @ApiOperation({ summary: '获取应用详情' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetApplicationResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getApplicationById(@Param('id') id: string) {
    const result = await this.applicationService.getApplicationById(id)
    return result
  }

  @Put(':id')
  @ApiOperation({ summary: '更新应用' })
  @ApiOkResponse({
    description: '更新成功',
    type: GetApplicationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async updateApplication(@Param('id') id: string, @Body() updateDto: UpdateApplicationRequestDto) {
    const result = await this.applicationService.updateApplication(id, updateDto)
    return result
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除应用' })
  @ApiOkResponse({
    description: '删除成功'
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async deleteApplication(@Param('id') id: string) {
    return this.applicationService.deleteApplication(id)
  }

  @Post(':id/regenerate-secret')
  @ApiOperation({ summary: '重新生成应用密钥' })
  @ApiOkResponse({
    description: '重新生成成功',
    type: RegenerateSecretResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async regenerateSecret(@Param('id') id: string) {
    const newSecretKey = await this.applicationService.regenerateSecret(id)
    return newSecretKey
  }

  @Get(':id/overview')
  @ApiOperation({
    summary: '获取应用概览统计',
    description: '获取指定应用下的全面统计数据和近30天趋势分析，包括账号点数、流量、用户和团队统计'
  })
  @ApiOkResponse({
    description: '获取成功',
    type: ApplicationOverviewOkResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getApplicationOverview(@Param('id') id: string) {
    const overview = await this.applicationService.getApplicationOverview(id)
    return overview
  }

  @Put(':id/webhook')
  @OpenPlatformAccess()
  @ApiOperation({
    summary: '设置应用Webhook',
    description: '设置指定应用的Webhook URL，设置后会立即进行可访问性验证'
  })
  @ApiOkResponse({
    description: '设置成功',
    type: SetWebhookResponseDto
  })
  @ApiBadRequestResponse({
    description: 'Webhook验证失败或参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async setWebhook(@Param('id') id: string, @Body() setWebhookDto: SetWebhookRequestDto) {
    const webhookStatus = await this.applicationService.setWebhook(id, setWebhookDto)
    return webhookStatus
  }

  @Get(':id/webhook')
  @OpenPlatformAccess()
  @ApiOperation({
    summary: '获取应用Webhook状态',
    description: '获取指定应用的Webhook配置和验证状态'
  })
  @ApiOkResponse({
    description: '获取成功',
    type: GetWebhookStatusResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getWebhookStatus(@Param('id') id: string) {
    const webhookStatus = await this.applicationService.getWebhookStatus(id)
    return webhookStatus
  }

  @Delete(':id/webhook')
  @OpenPlatformAccess()
  @ApiOperation({
    summary: '删除应用Webhook',
    description: '删除指定应用的Webhook配置'
  })
  @ApiOkResponse({
    description: '删除成功',
    type: SetWebhookResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async removeWebhook(@Param('id') id: string) {
    const webhookStatus = await this.applicationService.removeWebhook(id)
    return webhookStatus
  }
}
