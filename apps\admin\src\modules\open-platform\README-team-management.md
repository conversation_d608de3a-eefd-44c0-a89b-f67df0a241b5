# 开放平台团队管理API

## 概述

开放平台团队管理API提供了两个核心功能，允许开放平台应用通过token认证来管理其关联的团队：

1. **修改团队过期时间** - 延长或调整团队的VIP服务过期时间
2. **设置团队资源配额** - 调整团队的账号数量限制和流量配额

## 认证方式

这些API使用开放平台应用token认证，需要：
- 使用`@ApplicationAccess()`装饰器进行权限控制
- 通过`UnifiedTokenGuard`进行身份验证
- 请求头中包含有效的Bearer token

## API接口

### 1. 修改团队过期时间

**接口地址：** `PUT /open-platform/team-management/expiration`

**功能描述：** 允许开放平台应用修改指定团队的VIP过期时间

**权限要求：**
- 需要开放平台应用token认证
- 只能操作属于该应用的团队

**请求参数：**
```json
{
  "teamId": "507f1f77bcf86cd799439011",
  "expiredAt": "2024-12-31T23:59:59.999Z"
}
```

**参数说明：**
- `teamId` (string): 团队ID
- `expiredAt` (string): 新的过期时间，ISO 8601格式，必须大于当前时间

**验证规则：**
- 新的过期时间必须大于当前日期
- 团队必须属于当前应用
- 团队必须存在

**响应示例：**
```json
{
  "code": 200,
  "message": "团队过期时间修改成功",
  "data": {
    "teamId": "507f1f77bcf86cd799439011",
    "teamName": "我的团队",
    "oldExpiredAt": *************,
    "newExpiredAt": 1672531199999,
    "updatedAt": *************
  }
}
```

**错误响应：**
- `400` - 参数错误或过期时间无效
- `403` - 无权限操作该团队
- `404` - 团队不存在

### 2. 设置团队资源配额

**接口地址：** `PUT /open-platform/team-management/quota`

**功能描述：** 允许开放平台应用设置指定团队的账号数量限制和流量配额

**权限要求：**
- 需要开放平台应用token认证
- 只能操作属于该应用的团队

**请求参数：**
```json
{
  "teamId": "507f1f77bcf86cd799439011",
  "accountCountLimit": 50,
  "networkTraffic": 1048576
}
```

**参数说明：**
- `teamId` (string): 团队ID
- `accountCountLimit` (number): 账号数量限制，最小值为0
- `networkTraffic` (number): 流量配额（KB），最小值为0

**账号冻结逻辑：**
- 如果新的账号限制小于当前在线账号数量，系统会自动冻结多余的账号
- 冻结策略：优先冻结最久未活动的账号（按`updatedAt`时间排序）
- 被冻结的账号状态会从`Online`变为`Frozen`

**响应示例：**
```json
{
  "code": 200,
  "message": "团队资源配额设置成功",
  "data": {
    "teamId": "507f1f77bcf86cd799439011",
    "teamName": "我的团队",
    "oldAccountCountLimit": 30,
    "newAccountCountLimit": 50,
    "oldNetworkTraffic": 524288,
    "newNetworkTraffic": 1048576,
    "currentOnlineAccounts": 25,
    "frozenAccounts": [
      {
        "accountId": "507f1f77bcf86cd799439012",
        "platformAccountName": "user123",
        "platformName": "WeChat",
        "lastLoginTime": *************
      }
    ],
    "updatedAt": *************
  }
}
```

**错误响应：**
- `400` - 参数错误
- `403` - 无权限操作该团队
- `404` - 团队不存在

## 使用示例

### 修改团队过期时间

```bash
curl -X PUT "https://api.example.com/open-platform/team-management/expiration" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_APP_TOKEN" \
  -d '{
    "teamId": "507f1f77bcf86cd799439011",
    "expiredAt": "2024-12-31T23:59:59.999Z"
  }'
```

### 设置团队资源配额

```bash
curl -X PUT "https://api.example.com/open-platform/team-management/quota" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_APP_TOKEN" \
  -d '{
    "teamId": "507f1f77bcf86cd799439011",
    "accountCountLimit": 50,
    "networkTraffic": 1048576
  }'
```

## 权限验证机制

### 应用权限验证
1. **Token验证**: 验证请求中的Bearer token是否有效
2. **应用存在性**: 验证token对应的应用是否存在且有效
3. **团队归属验证**: 验证目标团队是否属于当前应用（通过`team.latestTeamId`字段）

### 数据隔离
- 每个应用只能操作属于自己的团队
- 通过`sourceAppId`和`team.latestTeamId`进行权限控制
- 确保数据安全和隔离

## 业务逻辑

### 过期时间修改
1. 验证新的过期时间必须大于当前时间
2. 使用数据库事务确保数据一致性
3. 记录详细的操作日志
4. 返回修改前后的时间对比

### 资源配额设置
1. **账号限制处理**:
   - 查询当前在线账号数量
   - 如果新限制小于当前数量，执行账号冻结
   - 冻结策略：按最后活动时间排序，优先冻结最久未活动的账号

2. **流量配额设置**:
   - 直接更新团队的流量配额
   - 支持设置为0（无限制）

3. **事务安全**:
   - 所有操作在数据库事务中执行
   - 确保账号冻结和配额更新的原子性

## 错误处理

### 常见错误场景
1. **权限错误**: 应用尝试操作不属于自己的团队
2. **参数错误**: 过期时间设置为过去时间，或配额参数无效
3. **数据不存在**: 团队ID不存在或应用ID无效
4. **系统错误**: 数据库操作失败或网络异常

### 错误响应格式
```json
{
  "code": 400,
  "message": "新的过期时间必须大于当前时间",
  "timestamp": *************
}
```

## 日志记录

### 操作日志
- 记录所有团队管理操作的详细信息
- 包含操作前后的数据对比
- 记录操作的应用ID和团队ID
- 包含错误信息和堆栈跟踪

### 日志示例
```
[TeamManagementService] 团队过期时间修改成功: teamId=507f1f77bcf86cd799439011, 
原过期时间=2024-01-01T00:00:00.000Z, 新过期时间=2024-12-31T23:59:59.999Z

[TeamManagementService] 团队资源配额设置成功: teamId=507f1f77bcf86cd799439011, 
账号限制: 30 -> 50, 流量配额: 524288 -> 1048576, 冻结账号数量: 0
```

## 安全考虑

### 数据验证
- 严格的参数类型和范围验证
- 防止SQL注入和XSS攻击
- 输入数据清理和转换

### 权限控制
- 基于token的身份验证
- 应用级别的数据隔离
- 操作权限的细粒度控制

### 审计追踪
- 完整的操作日志记录
- 数据变更前后的对比
- 操作者身份的记录

## 性能优化

### 数据库优化
- 使用索引加速查询
- 批量操作减少数据库连接
- 事务范围最小化

### 缓存策略
- 团队信息的适当缓存
- 应用权限信息缓存
- 减少重复查询

## 监控和告警

### 关键指标
- API调用频率和成功率
- 账号冻结操作的频率
- 过期时间修改的频率
- 错误率和响应时间

### 告警规则
- 大量账号被冻结时的告警
- API错误率超过阈值的告警
- 异常的权限访问尝试告警

## 测试场景

### 1. 过期时间修改测试

**正常场景：**
```bash
# 延长团队过期时间到明年
curl -X PUT "/open-platform/team-management/expiration" \
  -H "Authorization: Bearer valid_app_token" \
  -d '{"teamId": "valid_team_id", "expiredAt": "2025-12-31T23:59:59.999Z"}'
# 期望: 200 成功响应
```

**异常场景：**
```bash
# 设置过期时间为过去时间
curl -X PUT "/open-platform/team-management/expiration" \
  -H "Authorization: Bearer valid_app_token" \
  -d '{"teamId": "valid_team_id", "expiredAt": "2023-01-01T00:00:00.000Z"}'
# 期望: 400 错误响应

# 操作不属于自己的团队
curl -X PUT "/open-platform/team-management/expiration" \
  -H "Authorization: Bearer app_token_a" \
  -d '{"teamId": "team_belongs_to_app_b", "expiredAt": "2025-12-31T23:59:59.999Z"}'
# 期望: 403 权限错误
```

### 2. 资源配额设置测试

**正常场景：**
```bash
# 增加账号限制和流量配额
curl -X PUT "/open-platform/team-management/quota" \
  -H "Authorization: Bearer valid_app_token" \
  -d '{"teamId": "valid_team_id", "accountCountLimit": 100, "networkTraffic": 2097152}'
# 期望: 200 成功响应
```

**账号冻结场景：**
```bash
# 将账号限制从50减少到20（假设当前有30个在线账号）
curl -X PUT "/open-platform/team-management/quota" \
  -H "Authorization: Bearer valid_app_token" \
  -d '{"teamId": "team_with_30_accounts", "accountCountLimit": 20, "networkTraffic": 1048576}'
# 期望: 200 成功响应，frozenAccounts数组包含10个被冻结的账号
```

**异常场景：**
```bash
# 设置负数配额
curl -X PUT "/open-platform/team-management/quota" \
  -H "Authorization: Bearer valid_app_token" \
  -d '{"teamId": "valid_team_id", "accountCountLimit": -1, "networkTraffic": -1000}'
# 期望: 400 参数验证错误
```

## 集成指南

### 1. 获取应用Token
```javascript
// 首先通过应用认证获取token
const authResponse = await fetch('/open-platform/application-token/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    appId: 'your_app_id',
    secretKey: 'your_secret_key'
  })
})
const { token } = await authResponse.json()
```

### 2. 调用团队管理API
```javascript
// 修改团队过期时间
const updateExpiration = async (teamId, expiredAt) => {
  const response = await fetch('/open-platform/team-management/expiration', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ teamId, expiredAt })
  })
  return await response.json()
}

// 设置团队配额
const setQuota = async (teamId, accountCountLimit, networkTraffic) => {
  const response = await fetch('/open-platform/team-management/quota', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ teamId, accountCountLimit, networkTraffic })
  })
  return await response.json()
}
```

### 3. 错误处理
```javascript
const handleApiCall = async (apiCall) => {
  try {
    const result = await apiCall()
    if (result.code === 200) {
      console.log('操作成功:', result.data)
      return result.data
    } else {
      console.error('API错误:', result.message)
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('请求失败:', error.message)
    throw error
  }
}
```
