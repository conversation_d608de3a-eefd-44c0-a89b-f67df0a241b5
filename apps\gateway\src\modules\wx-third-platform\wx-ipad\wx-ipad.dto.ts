import { ApiProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from 'apps/gateway/src/common/dto/BaseResponseDTO'
import { IsOptional, IsString } from 'class-validator'

export class WxAssistantCreateRequest {
  @ApiProperty({
    type: String,
    description: '视频号唯一ID',
    required: true
  })
  @IsString()
  uniqId: string

  @ApiProperty({
    type: String,
    description: '视频号获取凭证',
    required: true
  })
  @IsString()
  finderUsername: string

  @ApiProperty({
    type: String,
    description: '视频号昵称',
    required: true
  })
  @IsString()
  nickname: string

  @ApiProperty({
    type: String,
    description: '视频号头像',
    required: true
  })
  @IsString()
  headImgUrl: string
}

export class postIpadQrcodeRequest {
  @ApiProperty({
    description: '媒体账号ID,账号重新授权时使用',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '账号格式不匹配' })
  platformAccountId: string

  /**
   * 代理地区
   */
  @ApiProperty({
    type: String,
    description: '新授权时需要传入',
    required: false,
    default: false
  })
  @IsOptional()
  kuaidailiAreaCode?: string
}

export class WxAssistantResponse {
  @ApiProperty({
    type: String,
    description: '微信号登录凭证',
    required: true
  })
  finderUsername: string

  @ApiProperty({
    type: String,
    description: '微信号昵称',
    required: true
  })
  nickname: string

  @ApiProperty({
    type: String,
    description: '微信号头像',
    required: true
  })
  headImgUrl: string

  @ApiProperty({
    type: String,
    description: '微信号ID',
    required: true
  })
  uniqId: string
}

export class WxAssistantResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: [WxAssistantResponse]
  })
  data: WxAssistantResponse[]
}
export class WxQrCodeResponse {
  @ApiProperty({
    type: String,
    description: 'base64编码的二维码图片'
  })
  base64: string

  @ApiProperty({
    type: String,
    description: '登陆状态结果查询ID'
  })
  uuid: string
}

export class WxQrCodeResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: WxQrCodeResponse
  })
  data: WxQrCodeResponse
}

export class UpdateVideoAccountResponse {
  @ApiProperty({
    type: String,
    description: '账号的cookie凭证'
  })
  cookie: string

  @ApiProperty({
    type: String,
    description: '账号的凭证'
  })
  localStorage?: string
}
export class UpdateVideoAccountResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: UpdateVideoAccountResponse
  })
  data: UpdateVideoAccountResponse
}

export class WxAccountIsLoginResponse {
  @ApiProperty({
    type: String,
    required: false,
    description: 'scaned已扫码 success登陆成功'
  })
  status: string

  @ApiProperty({
    type: String,
    required: false,
    description: '微信昵称'
  })
  nickname: string

  @ApiProperty({
    type: String,
    required: false,
    description: '微信头像'
  })
  avatar: string

  @ApiProperty({
    type: Boolean,
    required: true,
    description: '微信登陆状态true已 false未登陆'
  })
  isLogin: boolean

  @ApiProperty({
    type: String,
    required: false,
    description: '登陆的账号ID'
  })
  platformAccountId?: string

  @ApiProperty({
    type: Boolean,
    required: false,
    description: '是否是新添加账号'
  })
  isNew?: boolean
}
export class WxAccountIsLoginResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: WxAccountIsLoginResponse
  })
  data: WxAccountIsLoginResponse
}

export class AccountLockListResponse {
  @ApiProperty({
    type: String,
    description: '媒体账号ID'
  })
  platformAccountId: string

  @ApiProperty({
    type: String,
    description: '微信号下视频号使用密钥'
  })
  wxkey: string
}
export class AccountLockListResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: [AccountLockListResponse]
  })
  data: AccountLockListResponse[]
}
