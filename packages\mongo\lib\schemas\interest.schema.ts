import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from "@nestjs/mongoose"

@Schema({
  timestamps: true,
  versionKey: false
})
export class InterestEntity {
  /**
   * 团队账号数量限制 0时使用默认
   *
   * @deprecated 请使用 accountCapacityLimit
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCountLimit: number  // 变更为账号点数

  /**
   * 团队账号点数限制 0时使用默认
   */
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  accountCapacityLimit: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  memberCountLimit: number

  //素材库资源大小 单位字节
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  capacityLimit: number

  //网络流量大小 单位字节
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  networkTrafficLimit: number

  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  appPublish: boolean

  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  price: number
}

export const InterestSchema: ModelDefinition = {
  name: InterestEntity.name,
  schema: SchemaFactory.createForClass(InterestEntity)
}

export const InterestMongoose = MongooseModule.forFeature([InterestSchema])
