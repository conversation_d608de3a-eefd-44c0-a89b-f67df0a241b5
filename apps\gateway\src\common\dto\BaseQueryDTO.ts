import { ApiProperty } from '@nestjs/swagger'
import { IsArray, IsOptional, isString } from 'class-validator'
import { Transform, Type } from 'class-transformer'

export interface IGetPagedListInputDto {
  page: number
  size: number
}

export interface IFetchAllListInputDto {
  fetchAll: boolean
}

export interface IFindListInputDto {
  fields?: string[]
}

export class PagedListOutputDto {
  @ApiProperty({
    type: Number,
    title: '总数量',
    example: 764,
    required: true
  })
  totalSize: number
}



export class GetPagedListInputDto implements IGetPagedListInputDto {
  @ApiProperty({
    type: Number,
    description: '每页数量',
    example: 3,
    required: false,
    default: 1
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量',
    example: 20,
    required: false,
    default: 10
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10
}

export class FetchAllListInputDto extends GetPagedListInputDto implements IFetchAllListInputDto, IFindListInputDto {
  @ApiProperty({
    type: Boolean,
    description: '是否提取所有数据, 如果为 true, 则会返回所有符合条件的数据(忽略 page/size)',
    example: true,
    required: false,
    default: false
  })
  @IsOptional()
  @Type(() => Boolean)
  fetchAll: boolean = false

  @ApiProperty({
    type: [String],
    title: '返回结构中包含的字段',
    description: `返回结构中包含的字段, 用','分隔多个字段, 支持使用 '*' 表示全部;
     如果未指定, 则仅返回必填字段;
     如果指定, 则返回必填字段和指定字段;
     如果指定的字段不存在, 则忽略;`,
    example: 'creator.*,accounts',
    required: false
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => (isString(value) ? value.split(',').filter((x) => x) : value))
  fields?: string[] = []
}
