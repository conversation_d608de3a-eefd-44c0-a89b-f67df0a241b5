import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { MessageService } from './message.service'
import {
  MessageLatestResponseDTO,
  MessageListRequestDTO,
  MessagePageResponseDTO
} from './message.dto'

@Controller('messages')
@ApiTags('消息通知管理/系统消息')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  /**
   * 获取最新通知状态
   * @param time
   */
  @Get('latest')
  @ApiOkResponse({ type: MessageLatestResponseDTO })
  @ApiOperation({ summary: '获取最新通知' })
  getNewest() {
    return this.messageService.getNewest()
  }

  @Get()
  @ApiOkResponse({ type: MessagePageResponseDTO })
  @ApiOperation({ summary: '获取消息列表' })
  getList(@Query() query: MessageListRequestDTO) {
    return this.messageService.getPagedMessages(query)
  }
}
