import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { EventNames, MemberRolesChangedEvent } from '@yxr/common'
import { AuthorizationService } from '../../common/security/authorization.service'

@Injectable()
export class MemberRolesChangedListener {
  logger = new Logger('MemberRolesChangedListener')

  constructor(private readonly authorizationService: AuthorizationService) {
  }

  @OnEvent(EventNames.MemberRolesChangedEvent, { async: true })
  async handleMemberRolesChangedEvent(payload: MemberRolesChangedEvent) {

    // 更新用户的角色缓存, 让角色尽快生效
    await this.authorizationService.setRoleNames(
      payload.teamId,
      payload.userId,
      payload.newRoles
    )
  }
}
