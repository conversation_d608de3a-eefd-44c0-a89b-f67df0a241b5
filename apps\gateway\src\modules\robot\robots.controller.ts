import { Body, Controller, Get, Put, Post, Inject } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { RobotsService } from './robots.service'
import { PostRobotRequest, RobotResponseDTO, SendMessageTestRequest } from './robots.dto'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'

@Controller('robots')
@ApiTags('机器人管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ type: BaseBadRequestResponseDTO, description: '参数错误' })
@ApiHeader({ name: 'authorization', required: true })
export class RobotsController {
  constructor(
    private readonly robotsService: RobotsService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Put()
  @ApiOperation({ summary: '创建/修改数据推送设置' })
  @ApiOkResponse({ type: RobotResponseDTO, description: '操作成功' })
  async postRobot(@Body() body: PostRobotRequest) {
    const { teamId: currentTeamId } = this.request.session

    return await this.robotsService.putRobot(currentTeamId, body)
  }

  @Get()
  @ApiOperation({ summary: '获取数据推送设置信息' })
  @ApiOkResponse({ type: RobotResponseDTO, description: '操作成功' })
  async getRobot() {
    const { teamId: currentTeamId } = this.request.session
    return await this.robotsService.getRobot(currentTeamId)
  }

  @Post('messages/test')
  @ApiOperation({ summary: '测试发送' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  async sendTestMessage(@Body() body: SendMessageTestRequest) {
    const { teamId: currentTeamId } = this.request.session

    return await this.robotsService.sendTestMessage(currentTeamId, body)
  }
}
