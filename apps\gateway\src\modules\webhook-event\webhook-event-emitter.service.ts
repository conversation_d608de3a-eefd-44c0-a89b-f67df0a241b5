import { Injectable, Logger } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { EventListenerParams } from './webhook-event.types'

/**
 * Webhook事件触发器服务
 * 用于在业务逻辑中触发各种事件，供WebhookEventService监听和处理
 */
@Injectable()
export class WebhookEventEmitterService {
  private readonly logger = new Logger(WebhookEventEmitterService.name)

  constructor(private readonly eventEmitter: EventEmitter2) {}

  /**
   * 触发媒体账号创建事件
   */
  async emitPlatformAccountCreated(params: {
    teamId: string
    accountId: string
    platformName: string
    nickname: string
    userId: string
    capacity?: number
  }): Promise<void> {
    try {
      const eventParams: EventListenerParams = {
        teamId: params.teamId,
        data: {
          accountId: params.accountId,
          platformName: params.platformName,
          nickname: params.nickname,
          userId: params.userId,
          capacity: params.capacity || 1
        }
      }

      this.eventEmitter.emit('platform.account.created', eventParams)
      
      this.logger.debug(
        `触发媒体账号创建事件: 团队ID=${params.teamId}, 账号ID=${params.accountId}, ` +
        `平台=${params.platformName}, 昵称=${params.nickname}`
      )
    } catch (error) {
      this.logger.error(`触发媒体账号创建事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 触发媒体账号删除事件
   */
  async emitPlatformAccountDeleted(params: {
    teamId: string
    accountId: string
    platformName: string
    nickname: string
    userId: string
  }): Promise<void> {
    try {
      const eventParams: EventListenerParams = {
        teamId: params.teamId,
        data: {
          accountId: params.accountId,
          platformName: params.platformName,
          nickname: params.nickname,
          userId: params.userId
        }
      }

      this.eventEmitter.emit('platform.account.deleted', eventParams)
      
      this.logger.debug(
        `触发媒体账号删除事件: 团队ID=${params.teamId}, 账号ID=${params.accountId}, ` +
        `平台=${params.platformName}, 昵称=${params.nickname}`
      )
    } catch (error) {
      this.logger.error(`触发媒体账号删除事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 触发任务集创建事件
   */
  async emitTaskSetCreated(params: {
    teamId: string
    taskSetId: string
    taskSetName: string
    userId: string
    taskCount?: number
  }): Promise<void> {
    try {
      const eventParams: EventListenerParams = {
        teamId: params.teamId,
        data: {
          taskSetId: params.taskSetId,
          taskSetName: params.taskSetName,
          userId: params.userId,
          taskCount: params.taskCount || 0
        }
      }

      this.eventEmitter.emit('taskset.created', eventParams)
      
      this.logger.debug(
        `触发任务集创建事件: 团队ID=${params.teamId}, 任务集ID=${params.taskSetId}, ` +
        `任务集名称=${params.taskSetName}, 任务数量=${params.taskCount || 0}`
      )
    } catch (error) {
      this.logger.error(`触发任务集创建事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 触发任务状态更新事件
   */
  async emitTaskStatusUpdated(params: {
    teamId: string
    taskId: string
    taskSetId: string
    taskSetName: string
    userId: string
    oldStatus: string
    newStatus: string
    platformName: string
    accountNickname: string
  }): Promise<void> {
    try {
      const eventParams: EventListenerParams = {
        teamId: params.teamId,
        data: {
          taskId: params.taskId,
          taskSetId: params.taskSetId,
          taskSetName: params.taskSetName,
          userId: params.userId,
          oldStatus: params.oldStatus,
          newStatus: params.newStatus,
          platformName: params.platformName,
          accountNickname: params.accountNickname
        }
      }

      this.eventEmitter.emit('task.status.updated', eventParams)
      
      this.logger.debug(
        `触发任务状态更新事件: 团队ID=${params.teamId}, 任务ID=${params.taskId}, ` +
        `状态变更: ${params.oldStatus} -> ${params.newStatus}, 平台=${params.platformName}`
      )
    } catch (error) {
      this.logger.error(`触发任务状态更新事件失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 触发自定义事件（用于扩展）
   */
  async emitCustomEvent(eventName: string, params: EventListenerParams): Promise<void> {
    try {
      this.eventEmitter.emit(eventName, params)
      
      this.logger.debug(
        `触发自定义事件: ${eventName}, 团队ID=${params.teamId}`
      )
    } catch (error) {
      this.logger.error(`触发自定义事件失败: ${eventName}, ${error.message}`, error.stack)
    }
  }
}
