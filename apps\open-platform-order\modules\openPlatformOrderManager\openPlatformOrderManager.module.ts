import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'
import { OpenPlatformOrderManagerService } from './openPlatformOrderManager.service'
import { OpenPlatformOrderTrafficCleanupService } from './openPlatformOrderTrafficCleanup.service'
import {
  OrderMongoose,
  TeamMongoose,
  OpenPlatformUserMongoose,
  OpenPlatformUserRoleMongoose,
  OpenPlatformApplicationBalanceMongoose,
  MemberMongoose
} from '@yxr/mongo'

@Module({
  imports: [
    ScheduleModule.forRoot(),
    OrderMongoose,
    TeamMongoose,
    OpenPlatformUserMongoose,
    OpenPlatformUserRoleMongoose,
    OpenPlatformApplicationBalanceMongoose,
    MemberMongoose
  ],
  providers: [
    OpenPlatformOrderManagerService,
    OpenPlatformOrderTrafficCleanupService
  ],
  exports: [
    OpenPlatformOrderManagerService,
    OpenPlatformOrderTrafficCleanupService
  ]
})
export class OpenPlatformOrderManagerModule {}
