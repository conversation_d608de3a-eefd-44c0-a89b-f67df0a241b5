import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'
import { OpenPlatformOrderManagerService } from './openPlatformOrderManager.service'
import { OpenPlatformBenefitCalculationService } from './openPlatformBenefitCalculation.service'
import { OpenPlatformOrderTrafficCleanupService } from './openPlatformOrderTrafficCleanup.service'
import {
  OrderMongoose,
  TeamMongoose,
  OpenPlatformUserMongoose,
  OpenPlatformUserRoleMongoose,
  OpenPlatformApplicationBalanceMongoose,
  MemberMongoose
} from '@yxr/mongo'

@Module({
  imports: [
    ScheduleModule.forRoot(),
    OrderMongoose,
    TeamMongoose,
    OpenPlatformUserMongoose,
    OpenPlatformUserRoleMongoose,
    OpenPlatformApplicationBalanceMongoose,
    MemberMongoose
  ],
  providers: [
    OpenPlatformOrderManagerService,
    OpenPlatformOrderTrafficCleanupService,
    OpenPlatformBenefitCalculationService
  ],
  exports: [
    OpenPlatformOrderManagerService,
    OpenPlatformOrderTrafficCleanupService,
    OpenPlatformBenefitCalculationService
  ]
})
export class OpenPlatformOrderManagerModule {}
