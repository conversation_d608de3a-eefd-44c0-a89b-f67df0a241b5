import { Inject, Injectable, OnModuleInit } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ContentStatisticEntity } from '@yxr/mongo'
import { Model } from 'mongoose'
import { Cron } from '@nestjs/schedule'
import dayjs from 'dayjs'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class ContentStatisticCleanCornService {
  private lockValue: string = 'handleContentStatisticClean'
  private readonly lockPrefix = 'lock:'

  constructor(
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly loggerService: TlsService
  ) {}

  /**
   * 清除七天以上的作品数据
   * 每天1:00执行
   */
  @Cron('0 0 1 * * *', {
    name: 'contentStatisticClean',
    timeZone: 'Asia/Shanghai'
  })
  async ContentStatisticCleanCronTask() {
    if (await this.acquireLock(this.lockValue, 60)) {
      try {
        const sevenDaysAgo = dayjs().tz('Asia/Shanghai').subtract(7, 'day').startOf('day')
        const sevenDaysAgoUtc = sevenDaysAgo.utc().toDate()
        await this.contentStatisticModel.deleteMany({
          publishTime: { $lt: sevenDaysAgoUtc }
        })
      } catch (e) {
        await this.loggerService.error(null, '清除七天外作品统计数据定时处理失败', {
          error: e
        })
      } finally {
        await this.releaseLock(this.lockValue)
      }
    }
  }

  // 获取锁
  async acquireLock(key: string, ttl: number): Promise<boolean> {
    const result = await this.cacheManager.store.client.set(
      this.lockPrefix + key,
      'locked',
      'EX',
      ttl,
      'NX'
    )
    return result === 'OK'
  }

  // 释放锁
  async releaseLock(key: string) {
    await this.cacheManager.store.client.del(this.lockPrefix + key)
  }
}
