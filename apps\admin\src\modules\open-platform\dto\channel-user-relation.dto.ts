import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsArray } from 'class-validator'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

/**
 * 创建用户关联请求DTO
 */
export class CreateChannelUserRelationRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  applicationId: string

  @ApiProperty({
    description: '关联用户ID列表',
    example: ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013']
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  relatedUserIds: string[]

  @ApiProperty({
    description: '关联备注',
    example: '批量关联用户',
    required: false
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 更新用户关联请求DTO
 */
export class UpdateChannelUserRelationRequestDto {
  @ApiProperty({
    description: '关联用户ID列表',
    example: ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013']
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  relatedUserIds: string[]

  @ApiProperty({
    description: '关联备注',
    example: '更新关联用户',
    required: false
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 用户关联关系DTO
 */
export class ChannelUserRelationDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiResponseProperty({
    example: '我的应用'
  })
  applicationName: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439012'
  })
  channelUserId: string

  @ApiResponseProperty({
    example: '13800138001'
  })
  channelUserPhone: string

  @ApiResponseProperty({
    example: '张三'
  })
  channelUserNickname: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439013'
  })
  relatedUserId: string

  @ApiResponseProperty({
    example: '13800138002'
  })
  relatedUserPhone: string

  @ApiResponseProperty({
    example: '李四'
  })
  relatedUserNickname: string

  @ApiResponseProperty({
    example: 0
  })
  status: number

  @ApiResponseProperty({
    example: '关联备注'
  })
  remark: string

  @ApiResponseProperty({
    example: 1640995200000
  })
  createdAt: number

  @ApiResponseProperty({
    example: 1640995200000
  })
  updatedAt: number
}

/**
 * 用户关联列表请求DTO
 */
export class ChannelUserRelationListRequestDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  size?: number = 10

  @ApiProperty({
    description: '应用ID筛选',
    example: '507f1f77bcf86cd799439011',
    required: false
  })
  @IsOptional()
  @IsString()
  applicationId?: string

  @ApiProperty({
    description: '搜索关键词（用户手机号或昵称）',
    example: '138',
    required: false
  })
  @IsOptional()
  @IsString()
  keyword?: string
}

/**
 * 用户关联列表响应DTO
 */
export class ChannelUserRelationListResponseDto {
  @ApiResponseProperty({
    type: [ChannelUserRelationDto]
  })
  data: ChannelUserRelationDto[]

  @ApiResponseProperty({
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  size: number
}

/**
 * 创建用户关联响应DTO
 */
export class CreateChannelUserRelationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [ChannelUserRelationDto]
  })
  data: ChannelUserRelationDto[]
}

/**
 * 获取用户关联列表响应DTO
 */
export class GetChannelUserRelationListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ChannelUserRelationListResponseDto
  })
  data: ChannelUserRelationListResponseDto
}

/**
 * 渠道商关联的用户简要信息DTO
 */
export class RelatedUserDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439013'
  })
  id: string

  @ApiResponseProperty({
    example: '13800138002'
  })
  phone: string

  @ApiResponseProperty({
    example: '李四'
  })
  nickname: string

  @ApiResponseProperty({
    example: 0
  })
  status: number
}

/**
 * 渠道商关联用户列表响应DTO
 */
export class RelatedUserListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [RelatedUserDto]
  })
  data: RelatedUserDto[]
}
