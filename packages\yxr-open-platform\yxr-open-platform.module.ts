import { Module } from '@nestjs/common'
import { YxrOpenPlatformService } from './src/yxr-open-platform.service'
import { HuoshanModule } from '@yxr/huoshan'
import { PlatformAccountCookieMongoose, PlatformAccountMongoose } from '@yxr/mongo'

@Module({
  imports: [PlatformAccountMongoose, PlatformAccountCookieMongoose],
  providers: [YxrOpenPlatformService, HuoshanModule],
  exports: [YxrOpenPlatformService]
})
export class YxrOpenPlatformModule {}
