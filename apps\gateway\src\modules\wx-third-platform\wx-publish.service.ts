import {
  ForbiddenException,
  HttpException,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import {
  WxMaterialRequest,
  WxMaterialResponse,
  WxMediaType,
  WxPublishCreateRequest,
  WxPublishPreviewRequest,
  WxUploadimgResponse
} from './wx-publish.dto'
import { WxBasicService } from './wx-basic.service'
import {
  ContentEntity,
  MemberEntity,
  PlatformAccountEntity,
  TaskEntity,
  TaskSetEntity,
  UserEntity
} from '@yxr/mongo'
import { Connection, Model, Types } from 'mongoose'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { v4 as uuid } from 'uuid'
import {
  EventNames,
  LiteSystemErrorCode,
  PlatformNameEnum,
  PublishChannel,
  StageStatus,
  TaskAuditStatusEvent,
  TaskSetStatusEnum,
  TaskStages,
  WeChatErrorCode
} from '@yxr/common'
import { eventKey, wechatPublishEventEmitter } from './wx-publish.event'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { WxCommonService } from './wx-common.service'
import { TlsService } from '@yxr/huoshan'
import { TeamService } from '../team/team.service'
import dayjs from 'dayjs'

@Injectable()
export class WxPublishService {
  logger = new Logger('WxPublishService')

  constructor(
    private readonly wxBasicService: WxBasicService,
    private readonly wxCommonService: WxCommonService,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @InjectModel(TaskSetEntity.name)
    private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name)
    private contentModel: Model<ContentEntity>,
    private eventEmitter: EventEmitter2,
    @InjectConnection() private readonly connection: Connection,
    private readonly teamService: TeamService,
    private readonly loggerService: TlsService
  ) {}

  /**
   * 公众号发布
   * @param input
   */
  async postWxDraft(currentTeamId: string, currentUserId: string, input: WxPublishCreateRequest) {
    const accounts = await this.platformAccountModel.find(
      {
        _id: { $in: input.platformAccountIds.map((e) => new Types.ObjectId(e)) },
        teamId: new Types.ObjectId(currentTeamId)
      },
      { _id: 1, platformAvatar: 1, platformName: 1, platformAccountName: 1, verifyTypeInfo: 1 }
    )

    // 发布账号数量为0, 直接返回
    if (accounts.length === 0) {
      throw new ForbiddenException('发布失败,没有一个合法的账号')
    }

    if (input.sendAll == 1) {
      //判断账号是否有群发权限
      const noSendPermission = accounts.filter((item) => item.verifyTypeInfo == '-1')
      if (noSendPermission.length > 0) {
        throw new ForbiddenException('群发失败,存在未认证的账号')
      }
    }
    const checkPush = await this.teamService.checkPush(currentTeamId)
    if (!checkPush) {
      throw new ForbiddenException({
        code: LiteSystemErrorCode.IsNotVip,
        message: '今日发布次数已达上限'
      })
    }
    const currentUser = await this.userModel.findById(new Types.ObjectId(currentUserId))
    const memberRemark = await this.memberModel.findOne({
      userId: new Types.ObjectId(currentUserId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    let contents: (ContentEntity & { _id: Types.ObjectId })[] = null
    const taskIdentityId = uuid()

    try {
      await this.connection.transaction(
        async (session) => {
          // 创建任务集
          await this.taskSetModel.create(
            [
              {
                teamId: new Types.ObjectId(currentTeamId),
                userId: new Types.ObjectId(currentUserId),
                platforms: [PlatformNameEnum.微信公众号],
                phone: currentUser.phone,
                nickName: memberRemark.remark ?? currentUser.nickName,
                cover: input.coverKey,
                desc: input.desc,
                descRich: input.descRich,
                taskIdentityId: taskIdentityId,
                publishType: input.publishType,
                taskSetStatus: TaskSetStatusEnum.Publishing,
                isAppContent: false,
                publishChannel: PublishChannel.local
              }
            ],
            { session, ordered: true }
          )

          // 新增内容
          const contentEntities: ContentEntity[] = accounts.map((account) => {
            return {
              teamId: new Types.ObjectId(currentTeamId),
              userId: new Types.ObjectId(currentUserId),
              platformAccountId: new Types.ObjectId(account.id),
              publishType: input.publishType,
              platformAvatar: account.platformAvatar,
              platformName: account.platformName,
              platformAccountName: account.platformAccountName,
              phone: currentUser.phone,
              nickName: memberRemark.remark ?? currentUser.nickName,
              content: null,
              isAppContent: false,
              sendAll: input.sendAll,
              publishArgs: input.articles,
              publishChannel: PublishChannel.local,
              sendIgnoreReprint: input.sendIgnoreReprint,
              taskSetId: taskIdentityId
            }
          })
          contents = await this.contentModel.create<ContentEntity>(contentEntities, {
            session,
            ordered: true
          })

          // 新增任务
          const taskEntities: TaskEntity[] = accounts.map((account) => {
            const content = contents.find((x) => x.platformAccountId.equals(account.id))

            return {
              contentId: new Types.ObjectId(content._id),
              taskId: uuid(),
              userId: new Types.ObjectId(currentUserId),
              teamId: new Types.ObjectId(currentTeamId),
              platformAccountId: new Types.ObjectId(account.id),
              progress: 0,
              taskSetId: taskIdentityId,
              publishChannel: PublishChannel.local,
              publishType: input.publishType,
              stages: TaskStages.Push,
              stageStatus: StageStatus.Doing
            }
          })
          await this.taskModel.create<TaskEntity>(taskEntities, { session, ordered: true })
        },
        { maxTimeMS: 1000 * 10 }
      )
      await this.teamService.setPush(currentTeamId)
    } catch (error) {
      this.logger.error(`Transaction failed: ${error.message}`, error.stack)
      throw new HttpException('创建任务失败, 请稍后再试', -1)
    }

    wechatPublishEventEmitter.emit(eventKey, {
      taskIdentityId: taskIdentityId,
      contentIds: contents.map((item) => item._id)
    })
  }

  /**
   * 更新微信发布状态
   * @param taskId
   */
  async getPublishStatus(taskId: string) {
    const task = await this.taskModel.findOne({
      taskId: taskId
    })

    if (!task) {
      throw new NotFoundException('任务不存在')
    }
    if (task.stages != TaskStages.Review) {
      //只更新审核中状态
      return
    }
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(task.platformAccountId)
    })
    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }
    if (platformAccount.platformName !== PlatformNameEnum.微信公众号) {
      throw new ForbiddenException('账号信息错误')
    }

    const access_token = await this.wxCommonService.getAuthorizerAccessToken(
      platformAccount.platformAuthorId,
      platformAccount.token
    )
    const result = await this.wxBasicService.freePublishStatus(access_token, task.publishId)
    let article_urls = null
    if (result.errcode == 0) {
      article_urls = JSON.stringify(result.article_detail.item)
    }
    await this.updateWxPublishStatus(task.publishId, result.publish_status, article_urls)
  }

  /**
   * 上传图文消息内的图片获取URL
   * @param platformAccountId
   * @param image
   * @param filename
   * @param filelength
   * @param fileType
   * @returns
   */
  async postUploadimg(
    currentTeamId: string,
    platformAccountId: string,
    image: Buffer,
    filename: string,
    filelength: number,
    fileType: string
  ): Promise<WxUploadimgResponse> {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    const accessToken = await this.wxCommonService.getAuthorizerAccessToken(
      platformAccount.platformAuthorId,
      platformAccount.token
    )

    const result = await this.wxBasicService.postUploadimg(
      accessToken,
      image,
      filename,
      filelength,
      fileType
    )
    return result.url
  }

  /**
   * 新增其他类型永久素材
   * @param platformAccountId
   * @param image
   * @param filename
   * @param filelength
   * @param fileType
   * @param body
   * @returns
   */
  async postAddmaterial(
    currentTeamId: string,
    platformAccountId: string,
    image: Buffer,
    filename: string,
    fileType: string,
    body: WxMaterialRequest
  ): Promise<WxMaterialResponse> {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    const accessToken = await this.wxCommonService.getAuthorizerAccessToken(
      platformAccount.platformAuthorId,
      platformAccount.token
    )

    const result = await this.wxBasicService.postAddmaterial(
      accessToken,
      image,
      filename,
      image.length,
      fileType,
      body.type,
      body.title,
      body.introduction
    )
    return result
  }

  /**
   * 更新状态
   * @param publishId
   * @param publishStatus
   */
  async updateWxPublishStatus(
    publishId: string,
    publishStatus: number,
    article_id?: string,
    article_urls?: string
  ) {
    const task = await this.taskModel.findOne({
      publishId: publishId
    })

    if (task) {
      let stages = TaskStages.Review
      let stageStatus = StageStatus.Fail
      let errorMessage = null
      let openUrl = null
      let documentId = null
      if (publishStatus == 0) {
        documentId = article_id
        openUrl = article_urls
        stages = TaskStages.Success
        stageStatus = StageStatus.Success
      } else {
        switch (publishStatus) {
          case 2:
            errorMessage = '原创失败'
            break
          case 3:
            errorMessage = '常规失败'
            break
          case 4:
            errorMessage = '平台审核不通过'
            break
          case 5:
            errorMessage = '成功后用户删除所有文章'
            break
          case 6:
            errorMessage = '成功后系统封禁所有文章'
            break
        }
        stageStatus = StageStatus.Fail
      }
      await this.taskModel.updateOne(
        {
          _id: task._id
        },
        {
          stageStatus: stageStatus,
          stages: stages,
          errorMessage: errorMessage,
          documentId: documentId,
          openUrl: openUrl
        }
      )

      // 触发任务状态变更事件
      await this.eventEmitter.emitAsync(
        EventNames.TaskAuditStatusChangedEvent,
        new TaskAuditStatusEvent(task.teamId.toString(), task.taskSetId)
      )
    }
  }

  /**
   * 更新微信群发状态
   * @param publishId
   * @param publishStatus
   */
  async updateWxSendAllStatus(publishId: string, publishStatus: string) {
    const task = await this.taskModel.findOne({
      publishId: publishId
    })

    if (task) {
      let stages = TaskStages.Review
      let stageStatus = StageStatus.Fail
      if (publishStatus == 'send success') {
        stageStatus = StageStatus.Success
      } else {
        await this.loggerService.error(
          null,
          '微信公众号群发失败组结果：msg_id：' + publishId + ' status:' + publishStatus
        )
      }
      await this.taskModel.updateOne(
        {
          _id: task._id
        },
        {
          stages: stages,
          stageStatus: stageStatus
        }
      )

      // 触发任务状态变更事件
      await this.eventEmitter.emitAsync(
        EventNames.TaskAuditStatusChangedEvent,
        new TaskAuditStatusEvent(task.teamId.toString(), task.taskSetId)
      )
    }
  }

  /**
   * 微信发布预览
   * @param platformAccountId
   * @param body
   */
  async publishPreview(
    currentTeamId: string,
    platformAccountId: string,
    body: WxPublishPreviewRequest
  ) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }

    if (platformAccount.verifyTypeInfo == '-1') {
      throw new ForbiddenException('账号未认证，不能预览')
    }
    const accessToken = await this.wxCommonService.getAuthorizerAccessToken(
      platformAccount.platformAuthorId,
      platformAccount.token
    )

    // 转换内容图片标签
    for (let index = 0; index < body.articles.length; index++) {
      const element = body.articles[index]
      //封面处理
      if (element.thumbUrl) {
        const thumbResult = await this.wxCommonService.uploadWxMaterial(
          accessToken,
          element.thumbUrl,
          WxMediaType.image
        )
        if (thumbResult && thumbResult.media_id) {
          element.thumbUrl = thumbResult.media_id
        }
      }
      element.content = await this.wxCommonService.imgTagReplace(accessToken, element.content)
    }

    const result = await this.wxBasicService.addWxDraft(accessToken, body.articles)
    if (result?.errcode && result.errcode !== 0) {
      throw new NotFoundException('预览失败:' + WeChatErrorCode.getErrorMessage(result.errcode))
    }

    for (let i = 0; i < body.towxname.length; i++) {
      await this.wxBasicService.publishPreview(accessToken, result.media_id, body.towxname[i])
    }
  }

  /**
   * 删除发布文章
   * @param platformAuthorId
   * @param token
   * @param publishId
   * @param documentId
   */
  async deletePublishTask(
    platformAuthorId: string,
    token: string,
    publishId: string,
    documentId: string
  ) {
    const access_token = await this.wxCommonService.getAuthorizerAccessToken(
      platformAuthorId,
      token
    )
    if (!documentId) {
      //兼容老数据，未更新documentId
      const result = await this.wxBasicService.freePublishStatus(access_token, publishId)
      documentId = result.article_id
    }

    await this.wxBasicService.deletePublishs(access_token, documentId)
  }

  /**
   * 获取累计用户
   * @param currentTeamId
   * @param platformAccountId
   * @returns
   */
  async getUserCumulate(currentTeamId: string, platformAccountId: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }
    try {
      const access_token = await this.wxCommonService.getAuthorizerAccessToken(
        platformAccount.platformAuthorId,
        platformAccount.token
      )

      const beginDate = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
      return await this.wxBasicService.getUserCumulate(access_token, beginDate, beginDate)
    } catch (e) {
      await this.loggerService.error(null, '获取用户累计数据失败', { error: e })
    }
  }

  /**
   * 获取群发数据
   * @param currentTeamId
   * @param platformAccountId
   * @returns
   */
  async getArticleTotal(currentTeamId: string, platformAccountId: string) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号未找到')
    }
    try {
      const access_token = await this.wxCommonService.getAuthorizerAccessToken(
        platformAccount.platformAuthorId,
        platformAccount.token
      )

      const beginDate = dayjs().tz('Asia/Shanghai').subtract(1, 'day').format('YYYY-MM-DD')
      return await this.wxBasicService.getArticleTotal(access_token, beginDate, beginDate)
    } catch (e) {
      await this.loggerService.error(null, '获取群发数据失败', { error: e })
    }
  }
}
