import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { ReportTypeEnum, RobotTypeEnum, SendingTimeTypeEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class RobotEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId?: Types.ObjectId

  //机器人类型
  @Prop({
    type: String,
    enum: RobotTypeEnum,
    required: true
  })
  robotType: RobotTypeEnum

  //报表类型 日报周报
  @Prop({
    type: [String],
    required: true
  })
  reportType: string[]


  //推送地址
  @Prop({
    type: String,
    required: true
  })
  webhookUrl: string

  /**
   * 发送时间类型
   */
  @Prop({
    type: [String],
    required: true
  })
  sendTimeType: string[]

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const RobotSchema: ModelDefinition = {
  name: RobotEntity.name,
  schema: SchemaFactory.createForClass(RobotEntity)
}

export const RobotMongoose = MongooseModule.forFeature([RobotSchema])
