import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  Inject
} from '@nestjs/common'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformApplicationRechargeEntity,
  OpenPlatformApplicationBalanceEntity,
  AdminEntity,
  OpenPlatformUserEntity
} from '@yxr/mongo'
import { RechargeType, RechargeStatus, UserType } from '@yxr/common'
import {
  CreateRechargeRequestDto,
  UpdateRechargeStatusRequestDto,
  GetRechargeListRequestDto,
  RechargeRecordResponseDto,
  RechargeListResponseDto,
  ApplicationBalanceResponseDto
} from '../dto/recharge.dto'

@Injectable()
export class RechargeService {
  private readonly logger = new Logger(RechargeService.name)

  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformApplicationRechargeEntity.name)
    private rechargeModel: Model<OpenPlatformApplicationRechargeEntity>,
    @InjectModel(OpenPlatformApplicationBalanceEntity.name)
    private balanceModel: Model<OpenPlatformApplicationBalanceEntity>,
    @InjectModel(OpenPlatformUserEntity.name)
    private openPlatformUserModel: Model<OpenPlatformUserEntity>,
    @InjectConnection() private connection: Connection,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建充值订单
   */
  async createRecharge(
    createRechargeDto: CreateRechargeRequestDto,
    operatorId?: string
  ): Promise<RechargeRecordResponseDto> {
    const { applicationId, rechargeType, virtualCoinAmount, remark, paymentAmount } =
      createRechargeDto
    const { session: userSession } = this.request

    const user = await this.openPlatformUserModel.findOne({ phone: createRechargeDto.phone })
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    // 验证应用是否存在
    const application = await this.applicationModel.findById(applicationId)
    if (!application && application.userId.toString() !== user.id.toString()) {
      throw new NotFoundException('应用不存在')
    }

    // 获取开放平台用户ID - 优先从应用信息中获取，如果当前用户是开放平台用户则使用当前用户ID
    let openPlatformUserId: Types.ObjectId

    if (userSession?.userType === UserType.OPEN_PLATFORM) {
      // 如果当前用户是开放平台用户，使用当前用户ID
      openPlatformUserId = new Types.ObjectId(userSession.userId)

      // 验证用户是否有权限操作该应用
      if (application.userId.toString() !== userSession.userId) {
        throw new ForbiddenException('无权限操作该应用')
      }
    } else {
      // 如果是管理员操作，使用应用的所有者ID
      openPlatformUserId = application.userId
    }

    // 生成充值订单号
    const rechargeOrderNo = this.generateRechargeOrderNo()

    // 创建充值记录
    const rechargeData: Partial<OpenPlatformApplicationRechargeEntity> = {
      applicationId: new Types.ObjectId(applicationId),
      openPlatformUserId, // 添加开放平台用户ID
      rechargeOrderNo,
      rechargeType,
      rechargeAmount: virtualCoinAmount,
      virtualCoinAmount,
      paymentAmount: rechargeType === RechargeType.GIFT ? 0 : paymentAmount,
      rechargeStatus:
        rechargeType === RechargeType.GIFT ? RechargeStatus.SUCCESS : RechargeStatus.PENDING,
      remark,
      operatorId: operatorId ? new Types.ObjectId(operatorId) : undefined
    }

    // 如果是赠送类型，立即设置充值时间和到账金额
    if (rechargeType === RechargeType.GIFT) {
      rechargeData.rechargeTime = new Date()
      rechargeData.paymentAmount = paymentAmount
    }

    const session = await this.connection.startSession()

    try {
      await session.withTransaction(async () => {
        // 创建充值记录
        const [rechargeRecord] = await this.rechargeModel.create([rechargeData], { session })

        // 如果是赠送类型，立即更新余额
        if (rechargeType === RechargeType.GIFT) {
          await this.updateApplicationBalance(applicationId, virtualCoinAmount, session)
        }

        return rechargeRecord
      })

      this.logger.log(
        `创建充值订单成功: ${rechargeOrderNo}, 应用ID: ${applicationId}, 金额: ${virtualCoinAmount}`
      )

      // 查询并返回创建的记录
      return await this.getRechargeById(rechargeOrderNo)
    } catch (error) {
      this.logger.error(`创建充值订单失败: ${error.message}`, error.stack)
      throw new BadRequestException('创建充值订单失败')
    } finally {
      await session.endSession()
    }
  }

  /**
   * 更新充值状态
   */
  async updateRechargeStatus(
    rechargeOrderNo: string,
    updateStatusDto: UpdateRechargeStatusRequestDto,
    operatorId?: string
  ): Promise<RechargeRecordResponseDto> {
    const { remark } = updateStatusDto

    const rechargeRecord = await this.rechargeModel.findOne({ rechargeOrderNo })
    if (!rechargeRecord) {
      throw new NotFoundException('充值记录不存在')
    }

    // 检查状态流转是否合法
    if (rechargeRecord.rechargeStatus === RechargeStatus.SUCCESS) {
      throw new BadRequestException('充值已成功，无法修改状态')
    }

    if (rechargeRecord.rechargeStatus === RechargeStatus.CANCELLED) {
      throw new BadRequestException('充值已取消，无法修改状态')
    }

    const session = await this.connection.startSession()

    try {
      await session.withTransaction(async () => {
        // 更新充值记录
        const updateData: any = {
          rechargeStatus: RechargeStatus.SUCCESS,
          remark: remark || rechargeRecord.remark
        }

        updateData.rechargeTime = new Date()
        updateData.paymentAmount = rechargeRecord.paymentAmount

        // 更新应用余额
        await this.updateApplicationBalance(
          rechargeRecord.applicationId.toString(),
          updateData.virtualCoinAmount,
          session
        )

        if (operatorId) {
          updateData.operatorId = new Types.ObjectId(operatorId)
        }

        await this.rechargeModel.updateOne({ rechargeOrderNo }, updateData, { session })
      })

      this.logger.log(`更新充值状态成功: ${rechargeOrderNo}`)

      return await this.getRechargeById(rechargeOrderNo)
    } catch (error) {
      this.logger.error(`更新充值状态失败: ${error.message}`, error.stack)
      throw new BadRequestException('更新充值状态失败')
    } finally {
      await session.endSession()
    }
  }

  /**
   * 查询充值记录列表
   */
  async getRechargeList(query: GetRechargeListRequestDto): Promise<RechargeListResponseDto> {
    const { applicationId, rechargeType, rechargeStatus, page = 1, size = 10 } = query
    const { session: userSession } = this.request

    const filter: any = {}

    // 数据隔离：开放平台用户只能查看自己的充值记录
    if (userSession?.userType === UserType.OPEN_PLATFORM) {
      filter.openPlatformUserId = new Types.ObjectId(userSession.userId)
    }

    if (applicationId) {
      filter.applicationId = new Types.ObjectId(applicationId)
    }
    if (rechargeType) {
      filter.rechargeType = rechargeType
    }
    if (rechargeStatus) {
      filter.rechargeStatus = rechargeStatus
    }

    const skip = (page - 1) * size

    const [records, totalSize] = await Promise.all([
      this.rechargeModel.find(filter).sort({ createdAt: -1 }).skip(skip).limit(size).lean(),
      this.rechargeModel.countDocuments(filter)
    ])

    const data = await Promise.all(records.map((record) => this.formatRechargeRecord(record)))

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  /**
   * 查询应用余额
   */
  async getApplicationBalance(applicationId: string): Promise<ApplicationBalanceResponseDto> {
    // 验证应用是否存在
    const application = await this.applicationModel.findById(applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    let balance = await this.balanceModel.findOne({
      applicationId: new Types.ObjectId(applicationId)
    })

    // 如果余额记录不存在，创建一个初始记录
    if (!balance) {
      balance = await this.balanceModel.create({
        applicationId: new Types.ObjectId(applicationId),
        totalBalance: 0,
        frozenBalance: 0,
        availableBalance: 0,
        totalRecharge: 0,
        totalConsumption: 0
      })
    }

    return {
      applicationId: balance.applicationId.toString(),
      totalBalance: balance.totalBalance,
      frozenBalance: balance.frozenBalance,
      availableBalance: balance.availableBalance,
      totalRecharge: balance.totalRecharge,
      totalConsumption: balance.totalConsumption,
      lastRechargeTime: balance.lastRechargeTime?.getTime(),
      updatedAt: balance.updatedAt?.getTime() || Date.now()
    }
  }

  /**
   * 生成充值订单号
   */
  private generateRechargeOrderNo(): string {
    const now = new Date()
    const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '')
    const timeStr = now.getTime().toString().slice(-6)
    const randomStr = Math.random().toString(36).substring(2, 6).toUpperCase()
    return `RC${dateStr}${timeStr}${randomStr}`
  }

  /**
   * 更新应用余额
   */
  private async updateApplicationBalance(
    applicationId: string,
    coinAmount: number,
    session?: any
  ): Promise<void> {
    const options = session ? { session, upsert: true } : { upsert: true }

    await this.balanceModel.updateOne(
      { applicationId: new Types.ObjectId(applicationId) },
      {
        $inc: {
          totalBalance: coinAmount,
          availableBalance: coinAmount,
          totalRecharge: coinAmount
        },
        $set: {
          lastRechargeTime: new Date()
        }
      },
      options
    )
  }

  /**
   * 根据订单号查询充值记录
   */
  private async getRechargeById(rechargeOrderNo: string): Promise<RechargeRecordResponseDto> {
    const record = await this.rechargeModel
      .findOne({ rechargeOrderNo })
      .populate('applicationId', 'name')
      .lean()

    if (!record) {
      throw new NotFoundException('充值记录不存在')
    }

    if (!record.applicationId) {
      throw new Error('应用信息不存在')
    }

    return await this.formatRechargeRecord({ record })
  }

  /**
   * 格式化充值记录
   */
  private async formatRechargeRecord(record: any): Promise<RechargeRecordResponseDto> {
    const application = await this.applicationModel.findById(record.applicationId)
    const user = await this.openPlatformUserModel.findById(record.openPlatformUserId)

    return {
      id: record._id.toString(),
      applicationId: record.applicationId._id?.toString() || record.applicationId.toString(),
      applicationName: application.name || '未知应用',
      openPlatformUserId: record.openPlatformUserId?.toString() || '',
      phone: user?.phone || '',
      rechargeOrderNo: record.rechargeOrderNo,
      rechargeType: record.rechargeType,
      rechargeAmount: record.rechargeAmount,
      virtualCoinAmount: record.virtualCoinAmount,
      paymentAmount: record.paymentAmount,
      rechargeStatus: record.rechargeStatus,
      rechargeTime: record.rechargeTime?.getTime(),
      remark: record.remark,
      operatorId: record.operatorId?.toString(),
      createdAt: record.createdAt?.getTime() || Date.now(),
      updatedAt: record.updatedAt?.getTime() || Date.now()
    }
  }
}
