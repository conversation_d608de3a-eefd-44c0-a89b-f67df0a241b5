import { OverseasProviderFactory } from './overseas-provider.factory'
import { Module } from '@nestjs/common'
import { providers as tiktok } from './providers/tiktok/providers'
import { providers as twitter } from './providers/twitter/providers'
import { providers as youtube } from './providers/youtube/providers'
import { providers as facebook } from './providers/facebook/providers'
import { providers as instagram } from './providers/instagram/providers'

@Module({
  imports: [],
  providers: [OverseasProviderFactory, ...tiktok, ...twitter, ...youtube, ...facebook, ...instagram],
  exports: [OverseasProviderFactory]
})
export class OverseasModule {
}
