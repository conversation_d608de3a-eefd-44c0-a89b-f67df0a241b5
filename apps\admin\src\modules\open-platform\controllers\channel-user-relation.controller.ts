import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiUnauthorizedResponse,
  ApiHeader
} from '@nestjs/swagger'
import { ChannelUserRelationService } from '../services/channel-user-relation.service'
import { OpenPlatformAccess } from '../../../common/decorators/access-control.decorator'
import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseNotFoundResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import {
  CreateChannelUserRelationRequestDto,
  UpdateChannelUserRelationRequestDto,
  ChannelUserRelationListRequestDto,
  CreateChannelUserRelationResponseDto,
  GetChannelUserRelationListResponseDto,
  RelatedUserListResponseDto
} from '../dto/channel-user-relation.dto'

@Controller('open-platform/channel-user-relations')
@OpenPlatformAccess()
@ApiTags('开放平台用户关联管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class ChannelUserRelationController {
  constructor(private readonly relationService: ChannelUserRelationService) {}

  @Post()
  @ApiOperation({ 
    summary: '创建用户关联关系',
    description: '渠道商用户可以关联应用下的用户，建立一对多的关联关系'
  })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: CreateChannelUserRelationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或关联关系已存在',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  @ApiNotFoundResponse({
    description: '应用或用户不存在',
    type: BaseNotFoundResponseDTO
  })
  async createRelations(@Body() createDto: CreateChannelUserRelationRequestDto) {
    const result = await this.relationService.createRelations(createDto)
    return result
  }

  @Get()
  @ApiOperation({ 
    summary: '获取用户关联列表',
    description: '获取当前渠道商用户的所有用户关联关系'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: GetChannelUserRelationListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getRelationList(@Query() queryDto: ChannelUserRelationListRequestDto) {
    const result = await this.relationService.getRelationList(queryDto)
    return result
  }

  @Put(':applicationId')
  @ApiOperation({ 
    summary: '更新用户关联关系',
    description: '更新指定应用下的用户关联关系，会替换现有的所有关联'
  })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: CreateChannelUserRelationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  async updateRelations(
    @Param('applicationId') applicationId: string,
    @Body() updateDto: UpdateChannelUserRelationRequestDto
  ) {
    const result = await this.relationService.updateRelations(applicationId, updateDto)
    return result
  }

  @Delete(':relationId')
  @ApiOperation({ 
    summary: '删除用户关联关系',
    description: '删除指定的用户关联关系'
  })
  @ApiResponse({
    status: 200,
    description: '删除成功'
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  @ApiNotFoundResponse({
    description: '关联关系不存在',
    type: BaseNotFoundResponseDTO
  })
  async deleteRelation(@Param('relationId') relationId: string) {
    const result = await this.relationService.deleteRelation(relationId)
    return result
  }

  @Get('applications/:applicationId/related-users')
  @ApiOperation({ 
    summary: '获取渠道商关联的用户列表',
    description: '获取当前渠道商在指定应用下关联的所有用户'
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: RelatedUserListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getRelatedUsers(@Param('applicationId') applicationId: string) {
    const result = await this.relationService.getRelatedUsers(applicationId)
    return result
  }
}
