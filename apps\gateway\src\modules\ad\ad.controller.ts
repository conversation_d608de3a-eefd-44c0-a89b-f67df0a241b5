import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { AdService } from './ad.service'
import { AdsListResponseDTO } from './ad.dto'

@Controller('ads')
@ApiTags('广告管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class AdController {
  constructor(private readonly adService: AdService) {}

  @Get()
  @ApiOperation({ summary: '获取广告列表' })
  @ApiOkResponse({ description: '操作成功', type: AdsListResponseDTO })
  async getAds() {
    return this.adService.getAds()
  }

  @Get('popup')
  @ApiOperation({ summary: '获取弹框广告' })
  @ApiOkResponse({ description: '操作成功', type: AdsListResponseDTO })
  async getPopups() {
    return this.adService.getPopups()
  }
}
