import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader, ApiOkResponse, ApiOperation, ApiParam, ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO, BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  createInvitationRequestBodyDTO,
  InvitationUserStatusResponseDTO,
  patchInvitationRequestBodyDTO
} from './invitation.dto'
import { InvitationService } from './invitation.service'

@Controller('invitations')
@ApiTags('团队管理/邀请函')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class InvitationController {

  constructor(private readonly invitationService: InvitationService) {
  }

  /**
   * 获取邀请码状态
   */
  @Get('user-status')
  @ApiOperation({
    summary: '查询用户状态',
    description: '通过账号邀请用户加入团队之前, 可以通过此接口查询用户的加入状态'
  })
  @ApiOkResponse({ type: InvitationUserStatusResponseDTO, description: '操作成功' })
  @ApiQuery({ name: 'phone', required: true, description: '用户账号(手机号)', example: '13800001111', type: String })
  getUserStatus(
    @Query('phone') phone: string
  ) {
    return this.invitationService.getUserStatus( phone)
  }

  /**
   * 创建邀请函
   * @param teamId
   * @param data
   */
  @Post()
  @ApiOperation({ summary: '创建邀请函' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  create(
    @Body() { phone }: createInvitationRequestBodyDTO
  ) {
    return this.invitationService.create( phone)
  }

  /**
   * 接受/拒绝邀请
   * @param invitationId
   * @param data
   */
  @Patch(':invitationId')
  @ApiOperation({ summary: '接受/拒绝邀请' })
  @ApiOkResponse({ type: BaseResponseDTO, description: '操作成功' })
  @ApiParam({ name: 'invitationId', required: true, description: '邀请函ID' })
  patch(
    @Param('invitationId') invitationId: string,
    @Body() data: patchInvitationRequestBodyDTO
  ) {
    return this.invitationService.handle({invitationId, approved: data.approved})
  }
}
