import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'
import { Types } from 'mongoose'
import { ContentType } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})

/**
 * 作品趋势表 每天归档一次
 */
export class ContentTrendEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    index: true,
    required: true
  })
  platformName: string

  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  @Prop({
    type: String,
    index: true,
    enum: ContentType,
    default: ContentType.article
  })
  contentType: ContentType

  //推荐数
  @Prop({
    type: Number
  })
  reCommand: number

  //播放数
  @Prop({
    type: Number
  })
  play: number

  //阅读数
  @Prop({
    type: Number
  })
  read: number

  //点赞
  @Prop({
    type: Number
  })
  great: number

  //评论
  @Prop({
    type: Number
  })
  comment: number

  //分享
  @Prop({
    type: Number
  })
  share: number

  //收藏
  @Prop({
    type: Number
  })
  collect: number

  //新增粉丝
  @Prop({
    type: Number
  })
  addFans: number

  //完播率
  @Prop({
    type: Number
  })
  finishPlay: number

  //爱心
  @Prop({
    type: Number
  })
  love: number

  //弹幕
  @Prop({
    type: Number
  })
  danmu: number

  //硬币
  @Prop({
    type: Number
  })
  coin: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const ContentTrendSchema: ModelDefinition = {
  name: ContentTrendEntity.name,
  schema: SchemaFactory.createForClass(ContentTrendEntity)
}

export const ContentTrendMongoose = MongooseModule.forFeature([ContentTrendSchema])
