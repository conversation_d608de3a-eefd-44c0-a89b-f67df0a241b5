import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { postIpadQrcodeRequest, WxAccountIsLoginResponse, WxQrCodeResponse } from './wx-ipad.dto'
import { PlatformAccountService } from '../../platform/platform-account.service'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountEntity, TeamEntity } from '@yxr/mongo'
import { CacheKeyService, PlatformNameEnum } from '@yxr/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TeamService } from '../../team/team.service'
import { KuaidailiService } from '../../kuaidaili/kuaidaili.service'
import { WechatIpadSdkService } from '../../wechat-ipad/wechat-sdk.service'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class WxIpadService {
  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private platformAccountService: PlatformAccountService,
    private teamService: TeamService,
    private kuaidailiService: KuaidailiService,
    private readonly loggerService: TlsService,
    private readonly wechatIpadSdkService: WechatIpadSdkService
  ) {}

  /**
   * 第三方微信回调
   * @param body
   */
  async postIpadCallback(body: any) {
    await this.loggerService.info(null, '微信Ipad协议回调接收结果：', {
      data: JSON.stringify(body)
    })
    if (body.payload.length > 0) {
      for (const item of body.payload) {
        if (item.type === 'online') {
          //微信账号登陆
          // const accountInfo = item.data[2]
          // await this.platformAccountService.postWxIpadAccount({
          //   wxid: item.data[0],
          //   uuid: item.data[1],
          //   nickName: accountInfo.nickname,
          //   imgHead: accountInfo.imgHead
          // })
        } else if (item.type === 'offline') {
          // 微信账号离线
          // {"payload":[{"type":"offline","data":["wxid_0wihtgo0beir21"],"timestamp":*************}]}
          const wxid = item.data[0]
          await this.platformAccountService.wxIpadAccountOffline(wxid)
        }
      }
    }
  }

  /**
   * 第三方微信获取二维码
   * @param currentTeamId
   * @param currentUserId
   * @returns
   */
  async postIpadQrcode(
    currentTeamId: string,
    currentUserId: string,
    body: postIpadQrcodeRequest
  ): Promise<WxQrCodeResponse> {
    const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))
    if (!team) {
      throw new NotFoundException('团队未找到')
    }

    let platformAccount: PlatformAccountEntity = null
    if (!body.platformAccountId) {
      // 新增账号数量限制判断
      if (team.isVip === false) {
        throw new ForbiddenException('团队暂无权益，请联系客服申请')
      }
      await this.teamService.checkTeamAccountLimit(currentTeamId)
    } else {
      platformAccount = await this.platformAccountModel.findById(
        new Types.ObjectId(body.platformAccountId)
      )
    }

    let kuaidailiIp = null
    if (platformAccount?.kuaidailiIp) {
      kuaidailiIp = platformAccount.kuaidailiIp
    } else {
      kuaidailiIp = await this.kuaidailiService.getProxyAllocation(PlatformNameEnum.微信, body.kuaidailiAreaCode)
    }
    if (!kuaidailiIp) {
      throw new ForbiddenException('请先选择代理地区')
    }

    try {
      let deviceId = Date.now().toString(36) + Math.floor(Math.random() * 1000).toString(36)
      if (platformAccount?.deviceId) {
        deviceId = platformAccount.deviceId
      }
      const data = {
        deviceId: deviceId,
        deviceName: deviceId,
        proxy: {
          address: kuaidailiIp,
          username: '',
          password: ''
        }
      }
      const result = await this.wechatIpadSdkService.qrcode(data)

      await this.cacheManager.set(
        CacheKeyService.getKuaidailiKey(result.uuid),
        {
          deviceId: deviceId,
          teamId: currentTeamId,
          userId: currentUserId,
          kuaidailiArea: body.kuaidailiAreaCode,
          ip: kuaidailiIp
        },
        1800 * 1000
      )

      await this.cacheManager.set(
        CacheKeyService.getWxAccountLoginInfoKey(result.uuid),
        {
          isLogin: false
        },
        300 * 1000
      )

      return {
        base64: result.base64,
        uuid: result.uuid
      }
    } catch (e) {
      await this.loggerService.error(null, '获取第三方微信二维码失败：', { error: e.message })
      throw new ForbiddenException('获取微信二维码失败')
    }
  }

  /**
   * 检测账号登陆状态
   * @param uuid
   * @param currentTeamId
   * @param currentUserId
   */
  async getCheckLogin(uuid: string): Promise<WxAccountIsLoginResponse> {
    try {
      const res = await this.wechatIpadSdkService.checkLogin(uuid)
      let nickname = ''
      let avatar = ''
      let codeStatus = ''
      if (res) {
        if (res.status === 'success') {
          //登陆成功
          await this.platformAccountService.postWxIpadAccount({
            wxid: res?.data?.wxid,
            uuid: res?.data?.uuid,
            nickName: res?.data.info?.nickname,
            imgHead: res?.data.info?.imgHead
          })
          nickname = res?.data.info?.nickname
          avatar = res?.data.info?.imgHead
        } else if (res?.status === 'scaned') {
          nickname = res?.data.nickname
          avatar = res?.data.avatar
        } else if (res.status === 'error') {
          await this.loggerService.error(null, '微信扫码失败：', { error: res?.data.message })
          throw new ForbiddenException('登录失败，请重试')
        }

        codeStatus = res.status
      }

      const accountLoginInfo = await this.cacheManager.get<{
        isLogin: boolean
        platformAccountId?: string
        isNew?: boolean
      }>(CacheKeyService.getWxAccountLoginInfoKey(uuid))
      return {
        nickname: nickname,
        avatar: avatar,
        status: codeStatus,
        isLogin: accountLoginInfo?.isLogin,
        platformAccountId: accountLoginInfo?.platformAccountId,
        isNew: accountLoginInfo?.isNew
      }
    } catch (error) {
      await this.loggerService.error(null, '获取微信扫码信息失败：', { error: error.message })
      throw new ForbiddenException(error.message)
    }
  }
}
