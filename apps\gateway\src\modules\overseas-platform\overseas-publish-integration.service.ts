import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import axios from 'axios'
import {
  TaskEntity,
  PlatformAccountEntity
} from '@yxr/mongo'
import {
  PlatformType,
  TianyiyunOssService
} from '@yxr/common'

export interface OverseasPublishTaskData {
  taskId: string
  taskSetId: string
  teamId: string
  userId: string
  accountOpenId: string
  platform: string
  content: any
  callbackUrl: string
  maxRetries?: number
  credentials?: any
}

@Injectable()
export class OverseasPublishIntegrationService {
  private readonly logger = new Logger(OverseasPublishIntegrationService.name)

  constructor(
    @InjectModel(TaskEntity.name) private readonly taskModel: Model<TaskEntity>,
    @InjectModel(PlatformAccountEntity.name) private readonly platformAccountModel: Model<PlatformAccountEntity>,
    private readonly tianyiyunOssService: TianyiyunOssService
  ) {}

  /**
   * 处理海外平台发布任务
   * 在现有发布流程中调用此方法，识别海外平台账号并发送到香港服务
   */
  async handleOverseasPublishTasks(taskSetId: string): Promise<void> {
    this.logger.log(`处理海外平台发布任务: taskSetId=${taskSetId}`)

    try {
      // 查找该任务集中的海外平台任务
      const overseasTasks = await this.taskModel.aggregate([
        // 匹配任务
        { $match: { taskSetId: taskSetId } },

        // 关联账号
        { $lookup: { from: 'platformaccountentities', localField: 'platformAccountId', foreignField: '_id', as: 'account' } },
        { $unwind: '$account' },
        { $match: { 'account.platformType': PlatformType.海外平台 } }, // 仅保留海外平台

        // 关联内容
        { $lookup: { from: 'contententities', localField: 'contentId', foreignField: '_id', as: 'content' } },
        { $unwind: '$content' },

        // 字段筛选
        { $project: {
          taskSetId: 1,
          teamId: 1,
          userId: 1,
          'account._id': 1,
          'account.platformName': 1,
          'account.platformAuthorId': 1,
          'account.credentials': 1,
          'content._id': 1,
          'content.publishArgs': 1,
          'content.publishType': 1
        } }
      ])

      if (overseasTasks.length === 0) {
        this.logger.log(`没有找到海外平台任务: taskSetId=${taskSetId}`)
        return
      }

      // 构建发布任务数据(这里可能还需要逐步完善)
      const publishTasks: OverseasPublishTaskData[] = overseasTasks.map(task => ({
        taskId: task._id.toString(),
        taskSetId: task.taskSetId,
        teamId: task.teamId.toString(),
        userId: task.userId.toString(),
        accountOpenId: task.account.platformAuthorId,
        platform: task.account.platformName.toLowerCase(),
        content: this.transformContentForOverseas(task.content.publishArgs || {}, task.content.publishType),
        callbackUrl: `${process.env.LITE_BASE_ADDRESS}overseas-integration/publish-callback`,
        maxRetries: 3,
        credentials: task.account.credentials
      }))

      // 发送到香港服务
      await this.sendTasksToOverseasService(publishTasks)

      this.logger.log(`海外平台任务发送完成: ${publishTasks.length} 个任务`)
    } catch (error) {
      this.logger.error(`处理海外平台发布任务失败: taskSetId=${taskSetId}`, error)
    }
  }

  /**
   * 检查是否有海外平台账号
   */
  async hasOverseasAccounts(platformAccountIds: string[]): Promise<boolean> {
    const count = await this.platformAccountModel.countDocuments({
      _id: { $in: platformAccountIds.map(id => new Types.ObjectId(id)) },
      platformType: PlatformType.海外平台
    })
    return count > 0
  }

  /**
   * 发送任务到香港服务
   */
  private async sendTasksToOverseasService(tasks: OverseasPublishTaskData[]): Promise<void> {
    try {
      const overseasBaseUrl = process.env.OVERSEAS_BASE_ADDRESS

      // 发送到香港服务
      setImmediate(async () => {
        try {
          await axios.post(`${overseasBaseUrl}content-publish/tasks/batch`, {
            tasks: tasks
          }, {
            timeout: 30000,
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'GatewayService/1.0'
            }
          })
          this.logger.log(`任务发送到海外服务成功: ${tasks.length} 个任务`)
        } catch (error) {
          this.logger.error(`任务发送到海外服务失败`, error)
          // TODO: 实现重试机制或错误处理
        }
      })
    } catch (error) {
      this.logger.error(`准备发送任务到海外服务失败`, error)
    }
  }

  /**
   * 转换内容格式为海外平台格式
   */
  private transformContentForOverseas(publishArgs: any, publishType: string): any {
    // 根据发布类型转换内容格式
    const content: any = {
      type: this.mapPublishTypeToContentType(publishType)
    }

    if (publishArgs) {
      // 处理不同类型的内容
      if (publishArgs.articles && publishArgs.articles.length > 0) {
        // 微信公众号格式
        const article = publishArgs.articles[0]
        content.title = article.title
        content.text = article.content
        content.description = article.digest
        if (article.thumbUrl) {
          content.images = [article.thumbUrl]
        }
      } else {
        // 其他格式
        content.text = publishArgs.desc || publishArgs.content || publishArgs.text || publishArgs.title
        content.title = publishArgs.title
        content.description = publishArgs.description || publishArgs.desc

        // 处理图片
        if (publishArgs.cover) {
          content.images = [publishArgs.cover]
        }
        if (publishArgs.images && Array.isArray(publishArgs.images)) {
          content.images = publishArgs.images
        }

        // 处理视频
        if (publishArgs.video) {
          content.videoUrl = publishArgs.video
          content.videoCover = publishArgs.cover
        }

        // 处理标签
        if (publishArgs.tags && Array.isArray(publishArgs.tags)) {
          content.tags = publishArgs.tags
        }
      }
    }

    // 确保至少有一些内容
    if (!content.text && !content.title && !content.description) {
      content.text = '海外平台发布内容'
    }

    return content
  }

  /**
   * 映射发布类型到内容类型
   */
  private mapPublishTypeToContentType(publishType: string): string {
    switch (publishType) {
      case 'verticalVideo':
      case 'horizontalVideo':
        return 'video'
      case 'image':
      case 'multipleImages':
        return 'image'
      case 'article':
      case 'text':
        return 'text'
      default:
        return 'mixed'
    }
  }
}
