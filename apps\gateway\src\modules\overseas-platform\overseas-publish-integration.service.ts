import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import axios from 'axios'
import {
  TaskEntity,
  PlatformAccountEntity
} from '@yxr/mongo'
import {
  PlatformType,
  TianyiyunOssService
} from '@yxr/common'

export interface OverseasPublishTaskData {
  taskId: string
  taskSetId: string
  teamId: string
  userId: string
  accountOpenId: string
  platform: string
  content: any
  callbackUrl: string
  maxRetries?: number
  credentials?: any
}

@Injectable()
export class OverseasPublishIntegrationService {
  private readonly logger = new Logger(OverseasPublishIntegrationService.name)

  constructor(
    @InjectModel(TaskEntity.name) private readonly taskModel: Model<TaskEntity>,
    @InjectModel(PlatformAccountEntity.name) private readonly platformAccountModel: Model<PlatformAccountEntity>,
    private readonly tianyiyunOssService: TianyiyunOssService
  ) {}

  /**
   * 处理海外平台发布任务
   * 在现有发布流程中调用此方法，识别海外平台账号并发送到香港服务
   */
  async handleOverseasPublishTasks(taskSetId: string): Promise<void> {
    this.logger.log(`处理海外平台发布任务: taskSetId=${taskSetId}`)

    try {
      // 查找该任务集中的海外平台任务
      const overseasTasks = await this.taskModel.aggregate([
        // 匹配任务
        { $match: { taskSetId: taskSetId } },

        // 关联账号
        { $lookup: { from: 'platformaccountentities', localField: 'platformAccountId', foreignField: '_id', as: 'account' } },
        { $unwind: '$account' },
        { $match: { 'account.platformType': PlatformType.海外平台 } }, // 仅保留海外平台

        // 关联内容
        { $lookup: { from: 'contententities', localField: 'contentId', foreignField: '_id', as: 'content' } },
        { $unwind: '$content' },

        // 关联任务集
        { $lookup: { from: 'tasksetentities', localField: 'taskSetId', foreignField: 'taskIdentityId', as: 'taskSet' } },
        { $unwind: '$taskSet' },

        // 字段筛选
        { $project: {
          taskSetId: 1,
          teamId: 1,
          userId: 1,          
          'account._id': 1,
          'account.platformName': 1,
          'account.platformAuthorId': 1,
          'account.credentials': 1,
          'content._id': 1,
          'content.publishType': 1,
          'taskSet.publishArgs': 1
        } }
      ])

      console.log('overseasTasks', overseasTasks)

      if (overseasTasks.length === 0) {
        this.logger.log(`没有找到海外平台任务: taskSetId=${taskSetId}`)
        return
      }

      // 构建发布任务数据(这里可能还需要逐步完善)
      const publishTasks: OverseasPublishTaskData[] = await Promise.all(
        overseasTasks.map(async task => ({
          taskId: task._id.toString(),
          taskSetId: task.taskSetId,
          teamId: task.teamId.toString(),
          userId: task.userId.toString(),
          accountOpenId: task.account.platformAuthorId,
          platform: task.account.platformName.toLowerCase(),
          content: await this.transformContentForOverseas(task.taskSet.publishArgs || {}, task.content.publishType),
          callbackUrl: `${process.env.LITE_BASE_ADDRESS}overseas-integration/publish-callback`,
          maxRetries: 3,
          credentials: task.account.credentials
        }))
      )

      // 发送到香港服务
      console.log('publishTasks', publishTasks)
      this.logger.log(`准备发送 ${publishTasks.length} 个海外发布任务`)
      
      // 记录每个任务的详细信息用于调试
      publishTasks.forEach((task, index) => {
        this.logger.log(`任务 ${index + 1}:`, {
          taskId: task.taskId,
          platform: task.platform,
          accountOpenId: task.accountOpenId,
          contentType: task.content.type,
          hasCredentials: !!task.credentials
        })
      })
      
      
      await this.sendTasksToOverseasService(publishTasks)

      this.logger.log(`海外平台任务发送完成: ${publishTasks.length} 个任务`)
    } catch (error) {
      this.logger.error(`处理海外平台发布任务失败: taskSetId=${taskSetId}`, error)
    }
  }

  /**
   * 检查是否有海外平台账号
   */
  async hasOverseasAccounts(platformAccountIds: string[]): Promise<boolean> {
    const count = await this.platformAccountModel.countDocuments({
      _id: { $in: platformAccountIds.map(id => new Types.ObjectId(id)) },
      platformType: PlatformType.海外平台
    })
    return count > 0
  }

  /**
   * 发送任务到香港服务
   */
  private async sendTasksToOverseasService(tasks: OverseasPublishTaskData[]): Promise<void> {
    try {
      const overseasBaseUrl = process.env.OVERSEAS_BASE_ADDRESS
      
      if (!overseasBaseUrl) {
        this.logger.error('OVERSEAS_BASE_ADDRESS 环境变量未配置')
        return
      }

      this.logger.log(`准备发送 ${tasks.length} 个任务到海外服务: ${overseasBaseUrl}`)

      // 直接发送请求，不使用 setImmediate
      const response = await axios.post(`${overseasBaseUrl}content-publish/tasks/batch`, {
        tasks: tasks
      }, {
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GatewayService/1.0'
        }
      })

      this.logger.log(`任务发送到海外服务成功: ${tasks.length} 个任务, 响应状态: ${response.status}`)
      
      // 记录响应数据用于调试
      if (response.data) {
        this.logger.log(`海外服务响应: ${JSON.stringify(response.data)}`)
      }
    } catch (error) {
      this.logger.error(`任务发送到海外服务失败`, {
        error: error.message,
        stack: error.stack,
        tasksCount: tasks.length,
        overseasBaseUrl: process.env.OVERSEAS_BASE_ADDRESS
      })
      
      // 实现简单的重试机制
      await this.retrySendTasksToOverseasService(tasks, 1)
    }
  }

  /**
   * 重试发送任务到海外服务
   */
  private async retrySendTasksToOverseasService(tasks: OverseasPublishTaskData[], retryCount: number): Promise<void> {
    const maxRetries = 3
    const retryDelay = 5000 // 5秒

    if (retryCount > maxRetries) {
      this.logger.error(`重试次数已达上限，放弃发送任务到海外服务`)
      return
    }

    this.logger.log(`第 ${retryCount} 次重试发送任务到海外服务`)

    setTimeout(async () => {
      try {
        const overseasBaseUrl = process.env.OVERSEAS_BASE_ADDRESS
        const response = await axios.post(`${overseasBaseUrl}content-publish/tasks/batch`, {
          tasks: tasks
        }, {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'GatewayService/1.0'
          }
        })

        this.logger.log(`重试成功，任务发送到海外服务: ${tasks.length} 个任务`)
      } catch (error) {
        this.logger.error(`重试失败，第 ${retryCount} 次`, error.message)
        await this.retrySendTasksToOverseasService(tasks, retryCount + 1)
      }
    }, retryDelay)
  }

  /**
   * 转换内容格式为海外平台格式
   */
  private async transformContentForOverseas(publishArgs: any, publishType: string): Promise<any> {
    console.log('publishArgs', publishArgs)

    // 根据发布类型转换内容格式
    const content: any = {
      type: this.mapPublishTypeToContentType(publishType)
    }

    if (publishArgs) {
      // 处理不同类型的内容
      if (publishArgs.articles && publishArgs.articles.length > 0) {
        // 微信公众号格式
        const article = publishArgs.articles[0]
        content.title = article.title
        content.text = article.content
        content.description = article.digest
        if (article.thumbUrl) {
          content.images = [article.thumbUrl]
        }
      } else {
        // 其他格式
        content.text = publishArgs.desc || publishArgs.content || publishArgs.text || publishArgs.title
        content.title = publishArgs.title
        content.description = publishArgs.description || publishArgs.desc

        // 处理图片 - 转换天翼云 key 为可访问 URL
        if (publishArgs.cover) {
          try {
            const coverUrl = await this.tianyiyunOssService.getOssAccessUrl(publishArgs.cover, false)
            content.images = [coverUrl]
          } catch (error) {
            this.logger.warn(`转换封面图片失败: ${publishArgs.cover}`, error)
            content.images = [publishArgs.cover] // 降级使用原始值
          }
        }
        if (publishArgs.images && Array.isArray(publishArgs.images)) {
          try {
            const imageUrls = await Promise.all(
              publishArgs.images.map(async (imageKey: string) => {
                try {
                  return await this.tianyiyunOssService.getOssAccessUrl(imageKey, false)
                } catch (error) {
                  this.logger.warn(`转换图片失败: ${imageKey}`, error)
                  return imageKey // 降级使用原始值
                }
              })
            )
            content.images = imageUrls
          } catch (error) {
            this.logger.warn('批量转换图片失败', error)
            content.images = publishArgs.images // 降级使用原始值
          }
        }

        // 处理视频 - 转换天翼云 key 为可访问 URL
        if (publishArgs.video) {
          try {
            const videoUrl = await this.tianyiyunOssService.getOssAccessUrl(publishArgs.video, false)
            content.videoUrl = videoUrl
          } catch (error) {
            this.logger.warn(`转换视频失败: ${publishArgs.video}`, error)
            content.videoUrl = publishArgs.video // 降级使用原始值
          }
        }
        
        // 处理视频封面
        if (publishArgs.cover && content.videoUrl) {
          content.videoCover = content.images?.[0] || publishArgs.cover
        }

        // 处理标签
        if (publishArgs.tags && Array.isArray(publishArgs.tags)) {
          content.tags = publishArgs.tags
        }
      }
    }

    // 确保至少有一些内容
    if (!content.text && !content.title && !content.description) {
      content.text = '海外平台发布内容'
    }

    // 记录转换后的内容格式用于调试
    this.logger.log(`内容转换完成:`, {
      type: content.type,
      hasText: !!content.text,
      hasTitle: !!content.title,
      hasDescription: !!content.description,
      imagesCount: content.images?.length || 0,
      hasVideo: !!content.videoUrl,
      hasVideoCover: !!content.videoCover,
      tagsCount: content.tags?.length || 0
    })

    return content
  }

  /**
   * 映射发布类型到内容类型
   */
  private mapPublishTypeToContentType(publishType: string): string {
    switch (publishType) {
      case 'verticalVideo':
      case 'horizontalVideo':
        return 'video'
      case 'image':
      case 'multipleImages':
        return 'image'
      case 'article':
      case 'text':
        return 'text'
      default:
        return 'mixed'
    }
  }
}
