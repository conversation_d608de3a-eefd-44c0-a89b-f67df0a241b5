import { Controller, Get } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { OverviewService } from './overview.service'
import { HomeOverviewResponseDTO, HomeOverviewTrendsResponseDTO } from './overview.dto'

@Controller('overview')
@ApiTags('概览数据管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ type: BaseBadRequestResponseDTO, description: '参数错误' })
@ApiHeader({ name: 'authorization', required: true })
export class OverviewController {
  constructor(private readonly overviewService: OverviewService) {}

  @Get('home')
  @ApiOperation({ summary: '获取首页概览数据' })
  @ApiOkResponse({ type: HomeOverviewResponseDTO })
  async getHomeOverview() {
    return await this.overviewService.getHomeOverview()
  }

  @Get('home/trends')
  @ApiOperation({ summary: '获取首页概览趋势数据' })
  @ApiOkResponse({ type: HomeOverviewTrendsResponseDTO })
  async getHomeOverviewTrends() {
    return await this.overviewService.getHomeOverviewTrends()
  }
}
