import { Injectable, Logger, Scope } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { OverseasContext } from '../types'
import { createOverseasAxiosInstance, InterceptorConfig } from '../../utils/axios-config'
import { createTikTokBusinessErrorChecker } from './tiktok-error-handler'
import { safeApiCall, createPlatformSpecificAxiosInstance/*, checkBusinessError*/ } from '../utils'
import {
  TikTokApiResponse,
  TikTokOAuth2TokenResponse,
  TikTokAccountOverviewResponse,
  TikTokVideoUploadResponse,
  TikTokVideoInfoResponse,
  TikTokVideoDeleteResponse,
  TikTokVideoUploadParams,
  TikTokOAuth2Params,
  TikTokApiEndpoints,
  TikTokContext
} from './tiktok-api-types'

@Injectable({ scope: Scope.TRANSIENT })
export class TiktokApi {
  logger = new Logger(TiktokApi.name)

  private readonly clientKey = process.env.TIKTOK_CLIENT_KEY

  private readonly clientSecret = process.env.TIKTOK_CLIENT_SECRET

  /**
   * 创建TikTok专用的axios实例
   */
  private createTikTokAxiosInstance(context: OverseasContext): AxiosInstance {
    const tiktokContext: TikTokContext = {
      ...context,
      platform: 'tiktok'
    } as TikTokContext

    const interceptorConfig: InterceptorConfig = {
      context: tiktokContext,
      businessErrorChecker: createTikTokBusinessErrorChecker(),
      enableRetry: true,
      enableBusinessErrorCheck: true,
      retryConfig: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000
      }
    }

    return createOverseasAxiosInstance(
      'https://business-api.tiktok.com',
      interceptorConfig
    )
  }

  async oauth2_token(context: OverseasContext, params: TikTokOAuth2Params): Promise<TikTokApiResponse<TikTokOAuth2TokenResponse>> {
    const axios = this.createTikTokAxiosInstance(context)

    const response = await axios.post<TikTokApiResponse<TikTokOAuth2TokenResponse>>(
      TikTokApiEndpoints.OAuth2Token,
      {
        client_id: this.clientKey,
        client_secret: this.clientSecret,
        grant_type: 'authorization_code',
        ...params
      },
      {
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        }
      }
    )

    return response.data
  }

  /**
   * 获取 TikTok 账号主页数据
   */
  async getAccountOverview(
    context: OverseasContext,
    accessToken: string,
    openId: string,
    options?: {
      start_date?: string
      end_date?: string
      fields?: string[]
    }
  ): Promise<TikTokApiResponse<TikTokAccountOverviewResponse>> {
    const axios = this.createTikTokAxiosInstance(context)

    const url = new URL('https://business-api.tiktok.com/open_api/v1.3/business/get/')
    url.searchParams.set('business_id', openId)
    if (options?.start_date) url.searchParams.set('start_date', options.start_date)
    if (options?.end_date) url.searchParams.set('end_date', options.end_date)
    if (options?.fields) url.searchParams.set('fields', `[${options.fields.map((f) => `"${f}"`).join(',')}]`)

    const response = await axios.get<TikTokApiResponse<TikTokAccountOverviewResponse>>(
      url.toString(),
      {
        headers: { 'Access-Token': accessToken }
      }
    )

    return response.data
  }




  /**
   * 上传视频到TikTok
   * https://business-api.tiktok.com/portal/docs?id=****************
   */
  async uploadVideo(context: OverseasContext, params: TikTokVideoUploadParams) {
    const accessToken = this.getAccessTokenFromContext(context)
    const openId = this.getOpenIdFromContext(context)
    const tiktokContext = { ...context, platform: 'tiktok' }

    console.log('上传视频到TikTok', params)

    return await safeApiCall(
      tiktokContext,
      async () => {
        const axios = createPlatformSpecificAxiosInstance('https://business-api.tiktok.com', tiktokContext)
        const response = await axios.post<TikTokApiResponse<TikTokVideoUploadResponse>>('/open_api/v1.3/business/video/publish/', {
          business_id: openId,
          video_url: params.video_url,
          post_info: {
            /** 视频文案/描述 - 包含自有 TikTok 账号的（互关） 朋友的话题标签（#hashtags）和提及（@mentions）。 */
            caption: params.caption,

            /** 是否为该视频开启原生品牌内容开关 */
            is_brand_organic: params.is_brand_organic || false,

            /** 是否为该视频开启品牌内容开关 */
            is_branded_content: params.is_branded_content || false,

            /** 是否禁止对所发布的视频帖子发表评论 */
            disable_comment: params.disable_comment || false,

            /** 是否禁止对所发布的视频帖子进行合拍 */
            disable_duet: params.disable_duet || false,

            /** 是否禁止对所发布的视频帖子进行拼接 */
            disable_stitch: params.disable_stitch || false,

            /** 用于选择所发布视频中的一帧作为视频封面的设置，格式为以毫秒为单位的时间戳 */
            thumbnail_offset: params.thumbnail_offset || false,

            /** 是否为视频帖子启用 “AI 生成的内容” 开关 */
            is_ai_generated: params.is_ai_generated || false,

            /** 发布视频的隐私级别: PUBLIC_TO_EVERYONE(所有人), MUTUAL_FOLLOW_FRIENDS(好友, 互关), SELF_ONLY(仅自己) */
            privacy_level: params.privacy_level || 'PUBLIC_TO_EVERYONE',

            /** 是否将帖子上传为草稿 */
            upload_to_draft: params.upload_to_draft || false
          }
        }, {
          headers: {
            'Access-Token': accessToken,
            'Content-Type': 'application/json; charset=UTF-8'
          }
        })

        console.log('上传视频响应', response.data)

        // 检查TikTok特有的业务错误（code !== 0）
        // await checkBusinessError(response, tiktokContext, (data) => data.code !== 0)

        return response
      }
    )
  }

  /**
   * 获取视频信息
   */
  async getVideoInfo(context: OverseasContext, videoId: string, access_token?: string): Promise<TikTokApiResponse<TikTokVideoInfoResponse>> {
    const accessToken = access_token || this.getAccessTokenFromContext(context)
    const tiktokContext = { ...context, platform: 'tiktok' }

    return await safeApiCall(
      tiktokContext,
      async () => {
        const axios = createPlatformSpecificAxiosInstance('https://business-api.tiktok.com', tiktokContext)
        const response = await axios.get<TikTokApiResponse<TikTokVideoInfoResponse>>(`/open_api/v1.3/post/list/`, {
          params: {
            fields: JSON.stringify([
              'video_id',
              'share_url',
              'embed_link',
              'unique_id',
              'title',
              'video_description',
              'duration',
              'cover_image_url',
              'create_time',
              'like_count',
              'comment_count',
              'share_count',
              'view_count'
            ]),
            cursor: 0,
            max_count: 1,
            video_id: videoId
          },
          headers: {
            'Access-Token': accessToken
          }
        })

        // 检查TikTok特有的业务错误（code !== 0）
        // await checkBusinessError(response, tiktokContext, (data) => data.code !== 0)

        return response
      }
    )
  }

  /**
   * 删除视频
   */
  async deleteVideo(context: OverseasContext, videoId: string, access_token?: string): Promise<TikTokApiResponse<TikTokVideoDeleteResponse>> {
    const accessToken = access_token || this.getAccessTokenFromContext(context)
    const tiktokContext = { ...context, platform: 'tiktok' }

    return await safeApiCall(
      tiktokContext,
      async () => {
        const axios = createPlatformSpecificAxiosInstance('https://business-api.tiktok.com', tiktokContext)
        const response = await axios.post<TikTokApiResponse<TikTokVideoDeleteResponse>>('/open_api/v1.3/post/delete/', {
          video_id: videoId
        }, {
          headers: {
            'Access-Token': accessToken,
            'Content-Type': 'application/json; charset=UTF-8'
          }
        })

        // 检查TikTok特有的业务错误（code !== 0）
        // await checkBusinessError(response, tiktokContext, (data) => data.code !== 0)

        return response
      }
    )
  }

  /**
   * 从上下文中获取访问令牌
   */
  private getAccessTokenFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.access_token) {
      throw new Error('缺少TikTok访问令牌')
    }
    return credentials.access_token
  }

  /**
   * 从上下文中获取OpenID
   */
  private getOpenIdFromContext(context: OverseasContext): string {
    const open_id = context.accountOpenId
    if (!open_id) {
      throw new Error('缺少TikTok OpenID')
    }
    return open_id
  }
}
