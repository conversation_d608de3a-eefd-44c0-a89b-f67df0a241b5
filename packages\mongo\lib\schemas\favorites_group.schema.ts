import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class FavoritesGroupEntity {
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  name: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const FavoritesGroupSchema: ModelDefinition = {
  name: FavoritesGroupEntity.name,
  schema: SchemaFactory.createForClass(FavoritesGroupEntity)
}

export const FavoritesGroupMongoose = MongooseModule.forFeature([FavoritesGroupSchema])
