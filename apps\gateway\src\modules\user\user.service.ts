import {
  Injectable,
  Inject,
  Logger,
  BadRequestException,
  HttpException,
  NotFoundException,
  ForbiddenException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { ConfigService } from '@nestjs/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { REQUEST } from '@nestjs/core'
import Dysmsapi20170525, * as $Dysmsapi20170525 from '@alicloud/dysmsapi20170525'
import * as $OpenApi from '@alicloud/openapi-client'
import * as Util from '@alicloud/tea-util'
import { type FastifyRequest } from 'fastify'
import { Model, Types, UpdateQuery } from 'mongoose'
import crypto from 'crypto'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { nanoid, customAlphabet } from 'nanoid'
import { ChannelEntity, MemberEntity, TeamEntity, UserEntity } from '@yxr/mongo'
import { RootConfigMap } from '@yxr/config'
import {
  changePhoneRequestDto,
  patchUserRequestBodyDTO,
  resetPasswordRequestDto,
  SMSCodeSence,
  UserInfoResponseDTO,
  UserLoginRegisterRequestBodyDTO,
  UserSendCodeRequestBodyDTO,
  UserLoginResphoneDTO,
  TokenValidateRequestBodyDTO
} from './user.dto'
import { generateRandomCode } from '../../common/utils'
import { MemberStatusEnum, TeamFeatures, TeamRoleNames, UserFeatures } from '@yxr/common'
import * as process from 'node:process'
import { genSocketRedisKey, getTeamOnlineUsersRedisKey } from '@yxr/utils'
import { UserDevicesService } from './user-devices.service'
import { TosService } from '@yxr/huoshan'
import { OrderManagerService } from '@yxr/order'
import { WebhookEvents } from '../webhook/constant'
import { WebhookService } from '../webhook/webhook.service'

@Injectable()
export class UserService {
  logger = new Logger('UserService')

  constructor(
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(ChannelEntity.name) private channelModel: Model<ChannelEntity>,
    private readonly ossService: TosService,
    private readonly userDevicesService: UserDevicesService,
    private readonly orderManagerService: OrderManagerService,
    private readonly webhookService: WebhookService,
    @Inject(REQUEST) private request: FastifyRequest,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>
  ) {}

  async tokenValidate(body: TokenValidateRequestBodyDTO):Promise<Boolean> {
    const result = await this.cacheManager.store.client.get(`session:au-${body.token}`)
    if(result){
      return true
    }
    return false
  }

  /**
   * 用户登录
   * @param param0
   * @returns
   */
  async putLoginUser(body: UserLoginRegisterRequestBodyDTO): Promise<UserLoginResphoneDTO> {
    let result: UserLoginResphoneDTO
    if (body.code) {
      //手机号登陆
      result = await this.phoneLogin(body)
    } else {
      //密码登陆
      result = await this.passwordLogin(body)
    }

    await this.userDevicesService.putUserDevices(body.phone, body.deviceId)
    return result
  }

  async phoneLogin(body: UserLoginRegisterRequestBodyDTO) {
    const phone = body.phone
    const code = body.code
    // 先校验验证码是否匹配
    if (phone !== '18800000888') {
      const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.Auth}`)
      if (!phoneValid || phoneValid !== phone) {
        throw new BadRequestException('验证码无效')
      }
    }
    // 根据手机号码查找用户
    let user = await this.userModel.findOne({
      phone: phone
    })

    let latestTeamId: string
    if (user === null) {
      //注册渠道绑定
      let channelInfo = null
      if (body.channel) {
        channelInfo = await this.channelModel.findOne({
          channelCode: body.channel,
          enabled: true
        })
      }
      const session = await this.userModel.db.startSession()
      session.startTransaction()
      try {
        const nickname = this.GenerateRandomNickName()
        const avatar = this.GenerateRandomAvatarUrl()

        // TODO: 当团队数量越来越多时, 会有比较大的概览出现CODE冲突, 需要更好的技术机制来确保 code 唯一
        const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 6)
        const code = nanoid()

        // 创建默认团队
        const teams = await this.teamModel.create<TeamEntity>(
          [
            {
              name: '未命名团队',
              logo: 'avatars/team-default.png',
              code: code,
              accountCountLimit: TeamFeatures.DefaultAccountCountLimit, // 变更为账号点数
              accountCapacityLimit: TeamFeatures.DefaultAccountCapacityLimit,
              accountCount: 0, // 变更为账号点数
              accountCapacity: 0,
              memberCountLimit: TeamFeatures.DefaultMemberCountLimit,
              memberCount: 1,
              enabled: true,
              isVip: false,
              isDeleted: false,
              source: 'gateway'
            }
          ],
          { session }
        )

        latestTeamId = teams[0].id
        const platform = this.request.client?.platform ?? 'other'
        const users = await this.userModel.create<UserEntity>(
          [
            {
              phone: phone,
              nickName: nickname,
              avatar: avatar,
              latestTeamId: new Types.ObjectId(latestTeamId),
              registrationSource: platform,
              channelCode: channelInfo ? channelInfo.channelCode : null,
              source: 'gateway'
            }
          ],
          { session }
        )

        // 创建默认成员关系
        await this.memberModel.create<MemberEntity>(
          [
            {
              userId: new Types.ObjectId(users[0]._id),
              teamId: new Types.ObjectId(teams[0]._id),
              roles: [TeamRoleNames.MASTER],
              remark: nickname,
              accounts: [],
              status: MemberStatusEnum.Joined
            }
          ],
          { session }
        )

        await session.commitTransaction()
        user = users[0]
      } catch (error) {
        await session.abortTransaction()

        // 这里最后可能出现异常的目前就是团队编码的重复冲突, 团队数量不大时, 概率较小, 暂时简单处理
        throw new HttpException('网络异常, 请稍后再试', -1)
      } finally {
        await session.endSession()
      }

      if (channelInfo && channelInfo.giftDays > 0) {
        const orderNo = await this.orderManagerService.channelGiftOrder(
          latestTeamId,
          user.id,
          channelInfo.channelCode,
          channelInfo.giftDays,
          '渠道包注册赠送'
        )
        await this.orderManagerService.handleCompletedOrder(orderNo)
      }
    } else {
      latestTeamId = user.latestTeamId?.toString()
      // 正常情况下, 用户都会有 latestTeamId, 但用户被移出团队和团队解散将可能导致 latestTeamId 为 undefined
      if (latestTeamId === undefined || latestTeamId === null) {
        const members = await this.memberModel
          .find({
            userId: new Types.ObjectId(user.id),
            status: MemberStatusEnum.Joined
          })
          .select('teamId')
        const teamIds = members.map((item) => new Types.ObjectId(item.teamId))
        const team = await this.teamModel.findOne({
          isDeleted: false,
          _id: { $in: teamIds }
        })

        if (team) {
          latestTeamId = team.id
        } else {
          throw new NotFoundException('服务端异常, 找不到用户关联的团队, 用户无法登录')
        }
      }

      // 设置最后一次使用的团队Id
      user.latestTeamId = new Types.ObjectId(latestTeamId)
      await user.save()
    }

    // 用户允许登录, 生成 token
    const authorization = await this.generateAuthorization(user, user.id, latestTeamId)

    // 移除验证码缓存
    await this.cacheManager.del(`${code}:${SMSCodeSence.Auth}`)

    return {
      authorization: authorization
    }
  }

  /**
   * 用户密码登录
   * @param data
   * @returns
   */
  async passwordLogin(data: UserLoginRegisterRequestBodyDTO) {
    // 根据手机号码查找用户
    let user = await this.userModel.findOne({
      phone: data.phone
    })
    if (!user) {
      throw new NotFoundException('手机号未注册')
    }
    if (!(await this.checkErrorPassword(user.id))) {
      throw new ForbiddenException(
        `密码错误已超${UserFeatures.ErrorPasswordLoginCountLimit}次，请明日再试`
      )
    }
    let latestTeamId: string
    if (
      !user?.salt ||
      !user?.password ||
      !this.verifyPassword(data.password, user?.salt, user?.password)
    ) {
      const remaining = await this.setErrorPassword(user.id)
      if (remaining == 0) {
        throw new ForbiddenException(
          `密码错误已超${UserFeatures.ErrorPasswordLoginCountLimit}次，请明日再试`
        )
      }
      throw new ForbiddenException(`账号或密码错误，今日剩余尝试${remaining}`)
    }
    latestTeamId = user.latestTeamId?.toString()
    // 正常情况下, 用户都会有 latestTeamId, 但用户被移出团队和团队解散将可能导致 latestTeamId 为 undefined
    if (latestTeamId === undefined || latestTeamId === null) {
      const members = await this.memberModel
        .find({
          userId: new Types.ObjectId(user.id),
          status: MemberStatusEnum.Joined
        })
        .select('teamId')
      const teamIds = members.map((item) => new Types.ObjectId(item.teamId))
      const team = await this.teamModel.findOne({
        isDeleted: false,
        _id: { $in: teamIds }
      })

      if (team) {
        latestTeamId = team.id
      } else {
        throw new NotFoundException('服务端异常, 找不到用户关联的团队, 用户无法登录')
      }
    }

    // 设置最后一次使用的团队Id
    user.latestTeamId = new Types.ObjectId(latestTeamId)
    await user.save()
    // 用户允许登录, 生成 token
    const authorization = await this.generateAuthorization(user, user.id, latestTeamId)

    return {
      authorization: authorization
    }
  }

  /**
   *  生成 token 并缓存
   * @param user
   * @param userId
   * @param teamId
   */
  async generateAuthorization(user: UserEntity, userId: string, teamId: string) {
    const userIdKey = this.request.client?.name
      ? `session:ui-${userId}:${this.request.client?.name}`
      : `session:ui-${userId}`

    // 用户登录场景中 request 中是取不到 token 的, 需要使用配对的缓存键来获取
    const oldToken = await this.cacheManager.store.client.get(userIdKey)
    if (oldToken) {
      this.logger.debug(`10秒后移除旧 token: ${oldToken}`)
      await this.cacheManager.store.client.expire(oldToken, 10) // 延期删除老的token
    }
    if (this.request.client?.name === 'desktop') {
      // 客户端登录向已登录该账号发其他设备登录消息
      await this.webhookService.grpchook([userId], null, {
        event: WebhookEvents.LoginOnAnotherDevice
      })
    }
    // 生成新 token
    const token = nanoid()
    const tokenKey = `session:au-${token}`
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    await Promise.all([
      this.cacheManager.set(userIdKey, tokenKey, overdueToken),
      this.cacheManager.set(
        tokenKey,
        {
          user: user, // 保留和旧设计不合理的兼容, 当 request 中的 user 可以安全删除时, 可以删除
          userId: userId,
          teamId: teamId
        },
        overdueToken
      )
    ])

    return token
  }

  async sendVerificationCode({ phone, sence }: UserSendCodeRequestBodyDTO) {
    const {
      smsCodeTime,
      smsAccessKeyId,
      smsAccessKeySecret,
      smsEndpoint,
      smsSignName,
      smsTemplateCode
    } = this.configService.get<RootConfigMap['app']>('app')

    const realSendSms = process.env.NODE_ENV === 'prod'

    const code = generateRandomCode().toString().padStart(6, '0')

    // 验证码发送
    if (realSendSms) {
      let error = ''
      const config = new $OpenApi.Config({
        accessKeyId: smsAccessKeyId,
        accessKeySecret: smsAccessKeySecret
      })
      config.endpoint = smsEndpoint

      const dysmsapiClient = new Dysmsapi20170525(config)

      const sendSmsRequest = new $Dysmsapi20170525.SendSmsRequest({
        phoneNumbers: phone,
        signName: smsSignName,
        templateCode: smsTemplateCode,
        templateParam: `{"code":${code}}`
      })

      const runtime = new Util.RuntimeOptions({})
      try {
        const response = await dysmsapiClient.sendSmsWithOptions(sendSmsRequest, runtime)

        if (response.body.code !== 'OK') {
          if (response.body.code === 'isv.BUSINESS_LIMIT_CONTROL') {
            if (response.body.message.indexOf('分钟') > -1) {
              error = '短信发送频率太高, 请稍后再试'
            } else if (response.body.message.indexOf('时') > -1) {
              error = '短信发送频率太高, 请稍后再试'
            } else if (response.body.message.indexOf('天') > -1) {
              error = '短信发送频率太高, 请明日再试'
            } else {
              error = '短信发送频率太高, 请稍后再试'
              this.logger.warn(
                `短信发送触发云通信流控限制, 但未能识别流控级别, message: ${response.body.message}`
              )
            }
          } else {
            this.logger.warn(
              `短信发送失败, code: ${response.body.code}, message: ${response.body.message}`
            )
          }
        }
      } catch (error) {
        this.logger.error(error)
        throw new BadRequestException('验证码发送失败')
      }

      if (error !== '') {
        throw new HttpException(error, 429)
      }
    } else {
      // 在非生产环境模拟短信发送量限流异常
      if (phone === '13800138000') {
        throw new HttpException('短信发送频率太高, 请稍后再试', 429)
      }
      if (phone === '13800138001') {
        throw new HttpException('短信发送频率太高, 请明日再试', 429)
      }

      // 模拟验证码发送
      this.logger.log(`验证码发送成功, 手机号: ${phone}, 验证码: ${code}`)
    }

    // 缓存验证码
    try {
      await this.cacheManager.set(`${code}:${sence}`, phone, smsCodeTime)
    } catch (error) {
      this.logger.log(error)
    }

    if (!realSendSms) {
      return `${code}`
    }
  }

  /**
   * 退出登录
   */
  async logout() {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const userIdKey = this.request.client?.name
      ? `session:ui-${currentUserId}:${this.request.client?.name}`
      : `session:ui-${currentUserId}`

    await Promise.all([
      this.cacheManager.del(userIdKey),
      this.cacheManager.del(`session:au-${this.request.authorization}`),
      this.cacheManager.del(genSocketRedisKey(currentUserId)),
      //删除团队人员在线缓存
      this.cacheManager.store.client.zrem(getTeamOnlineUsersRedisKey(currentTeamId), currentUserId)
    ])
  }

  async getUserInfo(): Promise<UserInfoResponseDTO> {
    const { userId: currentUserId } = this.request.session

    const user = await this.userModel.findById(new Types.ObjectId(currentUserId)).exec()

    if (user === null) throw new NotFoundException('用户不存在')

    const avatarUrl = await this.ossService.getAccessSignatureUrl(user.avatar)
    return {
      id: user.id,
      avatarUrl: avatarUrl,
      avatarKey: user.avatar,
      createdAt: user.createdAt.getTime(),
      updatedAt: user.updatedAt.getTime(),
      nickName: user.nickName,
      isPassword: user?.password ? true : false,
      phone: user.phone,
      latestTeamId: user.latestTeamId?.toString()
    }
  }

  async updateUserInfo({
    nickName,
    avatarKey
  }: patchUserRequestBodyDTO): Promise<UserInfoResponseDTO> {
    const { userId: currentUserId } = this.request.session

    const update: UpdateQuery<UserEntity> = {}
    if (nickName && nickName.length > 0) update.nickName = nickName

    if (avatarKey && avatarKey.length > 0) update.avatar = avatarKey

    // 修改
    const user = await this.userModel
      .findByIdAndUpdate(new Types.ObjectId(currentUserId), update)
      .exec()

    const avatarUrl = await this.ossService.getAccessSignatureUrl(
      avatarKey && avatarKey.length > 0 ? avatarKey : user.avatar
    )
    return {
      id: user.id,
      avatarUrl: avatarUrl,
      avatarKey: user.avatar,
      createdAt: user.createdAt.getTime(),
      updatedAt: user.updatedAt.getTime(),
      nickName: nickName && nickName.length > 0 ? nickName : user.nickName,
      isPassword: user?.password ? true : false,
      phone: user.phone,
      latestTeamId: user.latestTeamId?.toString()
    }
  }

  async resetPassword(data: resetPasswordRequestDto): Promise<UserInfoResponseDTO> {
    const { userId: currentUserId } = this.request.session
    const user = await this.userModel.findById(new Types.ObjectId(currentUserId))
    if (!user) {
      throw new NotFoundException('用户不存在')
    }
    const phoneValid = await this.cacheManager.get<string>(
      `${data.code}:${SMSCodeSence.ResetPassword}`
    )
    if (!phoneValid || phoneValid !== user.phone) {
      throw new BadRequestException('验证码无效')
    }

    const result = this.validatePassword(data.password)
    if (!result) {
      throw new ForbiddenException('密码至少包含一个大写字母、一个小写字母、一位数字和一个特殊字符')
    }
    if (data.password) {
      const { salt, hash } = this.hashPassword(data.password)
      user.password = hash
      user.salt = salt
      await this.userModel.updateOne(
        {
          _id: new Types.ObjectId(currentUserId)
        },
        {
          password: hash,
          salt: salt
        }
      )
    }

    const avatarUrl = await this.ossService.getAccessSignatureUrl(user.avatar)

    return {
      id: user.id,
      avatarUrl: avatarUrl,
      avatarKey: user.avatar,
      createdAt: user.createdAt.getTime(),
      updatedAt: user.updatedAt.getTime(),
      nickName: user.nickName,
      isPassword: user?.password ? true : false,
      phone: user.phone,
      latestTeamId: user.latestTeamId?.toString()
    }
  }

  /**
   * 检测错误密码次数
   */
  async checkErrorPassword(userId: string): Promise<Boolean> {
    const counterKey = this.genErrorPassKey(userId)
    const cacheValue = (await this.cacheManager.store.client.get(counterKey)) || 0
    const currentValue = cacheValue ? parseInt(cacheValue, 10) : 0
    // 如果已超过 5 次，直接返回 false
    if (UserFeatures.ErrorPasswordLoginCountLimit - currentValue <= 0) {
      return false
    }

    return true
  }

  /**
   * 设置用户错误密码次数
   * @param teamId
   */
  async setErrorPassword(userId: string) {
    const counterKey = this.genErrorPassKey(userId)
    const currentValue = await this.cacheManager.store.client.incr(counterKey)
    // 设置每日0点重置的过期时间
    const now = new Date()
    const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
    const expireTime = midnight.getTime() - now.getTime()
    await this.cacheManager.store.client.expire(counterKey, Math.floor(expireTime / 1000))

    return UserFeatures.ErrorPasswordLoginCountLimit - currentValue
  }

  async getPhoneToken(code: string) {
    const { userId: currentUserId } = this.request.session
    const user = await this.userModel.findOne({
      _id: new Types.ObjectId(currentUserId)
    })
    if (!user) {
      throw new NotFoundException('用户不存在')
    }
    const phoneValid = await this.cacheManager.get<string>(`${code}:${SMSCodeSence.CheckPhone}`)
    if (!phoneValid || phoneValid !== user.phone) {
      throw new BadRequestException('验证码无效')
    }
    // 用户手机号允许修改, 生成 token
    const authorization = nanoid()
    const tokenKey = `phone:check-${authorization}`
    const { smsCodeTime } = this.configService.get<RootConfigMap['app']>('app')
    await Promise.all([this.cacheManager.set(tokenKey, authorization, smsCodeTime)])

    return {
      authorization: authorization
    }
  }

  async changePhone(data: changePhoneRequestDto) {
    const { userId: currentUserId } = this.request.session
    const user = await this.userModel.findOne({
      _id: new Types.ObjectId(currentUserId)
    })
    if (!user) {
      throw new NotFoundException('用户不存在')
    }
    const isBind = await this.userModel.findOne({
      phone: data.phone
    })
    if (isBind) {
      throw new ForbiddenException('无法更换，已注册的手机号')
    }
    const phoneValid = await this.cacheManager.get<string>(
      `${data.code}:${SMSCodeSence.ChangePhone}`
    )
    if (!phoneValid || phoneValid !== data.phone) {
      throw new BadRequestException('验证码无效')
    }
    const tokenKey = `phone:check-${data.authorization}`
    const token = await this.cacheManager.get<string>(tokenKey)
    if (token !== data.authorization) {
      throw new ForbiddenException('验证信息过期，请重新操作')
    }
    user.phone = data.phone
    await user.save()
    await this.cacheManager.del(tokenKey)
  }

  private genErrorPassKey(userId: string) {
    return `errorPass:counter:${userId}`
  }

  verifyPassword(password: string, salt: string, hash: string) {
    const hashToVerify = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return hash === hashToVerify
  }

  hasAlphabet(password: string): boolean {
    return /[a-zA-Z]/.test(password)
  }

  hasDigit(password: string): boolean {
    return /\d/.test(password)
  }

  validatePassword(password: string) {
    return this.hasAlphabet(password) && this.hasDigit(password)
  }

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  private GenerateRandomNickName() {
    const Adjectives = [
      '温柔',
      '内向',
      '腼腆',
      '害羞',
      '率性',
      '活泼',
      '开朗',
      '多情',
      '热情',
      '飘逸',
      '可爱',
      '慈祥',
      '老实',
      '暴躁',
      '急躁',
      '虚心',
      '勤奋',
      '热心',
      '自信',
      '任性',
      '冲动',
      '胆小',
      '安静',
      '憨厚',
      '淡定',
      '坚强',
      '火爆',
      '奔放',
      '痴情',
      '调皮',
      '捣蛋',
      '坏坏',
      '安静',
      '斯文',
      '愉快',
      '兴奋',
      '活泼',
      '幸福',
      '高兴',
      '微笑',
      '乐观',
      '深情',
      '快乐',
      '激动',
      '欢乐',
      '欢快',
      '痛苦',
      '烦恼',
      '紧张',
      '忧郁',
      '焦虑',
      '苦闷',
      '着急',
      '难过',
      '愤怒',
      '失望',
      '苦恼',
      '悲伤',
      '开心',
      '不开心',
      '无聊',
      '孤独',
      '空虚',
      '寂寞',
      '失恋',
      '单身',
      '发呆',
      '发怒',
      '失眠',
      '睡不着',
      '刚分手',
      '刚失恋',
      '曾经爱过',
      '曾深爱过',
      '伤情',
      '时尚',
      '路过',
      '飞翔',
      '行走',
      '奔跑',
      '暴走',
      '飞奔',
      '销魂',
      '火星上',
      '星星上',
      '月球上',
      '奋斗',
      '逆袭',
      '拉风',
      '咆哮',
      '很拉风',
      '要出家',
      '想出家',
      '呐喊',
      '笑点低',
      '没人理',
      '会搭讪',
      '爱搭讪',
      '私奔',
      '逃跑',
      '越狱',
      '打盹',
      '喝醉',
      '微醺',
      '求醉',
      '买醉',
      '耍酷',
      '酷酷',
      '灰常酷',
      '很酷',
      '非常酷',
      '想发财',
      '发财',
      '犯傻',
      '想旅行',
      '旅行中',
      '旅途中',
      '想表白',
      '不敢表白',
      '从未表白',
      '被表白',
      '千年单身',
      '一直单身',
      '至今单身',
      '还单身',
      '道上混',
      '玩手机',
      '不要命',
      '玩命',
      '有爱心',
      '热心肠',
      '会开车',
      '闯红灯',
      '唠叨',
      '迷茫',
      '彷徨',
      '忐忑',
      '茫然',
      '失落',
      '逃课',
      '怕考试',
      '想出国',
      '考研',
      '读研',
      '没读研',
      '爱逃课',
      '挂过科',
      '不爱学习',
      '暗恋学妹',
      '爱玩',
      '贪玩',
      '有腹肌',
      '瘦瘦',
      '小眼睛',
      '眼睛小',
      '鼻子大',
      '大鼻子',
      '眉毛粗',
      '粗眉毛',
      '帅气',
      '帅呆',
      '好帅',
      '近视',
      '跑龙套',
      '打酱油',
      '八块腹肌',
      '一身肌肉',
      '满身肌肉',
      '没有腹肌',
      '爱听歌',
      '爱跑步',
      '爱看球',
      '玩滑板',
      '爱看书',
      '爱热闹',
      '爱吹牛',
      '健身',
      '爱健身',
      '阳光',
      '帅气',
      '温暖',
      '绅士',
      '礼貌',
      '宽容',
      '大气',
      '爱笑',
      '温柔',
      '不羁',
      '追风',
      '完美',
      '耍酷',
      '魁梧',
      '睿智',
      '深沉',
      '稳重',
      '豪爽',
      '低调',
      '淡定',
      '活泼',
      '狂野',
      '开朗',
      '安静',
      '高大',
      '仗义',
      '正直',
      '博学',
      '爽快',
      '直爽',
      '果断',
      '豁达',
      '沉着',
      '儒雅',
      '冷静',
      '从容',
      '谦逊',
      '精明',
      '干练',
      '机灵',
      '聪明',
      '健壮',
      '阳刚',
      '慷慨',
      '善良',
      '坚强',
      '乐观',
      '心软',
      '刚毅',
      '俊逸',
      '俊秀',
      '严肃',
      '飘逸',
      '成熟',
      '沉稳',
      '谦和',
      '坚韧',
      '憨厚',
      '老实',
      '含蓄',
      '文雅',
      '大方',
      '强悍',
      '强健',
      '高大',
      '深情',
      '长情',
      '踏实',
      '痴情',
      '体贴',
      '细心',
      '任性',
      '独立',
      '个性',
      '另类',
      '腹黑',
      '腼腆',
      '纯真',
      '酷酷',
      '怕老婆',
      '冷冷',
      '听话',
      '乖乖',
      '卖萌',
      '逆袭',
      '叛逆',
      '鬼畜',
      '无邪',
      '傻傻',
      '逼格高',
      '性感',
      '坏坏',
      '留胡子',
      '小胡子',
      '英俊',
      '潇洒',
      '风流',
      '大力',
      '爱运动',
      '爱旅游',
      '打篮球',
      '踢足球',
      '玩篮球',
      '玩足球',
      '霸气',
      '豪气',
      '威武',
      '重情义',
      '讲道义',
      '重感情',
      '爱喝酒',
      '酒量大',
      '酒量小',
      '骑白马',
      '风流倜傥',
      '玉树临风',
      '神勇威武',
      '文武双全',
      '力能扛鼎',
      '刀枪不入',
      '侠义非凡',
      '谦虚好学',
      '聪明伶俐',
      '慷慨大方',
      '有情有义',
      '有胆有识',
      '谈吐大方',
      '风度翩翩',
      '气势凌人',
      '气宇轩昂',
      '英勇无比',
      '千杯不醉',
      '坐怀不乱',
      '知识渊博',
      '才高八斗',
      '傲视众生',
      '光明磊落',
      '文质彬彬',
      '面冷心慈',
      '豪情万千',
      '温文尔雅',
      '年轻有为',
      '英姿勃勃',
      '朝气蓬勃',
      '不拘小节',
      '胡子拉碴',
      '阳光',
      '帅气',
      '温暖',
      '绅士',
      '礼貌',
      '宽容',
      '大气',
      '爱笑',
      '温柔',
      '不羁',
      '追风',
      '完美',
      '耍酷',
      '魁梧',
      '睿智',
      '深沉',
      '稳重',
      '豪爽',
      '低调',
      '淡定',
      '活泼',
      '狂野',
      '开朗',
      '安静',
      '高大',
      '仗义',
      '正直',
      '博学',
      '爽快',
      '直爽',
      '果断',
      '豁达',
      '沉着',
      '儒雅',
      '冷静',
      '从容',
      '谦逊',
      '精明',
      '干练',
      '机灵',
      '聪明',
      '健壮',
      '阳刚',
      '慷慨',
      '善良',
      '坚强',
      '乐观',
      '心软',
      '刚毅',
      '俊逸',
      '俊秀',
      '严肃',
      '飘逸',
      '成熟',
      '沉稳',
      '谦和',
      '坚韧',
      '憨厚',
      '老实',
      '含蓄',
      '文雅',
      '强悍',
      '闷骚',
      '大方',
      '强健',
      '高大',
      '深情',
      '长情',
      '踏实',
      '痴情',
      '体贴',
      '细心',
      '任性',
      '独立',
      '个性',
      '另类',
      '腹黑',
      '腼腆',
      '纯真',
      '酷酷',
      '怕老婆',
      '冷冷',
      '听话',
      '乖乖',
      '卖萌',
      '逆袭',
      '叛逆',
      '鬼畜',
      '无邪',
      '傻傻',
      '逼格高',
      '性感',
      '坏坏',
      '留胡子',
      '小胡子',
      '英俊',
      '潇洒',
      '风流',
      '大力',
      '爱运动',
      '爱旅游',
      '打篮球',
      '踢足球',
      '玩篮球',
      '玩足球',
      '霸气',
      '豪气',
      '威武',
      '重情义',
      '讲道义',
      '重感情',
      '爱喝酒',
      '酒量大',
      '酒量小',
      '骑白马',
      '风流倜傥',
      '玉树临风',
      '神勇威武',
      '文武双全',
      '力能扛鼎',
      '刀枪不入',
      '侠义非凡',
      '谦虚好学',
      '聪明伶俐',
      '慷慨大方',
      '有情有义',
      '有胆有识',
      '谈吐大方',
      '风度翩翩',
      '气势凌人',
      '气宇轩昂',
      '英勇无比',
      '千杯不醉',
      '坐怀不乱',
      '知识渊博',
      '才高八斗',
      '傲视众生',
      '光明磊落',
      '文质彬彬',
      '面冷心慈',
      '豪情万千',
      '温文尔雅',
      '年轻有为',
      '英姿勃勃',
      '朝气蓬勃',
      '不拘小节',
      '胡子拉碴',
      '有腹肌',
      '瘦瘦',
      '小眼睛',
      '眼睛小',
      '鼻子大',
      '大鼻子',
      '眉毛粗',
      '粗眉毛',
      '帅气',
      '帅呆',
      '好帅',
      '近视',
      '跑龙套',
      '打酱油',
      '八块腹肌',
      '一身肌肉',
      '满身肌肉',
      '没有腹肌',
      ''
    ]

    const Nouns = [
      '西红柿',
      '番茄',
      '菠萝',
      '西瓜',
      '香蕉',
      '柚子',
      '桔子',
      '橙子',
      '苹果',
      '柠檬',
      '梨子',
      '椰子',
      '葡萄',
      '甘蔗',
      '芒果',
      '木瓜',
      '柿子',
      '石榴',
      '槟榔',
      '猕猴桃',
      '蟠桃',
      '山楂',
      '香瓜',
      '甜瓜',
      '地瓜',
      '李子',
      '杨桃',
      '枇杷',
      '柑橘',
      '荔枝',
      '火龙果',
      '南瓜',
      '玉米',
      '生菜',
      '莴苣',
      '大白菜',
      '萝卜',
      '胡萝卜',
      '韭菜',
      '木耳',
      '豌豆',
      '马铃薯',
      '土豆',
      '黄瓜',
      '苦瓜',
      '洋葱',
      '芹菜',
      '蘑菇',
      '菠菜',
      '莲藕',
      '紫菜',
      '茄子',
      '香菜',
      '青椒',
      '四季豆',
      '茴香',
      '金针菇',
      '扁豆',
      '竹笋',
      '绿豆',
      '红豆',
      '黄豆',
      '毛豆',
      '黄花菜',
      '豆芽',
      '丝瓜',
      '大蒜',
      '生姜',
      '大葱',
      '香菇',
      '酱牛肉',
      '酱肘子',
      '小虾米',
      '鸡蛋',
      '鸭蛋',
      '皮蛋',
      '牛腩',
      '罐头',
      '豆腐',
      '火腿肠',
      '脆皮肠',
      '小马驹',
      '斑马',
      '山羊',
      '长颈鹿',
      '大象',
      '鸵鸟',
      '骆驼',
      '猴子',
      '松鼠',
      '蚂蚁',
      '刺猬',
      '企鹅',
      '啄木鸟',
      '小蝌蚪',
      '青蛙',
      '海龟',
      '海豚',
      '熊猫',
      '大熊猫',
      '小熊猫',
      '野马',
      '烈马',
      '奔马',
      '小狗',
      '热带鱼',
      '红金鱼',
      '金鱼',
      '仙人掌',
      '仙人球',
      '松树',
      '柳树',
      '圣诞树',
      '筷子',
      '碗',
      '勺子',
      '凳子',
      '板凳',
      '椅子',
      '电脑桌',
      '沙发',
      '台灯',
      '杯子',
      '保温杯',
      '茶壶',
      '灯泡',
      '日光灯',
      '钱包',
      '钥匙',
      '蜡烛',
      '手电筒',
      '钥匙扣',
      '热水瓶',
      '开水瓶',
      '水桶',
      '水龙头',
      '脸盆',
      '镜子',
      '火柴',
      '打火机',
      '抽屉',
      '剪刀',
      '枕头',
      '毛巾',
      '牙膏',
      '电池',
      '路灯',
      '拖把',
      '马克杯',
      '砖头',
      '鞭炮',
      '硬币',
      '水煮鱼',
      '水煮肉',
      '酸菜鱼',
      '红烧肉',
      '回锅肉',
      '紫菜汤',
      '米饭',
      '稀饭',
      '肉夹馍',
      '灌汤包',
      '小笼包',
      '馒头',
      '花卷',
      '包子',
      '油条',
      '煎饼',
      '煎饼果子',
      '牛肉面',
      '汉堡包',
      '炒饭',
      '炒粉',
      '炒面',
      '烤地瓜',
      '红薯',
      '烤红薯',
      '泡面',
      '鸡蛋面',
      '乌冬面',
      '牛肉面',
      '饺子',
      '凉面',
      '春卷',
      '羊肉串',
      '汤圆',
      '八宝粥',
      '牛排',
      '煎鸡蛋',
      '卤蛋',
      '盒饭',
      '便当',
      '花生',
      '开心果',
      '板栗',
      '核桃',
      '薯片',
      '棒棒糖',
      '吐司',
      '烤土司',
      '面包',
      '烤面包',
      '蛋挞',
      '冰淇淋',
      '冰棍',
      '雪糕',
      '饼干',
      '麦片',
      '爆米花',
      '铅笔',
      '钢笔',
      '日记本',
      '课本',
      '橡皮擦',
      '书包',
      '饭卡',
      '书签',
      '电影票',
      '草稿纸',
      '作业本',
      '草稿本',
      '签字笔',
      '啤酒',
      '红酒',
      '伏特加',
      '烈酒',
      '葡萄酒',
      '香槟',
      '汽水',
      '豆浆',
      '可乐',
      '凉茶',
      '白开水',
      '乌龙茶',
      '红茶',
      '绿茶',
      '咖啡',
      '苦咖啡',
      '茶叶',
      '咖啡豆',
      '卡布奇诺',
      '足球',
      '篮球',
      '排球',
      '羽毛球',
      '乒乓球',
      '显示器',
      '键盘',
      '数据线',
      '充电器',
      '移动电源',
      '硬盘',
      '鼠标',
      '鼠标垫',
      '投影仪',
      '充值卡',
      '火锅',
      '麻辣香锅',
      '铁板烧',
      '葫芦',
      '佛珠',
      '手链',
      '大脸猫',
      '机器人',
      '机器猫',
      '上铺',
      '创口贴',
      '伤痕',
      '伤疤',
      '手术刀',
      '饭盒',
      '楼梯',
      '楼房',
      '电梯',
      '口罩',
      '灭火器',
      '遥控器',
      '闹钟',
      '拐杖',
      '感冒药',
      '消炎药',
      '山寨机',
      '自行车',
      '小摩托',
      '单车',
      '滑板',
      '火车',
      '警车',
      '消防车',
      '围巾',
      '手套',
      '帽子',
      '风衣',
      '沙滩裤',
      '跑步鞋',
      '人字拖',
      '眼镜',
      '墨镜',
      '毛衣',
      '针织衫',
      '黑框眼镜',
      '皮带',
      '领带',
      '西装',
      '领结',
      '冲锋衣',
      '登山鞋',
      '瀑布',
      '树叶',
      '松球',
      '夕阳',
      '太阳',
      '大海',
      '高山',
      '荒野',
      '双杠',
      '单杠',
      '哑铃',
      '跑步机',
      '打火机',
      '香烟',
      '匕首',
      '小刀',
      '弓箭',
      '铁链',
      '打火机',
      '香烟',
      '匕首',
      '小刀',
      '弓箭',
      '铁链',
      '围巾',
      '手套',
      '帽子',
      '风衣',
      '沙滩裤',
      '跑步鞋',
      '人字拖',
      '眼镜',
      '墨镜',
      '毛衣',
      '针织衫',
      '黑框眼镜',
      '皮带',
      '领带',
      '西装',
      '领结',
      '冲锋衣',
      '登山鞋',
      '小马驹',
      '斑马',
      '山羊',
      '长颈鹿',
      '大象',
      '鸵鸟',
      '骆驼',
      '猴子',
      '松鼠',
      '蚂蚁',
      '刺猬',
      '企鹅',
      '啄木鸟',
      '小蝌蚪',
      '青蛙',
      '海龟',
      '海豚',
      '熊猫',
      '大熊猫',
      '小熊猫',
      '野马',
      '烈马',
      '奔马'
    ]

    const adjective = Adjectives[Math.floor(Math.random() * Adjectives.length)]
    const noun = Nouns[Math.floor(Math.random() * Nouns.length)]

    return `${adjective}的${noun}`
  }

  private GenerateRandomAvatarUrl() {
    const code = (Math.floor(Math.random() * 59) + 1).toString().padStart(2, '0')
    return `avatars/${code}.png`
  }
}
