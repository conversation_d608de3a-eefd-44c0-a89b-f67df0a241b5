MONGO_URL = mongodb://yxr-lite-prod:<EMAIL>:3717,mongoreplicaf6f3d9f31def1.mongodb.cn-shanghai.ivolces.com:3717/?authSource=yxr-lite-prod&replicaSet=rs-mongo-replica-f6f3d9f31def&retryWrites=true
REDIS_HOST = redis-shzlzakn6guw7lymz.redis.ivolces.com
REDIS_PORT = 6379
REDIS_NORMAL_DB = 66
REDIS_SYNC_DB = 67
REDIS_PASSWORD = EL1JK6EEtYHjcsf4fiKc

# 阿里云OSS下载地址
OSS_DOWNLOAD_URL= https://static-lite.yixiaoer.cn
# 火山云OSS访问密钥对
OSS_ACCESS_KEY_ID= 'AKLTYWU4MzZiYjQxYmJhNGY1NGFjMmYzNzJiNzA5NTlmMGM'
OSS_ACCESS_KEY_SECRET= 'WXpobVlXUTBObVEzWW1JNE5HUXpNbUV5WXpObFpEVTBPREpsWkRkbVlURQ=='
# 火山云SLS访问密钥对
TLS_ACCESS_KEY_ID= 'AKLTMWFmZWI1YjlmMzE2NGU4YjllOGU3OGYxMzgwNmJlMWQ'
TLS_SECRET_ACCESS_KEY= 'WlRRMFlUSXpNemxpWlRsaE5EZzBZMkV3WTJGaVpHWmtaamsyTXprelpUUQ=='
TLS_HOST= 'tls-cn-shanghai.ivolces.com'
TLS_REGION= 'cn-shanghai'
TLS_PROJECT= 'yixiaoer-lite'
TLS_LOG_TOPIC_ID= '36f3586f-5d58-4ad9-bd70-10e69edce69e'

# 阿里云校验配置
CAPTCHA_ACCESS_KEY_ID= LTAI5tG7YiLr2KVaTeHwpcDj
CAPTCHA_ACCESS_KEY_SECRET= ******************************

# 微信第三方平台
WX_THIRD_COMPONENT_APPID= wx9bc73e9a65c7b101
WX_THIRD_COMPONENT_APPSECRET= 2f54a05404f2afb6e50b5e9d69162cb6

# 消息校验Token
WX_TOKEN= yixiaoerlite

# 微信消息加解密Key
WX_ENCRYPT_DECRYPT_KEY= d1b310b4ee7a41dc942c25d7abb7757f1234123zcdf

# 第三方IPAD微信基础参数
BAIZHUN_APP_ID= 215a91165ad350a1cfc6b345486de523
BAIZHUN_APP_SECRET= ff0eacad826c30693c86253c2557296ee67e9139
BAIZHUN_HOST= https://wxapi.yixiaoer.cn

# 快代理配置参数
KUAI_ADDRESS= p221.kdltpspro.com
KUAI_SOCKET_PORT= 20818
KUAI_HTTP_PORT= 15818
KUAI_USERNAME= t13993417110966
KUAI_PASSWORD= y3k7xdx6

# 蚁小二云端发布推送地址
YIXIAOER_CLOUD_PUSH_URL= http://crawler.yixiaoer.cn:81

# 天翼云oss
OSS_TIANYIYUN_DOWNLOAD_URL= https://yixiaoer-video-cloud.changsha42.zos.ctyun.cn
OSS_TIANYIYUN_REGION= changsha42
OSS_TIANYIYUN_BUCKET= yixiaoer-video-cloud
OSS_TIANYIYUN_ACCESS_Key_ID= 1TKO4QQRBAK7SY6TR5DT
OSS_TIANYIYUN_SECRET_ACCESS_Key= aOvAWoWucOvZykLSHlrjcjMpW4skSi8RN30cDjUs
OSS_TIANYIYUN_ENDPOINT= https://changsha42.zos.ctyun.cn
# 内网终端地址
OSS_TIANYIYUN_INTERNEL_ENDPOINT= http://**********:80

# Lite Api 的BaseUrl
LITE_BASE_ADDRESS=https://www.yixiaoer.cn/api/

# 海外服务的BaseUrl
OVERSEAS_BASE_ADDRESS=https://anter-huoshanyun.yixiaoer.cn/vp/

# 海外平台代理配置
OVERSEAS_USE_PROXY=false
OVERSEAS_REQUEST_TIMEOUT=30000

# Tiktok App
TIKTOK_CLIENT_KEY=7508568233654353936
TIKTOK_CLIENT_SECRET=013510afc01c822231af7c69c856cf0ae2aeda0c

# Twitter App
TWITTER_CLIENT_ID=LS1mRTRHSEJHZ2lvaXBuV2ZZZEE6MTpjaQ
TWITTER_CLIENT_SECRET=ysuW_uXXpRvVO8y5LY70L6diCPyV0JDsus_zKGUCA1ErH-LN7u

# GoogleApis
GOOGLE_CLIENT_ID=18462053711-cfdduck0l1jvpjm9hggiiu545di47krg.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Facebook
FACEBOOK_CLIENT_ID=1426256174961372
FACEBOOK_CLIENT_SECRET=********************************

# Instagram
INSTAGRAM_CLIENT_ID=667559252708911
INSTAGRAM_CLIENT_SECRET=3ad5a7109014ea74dde6a5fc4ae9e5e5

# 超级编导 api
SUPER_API_URL=https://api.superdir.cn
