import { Provider } from '@nestjs/common'
import { OverseasPlatformNames } from '../../constants'
import { AccountAuthProvider } from '../account-auth.provider'
import { ContentPublishProvider } from '../content-publish.provider'
import { TwitterAccountAuthProvider } from './twitter-account-auth.provider'
import { TwitterContentPublishProvider } from './twitter-content-publish.provider'
import { TwitterApi } from './twitter-api'

export const providers = [
  TwitterApi,
  {
    provide: AccountAuthProvider.register_token(OverseasPlatformNames.Twitter),
    useClass: TwitterAccountAuthProvider
  },
  {
    provide: ContentPublishProvider.register_token(OverseasPlatformNames.Twitter),
    useClass: TwitterContentPublishProvider
  },
  // {
  //   provide: UserinfoService.token(Tiktok),
  //   useClass: TiktokBusinessUserinfoService
  // },
  // {
  //   provide: ContentService.token(Tiktok),
  //   useClass: TiktokBusinessContentService
  // },
  // {
  //   provide: CommentService.token(Tiktok),
  //   useClass: TiktokBusinessCommentService
  // },
  // {
  //   provide: MessageService.token(Tiktok),
  //   useClass: TiktokBusinessMessageService
  // },
  // {
  //   provide: DataRetrievalService.token(Tiktok),
  //   useClass: TiktokBusinessDataRetrievalService
  // },
  // {
  //   provide: PermissionProvider.token(Tiktok),
  //   useClass: TiktokBusinessPermissionProvider
  // },
  // {
  //   provide: WebhookProvider.token(Tiktok),
  //   useClass: TiktokBusinessWebhookProvider
  // }
] as Provider[]
