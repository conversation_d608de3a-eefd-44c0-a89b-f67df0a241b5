import { Test, TestingModule } from '@nestjs/testing'
import { getModelToken } from '@nestjs/mongoose'
import { REQUEST } from '@nestjs/core'
import { ForbiddenException, NotFoundException } from '@nestjs/common'
import { Types } from 'mongoose'
import { ApplicationService } from './application.service'
import { 
  OpenPlatformApplicationEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformAppAuthorizationEntity,
  TeamEntity,
  MemberEntity,
  TrafficBillingEntity,
  PlatformAccountEntity
} from '@yxr/mongo'
import { 
  UserType, 
  OpenPlatformRoleNames 
} from '@yxr/common'

describe('ApplicationService - Application Overview Statistics', () => {
  let service: ApplicationService
  let applicationModel: any
  let userRoleModel: any
  let teamModel: any
  let memberModel: any
  let trafficBillingModel: any
  let platformAccountModel: any

  const mockUser = {
    userId: '507f1f77bcf86cd799439011',
    userType: UserType.OPEN_PLATFORM
  }

  const mockApplication = {
    _id: new Types.ObjectId('507f1f77bcf86cd799439012'),
    name: '测试应用',
    appId: 'app_test_123456',
    userId: new Types.ObjectId(mockUser.userId)
  }

  const mockTeams = [
    {
      _id: new Types.ObjectId('507f1f77bcf86cd799439013'),
      name: '团队1',
      source: 'open_platform_app',
      sourceAppId: mockApplication._id.toString(),
      isDeleted: false,
      accountCountLimit: 1000,
      accountCount: 750,
      accumulatedTraffic: 1048576 // 1GB in KB
    },
    {
      _id: new Types.ObjectId('507f1f77bcf86cd799439014'),
      name: '团队2',
      source: 'open_platform_app',
      sourceAppId: mockApplication._id.toString(),
      isDeleted: false,
      accountCountLimit: 2000,
      accountCount: 1500,
      accumulatedTraffic: 2097152 // 2GB in KB
    }
  ]

  beforeEach(async () => {
    const mockApplicationModel = {
      findById: jest.fn()
    }

    const mockUserRoleModel = {
      findOne: jest.fn()
    }

    const mockTeamModel = {
      find: jest.fn(),
      aggregate: jest.fn()
    }

    const mockMemberModel = {
      aggregate: jest.fn()
    }

    const mockTrafficBillingModel = {
      aggregate: jest.fn()
    }

    const mockPlatformAccountModel = {
      aggregate: jest.fn()
    }

    const mockAuthorizationModel = {}
    const mockRequest = { session: mockUser }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApplicationService,
        {
          provide: getModelToken(OpenPlatformApplicationEntity.name),
          useValue: mockApplicationModel
        },
        {
          provide: getModelToken(OpenPlatformUserRoleEntity.name),
          useValue: mockUserRoleModel
        },
        {
          provide: getModelToken(OpenPlatformAppAuthorizationEntity.name),
          useValue: mockAuthorizationModel
        },
        {
          provide: getModelToken(TeamEntity.name),
          useValue: mockTeamModel
        },
        {
          provide: getModelToken(MemberEntity.name),
          useValue: mockMemberModel
        },
        {
          provide: getModelToken(TrafficBillingEntity.name),
          useValue: mockTrafficBillingModel
        },
        {
          provide: getModelToken(PlatformAccountEntity.name),
          useValue: mockPlatformAccountModel
        },
        {
          provide: REQUEST,
          useValue: mockRequest
        }
      ]
    }).compile()

    service = module.get<ApplicationService>(ApplicationService)
    applicationModel = module.get(getModelToken(OpenPlatformApplicationEntity.name))
    userRoleModel = module.get(getModelToken(OpenPlatformUserRoleEntity.name))
    teamModel = module.get(getModelToken(TeamEntity.name))
    memberModel = module.get(getModelToken(MemberEntity.name))
    trafficBillingModel = module.get(getModelToken(TrafficBillingEntity.name))
    platformAccountModel = module.get(getModelToken(PlatformAccountEntity.name))
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('getApplicationOverview', () => {
    it('should return complete application overview statistics', async () => {
      // Arrange
      const applicationId = mockApplication._id.toString()

      applicationModel.findById.mockResolvedValue(mockApplication)
      userRoleModel.findOne.mockResolvedValue({
        userId: new Types.ObjectId(mockUser.userId),
        applicationId: mockApplication._id,
        roleName: OpenPlatformRoleNames.ADMIN
      })

      teamModel.find.mockResolvedValue(mockTeams)

      // Mock账号点数统计
      teamModel.aggregate.mockResolvedValueOnce([
        {
          _id: null,
          totalAccountPoints: 3000,
          usedAccountPoints: 2250
        }
      ])

      // Mock总流量统计
      teamModel.aggregate.mockResolvedValueOnce([
        {
          _id: null,
          totalTrafficKB: 3145728 // 3GB in KB
        }
      ])

      // Mock已使用流量统计
      trafficBillingModel.aggregate.mockResolvedValueOnce([
        {
          _id: null,
          usedTrafficBytes: ********** // 1GB in bytes
        }
      ])

      // Mock用户统计
      memberModel.aggregate.mockResolvedValue([
        { totalUsers: 25 }
      ])

      // Mock每日流量使用统计
      trafficBillingModel.aggregate.mockResolvedValueOnce([
        {
          _id: '2023-12-01',
          usedTraffic: ********* // 0.5GB in bytes
        }
      ])

      // Mock每日账号新增统计
      platformAccountModel.aggregate.mockResolvedValue([
        {
          _id: '2023-12-01',
          newAccountPoints: 10
        }
      ])

      // Act
      const result = await service.getApplicationOverview(applicationId)

      // Assert
      expect(result).toEqual({
        totalAccountPoints: 3000,
        usedAccountPoints: 2250,
        totalTraffic: 3.0, // 3GB
        usedTraffic: 1.0, // 1GB
        totalUsers: 25,
        totalTeams: 2,
        dailyStats: expect.arrayContaining([
          expect.objectContaining({
            date: expect.any(String),
            newTraffic: 0,
            usedTraffic: expect.any(Number),
            newAccountPoints: 0,
            usedAccountPoints: expect.any(Number)
          })
        ])
      })

      expect(result.dailyStats).toHaveLength(30) // 近30天数据
    })

    it('should throw NotFoundException when application does not exist', async () => {
      // Arrange
      const applicationId = 'nonexistent-id'
      applicationModel.findById.mockResolvedValue(null)

      // Act & Assert
      await expect(service.getApplicationOverview(applicationId)).rejects.toThrow(NotFoundException)
      await expect(service.getApplicationOverview(applicationId)).rejects.toThrow('应用不存在')
    })

    it('should throw ForbiddenException when user has no permission', async () => {
      // Arrange
      const applicationId = mockApplication._id.toString()

      applicationModel.findById.mockResolvedValue(mockApplication)
      userRoleModel.findOne.mockResolvedValue(null) // 无权限

      // Act & Assert
      await expect(service.getApplicationOverview(applicationId)).rejects.toThrow(ForbiddenException)
      await expect(service.getApplicationOverview(applicationId)).rejects.toThrow('无权限访问该应用')
    })

    it('should return empty statistics when application has no teams', async () => {
      // Arrange
      const applicationId = mockApplication._id.toString()

      applicationModel.findById.mockResolvedValue(mockApplication)
      userRoleModel.findOne.mockResolvedValue({
        userId: new Types.ObjectId(mockUser.userId),
        applicationId: mockApplication._id,
        roleName: OpenPlatformRoleNames.ADMIN
      })

      teamModel.find.mockResolvedValue([]) // 无团队

      // Act
      const result = await service.getApplicationOverview(applicationId)

      // Assert
      expect(result).toEqual({
        totalAccountPoints: 0,
        usedAccountPoints: 0,
        totalTraffic: 0,
        usedTraffic: 0,
        totalUsers: 0,
        totalTeams: 0,
        dailyStats: []
      })
    })

    it('should handle aggregation errors gracefully', async () => {
      // Arrange
      const applicationId = mockApplication._id.toString()

      applicationModel.findById.mockResolvedValue(mockApplication)
      userRoleModel.findOne.mockResolvedValue({
        userId: new Types.ObjectId(mockUser.userId),
        applicationId: mockApplication._id,
        roleName: OpenPlatformRoleNames.ADMIN
      })

      teamModel.find.mockResolvedValue(mockTeams)

      // Mock聚合查询失败
      teamModel.aggregate.mockRejectedValue(new Error('Database error'))
      trafficBillingModel.aggregate.mockRejectedValue(new Error('Database error'))
      memberModel.aggregate.mockRejectedValue(new Error('Database error'))
      platformAccountModel.aggregate.mockRejectedValue(new Error('Database error'))

      // Act
      const result = await service.getApplicationOverview(applicationId)

      // Assert
      expect(result).toEqual({
        totalAccountPoints: 0,
        usedAccountPoints: 0,
        totalTraffic: 0,
        usedTraffic: 0,
        totalUsers: 0,
        totalTeams: 2,
        dailyStats: []
      })
    })

    it('should verify correct data isolation by sourceAppId', async () => {
      // Arrange
      const applicationId = mockApplication._id.toString()

      applicationModel.findById.mockResolvedValue(mockApplication)
      userRoleModel.findOne.mockResolvedValue({
        userId: new Types.ObjectId(mockUser.userId),
        applicationId: mockApplication._id,
        roleName: OpenPlatformRoleNames.ADMIN
      })

      teamModel.find.mockResolvedValue(mockTeams)
      teamModel.aggregate.mockResolvedValue([])
      trafficBillingModel.aggregate.mockResolvedValue([])
      memberModel.aggregate.mockResolvedValue([])
      platformAccountModel.aggregate.mockResolvedValue([])

      // Act
      await service.getApplicationOverview(applicationId)

      // Assert
      expect(teamModel.find).toHaveBeenCalledWith({
        source: 'open_platform_app',
        sourceAppId: applicationId,
        isDeleted: false
      })
    })
  })
})
