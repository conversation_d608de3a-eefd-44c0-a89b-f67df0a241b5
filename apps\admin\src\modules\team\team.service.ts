import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import {
  AdminEntity,
  MemberEntity,
  PlatformAccountEntity,
  PlatformAccountOverviewEntity,
  RefundEntity,
  TeamEntity,
  TeamExpiredLogEntity,
  UserEntity
} from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import {
  RefundOrderLogsResponse,
  RefundOrderRequestBodyDTO,
  RefundOrdersResponse,
  TeamAccountsResponse,
  TeamListRequest,
  TeamMembersResponse,
  TeamResponse
} from './team.dto'
import { AdminRole, MemberStatusEnum, TeamFeatures, TeamRoleNames } from '@yxr/common'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { OrderManagerService } from '@yxr/order'

@Injectable()
export class TeamService {
  logger = new Logger('TeamService')

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(RefundEntity.name) private refundModel: Model<RefundEntity>,
    @InjectModel(TeamExpiredLogEntity.name)
    private teamExpiredLogModel: Model<TeamExpiredLogEntity>,
    @InjectModel(PlatformAccountOverviewEntity.name)
    private platformAccountOverviewModel: Model<PlatformAccountOverviewEntity>,
    private readonly orderManagerService: OrderManagerService,
    private readonly webhookService: WebhookService
  ) {}

  async getTeams(query: TeamListRequest): Promise<TeamResponse> {
    const where: any = {
      $match: {}
    }

    where.$match['isDeleted'] = false
    if (query.teamName) {
      where.$match.$or = [
        { name: { $regex: query.teamName, $options: 'i' } },
        { code: { $regex: query.teamName, $options: 'i' } }
      ]
    }
    if (query.startTime && query.endTime) {
      where.$match.createdAt = {
        $gte: new Date(Number(query.startTime)),
        $lte: new Date(Number(query.endTime))
      }
    }
    if (query.expiredStartTime && query.expiredEndTime) {
      where.$match.expiredAt = {
        $gte: new Date(Number(query.expiredStartTime)),
        $lte: new Date(Number(query.expiredEndTime))
      }
    }

    if (query.isVip !== undefined) {
      where.$match.isVip = query.isVip === 'true'
    }

    if (query.salesType !== undefined) {
      //传4查询新购和复购
      where.$match.salesType = query.salesType !== 4 ? query.salesType : { $gt: 0 }
    }

    if (query.customerId) {
      where.$match.customerId = new Types.ObjectId(query.customerId)
    }

    if (query.applicationId) {
      where.$match.sourceAppId = new Types.ObjectId(query.applicationId)
      where.$match.source = 'open_platform_app'
    }

    // 搜索手机号码或归属人对应的团队ID
    if (query.phone) {
      const userWhere: any = {
        $match: {}
      }
      if (query.phone) {
        userWhere.$match.phone = { $regex: query.phone, $options: 'i' }
      }
      const teamIdArr = await this.userModel.aggregate([
        userWhere,
        {
          $lookup: {
            from: 'memberentities',
            localField: '_id',
            foreignField: 'userId',
            pipeline: [
              {
                $match: {
                  status: MemberStatusEnum.Joined
                }
              },
              { $project: { teamId: 1, roles: 1, userId: 1 } }
            ],
            as: 'members'
          }
        },
        {
          $unwind: '$members'
        },
        {
          $group: {
            _id: '$members.teamId'
          }
        }
      ])

      if (teamIdArr.length > 0) {
        const teamIds = teamIdArr.map((item) => item._id)
        where.$match._id = { $in: teamIds }
      } else {
        where.$match._id = { $in: [] }
      }
    }

    const page = query.page
    const size = query.size
    const result = await this.teamModel.aggregate([
      where,
      {
        $facet: {
          counts: [{ $count: 'total' }],
          items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
        }
      }
    ])

    const teamIds = result[0]?.items.map((item) => new Types.ObjectId(item._id))
    const members = await this.memberModel
      .find({
        teamId: { $in: teamIds },
        roles: { $in: [TeamRoleNames.MASTER] },
        status: MemberStatusEnum.Joined
      })
      .select('_id userId teamId')
      .lean()
    const customers = await this.adminModel
      .find({
        role: AdminRole.Customer
      })
      .select('_id name')
      .lean()

    const userIds = members.map((item) => new Types.ObjectId(item.userId))
    const users = await this.userModel
      .find({
        _id: { $in: userIds }
      })
      .select('_id phone customerId')
      .lean()

    const data = result[0]?.items.map((item) => {
      const member = members.find((cs) => cs.teamId.toString() === item._id.toString())
      const user = users.find((cs) => cs._id.toString() === member.userId.toString())
      const customerName = item?.customerId
        ? customers.find((cs) => cs._id.toString() === item.customerId.toString())?.name
        : ''

      return {
        id: item._id,
        name: item.name,
        code: item.code,
        phone: user?.phone,
        accountCountLimit: item.accountCountLimit,
        accountCapacityLimit: item.accountCapacityLimit,
        accountCount: item.accountCount,
        memberCountLimit: item.memberCountLimit,
        memberCount: item.memberCount,
        capacity: item.capacity,
        usedCapacity: item.usedCapacity,
        networkTraffic: item.networkTraffic,
        useNetworkTraffic: item.useNetworkTraffic,
        capacityType: item.capacityType,
        interestCount: item?.interestCount ?? 0,
        appPublish: item?.appPublish ?? false,
        isVip: item?.isVip ?? false,
        customerName: customerName,
        salesType: item.salesType,
        createdAt: item.createdAt ? item.createdAt.getTime() : 0,
        expiredAt: item?.expiredAt ? item?.expiredAt.getTime() : 0
      }
    })

    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  /**
   * 降级一个团队
   * @param teamId
   * @private
   */
  async downgradeTeam(teamId: string) {
    const team = await this.teamModel.findById(new Types.ObjectId(teamId))
    if (team === null) return

    await this.teamExpiredLogModel.create({
      teamId: new Types.ObjectId(teamId),
      expiredAt: team.expiredAt,
      customerId: new Types.ObjectId(team.customerId)
    })

    team.accountCountLimit = TeamFeatures.DefaultAccountCountLimit
    team.accountCapacityLimit = TeamFeatures.DefaultAccountCapacityLimit
    team.memberCountLimit = TeamFeatures.DefaultMemberCountLimit
    team.isVip = false
    team.appPublish = false
    team.interestCount = 0
    team.capacity = 0
    team.accountCapacity = 0
    team.networkTraffic = 0
    team.useNetworkTraffic = 0

    await team.save()

    // 冻结账号, 清零账号点数
    await this.platformAccountModel.updateMany(
      { teamId: new Types.ObjectId(teamId) },
      { $set: { isFreeze: true, capacity: 0 } }
    )

    // 冻结成员
    await this.memberModel.updateMany(
      { teamId: new Types.ObjectId(teamId), roles: { $nin: [TeamRoleNames.MASTER] } },
      { $set: { isFreeze: true } }
    )

    await this.webhookService.grpchook(null, teamId, {
      event: WebhookEvents.TeamVersionDowngrade,
      body: {
        expiredAt: team.expiredAt,
        isVip: team.isVip,
        accountCountLimit: team.accountCountLimit,
        accountCapacityLimit: team.accountCapacityLimit,
        memberCountLimit: team.memberCountLimit
      }
    })
  }

  /**
   * 获取团队退费记录
   * @param teamId
   * @returns
   */
  async getTeamRefund(teamId: string): Promise<RefundOrderLogsResponse[]> {
    const result = await this.refundModel
      .find({
        teamId: new Types.ObjectId(teamId)
      })
      .sort({ createdAt: -1 })

    const userNames = result.map((item) => item?.username)
    const adminUsers = await this.adminModel.find(
      {
        username: { $in: userNames }
      },
      { username: 1, name: 1 }
    )

    return result.map((item) => ({
      refundNo: item.refundNo,
      refundableAmount: item.refundableAmount,
      actualAmount: item.actualAmount,
      remark: item.remark,
      creatorName: adminUsers.find((user) => user.username === item.username)?.name,
      createdAt: item.createdAt.getTime(),
      orderInfos: item.orderInfo
    }))
  }

  /**
   * 退款对应订单
   * @param teamId
   * @returns
   */
  async getOrderListByRefund(teamId: string): Promise<RefundOrdersResponse[]> {
    const result = await this.orderManagerService.orderListByRefund(teamId)
    return result.map((item) => ({
      orderNo: item.orderNo,
      refundAmount: item.refundAmount,
      actualRefundAmount: item.actualRefundAmount
    }))
  }

  /**
   * 退款
   * @param teamId
   * @returns
   */
  async refund(username: string, teamId: string, body: RefundOrderRequestBodyDTO) {
    await this.orderManagerService.refundOrder(username, {
      teamId: teamId,
      actualRefundAmount: body.realityPrice,
      remark: body.remark
    })
    await this.webhookService.grpchook(null, teamId, {
      event: WebhookEvents.TeamVersionDowngrade
    })
  }

  /**
   * 获取团队成员
   * @param teamId
   * @returns
   */
  async getTeamMembers(teamId: string): Promise<TeamMembersResponse[]> {
    const members = await this.memberModel.find({
      teamId: new Types.ObjectId(teamId)
    })

    const memberIds = members.map((member) => member.userId)
    if (members.length === 0) {
      return []
    }

    const memberMap = members.reduce((map, member) => {
      map[member.userId.toString()] = member
      return map
    }, {})

    const users = await this.userModel
      .find({
        _id: { $in: memberIds }
      })
      .sort({ createdAt: -1 })
      .select('phone avatar nickName')
      .lean()

    return users.map((item) => ({
      id: item._id.toString(),
      memberId: memberMap[item._id.toString()]._id.toString(),
      phone: item.phone,
      nickName: item.nickName,
      avatar: `${process.env.OSS_DOWNLOAD_URL}/${item.avatar}`
    }))
  }

  /**
   * 获取团队账号
   * @param teamId
   * @returns
   */
  async getTeamAccounts(teamId: string): Promise<TeamAccountsResponse[]> {
    const accounts = await this.platformAccountModel
      .find({
        teamId: new Types.ObjectId(teamId)
      })
      .sort({ createdAt: -1 })
      .select('_id platformName platformAvatar platformAccountName parentId')
      .lean()

    return accounts.map((item) => ({
      id: item._id.toString(),
      platformName: item.platformName,
      platformAvatar: item.platformAvatar,
      platformAccountName: item.platformAccountName,
      parentId: item.parentId?.toString()
    }))
  }
}
