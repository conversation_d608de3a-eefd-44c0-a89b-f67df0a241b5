import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { MemberEntity, NoticeEntity, ProposalEntity, TeamEntity, UserEntity } from '@yxr/mongo'
import { type FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import {
  MemberStatusEnum,
  NoticeTypesEnum,
  ProposalStatusEnum,
  TeamFeatures,
  TeamRoleNames
} from '@yxr/common'
import { ProposalTeamDetailsDto } from './proposal.dto'
import { AuthorizationService } from '../../common/security/authorization.service'
import { WebhookEvents } from '../webhook/constant'
import { WebhookService } from '../webhook/webhook.service'
import { TeamService } from './team.service'
import { TosService } from '@yxr/huoshan'

@Injectable()
export class ProposalService {
  logger = new Logger('ProposalService')

  constructor(
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(ProposalEntity.name) private proposalModel: Model<ProposalEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(NoticeEntity.name) private noticeModel: Model<NoticeEntity>,
    private readonly ossService: TosService,
    private readonly authorizationService: AuthorizationService,
    private readonly webhookService: WebhookService,
    private readonly teamService: TeamService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 获取当前用户是否加入团队的信息
   * @param code
   */
  async getTeamInfo(code: string): Promise<ProposalTeamDetailsDto> {
    this.validateCode(code)
    const team = await this.findTeamByCode(code)

    const { _id: userId } = this.request.user
    const status = await this.findMemberStatus(team.id, userId)

    return {
      id: team.id,
      name: team.name,
      logoUrl: await this.ossService.getAccessSignatureUrl(team.logo),
      logoKey: team.logo,
      status: status
    }
  }

  /**
   * 使用邀请码申请加入团队
   * @param code
   */
  async create({ code }: { code: string }) {
    const { _id: currentUserId, nickName } = this.request.user

    code = code.toUpperCase()
    this.validateCode(code)

    // 查询团队数量是否已达上限
    const teamCount = await this.teamService.teamCount(currentUserId)
    if (teamCount >= TeamFeatures.MaximumTeamCount) {
      throw new ForbiddenException('您加入的团队数量已达上限, 无法申请加入新团队')
    }

    const team = await this.findTeamByCode(code)

    // 判断人数是否超限
    if (team.memberCount >= team.memberCountLimit) {
      throw new ForbiddenException('团队人数已满, 无法加入')
    }

    const status = await this.findMemberStatus(team.id, currentUserId)
    if (status === MemberStatusEnum.Joined) {
      throw new ForbiddenException('您已加入该团队, 请勿重复申请。')
    }

    const session = await this.memberModel.db.startSession()
    session.startTransaction()

    let proposalId = ''

    try {
      const user = await this.userModel.findOne({
        _id: new Types.ObjectId(currentUserId)
      })
      // 创建成员关系
      await this.memberModel.create<MemberEntity>(
        [
          {
            userId: new Types.ObjectId(currentUserId),
            teamId: new Types.ObjectId(team._id),
            roles: [TeamRoleNames.NotJoined],
            accounts: [],
            remark: user.nickName,
            status: MemberStatusEnum.Pending
          }
        ],
        { session }
      )

      // 创建申请单
      const proposals = await this.proposalModel.create<ProposalEntity>(
        [
          {
            userId: new Types.ObjectId(currentUserId),
            teamId: new Types.ObjectId(team._id),
            roles: [TeamRoleNames.MEMBER],
            status: ProposalStatusEnum.Pending // 状态: 未批准, 已批准, 已拒绝
          }
        ],
        { session }
      )

      // 更新团队成员数
      const memberCount = await this.memberModel.countDocuments(
        { teamId: new Types.ObjectId(team._id) },
        { session }
      )
      await this.teamModel.updateOne(
        { _id: new Types.ObjectId(team._id) },
        { memberCount: memberCount },
        { session }
      )

      await session.commitTransaction()

      proposalId = proposals[0]._id.toString()
    } catch (error) {
      console.log(error)
      await session.abortTransaction()
      throw new HttpException('申请加入团队失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }

    // 确保事务成功后再执行的收尾操作
    // 全部管理员收到通知
    const adminUserIds = (
      await this.memberModel.find(
        {
          teamId: new Types.ObjectId(team._id),
          roles: { $in: [TeamRoleNames.ADMIN, TeamRoleNames.MASTER] },
          status: MemberStatusEnum.Joined
        },
        { _id: 0, userId: 1 }
      )
    ).map((u) => u.userId.toString())

    await this.noticeModel.create<NoticeEntity>({
      title: `${nickName} 申请加入 [${team.name}] 团队`,
      content: `${nickName} 申请加入 [${team.name}] 团队`,
      type: NoticeTypesEnum.Proposal,
      teamId: new Types.ObjectId(team._id),
      senderId: new Types.ObjectId(currentUserId),
      receiverIds: adminUserIds,
      bizArgs: {
        proposalId: proposalId
      }
    })

    await this.webhookService.grpchook(adminUserIds, team.id, { event: WebhookEvents.NoticeCreate })
  }

  /**
   * 处理加入团队的申请单
   * @param param
   */
  async handle({ approved, proposalId }: { approved: boolean; proposalId: string }) {
    const proposal = await this.proposalModel.findById<ProposalEntity>(
      new Types.ObjectId(proposalId)
    )

    if (proposal === null) {
      throw new NotFoundException('申请不存在')
    }

    if (proposal.status !== ProposalStatusEnum.Pending) {
      throw new BadRequestException('申请已经处理, 请勿重复处理')
    }

    const { userId: currentUserId } = this.request.session

    // 是否是团队管理员
    await this.authorizationService.checkRoleNames(
      proposal.teamId.toString(),
      currentUserId,
      [TeamRoleNames.MASTER, TeamRoleNames.ADMIN],
      '您不是团队管理员, 无权处理申请'
    )

    // 当同意加入团队时，需要检查团队实际生效的成员数量是否超限
    if (approved) {
      const memberCount = await this.memberModel.countDocuments({
        teamId: new Types.ObjectId(proposal.teamId.toString()), // 只检查当前团队
        status: MemberStatusEnum.Joined, // 只检查已加入的
        isFreeze: false // 只检查未冻结的
      })

      const team = await this.teamModel.findById(new Types.ObjectId(proposal.teamId.toString()))
      if (memberCount >= team.memberCountLimit) {
        throw new ForbiddenException(`该团队成员已达上限！`)
      }
    }

    const session = await this.proposalModel.db.startSession()
    session.startTransaction()

    const proposalTeam = await this.teamModel.findById(new Types.ObjectId(proposal.teamId))
    try {
      // 处理申请
      await this.proposalModel.updateOne(
        {
          _id: new Types.ObjectId(proposalId)
        },
        {
          handledAt: new Date(),
          handledBy: new Types.ObjectId(currentUserId),
          status: approved ? ProposalStatusEnum.Approved : ProposalStatusEnum.Rejected
        },
        { session }
      )

      // 处理成员关系
      if (approved) {
        await this.memberModel.updateOne(
          {
            teamId: new Types.ObjectId(proposal.teamId),
            userId: new Types.ObjectId(proposal.userId)
          },
          {
            status: MemberStatusEnum.Joined,
            roles: proposal.roles
          },
          { session }
        )

        await this.noticeModel.create<NoticeEntity>({
          title: `你已加入 [${proposalTeam.name}] 团队`,
          content: `你已加入 [${proposalTeam.name}] 团队`,
          type: NoticeTypesEnum.Regular,
          teamId: new Types.ObjectId(proposal.teamId),
          senderId: new Types.ObjectId(currentUserId),
          receiverIds: [proposal.userId.toString()],
          bizArgs: {
            proposalId: proposalId
          }
        })
      } else {
        await this.memberModel.deleteOne(
          {
            teamId: new Types.ObjectId(proposal.teamId),
            userId: new Types.ObjectId(proposal.userId)
          },
          { session }
        )

        // 拒绝时更新团队成员数
        const memberCount = await this.memberModel.countDocuments(
          { teamId: new Types.ObjectId(proposal.teamId) },
          { session }
        )
        await this.teamModel.updateOne(
          { _id: new Types.ObjectId(proposal.teamId) },
          { memberCount: memberCount },
          { session }
        )

        await this.noticeModel.create<NoticeEntity>({
          title: `你申请加入 [${proposalTeam.name}] 团队已被拒绝`,
          content: `你申请加入 [${proposalTeam.name}] 团队已被拒绝`,
          type: NoticeTypesEnum.Regular,
          teamId: new Types.ObjectId(proposal.teamId),
          senderId: new Types.ObjectId(currentUserId),
          receiverIds: [proposal.userId.toString()],
          bizArgs: {
            proposalId: proposalId
          }
        })
      }

      await session.commitTransaction()
    } catch (error) {
      console.log(error)
      await session.abortTransaction()
      throw new HttpException('处理加入申请失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }

    // 处理关联消息的业务状态
    await this.noticeModel.updateOne(
      { 'bizArgs.proposalId': proposalId },
      { bizState: approved ? 'approved' : 'rejected' }
    )

    if (approved) {
      await this.webhookService.grpchook([proposal.userId.toString()], null, {
        event: WebhookEvents.TeamJoined
      })
    }
  }

  private validateCode(code: string) {
    if (!/^[a-zA-Z0-9]{6}$/g.test(code)) {
      throw new BadRequestException('无效的邀请码')
    }
  }

  private async findTeamByCode(code: string) {
    const team = await this.teamModel.findOne({ code }).exec()
    if (team === null) {
      throw new BadRequestException('无效的邀请码')
    }
    return team
  }

  private async findMemberStatus(teamId: string, userId: string) {
    const member = await this.findMember(teamId, userId)
    return member === null ? MemberStatusEnum.NotJoined : member.status
  }

  private async findMember(teamId: string | Types.ObjectId, userId: string) {
    return await this.memberModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        userId: new Types.ObjectId(userId)
      })
      .exec()
  }
}
