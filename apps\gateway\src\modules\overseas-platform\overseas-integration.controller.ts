import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Body, Controller, Post } from '@nestjs/common'
import { OverseasIntegrationService } from './overseas-integration.service'
import { IntegrationAuthorizationCallbackInput } from './overseas-integration.dto'

@Controller('overseas-integration')
@ApiTags('海外平台内部集成专用接口')
export class OverseasIntegrationController {

  constructor(private readonly service: OverseasIntegrationService) {
  }
  @Post('auth-callback')
  @ApiOperation({ summary: '账号授权回调' })
  @ApiBody({ type: IntegrationAuthorizationCallbackInput })
  async authorizationCallback(@Body() input: IntegrationAuthorizationCallbackInput) {
    return await this.service.authorizationCallback(input)
  }
}
