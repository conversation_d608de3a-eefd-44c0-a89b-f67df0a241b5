import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger'
import { Body, Controller, Post, Res, Get, Param } from '@nestjs/common'
import { OverseasIntegrationService } from './overseas-integration.service'
import { IntegrationAuthorizationCallbackInput } from './overseas-integration.dto'
import { OverseasAccountRefreshService } from './overseas-account-refresh.service'
import { OverseasPublishIntegrationService } from './overseas-publish-integration.service'

@Controller('overseas-integration')
@ApiTags('海外平台内部集成专用接口')
export class OverseasIntegrationController {

  constructor(
    private readonly service: OverseasIntegrationService, 
    private readonly refreshService: OverseasAccountRefreshService,
    private readonly overseasPublishIntegrationService: OverseasPublishIntegrationService
  ) {
  }
  @Post('auth-callback')
  @ApiOperation({ summary: '账号授权回调' })
  @ApiBody({ type: IntegrationAuthorizationCallbackInput })
  async authorizationCallback(@Body() input: IntegrationAuthorizationCallbackInput) {
    return await this.service.authorizationCallback(input)
  }

  /**
   * 本地调试专用：手动触发刷新所有海外账号 token，仅 local 环境可用
   */
  @Post('refresh-all-accounts')
  async manualRefreshAllAccounts(@Res() res) {
    if (process.env.NODE_ENV !== 'local') {
      return res.status(403).json({ code: 403, message: 'Forbidden: only available in local env' });
    }
    await this.refreshService.refreshAllAccounts();
    return res.json({ code: 0, message: 'refreshAllAccounts triggered' });
  }

  /**
   * 手动触发海外发布任务处理（仅用于测试）
   */
  @Post('trigger-overseas-publish/:taskSetId')
  @ApiOperation({ summary: '手动触发海外发布任务处理' })
  async triggerOverseasPublish(@Param('taskSetId') taskSetId: string) {
    if (process.env.NODE_ENV !== 'local') {
      return { code: 403, message: 'Forbidden: only available in local env' };
    }
    
    try {
      await this.overseasPublishIntegrationService.handleOverseasPublishTasks(taskSetId)
      return { code: 0, message: '海外发布任务处理已触发' }
    } catch (error) {
      return { 
        code: 1, 
        message: '海外发布任务处理失败', 
        error: error.message 
      }
    }
  }
}
