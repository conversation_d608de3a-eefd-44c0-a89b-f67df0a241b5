import { ForbiddenException, Inject, Injectable } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { TeamEntity } from '@yxr/mongo'
import { TodayPushesCountDTO } from './push.dto'
import { CacheKeyService } from '@yxr/common'

@Injectable()
export class PushService {
  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getTodayPushesCount(): Promise<TodayPushesCountDTO> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const counterKey = CacheKeyService.genCounterKey(currentTeamId)
    const cacheValue = await this.cacheManager.store.client.get(counterKey)
    const currentValue = cacheValue ? parseInt(cacheValue, 10) : 0

    const team = await this.teamModel.findById(new Types.ObjectId(currentTeamId))

    return {
      count: team.isVip ? currentValue : Math.min(currentValue, 3),
      pushable: team.isVip || currentValue < 3
    }
  }
}
