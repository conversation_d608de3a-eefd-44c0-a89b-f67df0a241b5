import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { MaterialTypeEnum } from '@yxr/common'
import { IsNotEmpty, IsNumber, IsOptional, IsString, Max<PERSON>ength, <PERSON><PERSON>ength } from 'class-validator'
import { Types } from 'mongoose'

export class MaterialDTO {
  @ApiProperty({
    type: String,
    description: '素材ID',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '素材访问地址',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  filePath: string

  @ApiProperty({
    type: String,
    description: '缩略图访问地址',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  thumbPath: string

  @ApiProperty({
    type: String,
    enum: MaterialTypeEnum,
    description: '类别',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  type: MaterialTypeEnum

  @ApiProperty({
    type: String,
    description: '资源类型：video 或 image',
    example: 'image'
  })
  format: string

  @ApiProperty({
    type: Number,
    description: '资源大小 单位字节',
    example: 121333
  })
  size: number

  @ApiProperty({
    type: String,
    description: '文件类型：png｜jpg｜mp4等',
    example: 'png'
  })
  fileFormat: string

  @ApiProperty({
    type: String,
    description: '文件名称',
    example: '我的图片'
  })
  fileName: string

  @ApiProperty({
    type: Number,
    description: '宽度',
    example: 100
  })
  width: number

  @ApiProperty({
    type: Number,
    description: '高度',
    example: 100
  })
  height: number

  @ApiProperty({
    type: Types.ObjectId,
    description: '分组ID',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  groupId: Types.ObjectId

  @ApiProperty({
    type: String,
    description: '分组名称',
    example: '张三的分组'
  })
  groupName: string

  @ApiProperty({
    type: Number,
    description: '上传时间',
    required: true
  })
  createdAt: number
}

export class MaterialGroupDTO {
  @ApiProperty({
    type: String,
    description: '分组ID',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '分组名称',
    example: '分组一'
  })
  name: String
}

export class MaterialListDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    type: [MaterialDTO]
  })
  data: MaterialDTO[]
}

export class PostMaterialRequest {
  @ApiProperty({
    type: Types.ObjectId,
    required: false,
    description: '分组ID',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  @IsOptional()
  @IsString()
  groupId?: Types.ObjectId

  @ApiProperty({
    type: String,
    required: false,
    description: '缩略图的oss访问key',
    example: 't-671204c651762103085a57fc/as/1xlqeqayr0mfok_cfnepa'
  })
  @IsOptional()
  @IsString()
  thumbPath: string

  @ApiProperty({
    type: String,
    description: 'oss访问key',
    example: 't-671204c651762103085a57fc/as/1xlqeqayr0mfok_cfnepa'
  })
  @IsString()
  @IsNotEmpty({ message: '文件路径不能为空' })
  filePath: string

  @ApiProperty({
    type: String,
    description: '文件名称',
    example: '放点胡椒12323.png'
  })
  @IsString()
  @IsNotEmpty({ message: '文件名称不能为空' })
  fileName: string

  @ApiProperty({
    type: Number,
    description: '文件宽度',
    example: 100
  })
  @IsNumber()
  width: number

  @ApiProperty({
    type: Number,
    description: '文件高度',
    example: 100
  })
  @IsNumber()
  height: number

  @ApiProperty({
    type: String,
    enum: MaterialTypeEnum,
    description: '上传类型',
    example: 'video'
  })
  @IsString()
  @IsNotEmpty({ message: '类型不能为空' })
  type: MaterialTypeEnum
}

export class SetMaterialGroupRequest {
  @ApiProperty({
    type: Types.ObjectId,
    description: '分组ID, 不传时则设置为全部分组',
    example: '28547690-da27-42cb-9413-3941b7c06fa2'
  })
  @IsString()
  @IsOptional()
  groupId?: Types.ObjectId
}

/**
 * 分组
 */
export class PostMaterialGroupsRequest {
  @ApiProperty({
    type: String,
    example: '分组',
    required: true
  })
  @MaxLength(8, { message: '分组名称长度太长。不得多于 $constraint1 个字符' })
  @IsNotEmpty({ message: '分组名称不能为空' })
  @IsString()
  name: string
}

export class MaterialGroupResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MaterialGroupDTO
  })
  data: MaterialGroupDTO
}

export class MaterialGroupListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [MaterialGroupDTO]
  })
  data: MaterialGroupDTO[]
}

export class MaterialListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MaterialListDTO
  })
  data: MaterialListDTO
}

export class MaterialInfo {
  size: number
  width: number
  height: number
  format: string
}

export class DeleteMultiMaterialResponse {
  @ApiProperty({
    type: Number,
    description: '删除总数',
    example: 1
  })
  deleteTotal: number

  @ApiProperty({
    type: Number,
    description: '删除成功',
    example: 1
  })
  deleteSuccess: number

  @ApiProperty({
    type: Number,
    description: '删除失败',
    example: 1
  })
  deleteFail: number
}

export class DeleteMultiMaterialResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: DeleteMultiMaterialResponse
  })
  data: DeleteMultiMaterialResponse
}
