import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { MessageService } from './message.service'
import {
  MessageCreateRequestDTO,
  MessageDetailResponseDTO,
  MessageListRequestDTO,
  MessageListResponseDTO
} from './message.dto'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('message')
@ApiTags('消息管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class MessageController {
  constructor(private readonly messageService: MessageService) {}

  @Get()
  @ApiOperation({ summary: '消息列表' })
  @ApiOkResponse({ type: MessageListResponseDTO })
  getMessages(@Query() query: MessageListRequestDTO) {
    return this.messageService.getMessages(query)
  }

  @Post()
  @ApiOperation({ summary: '创建消息' })
  @ApiOkResponse({ description: '操作成功' })
  createAd(@Body() body: MessageCreateRequestDTO) {
    return this.messageService.createMessage(body)
  }

  @Patch(':messageId')
  @ApiOperation({ summary: '更新广告' })
  @ApiOkResponse({ type: MessageDetailResponseDTO })
  patchAdDetail(@Param('messageId') messageId: string, @Body() body: MessageCreateRequestDTO) {
    return this.messageService.patchAdDetail(messageId, body)
  }

  @Get(':messageId')
  @ApiOperation({ summary: '消息详情' })
  @ApiOkResponse({ type: MessageDetailResponseDTO })
  getAdDetail(@Param('messageId') messageId: string) {
    return this.messageService.getMessageDetail(messageId)
  }

  @Delete(':messageId')
  @ApiOperation({ summary: '消息删除' })
  @ApiOkResponse()
  deleteAdDetail(@Param('messageId') messageId: string) {
    return this.messageService.deleteMessage(messageId)
  }
}
