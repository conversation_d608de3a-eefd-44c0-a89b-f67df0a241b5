import { Injectable, Lo<PERSON>, Scope } from '@nestjs/common'
import { _ensureSuccessfulInvoke } from '../utils'
import { OverseasContext } from '../types'
import { createAxiosInstance } from '../../utils/axios-config'

@Injectable({ scope: Scope.TRANSIENT })
export class YoutubeApi {
  logger = new Logger(YoutubeApi.name)

  private readonly client_id = process.env.YOUTUBE_CLIENT_ID
  private readonly client_secret = process.env.YOUTUBE_CLIENT_SECRET

  /**
   * OAuth2.0 获取访问令牌
   */
  async oauth2_token(context: OverseasContext, params: {
    code: string
    redirect_uri: string
  }) {
    return await _ensureSuccessfulInvoke<{
      access_token: string
      expires_in: number
      refresh_token: string
      scope: string
      token_type: string
    }>(context, () => {
      const axios = createAxiosInstance('https://oauth2.googleapis.com')
      return axios.post('/token', {
        client_id: this.client_id,
        client_secret: this.client_secret,
        code: params.code,
        grant_type: 'authorization_code',
        redirect_uri: params.redirect_uri
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
    })
  }

  /**
   * 获取频道信息
   */
  async getChannelInfo(context: OverseasContext, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      kind: string
      etag: string
      pageInfo: {
        totalResults: number
        resultsPerPage: number
      }
      items: Array<{
        kind: string
        etag: string
        id: string
        snippet: {
          title: string
          description: string
          customUrl: string
          publishedAt: string
          thumbnails: {
            default: { url: string; width: number; height: number }
            medium: { url: string; width: number; height: number }
            high: { url: string; width: number; height: number }
          }
          localized: {
            title: string
            description: string
          }
          country: string
        }
        statistics: {
          viewCount: string
          subscriberCount: string
          hiddenSubscriberCount: boolean
          videoCount: string
        }
      }>
    }>(context, () => {
      const axios = createAxiosInstance('https://www.googleapis.com')
      return axios.get('/youtube/v3/channels', {
        params: {
          part: 'snippet,statistics',
          mine: true
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })
    })
  }

  /**
   * 上传视频到YouTube
   */
  async uploadVideo(context: OverseasContext, params: {
    video_url: string
    snippet: {
      title: string
      description: string
      tags?: string[]
      categoryId?: string
      defaultLanguage?: string
      defaultAudioLanguage?: string
    }
    status: {
      privacyStatus: 'public' | 'unlisted' | 'private'
      embeddable?: boolean
      license?: 'youtube' | 'creativeCommon'
      publicStatsViewable?: boolean
    }
    thumbnail_url?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)

    // 首先下载视频文件
    const axios = createAxiosInstance(new URL(params.video_url).origin)
    const videoResponse = await axios.get(params.video_url, { responseType: 'stream' })

    // 上传视频
    const FormData = require('form-data')
    const form = new FormData()

    // 添加元数据
    form.append('snippet', JSON.stringify(params.snippet))
    form.append('status', JSON.stringify(params.status))

    // 添加视频文件
    form.append('media', videoResponse.data, {
      filename: 'video.mp4',
      contentType: 'video/mp4'
    })

    const uploadResult = await _ensureSuccessfulInvoke<{
      kind: string
      etag: string
      id: string
      snippet: {
        publishedAt: string
        channelId: string
        title: string
        description: string
        thumbnails: {
          default: { url: string; width: number; height: number }
          medium: { url: string; width: number; height: number }
          high: { url: string; width: number; height: number }
          standard: { url: string; width: number; height: number }
          maxres: { url: string; width: number; height: number }
        }
        channelTitle: string
        tags: string[]
        categoryId: string
        liveBroadcastContent: string
        localized: {
          title: string
          description: string
        }
      }
      status: {
        uploadStatus: string
        privacyStatus: string
        license: string
        embeddable: boolean
        publicStatsViewable: boolean
      }
    }>(context, () => {
      const axios = createAxiosInstance('https://www.googleapis.com')
      return axios.post('/upload/youtube/v3/videos', form, {
        params: {
          part: 'snippet,status',
          uploadType: 'multipart'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          ...form.getHeaders()
        },
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      })
    })

    // 如果有缩略图，上传缩略图
    if (params.thumbnail_url && uploadResult.id) {
      try {
        await this.uploadThumbnail(context, uploadResult.id, params.thumbnail_url, accessToken)
      } catch (error) {
        this.logger.warn(`上传缩略图失败: ${error.message}`)
      }
    }

    return uploadResult
  }

  /**
   * 上传视频缩略图
   */
  async uploadThumbnail(context: OverseasContext, videoId: string, thumbnailUrl: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    // 下载缩略图
    const axios = createAxiosInstance(new URL(thumbnailUrl).origin)
    const thumbnailResponse = await axios.get(thumbnailUrl, { responseType: 'stream' })

    const FormData = require('form-data')
    const form = new FormData()
    form.append('media', thumbnailResponse.data, {
      filename: 'thumbnail.jpg',
      contentType: 'image/jpeg'
    })

    return await _ensureSuccessfulInvoke(context, () => {
      const axios = createAxiosInstance('https://www.googleapis.com')
      return axios.post(`/upload/youtube/v3/thumbnails/set`, form, {
        params: {
          videoId: videoId,
          uploadType: 'media'
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          ...form.getHeaders()
        }
      })
    })
  }

  /**
   * 获取视频信息
   */
  async getVideo(context: OverseasContext, videoId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      kind: string
      etag: string
      items: Array<{
        kind: string
        etag: string
        id: string
        snippet: {
          publishedAt: string
          channelId: string
          title: string
          description: string
          thumbnails: any
          channelTitle: string
          tags: string[]
          categoryId: string
        }
        status: {
          uploadStatus: string
          privacyStatus: string
          license: string
          embeddable: boolean
          publicStatsViewable: boolean
        }
        statistics: {
          viewCount: string
          likeCount: string
          dislikeCount: string
          favoriteCount: string
          commentCount: string
        }
      }>
    }>(context, () => {
      const axios = createAxiosInstance('https://www.googleapis.com')
      return axios.get('/youtube/v3/videos', {
        params: {
          part: 'snippet,status,statistics',
          id: videoId
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })
    })
  }

  /**
   * 删除视频
   */
  async deleteVideo(context: OverseasContext, videoId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke(context, () => {
      const axios = createAxiosInstance('https://www.googleapis.com')
      return axios.delete('/youtube/v3/videos', {
        params: {
          id: videoId
        },
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })
    })
  }

  /**
   * 从上下文中获取访问令牌
   */
  private getAccessTokenFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.access_token) {
      throw new Error('缺少YouTube访问令牌')
    }
    return credentials.access_token
  }
}
