import { Injectable, Logger, Scope } from '@nestjs/common'
import { AxiosInstance } from 'axios'
import { safeApiCall } from '../utils'
import { OverseasContext } from '../types'
import { createOverseasAxiosInstance, InterceptorConfig } from '../../utils/axios-config'
import { downloadMediaFile } from '../../utils/download-axios'
import { createTwitterBusinessErrorChecker } from './twitter-error-handler'
import {
  TwitterOAuth2TokenResponse,
  TwitterUserResponse,
  TwitterTweetResponse,
  TwitterTweetDetailResponse,
  TwitterDeleteTweetResponse,
  TwitterImageUploadResponse,
  TwitterVideoInitResponse,
  TwitterCreateTweetParams,
  TwitterGetUserMeParams,
  TwitterOAuth2Params,
  TwitterMediaType
} from './twitter-api-types'

@Injectable({ scope: Scope.TRANSIENT })
export class TwitterApi {
  constructor() {}

  logger = new Logger(TwitterApi.name)

  private readonly client_id = process.env.TWITTER_CLIENT_ID

  private readonly client_secret = process.env.TWITTER_CLIENT_SECRET

  /**
   * 创建Twitter专用的axios实例
   */
  private createTwitterAxiosInstance(context: OverseasContext, baseURL: string = 'https://api.twitter.com'): AxiosInstance {
    const twitterContext = {
      ...context,
      platform: 'twitter'
    }

    const interceptorConfig: InterceptorConfig = {
      context: twitterContext,
      businessErrorChecker: createTwitterBusinessErrorChecker(),
      enableRetry: true,
      enableBusinessErrorCheck: true,
      retryConfig: {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 30000
      }
    }

    return createOverseasAxiosInstance(baseURL, interceptorConfig)
  }


  /**
   * OAuth2.0
   *
   * // https://docs.x.com/resources/fundamentals/authentication/api-reference#post-oauth2%2Ftoken
   * @param body
   * @param context
   */
  async oauth2_token(context: OverseasContext, params: TwitterOAuth2Params) {
    const axios = this.createTwitterAxiosInstance(context, 'https://api.x.com')

    this.logger.log('开始 Twitter OAuth2 令牌交换')

    return await safeApiCall(context, async () => {
      return await axios.post<TwitterOAuth2TokenResponse>(
        '/2/oauth2/token',
        {
          grant_type: 'authorization_code',
          code: params.code,
          redirect_uri: params.redirect_uri,
          code_verifier: params.code_verifier
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Basic ${Buffer.from(`${this.client_id}:${this.client_secret}`).toString('base64')}`
          }
        }
      )
    })
  }

  /**
   * 刷新 access_token
   * https://developer.twitter.com/en/docs/authentication/oauth-2-0/refresh-token
   */
  async oauth2_refresh_token(context: OverseasContext, refresh_token: string) {
    const axios = this.createTwitterAxiosInstance(context, 'https://api.x.com')
    this.logger.log('开始 Twitter OAuth2 refresh_token 刷新')
    return await safeApiCall(context, async () => {
      return await axios.post<TwitterOAuth2TokenResponse>(
        '/2/oauth2/token',
        {
          grant_type: 'refresh_token',
          refresh_token: refresh_token,
          client_id: this.client_id,
          client_secret: this.client_secret
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Basic ${Buffer.from(`${this.client_id}:${this.client_secret}`).toString('base64')}`
          }
        }
      )
    })
  }

  /**
   * 获取用户信息
   *
   * https://docs.x.com/x-api/users/user-lookup-me
   *
   * @param context
   * @param access_token
   * @param options
   */
  async get_users_me(
    context: OverseasContext,
    access_token: string,
    options?: TwitterGetUserMeParams
  ) {
    const axios = this.createTwitterAxiosInstance(context)

    this.logger.log('获取 Twitter 用户信息')

    const params: Record<string, string> = {}
    if (options && options['user.fields']) {
      params['user.fields'] = options['user.fields'].join(',')
    }

    return await safeApiCall(context, async () => {
      return await axios.get<TwitterUserResponse>(
        '/2/users/me',
        {
          params,
          headers: { Authorization: `Bearer ${access_token}` }
        }
      )
    })
  }

  /**
   * 创建推文
   * https://docs.x.com/x-api/tweets/manage-tweets
   */
  async createTweet(context: OverseasContext, params: TwitterCreateTweetParams) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const axios = this.createTwitterAxiosInstance(context)

    this.logger.log(`创建推文: 文本长度=${params.text?.length || 0}, 媒体数量=${params.media?.media_ids?.length || 0}`)

    return await safeApiCall<TwitterTweetResponse>(context, () => {
      return axios.post('/2/tweets', {
        text: params.text,
        media: params.media,
        reply: params.reply,
        quote_tweet_id: params.quote_tweet_id,
        poll: params.poll,
        geo: params.geo
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        }
      })
    })
  }

  /**
   * 上传媒体文件
   * https://docs.x.com/x-api/v1/media/upload-media
   */
  async uploadMedia(context: OverseasContext, mediaUrl: string, mediaType: TwitterMediaType, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    this.logger.log(`开始下载媒体文件: ${mediaUrl}`)

    try {
      const mediaBuffer = await downloadMediaFile(mediaUrl)

      this.logger.log(`媒体文件下载完成: 大小=${mediaBuffer.length} bytes, 类型=${mediaType}`)

      if (mediaType === 'video') {
        // 视频需要分块上传
        return await this.uploadVideoMedia(context, mediaBuffer, accessToken)
      } else {
        // 图片可以直接上传
        return await this.uploadImageMedia(context, mediaBuffer, accessToken)
      }
    } catch (error) {
      this.logger.error(`媒体文件下载失败: ${mediaUrl}`, error)
      throw new Error(`媒体文件下载失败: ${error.message}`)
    }
  }

  /**
   * 上传图片媒体
   */
  private async uploadImageMedia(context: OverseasContext, mediaBuffer: Buffer, accessToken: string) {
    const FormData = require('form-data')
    const form = new FormData()
    form.append('media', mediaBuffer, { filename: 'image.jpg' })

    this.logger.log(`开始上传图片: 大小=${mediaBuffer.length} bytes`)

    const axios = this.createTwitterAxiosInstance(context, 'https://upload.twitter.com')

    return await safeApiCall<TwitterImageUploadResponse>(context, () => {
      return axios.post('/1.1/media/upload.json', form, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          ...form.getHeaders()
        }
      })
    })
  }

  /**
   * 上传视频媒体（分块上传）
   */
  private async uploadVideoMedia(context: OverseasContext, mediaBuffer: Buffer, accessToken: string) {
    this.logger.log(`开始视频分块上传: 总大小=${mediaBuffer.length} bytes`)

    const axios = this.createTwitterAxiosInstance(context, 'https://upload.twitter.com')

    // 初始化上传
    const initResponse = await safeApiCall<TwitterVideoInitResponse>(context, () => {
      return axios.post('/1.1/media/upload.json', {
        command: 'INIT',
        total_bytes: mediaBuffer.length,
        media_type: 'video/mp4'
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
    })

    const mediaId = initResponse.media_id_string
    const chunkSize = 5 * 1024 * 1024 // 5MB chunks
    const totalChunks = Math.ceil(mediaBuffer.length / chunkSize)

    this.logger.log(`视频上传初始化完成: media_id=${mediaId}, 分块数=${totalChunks}`)

    // 分块上传
    for (let i = 0; i < mediaBuffer.length; i += chunkSize) {
      const chunk = mediaBuffer.slice(i, i + chunkSize)
      const segmentIndex = Math.floor(i / chunkSize)

      this.logger.log(`上传视频分块 ${segmentIndex + 1}/${totalChunks}: 大小=${chunk.length} bytes`)

      const FormData = require('form-data')
      const form = new FormData()
      form.append('command', 'APPEND')
      form.append('media_id', mediaId)
      form.append('segment_index', segmentIndex.toString())
      form.append('media', chunk)

      await safeApiCall(context, () => {
        return axios.post('/1.1/media/upload.json', form, {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            ...form.getHeaders()
          }
        })
      })

      this.logger.log(`视频分块 ${segmentIndex + 1}/${totalChunks} 上传完成`)
    }

    this.logger.log('开始完成视频上传')

    // 完成上传
    await safeApiCall(context, () => {
      return axios.post('/1.1/media/upload.json', {
        command: 'FINALIZE',
        media_id: mediaId
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
    })

    this.logger.log(`视频上传完成: media_id=${mediaId}`)

    return {
      media_id: parseInt(mediaId),
      media_id_string: mediaId
    }
  }

  /**
   * 获取推文信息
   */
  async getTweet(context: OverseasContext, tweetId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)
    const axios = this.createTwitterAxiosInstance(context)

    this.logger.log(`获取推文信息: ${tweetId}`)

    return await safeApiCall<TwitterTweetDetailResponse>(context, () => {
      return axios.get(`/2/tweets/${tweetId}`, {
        params: {
          'tweet.fields': 'created_at,author_id,public_metrics'
        },
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 删除推文
   */
  async deleteTweet(context: OverseasContext, tweetId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)
    const axios = this.createTwitterAxiosInstance(context)

    this.logger.log(`删除推文: ${tweetId}`)

    return await safeApiCall<TwitterDeleteTweetResponse>(context, () => {
      return axios.delete(`/2/tweets/${tweetId}`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 从上下文中获取访问令牌
   */
  private getAccessTokenFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.access_token) {
      throw new Error('缺少Twitter访问令牌')
    }
    return credentials.access_token
  }
}
