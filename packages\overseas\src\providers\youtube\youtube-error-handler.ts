/**
 * YouTube平台特定错误处理器
 */

import { AxiosResponse } from 'axios'
import { BusinessErrorChecker, OverseasContext } from '../../utils/axios-config'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  extractRequestInfo,
  extractResponseInfo
} from '../../utils/error-handler'

/**
 * YouTube API错误详情接口
 */
export interface YouTubeApiErrorDetail {
  domain: string
  reason: string
  message: string
  locationType?: string
  location?: string
}

/**
 * YouTube API错误接口
 */
export interface YouTubeApiError {
  code: number
  message: string
  errors: YouTubeApiErrorDetail[]
}

/**
 * YouTube API响应接口
 */
export interface YouTubeApiResponse {
  error?: YouTubeApiError
}

/**
 * YouTube业务错误检查器
 */
export class YouTubeBusinessErrorChecker implements BusinessErrorChecker {
  /**
   * 检查YouTube响应是否包含业务错误
   * YouTube API在出现业务错误时会在响应中包含error字段
   */
  hasBusinessError(response: AxiosResponse<YouTubeApiResponse>): boolean {
    return response.data && response.data.error !== undefined
  }

  /**
   * 处理YouTube业务错误
   * 根据YouTube错误码和reason映射到标准错误类型
   */
  async handleBusinessError(response: AxiosResponse<YouTubeApiResponse>, context: OverseasContext): Promise<never> {
    const data = response.data

    if (!data || !data.error) {
      // 不应该调用这个方法，因为没有错误
      throw new RemoteApiError(
        context.platform,
        RemoteApiErrorCodes.UnknownRemoteApiError,
        { message: '意外调用了错误处理方法，但API返回成功' },
        extractRequestInfo(response),
        extractResponseInfo(response),
        context
      )
    }

    const error = data.error
    let errorCode: RemoteApiErrorCodes

    // 根据YouTube错误码和reason映射到标准错误类型
    switch (error.code) {
      // 认证相关错误
      case 401:
        errorCode = RemoteApiErrorCodes.AccessTokenInvalid
        break

      // 权限和配额相关错误
      case 403:
        if (error.message.includes('quota') || error.message.includes('Quota')) {
          errorCode = RemoteApiErrorCodes.QuotaExceeded
        } else if (error.message.includes('scope') || error.message.includes('permission')) {
          errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
        } else {
          errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
        }
        break

      // 参数错误
      case 400:
        errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
        break

      // 资源不存在
      case 404:
        errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
        break

      // 服务器错误
      case 500:
      case 502:
      case 503:
        errorCode = RemoteApiErrorCodes.ServerError
        break

      // 未知错误
      default:
        errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
    }

    // 进一步根据错误详情中的reason进行细化判断
    if (error.errors && error.errors.length > 0) {
      const firstError = error.errors[0]
      
      switch (firstError.reason) {
        case 'quotaExceeded':
        case 'dailyLimitExceeded':
        case 'rateLimitExceeded':
          errorCode = RemoteApiErrorCodes.QuotaExceeded
          break
        case 'authError':
        case 'required':
          errorCode = RemoteApiErrorCodes.AccessTokenInvalid
          break
        case 'forbidden':
        case 'insufficientPermissions':
          errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
          break
        case 'invalidValue':
        case 'badRequest':
          errorCode = RemoteApiErrorCodes.RequestParametersIncorrect
          break
        case 'uploadLimitExceeded':
          errorCode = RemoteApiErrorCodes.MediaUploadFailed
          break
      }
    }

    const apiError = new RemoteApiError(
      context.platform,
      errorCode,
      {
        message: error.message,
        youtubeErrorCode: error.code,
        youtubeErrors: error.errors,
        youtubeReason: error.errors?.[0]?.reason
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )

    console.warn(`[YouTube] API业务错误`, {
      errorCode: apiError.errorCode,
      youtubeCode: error.code,
      youtubeReason: error.errors?.[0]?.reason,
      message: error.message,
      context: context,
      requestInfo: apiError.requestInfo,
      responseInfo: apiError.responseInfo
    })

    throw apiError
  }
}

/**
 * 创建YouTube业务错误检查器实例
 */
export function createYouTubeBusinessErrorChecker(): BusinessErrorChecker {
  return new YouTubeBusinessErrorChecker()
}

/**
 * YouTube错误码到错误类型的映射表（用于文档和调试）
 */
export const YOUTUBE_ERROR_CODE_MAPPING = {
  // 认证错误
  401: 'ACCESS_TOKEN_INVALID',

  // 权限/配额错误
  403: 'QUOTA_EXCEEDED_OR_SCOPE_NOT_AUTHORIZED',

  // 参数错误
  400: 'REQUEST_PARAMETERS_INCORRECT',
  404: 'REQUEST_PARAMETERS_INCORRECT',

  // 服务器错误
  500: 'SERVER_ERROR',
  502: 'SERVER_ERROR',
  503: 'SERVER_ERROR'
} as const

/**
 * YouTube错误reason到错误类型的映射表
 */
export const YOUTUBE_ERROR_REASON_MAPPING = {
  quotaExceeded: 'QUOTA_EXCEEDED',
  dailyLimitExceeded: 'QUOTA_EXCEEDED',
  rateLimitExceeded: 'QUOTA_EXCEEDED',
  authError: 'ACCESS_TOKEN_INVALID',
  required: 'ACCESS_TOKEN_INVALID',
  forbidden: 'SCOPE_NOT_AUTHORIZED',
  insufficientPermissions: 'SCOPE_NOT_AUTHORIZED',
  invalidValue: 'REQUEST_PARAMETERS_INCORRECT',
  badRequest: 'REQUEST_PARAMETERS_INCORRECT',
  uploadLimitExceeded: 'MEDIA_UPLOAD_FAILED'
} as const

/**
 * 获取YouTube错误码的描述信息
 */
export function getYouTubeErrorDescription(code: number, reason?: string): string {
  if (reason) {
    const reasonMapping = YOUTUBE_ERROR_REASON_MAPPING as Record<string, string>
    return reasonMapping[reason] || 'UNKNOWN_ERROR'
  }
  
  const codeMapping = YOUTUBE_ERROR_CODE_MAPPING as Record<number, string>
  return codeMapping[code] || 'UNKNOWN_ERROR'
}
