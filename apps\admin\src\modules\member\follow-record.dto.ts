import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsNotEmpty, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class CreateFollowRecordDTO {
  @ApiProperty({
    type: String,
    example: '输入跟进内容',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '跟进内容不能为空' })
  content: string
}

export class FollowRecordResponse {
  @ApiProperty({
    type: String,
    description: '跟进记录id'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '客服名称'
  })
  customerName: string

  @ApiProperty({
    type: String,
    description: '跟进内容'
  })
  content: string

  @ApiProperty({
    type: Number,
    description: '时间'
  })
  createdAt: number
}

export class FollowRecordListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [FollowRecordResponse]
  })
  data: FollowRecordResponse[]
}
