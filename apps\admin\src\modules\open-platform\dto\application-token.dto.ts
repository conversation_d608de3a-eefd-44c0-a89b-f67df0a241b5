import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, Length } from 'class-validator'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

/**
 * 生成应用Token请求DTO
 */
export class GenerateApplicationTokenRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: 'app_1234567890abcdef'
  })
  @IsNotEmpty({ message: '应用ID不能为空' })
  @IsString({ message: '应用ID必须是字符串' })
  appId: string

  @ApiProperty({
    description: '应用密钥',
    example: 'sk_1234567890abcdef1234567890abcdef'
  })
  @IsNotEmpty({ message: '应用密钥不能为空' })
  @IsString({ message: '应用密钥必须是字符串' })
  @Length(35, 35, { message: '应用密钥长度必须为32位' })
  secretKey: string
}

/**
 * 刷新Token请求DTO
 */
export class RefreshTokenRequestDto {
  @ApiProperty({
    description: '当前Token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsNotEmpty({ message: 'Token不能为空' })
  @IsString({ message: 'Token必须是字符串' })
  token: string
}

/**
 * Token响应DTO
 */
export class TokenResponseDto {
  @ApiResponseProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  accessToken: string

  @ApiResponseProperty({
    example: 'Bearer'
  })
  tokenType: string

  @ApiResponseProperty({
    example: 86400 * 7
  })
  expiresIn: number

  @ApiResponseProperty({
    example: 1701475200000
  })
  expiresAt: number
}

/**
 * 生成Token响应DTO
 */
export class GenerateTokenResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TokenResponseDto,
  })
  data: TokenResponseDto
}

/**
 * 刷新Token响应DTO
 */
export class RefreshTokenResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TokenResponseDto,
  })
  data: TokenResponseDto
}

/**
 * 撤销Token请求DTO
 */
export class RevokeTokenRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty({ message: '应用ID不能为空' })
  @IsString({ message: '应用ID必须是字符串' })
  applicationId: string
}

/**
 * Token验证结果DTO
 */
export class TokenValidationResultDto {
  @ApiResponseProperty({
    example: true
  })
  valid: boolean

  @ApiResponseProperty({
    example: 'app_1234567890abcdef'
  })
  appId?: string

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  applicationId?: string

  @ApiResponseProperty({
    example: 'application'
  })
  userType?: string

  @ApiResponseProperty({
    example: 1701475200
  })
  expiresAt?: number

  @ApiResponseProperty({
    example: 'Token已过期'
  })
  error?: string
}
