import { BadRequestException, Inject, Injectable, Logger } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import type { FastifyRequest } from 'fastify'
import { NoticeEntity, OnlineScriptEntity } from '@yxr/mongo'
import {
  getNoticesNewestDTO,
  NoticesPageResponse,
  VersionQueryDTO,
  VersionsPageResponse
} from './notice.dto'

@Injectable()
export class NoticeService {
  logger = new Logger('NoticeService')

  constructor(
    @InjectModel(NoticeEntity.name) private noticeModel: Model<NoticeEntity>,
    @InjectModel(OnlineScriptEntity.name) private onlineScriptModel: Model<OnlineScriptEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 获取最新消息状态
   * @param latest
   */
  async getNewest(latest: Date): Promise<getNoticesNewestDTO> {
    const { _id: currentUserId } = this.request.user

    const notice = await this.noticeModel
      .where({ receiverIds: { $in: [currentUserId] }, createdAt: { $gt: latest } })
      .sort({ createdAt: -1 })
      .findOne()
      .exec()

    return {
      latest: (notice?.createdAt ?? latest).getTime(),
      hasNewest: notice !== null
    }
  }

  /**
   * 分页获取用户消息列表
   * @param size
   * @param page
   */
  async getPagedNotices({
    size,
    page
  }: {
    size: number
    page: number
  }): Promise<NoticesPageResponse> {
    const { _id: currentUserId } = this.request.user

    const result = await this.noticeModel
      .aggregate([
        { $match: { receiverIds: { $in: [currentUserId] } } },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
          }
        }
      ])
      .exec()

    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      data: result[0]?.items.map((notice: NoticeEntity & { _id: Types.ObjectId }) => ({
        id: notice._id,
        title: notice.title,
        content: notice.content,
        createdAt: notice.createdAt.getTime(),
        teamId: notice.teamId,
        type: notice.type,
        bizArgs: notice.bizArgs,
        bizState: notice.bizState
      })),
      page,
      size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / size)
    }
  }

  async getPagedVersions(query: VersionQueryDTO): Promise<VersionsPageResponse> {
    const s = query.type.toLowerCase()
    if (s !== 'windows' && s !== 'macos' && s !== 'ios' && s !== 'android') {
      throw new BadRequestException('只支持windows、macos、ios、android类型')
    }
    const page = query.page
    const size = query.size
    const result = await this.onlineScriptModel
      .aggregate([
        { $match: { type: query.type } },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
          }
        }
      ])
      .exec()
    const totalSize = result[0]?.counts[0]?.total ?? 0

    return {
      data: result[0]?.items.map((item) => ({
        id: item._id,
        version: item.version,
        notice: item.notice,
        createdAt: item.createdAt.getTime(),
        type: item.type
      })),
      page,
      size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / size)
    }
  }
}
