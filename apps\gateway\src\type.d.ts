import { UserEntity } from '@yxr/mongo'

declare module 'fastify' {
  interface FastifyRequest {
    /**
     * @deprecated request 对象中包含 user 模型对象的设计不合理, 请不要再使用, 需要取得当前会话信息, 请使用 request.session 对象
     */
    user: UserEntity & { _id: string }

    /**
     * 当前请求会话信息
     */
    session: {
      /**
       * 当前用户Id
       */
      userId: string

      /**
       * 当前团队Id
       */
      teamId: string
    }

    /**
     * 当前请求的授权令牌
     */
    authorization: string

    client: {
      name: string
      version: string
      platform: string
    }
  }
}
