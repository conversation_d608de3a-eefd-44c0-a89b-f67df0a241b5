import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Res } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseNotFoundResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { BrowserPublishRequest, PatchStatusRequest } from './task.dto'
import { TaskService } from './task.service'
import { FastifyReply } from 'fastify'
import { ExportTaskSetsRequest } from './taskSet.dto'
import { TaskCloudService } from './task-cloud.service'

@Controller('tasks')
@ApiTags('任务管理')
export class TaskController {
  constructor(
    private readonly taskService: TaskService,
    private readonly taskCloudService: TaskCloudService
  ) {}

  @Patch(':taskId/status')
  @ApiOperation({ summary: '设置子任务状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '任务不存在', type: BaseNotFoundResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async putStatus(@Param('taskId') taskId: string, @Body() body: PatchStatusRequest) {
    return await this.taskService.patchStatus(taskId, body)
  }

  @Get(':taskId/status')
  @ApiOperation({ summary: '获取任务状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiNotFoundResponse({ description: '任务不存在', type: BaseNotFoundResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getTaskStatus(@Param('taskId') taskId: string) {
    return await this.taskService.getTaskStatus(taskId)
  }

  @Post(':taskId/cancel')
  @ApiOperation({ summary: '取消发布中的任务' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async cancelTask(@Param('taskId') taskId: string) {
    return await this.taskCloudService.cancelTask(taskId)
  }

  @Delete(':taskId/publish')
  @ApiOperation({ summary: '删除发布稿件' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async deletePublishTask(@Param('taskId') taskId: string) {
    return await this.taskCloudService.deletePublishTask(taskId)
  }

  @Post('browser-publish')
  @ApiOperation({ summary: '浏览器发布上报' })
  @ApiOkResponse({ type: BaseResponseDTO })
  @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async postBrowserPublish(@Body() body: BrowserPublishRequest) {
    return await this.taskService.postBrowserPublish(body)
  }

  @Get('export')
  @ApiOperation({ summary: '导出任务集下任务列表数据' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async exportTaskSets(@Query() query: ExportTaskSetsRequest, @Res() res: FastifyReply) {
    const exportData = await this.taskService.exportTasks(query)
    if (exportData.length <= 0) {
      res.status(200).send({
        statusCode: 0
      })
      return
    }

    // 定义一个映射，将字段名映射为中文
    const titleMap = {
      desc: '发布标题/描述',
      createdAt: '创建时间',
      pulishType: '所属类型',
      isDraft: '是否草稿',
      nickName: '发布人',
      phone: '发布人手机号',
      platformName: '发布平台',
      platformAccountName: '账号名称',
      openUrl: '原文链接',
      updatedAt: '数据更新时间',
      viewCount: '播放量|阅读量',
      greatCount: '点赞量',
      commentCount: '评论量',
      shareCount: '分享量',
      collectCount: '收藏量'
    }

    // 动态生成 CSV 头部，并将字段名转换为中文
    const headers = Object.keys(exportData[0]).map((key) => ({
      id: key,
      title: titleMap[key] || key.charAt(0).toUpperCase() + key.slice(1) // 默认为字段名首字母大写
    }))

    // 生成 CSV 内容
    const csvContent = await this.taskService.generateCsv(exportData, headers)
    const exportFileName = '任务数据.csv'

    // 设置响应头
    res.header('Content-Type', 'text/csv')
    res.header('Content-Disposition', 'attachment;filename=' + encodeURIComponent(exportFileName))

    // 返回 CSV 内容
    res.send(csvContent)
  }
}
