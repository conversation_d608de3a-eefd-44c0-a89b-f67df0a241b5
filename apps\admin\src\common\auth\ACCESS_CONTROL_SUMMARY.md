# 访问控制配置总结

## 概述

基于统一认证服务(OptimizedUnifiedAuthService)和现有的守卫模式，已为admin模块下的不同控制器实现了基于用户类型的访问控制。

## 用户类型

系统支持三种用户类型：
- **ADMIN**: 管理员用户
- **OPEN_PLATFORM**: 开放平台用户  
- **APPLICATION**: 应用Token用户

## 全局守卫配置

在 `admin.module.ts` 中配置了两层守卫：

```typescript
providers: [
  // 第一层：身份认证
  { provide: APP_GUARD, useClass: OptimizedUnifiedTokenGuard },
  // 第二层：权限验证
  { provide: APP_GUARD, useClass: AccessControlGuard },
  // ...
]
```

## 访问控制装饰器

### 基础装饰器

- `@AdminOnly()` - 仅允许ADMIN类型用户访问
- `@OpenPlatformAccess()` - 仅允许OPEN_PLATFORM类型用户访问
- `@ApplicationAccess(requireDataIsolation?)` - 仅允许APPLICATION类型用户访问，支持数据隔离
- `@Anonymous()` - 匿名访问，跳过认证

### 组合装饰器

- `@AdminAndOpenPlatformAccess()` - 允许ADMIN和OPEN_PLATFORM用户访问
- `@AllAuthenticatedAccess(requireDataIsolation?)` - 允许所有已认证用户访问

## 控制器访问控制配置

### 1. 用户管理模块 (user)
- **控制器**: `UserController`
- **访问控制**: `@AdminOnly()`
- **说明**: 只允许管理员访问用户管理功能

### 2. 开放平台模块 (open-platform)

#### 应用管理
- **控制器**: `ApplicationController`
- **访问控制**: `@OpenPlatformAccess()`
- **说明**: 只允许开放平台用户管理应用

#### 授权管理
- **控制器**: `AuthorizationController`
- **访问控制**: `@OpenPlatformAccess()`
- **说明**: 只允许开放平台用户管理授权

#### 邀请管理
- **控制器**: `InvitationController`
- **访问控制**: `@OpenPlatformAccess()`
- **说明**: 只允许开放平台用户管理邀请

#### 充值管理
- **控制器**: `RechargeController`
- **访问控制**: `@AdminAndOpenPlatformAccess()`
- **特殊方法**:
  - `createRecharge()`: `@AdminOnly()` - 只有管理员可以创建充值
  - `updateRechargeStatus()`: `@AdminOnly()` - 只有管理员可以更新充值状态
  - 其他方法: 管理员和开放平台用户都可以访问

#### 应用Token管理
- **控制器**: `ApplicationTokenController`
- **访问控制**: 方法级别控制
- **特殊方法**:
  - `generateToken()`: `@Anonymous()` - 匿名生成Token
  - `refreshToken()`: `@Anonymous()` - 匿名刷新Token
  - `generateTokenByAdmin()`: `@AdminOnly()` - 管理员为应用生成Token
  - `revokeAllTokens()`: `@AdminOnly()` - 管理员撤销Token

#### Gateway用户管理
- **控制器**: `GatewayUserController`
- **访问控制**: 方法级别控制
- **特殊方法**:
  - 大部分方法: `@ApplicationAccess(true)` - 应用Token访问，启用数据隔离
  - `getAllUsersForAdmin()`: `@AdminOnly()` - 管理员查看所有用户

#### 认证管理
- **控制器**: `OpenPlatformAuthController`
- **访问控制**: `@Anonymous()` - 所有认证接口都是匿名访问

### 3. 会员模块 (member)
- **控制器**: `MemberController`
- **访问控制**: `@AdminAndOpenPlatformAccess()`
- **说明**: 管理员和开放平台用户都可以访问会员管理

### 4. 团队模块 (team)
- **控制器**: `TeamController`
- **访问控制**: `@AdminAndOpenPlatformAccess()`
- **说明**: 管理员和开放平台用户都可以访问团队管理

## 数据隔离机制

对于 `@ApplicationAccess(true)` 装饰器：
- 自动在request中设置 `dataIsolation` 信息
- 包含 `sourceAppId` 和 `applicationId`
- 服务层可以使用这些信息进行数据过滤

## 认证流程

1. **OptimizedUnifiedTokenGuard** 验证Token并识别用户类型
2. **AccessControlGuard** 根据装饰器配置验证权限
3. 设置request上下文信息（user、session、dataIsolation等）

## 向后兼容性

- 保持原有admin用户的认证方式不变
- 现有接口无需修改即可正常工作
- 新增的权限控制不影响现有功能

## 安全特性

- 不同用户类型之间完全隔离
- APPLICATION类型支持数据隔离
- 细粒度的方法级权限控制
- 统一的错误处理和权限验证
