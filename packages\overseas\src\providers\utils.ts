import { AxiosError, AxiosResponse } from 'axios'
import { OverseasContext } from './types'
import {
  createAxiosInstance,
  createOverseasAxiosInstance,
  BusinessErrorChecker,
  InterceptorConfig
} from '../utils/axios-config'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  extractRequestInfo,
  extractResponseInfo,
  createEnhancedErrorHandler,
  withRetry,
  OverseasApiErrorHandler,
  RetryConfig
} from '../utils/error-handler'

// 导入各平台专门的错误处理器
import { createTikTokBusinessErrorChecker } from './tiktok/tiktok-error-handler'
import { createFacebookBusinessErrorChecker } from './facebook/facebook-error-handler'
import { createInstagramBusinessErrorChecker } from './instagram/instagram-error-handler'
import { createTwitterBusinessErrorChecker } from './twitter/twitter-error-handler'
import { createYouTubeBusinessErrorChecker } from './youtube/youtube-error-handler'

/**
 * 创建带有标准错误处理的Axios实例
 * @param baseURL 基础URL
 * @param context 海外平台上下文
 * @param customErrorHandler 自定义错误处理器（可选）
 * @returns 配置好的axios实例
 */
export function createStandardAxiosInstance(
  baseURL: string,
  context: OverseasContext,
  customErrorHandler?: Partial<OverseasApiErrorHandler>
) {
  // 创建增强的错误处理器
  const enhancedErrorHandler = createEnhancedErrorHandler(context, customErrorHandler)

  // 使用增强错误处理器创建axios实例
  return createAxiosInstance(baseURL, {})
}

/**
 * 创建带有重试机制的标准Axios实例
 * @param baseURL 基础URL
 * @param context 海外平台上下文
 * @param retryConfig 重试配置（可选）
 * @param customErrorHandler 自定义错误处理器（可选）
 * @returns 配置好的axios实例
 */
export function createStandardAxiosInstanceWithRetry(
  baseURL: string,
  context: OverseasContext,
  retryConfig?: Partial<RetryConfig>,
  customErrorHandler?: Partial<OverseasApiErrorHandler>
) {
  const instance = createStandardAxiosInstance(baseURL, context, customErrorHandler)

  // 为实例的主要方法添加重试机制
  const originalGet = instance.get.bind(instance)
  const originalPost = instance.post.bind(instance)
  const originalPut = instance.put.bind(instance)
  const originalPatch = instance.patch.bind(instance)
  const originalDelete = instance.delete.bind(instance)

  instance.get = withRetry(originalGet, retryConfig)
  instance.post = withRetry(originalPost, retryConfig)
  instance.put = withRetry(originalPut, retryConfig)
  instance.patch = withRetry(originalPatch, retryConfig)
  instance.delete = withRetry(originalDelete, retryConfig)

  return instance
}

/**
 * 根据平台创建专用的Axios实例
 * @param baseURL 基础URL
 * @param context 海外平台上下文
 * @param enableRetry 是否启用重试机制
 * @returns 配置好的axios实例
 */
export function createPlatformSpecificAxiosInstance(
  baseURL: string,
  context: OverseasContext,
  enableRetry: boolean = true
) {
  // 根据平台创建专用的业务错误检查器
  let businessErrorChecker: BusinessErrorChecker | undefined

  switch (context.platform.toLowerCase()) {
    case 'facebook':
      businessErrorChecker = createFacebookBusinessErrorChecker()
      break
    case 'instagram':
      businessErrorChecker = createInstagramBusinessErrorChecker()
      break
    case 'twitter':
      businessErrorChecker = createTwitterBusinessErrorChecker()
      break
    case 'tiktok':
      businessErrorChecker = createTikTokBusinessErrorChecker()
      break
    case 'youtube':
      businessErrorChecker = createYouTubeBusinessErrorChecker()
      break
  }

  // 创建拦截器配置
  const interceptorConfig: InterceptorConfig = {
    context,
    businessErrorChecker,
    enableRetry,
    enableBusinessErrorCheck: true
  }

  // 使用新的createOverseasAxiosInstance创建实例
  return createOverseasAxiosInstance(baseURL, interceptorConfig)
}

/**
 * 安全执行API调用的辅助函数
 * @param context 海外平台上下文
 * @param func API调用函数
 * @param enableRetry 是否启用重试机制
 * @returns API响应数据
 */
export async function safeApiCall<T>(
  context: OverseasContext,
  func: () => Promise<AxiosResponse<T>>,
  enableRetry: boolean = true
): Promise<T> {
  const wrappedFunc = enableRetry ? withRetry(func) : func

  try {
    const response = await wrappedFunc()
    return response.data
  } catch (error) {
    // 错误已经在axios拦截器中处理并转换为RemoteApiError
    throw error
  }
}

/**
 * 批量执行API调用
 * @param context 海外平台上下文
 * @param calls API调用数组
 * @param concurrency 并发数量限制
 * @returns 执行结果数组
 */
export async function batchApiCalls<T>(
  context: OverseasContext,
  calls: Array<() => Promise<T>>,
  concurrency: number = 3
): Promise<Array<{ success: boolean; data?: T; error?: RemoteApiError }>> {
  const results: Array<{ success: boolean; data?: T; error?: RemoteApiError }> = []

  // 分批执行
  for (let i = 0; i < calls.length; i += concurrency) {
    const batch = calls.slice(i, i + concurrency)

    const batchResults = await Promise.allSettled(
      batch.map(call => call())
    )

    batchResults.forEach(result => {
      if (result.status === 'fulfilled') {
        results.push({ success: true, data: result.value })
      } else {
        results.push({
          success: false,
          error: result.reason instanceof RemoteApiError ? result.reason : new RemoteApiError(
            context.platform,
            RemoteApiErrorCodes.Unknown,
            { message: result.reason?.message || '未知错误' },
            { url: '' },
            { status: 0, statusText: 'Unknown Error' },
            context
          )
        })
      }
    })
  }

  return results
}

// /**
//  * 检查API响应是否包含业务错误
//  * @param response Axios响应对象
//  * @param context 海外平台上下文
//  * @param customChecker 自定义业务错误检查函数
//  * @throws RemoteApiError 当检测到业务错误时抛出
//  */
// export async function checkBusinessError(
//   response: AxiosResponse,
//   context: OverseasContext,
//   customChecker?: (data: any) => boolean
// ): Promise<void> {
//   // 根据平台创建专用的业务错误检查器
//   let businessErrorChecker: BusinessErrorChecker | undefined

//   switch (context.platform.toLowerCase()) {
//     case 'facebook':
//       businessErrorChecker = createFacebookBusinessErrorChecker()
//       break
//     case 'instagram':
//       businessErrorChecker = createInstagramBusinessErrorChecker()
//       break
//     case 'twitter':
//       businessErrorChecker = createTwitterBusinessErrorChecker()
//       break
//     case 'tiktok':
//       businessErrorChecker = createTikTokBusinessErrorChecker()
//       break
//     case 'youtube':
//       businessErrorChecker = createYouTubeBusinessErrorChecker()
//       break
//   }

//   // 检查是否有业务错误
//   let hasError = false

//   if (customChecker) {
//     // 如果有自定义检查器，使用自定义检查器
//     hasError = customChecker(response.data)
//   } else if (businessErrorChecker) {
//     // 使用平台特定的错误检查器
//     hasError = businessErrorChecker.hasBusinessError(response)
//   }

//   if (hasError && businessErrorChecker) {
//     // 调用平台特定的业务错误处理器，这会抛出RemoteApiError
//     await businessErrorChecker.handleBusinessError(response, context)
//   } else if (hasError) {
//     // 如果检测到错误但没有平台特定的处理器，抛出通用的业务错误
//     throw new RemoteApiError(
//       context.platform,
//       RemoteApiErrorCodes.UnknownRemoteApiError,
//       {
//         message: '检测到业务错误但无法识别具体错误类型',
//         responseData: response.data
//       },
//       extractRequestInfo(response),
//       extractResponseInfo(response),
//       context,
//       '检测到业务错误但无法识别具体错误类型'
//     )
//   }
// }

/**
 * 创建上下文对象的辅助函数
 * @param platform 平台名称
 * @param options 其他上下文选项
 * @returns 海外平台上下文对象
 */
export function createOverseasContext(
  platform: string,
  options: Partial<Omit<OverseasContext, 'platform'>> = {}
): OverseasContext {
  return {
    platform,
    ...options
  }
}
