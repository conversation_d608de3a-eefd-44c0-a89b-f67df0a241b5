import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { TeamEntity } from '@yxr/mongo'

/**
 * 流量过期清理服务
 * 负责清理过期的流量记录，确保流量有效期为1年
 */
@Injectable()
export class OpenPlatformOrderTrafficCleanupService {
  private readonly logger = new Logger(OpenPlatformOrderTrafficCleanupService.name)

  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>
  ) {}

  /**
   * 每天凌晨2点执行流量过期清理
   */
  @Cron('0 2 * * *', {
    name: 'traffic-cleanup',
    timeZone: 'Asia/Shanghai'
  })
  async handleTrafficCleanup(): Promise<void> {
    this.logger.log('开始执行流量过期清理任务')

    try {
      const now = new Date()
      let processedTeams = 0
      let totalCleanedTraffic = 0

      // 查找所有有流量记录的团队
      const teams = await this.teamModel.find({
        trafficRecords: { $exists: true, $ne: [] }
      })

      for (const team of teams) {
        const result = await this.cleanupExpiredTrafficForTeam(team, now)
        if (result.cleaned) {
          processedTeams++
          totalCleanedTraffic += result.cleanedAmount
        }
      }

      this.logger.log(
        `流量过期清理完成: 处理团队=${processedTeams}个, ` +
        `清理流量=${totalCleanedTraffic}KB(${(totalCleanedTraffic / 1024 / 1024).toFixed(2)}GB)`
      )
    } catch (error) {
      this.logger.error(`流量过期清理失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 手动执行流量清理
   */
  async manualCleanupExpiredTraffic(): Promise<{
    success: boolean
    processedTeams: number
    totalCleanedTraffic: number
    message: string
  }> {
    this.logger.log('手动执行流量过期清理')

    try {
      const now = new Date()
      let processedTeams = 0
      let totalCleanedTraffic = 0

      const teams = await this.teamModel.find({
        trafficRecords: { $exists: true, $ne: [] }
      })

      for (const team of teams) {
        const result = await this.cleanupExpiredTrafficForTeam(team, now)
        if (result.cleaned) {
          processedTeams++
          totalCleanedTraffic += result.cleanedAmount
        }
      }

      const result = {
        success: true,
        processedTeams,
        totalCleanedTraffic,
        message: `清理完成: 处理${processedTeams}个团队, 清理${(totalCleanedTraffic / 1024 / 1024).toFixed(2)}GB流量`
      }

      this.logger.log(`手动流量清理完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const result = {
        success: false,
        processedTeams: 0,
        totalCleanedTraffic: 0,
        message: `清理失败: ${error.message}`
      }

      this.logger.error(`手动流量清理失败: ${JSON.stringify(result)}`)
      return result
    }
  }

  /**
   * 清理单个团队的过期流量
   */
  private async cleanupExpiredTrafficForTeam(
    team: any,
    now: Date
  ): Promise<{ cleaned: boolean; cleanedAmount: number }> {
    if (!team.trafficRecords || team.trafficRecords.length === 0) {
      return { cleaned: false, cleanedAmount: 0 }
    }

    // 分离有效和过期的流量记录
    const validRecords = []
    let expiredTrafficAmount = 0

    for (const record of team.trafficRecords) {
      if (record.expiredAt > now) {
        // 流量未过期，保留
        validRecords.push(record)
      } else {
        // 流量已过期，累计过期流量
        expiredTrafficAmount += record.amount
      }
    }

    // 如果没有过期流量，无需处理
    if (expiredTrafficAmount === 0) {
      return { cleaned: false, cleanedAmount: 0 }
    }

    // 更新团队的流量记录和累积流量
    const newAccumulatedTraffic = Math.max(0, (team.accumulatedTraffic || 0) - expiredTrafficAmount)

    await this.teamModel.findByIdAndUpdate(team._id, {
      trafficRecords: validRecords,
      accumulatedTraffic: newAccumulatedTraffic,
      updatedAt: new Date()
    })

    this.logger.debug(
      `团队流量清理: teamId=${team._id}, teamName=${team.name}, ` +
      `清理过期流量=${expiredTrafficAmount}KB, ` +
      `剩余有效记录=${validRecords.length}个, ` +
      `更新后累积流量=${newAccumulatedTraffic}KB`
    )

    return { cleaned: true, cleanedAmount: expiredTrafficAmount }
  }

  /**
   * 获取流量清理统计信息
   */
  async getTrafficCleanupStats(): Promise<{
    totalTeamsWithTraffic: number
    totalAccumulatedTraffic: number
    totalTrafficRecords: number
    expiredRecordsCount: number
    expiredTrafficAmount: number
  }> {
    const now = new Date()

    // 聚合查询统计信息
    const stats = await this.teamModel.aggregate([
      {
        $match: {
          trafficRecords: { $exists: true, $ne: [] }
        }
      },
      {
        $project: {
          accumulatedTraffic: 1,
          trafficRecords: 1,
          expiredRecords: {
            $filter: {
              input: '$trafficRecords',
              cond: { $lte: ['$$this.expiredAt', now] }
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalTeamsWithTraffic: { $sum: 1 },
          totalAccumulatedTraffic: { $sum: '$accumulatedTraffic' },
          totalTrafficRecords: { $sum: { $size: '$trafficRecords' } },
          expiredRecordsCount: { $sum: { $size: '$expiredRecords' } },
          expiredTrafficAmount: {
            $sum: {
              $reduce: {
                input: '$expiredRecords',
                initialValue: 0,
                in: { $add: ['$$value', '$$this.amount'] }
              }
            }
          }
        }
      }
    ])

    const result = stats.length > 0 ? stats[0] : {
      totalTeamsWithTraffic: 0,
      totalAccumulatedTraffic: 0,
      totalTrafficRecords: 0,
      expiredRecordsCount: 0,
      expiredTrafficAmount: 0
    }

    // 移除MongoDB的_id字段
    delete result._id

    return result
  }

  /**
   * 获取指定团队的流量详情
   */
  async getTeamTrafficDetails(teamId: string): Promise<{
    teamId: string
    teamName: string
    accumulatedTraffic: number
    accumulatedTrafficGB: number
    trafficRecords: Array<{
      amount: number
      amountGB: number
      expiredAt: Date
      createdAt: Date
      isExpired: boolean
    }>
    validTrafficAmount: number
    expiredTrafficAmount: number
  }> {
    const team = await this.teamModel.findById(teamId)
    if (!team) {
      throw new Error('团队不存在')
    }

    const now = new Date()
    let validTrafficAmount = 0
    let expiredTrafficAmount = 0

    const trafficRecords = (team.trafficRecords || []).map(record => {
      const isExpired = record.expiredAt <= now
      const amount = record.amount || 0

      if (isExpired) {
        expiredTrafficAmount += amount
      } else {
        validTrafficAmount += amount
      }

      return {
        amount,
        amountGB: Number((amount / 1024 / 1024).toFixed(2)),
        expiredAt: record.expiredAt,
        createdAt: record.createdAt,
        isExpired
      }
    })

    return {
      teamId,
      teamName: team.name,
      accumulatedTraffic: team.accumulatedTraffic || 0,
      accumulatedTrafficGB: Number(((team.accumulatedTraffic || 0) / 1024 / 1024).toFixed(2)),
      trafficRecords,
      validTrafficAmount,
      expiredTrafficAmount
    }
  }
}
