import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class CustomerAssignRecordEntity {
  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  customerId: Types.ObjectId

  /**
   * 用户ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  userId: Types.ObjectId

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const CustomerAssignRecordSchema: ModelDefinition = {
  name: CustomerAssignRecordEntity.name,
  schema: SchemaFactory.createForClass(CustomerAssignRecordEntity)
}

export const CustomerAssignRecordMongoose = MongooseModule.forFeature([CustomerAssignRecordSchema])
