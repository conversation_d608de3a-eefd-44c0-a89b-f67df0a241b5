import { ApiProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsArray, IsUrl, IsDateString, IsNumber, Min, Max } from 'class-validator'
import { Type } from 'class-transformer'
import { PublishContentType, PublishTaskStatus } from '@yxr/overseas'

export class LocationDto {
  @ApiProperty({ description: '位置名称' })
  @IsString()
  @IsNotEmpty()
  name: string

  @ApiProperty({ description: '纬度', required: false })
  @IsOptional()
  @IsNumber()
  latitude?: number

  @ApiProperty({ description: '经度', required: false })
  @IsOptional()
  @IsNumber()
  longitude?: number
}

export class PublishContentDto {
  @ApiProperty({
    description: '内容类型',
    enum: PublishContentType
  })
  @IsEnum(PublishContentType)
  type: PublishContentType

  @ApiProperty({ description: '文本内容', required: false })
  @IsOptional()
  @IsString()
  text?: string

  @ApiProperty({ description: '标题', required: false })
  @IsOptional()
  @IsString()
  title?: string

  @ApiProperty({ description: '描述', required: false })
  @IsOptional()
  @IsString()
  description?: string

  @ApiProperty({ description: '图片URL列表', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  images?: string[]

  @ApiProperty({ description: '视频URL', required: false })
  @IsOptional()
  @IsUrl()
  videoUrl?: string

  @ApiProperty({ description: '视频封面URL', required: false })
  @IsOptional()
  @IsUrl()
  videoCover?: string

  @ApiProperty({ description: '标签列表', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[]

  @ApiProperty({ description: '位置信息', required: false, type: LocationDto })
  @IsOptional()
  @Type(() => LocationDto)
  location?: LocationDto

  @ApiProperty({ description: '平台特定参数', required: false })
  @IsOptional()
  platformSpecific?: Record<string, any>
}

export class CreatePublishTaskDto {
  @ApiProperty({ description: '任务ID' })
  @IsString()
  @IsNotEmpty()
  taskId: string

  @ApiProperty({ description: '任务集ID' })
  @IsString()
  @IsNotEmpty()
  taskSetId: string

  @ApiProperty({ description: '团队ID' })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({ description: '用户ID' })
  @IsString()
  @IsNotEmpty()
  userId: string

  @ApiProperty({ description: '平台账号OpenID' })
  @IsString()
  @IsNotEmpty()
  accountOpenId: string

  @ApiProperty({ description: '平台名称' })
  @IsString()
  @IsNotEmpty()
  platform: string

  @ApiProperty({ description: '发布内容', type: PublishContentDto })
  @Type(() => PublishContentDto)
  content: PublishContentDto

  @ApiProperty({ description: '发布时间（定时发布）', required: false })
  @IsOptional()
  @IsDateString()
  publishAt?: string

  @ApiProperty({ description: '回调URL' })
  @IsUrl()
  callbackUrl: string

  @ApiProperty({ description: '最大重试次数', required: false, minimum: 0, maximum: 5 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  maxRetries?: number

  @ApiProperty({ description: '平台认证凭证', required: false })
  @IsOptional()
  credentials?: any
}

export class PublishResultDto {
  @ApiProperty({ description: '任务ID' })
  taskId: string

  @ApiProperty({
    description: '发布状态',
    enum: PublishTaskStatus
  })
  status: PublishTaskStatus

  @ApiProperty({ description: '平台内容ID', required: false })
  platformContentId?: string

  @ApiProperty({ description: '平台内容URL', required: false })
  platformContentUrl?: string

  @ApiProperty({ description: '错误信息', required: false })
  errorMessage?: string

  @ApiProperty({ description: '错误代码', required: false })
  errorCode?: string

  @ApiProperty({ description: '完成时间' })
  completedAt: Date
}

export class GetPublishStatusDto {
  @ApiProperty({ description: '任务ID' })
  @IsString()
  @IsNotEmpty()
  taskId: string

  @ApiProperty({ description: '平台内容ID' })
  @IsString()
  @IsNotEmpty()
  platformContentId: string
}

export class BatchCreatePublishTaskDto {
  @ApiProperty({ description: '发布任务列表', type: [CreatePublishTaskDto] })
  @IsArray()
  @Type(() => CreatePublishTaskDto)
  tasks: CreatePublishTaskDto[]
}

export class PublishTaskCallbackDto {
  @ApiProperty({ description: '任务ID' })
  @IsString()
  @IsNotEmpty()
  taskId: string

  @ApiProperty({
    description: '发布状态',
    enum: PublishTaskStatus
  })
  @IsEnum(PublishTaskStatus)
  status: PublishTaskStatus

  @ApiProperty({ description: '平台内容ID', required: false })
  @IsOptional()
  @IsString()
  platformContentId?: string

  @ApiProperty({ description: '平台内容URL', required: false })
  @IsOptional()
  @IsUrl()
  platformContentUrl?: string

  @ApiProperty({ description: '错误信息', required: false })
  @IsOptional()
  @IsString()
  errorMessage?: string

  @ApiProperty({ description: '错误代码', required: false })
  @IsOptional()
  @IsString()
  errorCode?: string

  @ApiProperty({ description: '平台原始响应', required: false })
  @IsOptional()
  rawResponse?: any

  @ApiProperty({ description: '完成时间' })
  @IsDateString()
  completedAt: string
}
