import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { MaterialLibraryEntity, MaterialLibraryGroupEntity, TeamEntity } from '@yxr/mongo'
import {
  DeleteMultiMaterialResponse,
  MaterialListDTO,
  PostMaterialRequest,
  SetMaterialGroupRequest
} from './material.dto'
import { MemberService } from '../team/member.service'
import { TianyiyunOssService } from '@yxr/common'

@Injectable()
export class MaterialService {
  logger = new Logger('MaterialService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(MaterialLibraryGroupEntity.name)
    private materialLibraryGroupModel: Model<MaterialLibraryGroupEntity>,
    @InjectModel(MaterialLibraryEntity.name)
    private materialLibraryModel: Model<MaterialLibraryEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    private readonly ossService: TianyiyunOssService,
    private readonly memberService: MemberService
  ) {}

  private defaultCapacity = 500 * 1024 * 1024 //默认容量500MB
  /**
   * 获取素材分页
   * @param type
   * @param groupId
   * @param page
   * @param size
   * @returns
   */
  async getMaterial(
    type: string,
    groupId: string,
    page: number,
    size: number
  ): Promise<MaterialListDTO> {
    const skip = (page - 1) * size
    const { session } = this.request
    const query: any = {}

    query.teamId = new Types.ObjectId(session.teamId)
    if (type) {
      query.type = type
    }
    if (groupId) {
      if (Types.ObjectId.isValid(groupId)) {
        query.groupId = new Types.ObjectId(groupId)
      } else {
        throw new ForbiddenException('分组参数错误')
      }
    }

    const totalSize = await this.materialLibraryModel.countDocuments(query)
    const result = await this.materialLibraryModel
      .find(query)
      .sort({ createdAt: 'desc' })
      .skip(skip)
      .limit(size)

    const groups = await this.materialLibraryGroupModel
      .find({
        teamId: new Types.ObjectId(session.teamId)
      })
      .select('name _id')

    // 转换为对象，_id 为键，name 为值
    const groupMap = groups.reduce((acc, group) => {
      acc[group._id.toString()] = group.name // 将 _id 作为键，name 作为值
      return acc
    }, {})

    const data = await Promise.all(
      result.map(async (item) => ({
        id: item._id.toString(),
        type: item.type,
        format: item.format,
        height: item.height,
        width: item.width,
        size: item.size,
        fileName: item.fileName,
        fileFormat: item.fileFormat,
        thumbPath: item.thumbPath
          ? `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(item?.thumbPath)}?x-zos-process=image/resize,p_10`
          : `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(item?.filePath)}?x-zos-process=image/resize,p_10`,
        filePath: `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(item?.filePath)}`,
        groupId: item.groupId,
        groupName: groupMap[item.groupId?.toString()] ?? '全部',
        createdAt: item.createdAt.getTime()
      }))
    )

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }

  /**
   * 上传素材
   * @param body
   */
  async postMaterial(body: PostMaterialRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    if (body.groupId) {
      const group = await this.materialLibraryGroupModel.findById({
        _id: new Types.ObjectId(body.groupId),
        teamId: new Types.ObjectId(currentTeamId)
      })
    }

    // 判断上传前是否还有容量 - 减少容量不足oss访问
    const size = await this.checkLibraryCapacity(currentTeamId, body.filePath)
    const session = await this.materialLibraryModel.db.startSession()
    session.startTransaction()

    try {
      let fileFormat = body.fileName.split('.').pop()
      if (!fileFormat) {
        fileFormat = '未知'
      }
      // 获取oss文件信息
      const material = await this.materialLibraryModel.create(
        [
          {
            filePath: body.filePath,
            thumbPath: body.thumbPath,
            groupId: body.groupId ? new Types.ObjectId(body.groupId) : null,
            type: body.type,
            width: body.width,
            height: body.height,
            fileFormat: fileFormat,
            fileName: body.fileName,
            format: body.type,
            size: size,
            userId: new Types.ObjectId(currentUserId),
            teamId: new Types.ObjectId(currentTeamId)
          }
        ],
        { session }
      )

      await this.teamModel.updateOne(
        {
          _id: new Types.ObjectId(currentTeamId)
        },
        {
          $inc: {
            usedCapacity: size
          }
        },
        {
          session
        }
      )

      await session.commitTransaction()
    } catch (error) {
      await session.abortTransaction()
      this.logger.error('素材上传失败', error)
      throw new HttpException('上传失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }
  }

  /**
   * 设置素材分组
   * @param materialId
   * @param body
   */
  async setMaterialGroup(materialId: string, body: SetMaterialGroupRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const material = await this.materialLibraryModel.findOne({
      _id: new Types.ObjectId(materialId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!material) {
      throw new NotFoundException('资源不存在')
    }

    const isAdminRole = await this.memberService.checkIsAdminRole(currentTeamId, currentUserId)
    if (!isAdminRole && material.userId.toString() !== currentUserId) {
      throw new ForbiddenException('修改失败，没有权限')
    }
    if (body.groupId) {
      const group = await this.materialLibraryGroupModel.findById({
        _id: new Types.ObjectId(body.groupId),
        teamId: new Types.ObjectId(currentTeamId)
      })

      if (!group) {
        throw new NotFoundException('分组不存在')
      }
    }

    await this.materialLibraryModel.updateOne(
      { _id: new Types.ObjectId(materialId) },
      { groupId: body.groupId ? new Types.ObjectId(body.groupId) : null }
    )
  }

  /**
   * 删除资源
   * @param materialId
   * @returns
   */
  async deleteMaterial(materialId: string) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const material = await this.materialLibraryModel.findOne({
      _id: new Types.ObjectId(materialId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!material) {
      throw new NotFoundException('资源不存在')
    }

    const isAdminRole = await this.memberService.checkIsAdminRole(currentTeamId, currentUserId)
    if (!isAdminRole && material.userId.toString() !== currentUserId) {
      throw new ForbiddenException('删除失败，没有权限')
    }

    const session = await this.materialLibraryModel.db.startSession()
    session.startTransaction()

    try {
      await this.materialLibraryModel.deleteOne(
        {
          _id: new Types.ObjectId(materialId)
        },
        { session }
      )

      await this.teamModel.updateOne(
        {
          _id: new Types.ObjectId(currentTeamId)
        },
        {
          $inc: {
            usedCapacity: -material.size
          }
        },
        {
          session
        }
      )

      //删除oss资源 更新容量值
      let deleteNames = []
      if (material.filePath) {
        deleteNames.push(material.filePath)
      }
      if (material.thumbPath) {
        deleteNames.push(material.thumbPath)
      }
      if (deleteNames.length > 0) {
        await this.ossService.deleteMultiOssObject(deleteNames)
      }

      await session.commitTransaction()
    } catch (error) {
      await session.abortTransaction()
      throw new HttpException('删除失败, 请稍后再试', -1)
    } finally {
      await session.endSession()
    }
  }

  async deleteBatchMaterials(materialIds: string[]): Promise<DeleteMultiMaterialResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    if (!materialIds) {
      throw new ForbiddenException('请选择删除的素材')
    }
    materialIds.map((item) => new Types.ObjectId(item))

    const where: any = {
      _id: { $in: materialIds },
      teamId: new Types.ObjectId(currentTeamId)
    }
    const isAdminRole = await this.memberService.checkIsAdminRole(currentTeamId, currentUserId)
    if (!isAdminRole) {
      //不是管理员就只能删除自己的
      where.userId = new Types.ObjectId(currentUserId)
    }

    const materials = await this.materialLibraryModel.find(where)
    let deleteTotal = materialIds.length
    let deleteSuccess = 0
    let deleteFail = 0

    if (materials.length > 0) {
      const session = await this.materialLibraryModel.db.startSession()
      session.startTransaction()
      try {
        const deleteMaterialIds = []
        const deleteNames = []
        let deleteMaterialSize = 0
        materials.map((item) => {
          deleteMaterialIds.push(item._id)
          deleteMaterialSize += item.size

          if (item.filePath) {
            deleteNames.push(item.filePath)
          }
          if (item.thumbPath) {
            deleteNames.push(item.thumbPath)
          }
        })
        await this.materialLibraryModel.deleteMany(
          {
            _id: { $in: deleteMaterialIds },
            teamId: new Types.ObjectId(currentTeamId)
          },
          { session, ordered: true }
        )

        await this.teamModel.updateOne(
          {
            _id: new Types.ObjectId(currentTeamId)
          },
          {
            $inc: {
              usedCapacity: -deleteMaterialSize
            }
          },
          { session }
        )

        //删除oss资源 更新容量值
        if (deleteNames.length > 0) {
          await this.ossService.deleteMultiOssObject(deleteNames)
        }
        deleteSuccess = materials.length
        deleteFail = deleteTotal - deleteSuccess
        await session.commitTransaction()
      } catch (error) {
        await session.abortTransaction()
        throw new HttpException('删除失败, 请稍后再试', -1)
      } finally {
        await session.endSession()
      }
    } else {
      deleteFail = deleteTotal
    }

    return {
      deleteTotal,
      deleteSuccess,
      deleteFail
    }
  }

  /**
   * 获取资源head信息
   * @param fileKey
   * @returns
   */
  async headMaterialInfo(fileKey: string) {
    const info = await this.ossService.headFileInfo(fileKey)
    const size = Number(info ?? 0)
    if (size <= 0) {
      Logger.error(`Oss文件获取异常：${JSON.stringify(info)}`)
    }

    return {
      size: size
    }
  }

  /**
   * 检查团队素材库容量
   * @param teamId
   * @param filePath OSS文件路径
   */
  private async checkLibraryCapacity(teamId: string, filePath: string): Promise<number> {
    const team = await this.teamModel.findOne({
      _id: new Types.ObjectId(teamId)
    })

    let teamCapacity = team.capacity //团队素材库容量
    if (!team.capacity || team.capacity === 0) {
      teamCapacity = this.defaultCapacity
    }
    const teamUsedCapacity = team.usedCapacity //团队素材库已使用容量

    if (teamUsedCapacity >= teamCapacity) {
      throw new ForbiddenException('素材库容量已满')
    }
    //获取素材size信息
    const resultInfo = await this.headMaterialInfo(filePath)
    if (!resultInfo) {
      throw new NotFoundException('未找到素材')
    }
    if (teamUsedCapacity + resultInfo.size >= teamCapacity) {
      throw new ForbiddenException('素材库容量不足')
    }

    return resultInfo.size
  }
}
