import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class GroupItemsDTO {
  @ApiProperty({
    description: '网站地址',
    type: String,
    example: ''
  })
  websiteUrl: string

  @ApiProperty({
    description: '网站空间ID',
    type: String
  })
  browserId: string

  @ApiProperty({
    description: '数据来源表主键ID',
    type: String
  })
  originalId: string

  @ApiProperty({
    description: '数据来源表主键ID',
    type: String
  })
  groupId: string

  @ApiProperty({
    description: '账号ID',
    type: String
  })
  accountId: string
}

export class GroupsDTO {
  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    example: '娱乐分组'
  })
  name: string

  @ApiResponseProperty({
    type: [GroupItemsDTO]
  })
  originals: GroupItemsDTO[]

  @ApiProperty({
    type: Number
  })
  createdAt?: number
}

/**
 * 分组
 */
export class PostGroupsRequest {
  @ApiProperty({
    type: String,
    example: '分组',
    required: true
  })
  @IsString()
  name: string
}

export class PutCollectGroupItemsRequest {
  @ApiProperty({
    description: '网站地址',
    type: String,
    example: ''
  })
  @IsString()
  @IsOptional()
  websiteUrl?: string

  @ApiProperty({
    description: '网站空间ID',
    type: String
  })
  @IsString()
  browserId: string

  @ApiProperty({
    description: '数据来源表主键ID',
    type: String
  })
  @IsString()
  originalId: string

  @ApiProperty({
    description: '账号ID',
    type: String
  })
  @IsString()
  @IsOptional()
  accountId?: string
}

/**
 * 分组详情
 */
export class GroupsDetailResponse {
  @ApiResponseProperty({
    type: String,
    example: '********-da27'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '分组'
  })
  name: string

  @ApiResponseProperty({
    type: [String],
    example: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a']
  })
  originals: GroupItemsDTO[]

  @ApiResponseProperty({
    type: Number,
    example: '**********'
  })
  createdAt: number
}

export class GroupsListResponse {
  @ApiResponseProperty({
    type: [GroupsDTO]
  })
  data: GroupsDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

/**
 * 分组详情响应体
 */
export class GroupsDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: GroupsDetailResponse
  })
  data: GroupsDetailResponse
}

/**
 * 分组列表响应体
 */
export class GroupsListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: GroupsListResponse
  })
  data: GroupsListResponse
}
