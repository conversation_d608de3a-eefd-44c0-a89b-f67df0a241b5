import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Queue, Worker } from 'bullmq'
import { EventEmitter } from 'events'

export const socketConnectedEventKey = 'socket-connected'
export const socketEventEmitter = new EventEmitter()

@Injectable()
export class SocketEventService implements OnModuleInit {
  logger = new Logger('SocketEventService')

  taskQueue: Queue

  onModuleInit() {
    this.logger.log('SocketEventService init')

    socketEventEmitter.on(socketConnectedEventKey, this.socketConnected.bind(this))

    this.taskQueue = new Queue('socket-event-queue', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_NORMAL_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
  }

  async socketConnected({
    socketId,
    type,
    teamId,
    userId,
    deviceId,
    version
  }: {
    socketId: string
    type: string
    teamId: string
    userId: string
    deviceId: string
    version: string
  }) {
    const jobId = `socket-${type}-${socketId}`
    await this.taskQueue.add(
      type,
      { socketId, teamId, userId, deviceId, version },
      {
        delay: 0,
        removeOnComplete: true,
        removeOnFail: true,
        jobId: jobId
      }
    )
  }
}
