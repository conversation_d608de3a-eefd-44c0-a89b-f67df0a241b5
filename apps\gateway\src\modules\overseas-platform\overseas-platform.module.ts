import { Module } from '@nestjs/common'
import { CommonModule } from '@yxr/common'
import {
  ContentMongoose,
  ContentStatisticMongoose,
  GroupMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  PlatformAccountOverviewMongoose,
  PlatformAccountSummaryMongoose,
  TaskMongoose,
  TaskSetMongoose,
  TeamMongoose,
  UserMongoose
} from '@yxr/mongo'
import { TeamModule } from '../team/team.module'
import { WebhookModule } from '../webhook/webhook.module'
import { PlatformAccountModule } from '../platform/platform-account.model'
import { BrowserModule } from '../browser/browser.module'
import { KuaidailiModule } from '../kuaidaili/kuaidaiali.module'

import { WechatIpadModule } from '../wechat-ipad/wechat-ipad.module'
import { OverseasPlatformAuthService } from './overseas-platform-auth.service'
import { OverseasPlatformAuthController } from './overseas-platform-auth.controller'
import { OverseasIntegrationController } from './overseas-integration.controller'
import { OverseasIntegrationService } from './overseas-integration.service'
import { AccountCapacityService } from './account-capacity.service'
import { OverseasAccountService } from './overseas-account.service'
import { OverseasAccountRefreshService } from './overseas-account-refresh.service'
import { OverseasPublishIntegrationService } from './overseas-publish-integration.service'

@Module({
  imports: [
    MemberMongoose,
    TeamMongoose,
    TaskMongoose,
    TaskSetMongoose,
    ContentMongoose,
    UserMongoose,
    GroupMongoose,
    PlatformAccountMongoose,
    PlatformAccountOverviewMongoose,
    ContentStatisticMongoose,
    PlatformAccountSummaryMongoose,
    PlatformAccountModule,
    BrowserModule,
    TeamModule,
    WebhookModule,
    KuaidailiModule,
    CommonModule,
    WechatIpadModule
  ],
  controllers: [OverseasPlatformAuthController, OverseasIntegrationController],
  providers: [
    OverseasPlatformAuthService,
    OverseasIntegrationService,
    AccountCapacityService,
    OverseasAccountService,
    OverseasAccountRefreshService,
    OverseasPublishIntegrationService
  ]
})
export class OverseasPlatformModule {
}
