import { HttpStatus, ValidationPipe } from '@nestjs/common'
import { NestFactory } from '@nestjs/core'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { GatewayModule } from './gateway.module'
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify'
// import helmet from '@fastify/helmet'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { XMLParser } from 'fast-xml-parser'
import multipart from '@fastify/multipart'

import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import dayjs from 'dayjs'

dayjs.extend(utc)
dayjs.extend(timezone)

async function bootstrap() {
  const fastify = new FastifyAdapter()
  // fastify.register(helmet)
  fastify.register(multipart, {
    limits: {
      fileSize: 1024 * 1024 * 10
    }
  })

  const app = await NestFactory.create<NestFastifyApplication>(GatewayModule, fastify)

  // 获取 Fastify 实例
  const fastifyEntity = app.getHttpAdapter().getInstance()
  // 创建 XML 解析器实例
  const xmlParser = new XMLParser({
    ignoreAttributes: false, // 不忽略属性
    attributeNamePrefix: '', // 属性名前缀为空
    parseTagValue: true, // 解析标签值
    parseAttributeValue: true, // 解析属性值
    trimValues: true // 去除值的前后空格
  })
  // 注册 XML 内容类型解析器
  fastifyEntity.addContentTypeParser(['text/xml', 'application/xml'], (request, payload, done) => {
    let data = ''
    // 使用流读取 payload 数据
    payload.on('data', (chunk) => {
      data += chunk // 拼接数据
    })
    // 完成读取后，解析 XML 数据
    payload.on('end', () => {
      try {
        const parsedBody = xmlParser.parse(data) // 解析 XML 请求体
        done(null, parsedBody) // 将解析后的数据传递给下游处理
      } catch (err) {
        done(err) // 如果解析出错，传递错误
      }
    })
    payload.on('error', (err) => {
      done(err) // 处理流读取错误
    })
  })

  const configService = app.get(ConfigService) as ConfigService<RootConfigMap, true>
  const appConfig = configService.get<RootConfigMap['app']>('app')

  app.enableCors({
    origin: appConfig.cors.allowOrigin,
    methods: appConfig.cors.allowMethod,
    allowedHeaders: appConfig.cors.allowHeader,
    preflightContinue: false,
    credentials: true,
    optionsSuccessStatus: HttpStatus.NO_CONTENT
  })

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true
    })
  )

  if (process.env.NODE_ENV !== 'prod') {
    const ClientDocument = SwaggerModule.createDocument(
      app,
      new DocumentBuilder().setTitle('蚁小二 Lite API 文档').setVersion('1.0').build()
    )

    SwaggerModule.setup('gateway-api', app, ClientDocument)
  }

  await app.listen(3000, appConfig.http.host)
}

bootstrap()
