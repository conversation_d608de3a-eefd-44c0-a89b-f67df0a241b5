import { Controller, Get, Post,Inject, Query,Body } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger'
import { FastifyRequest } from 'fastify'

import { OnlineScriptService } from './online-script.service'
import {
  BaiduReportRequest,
  DesktopLatestRequest,
  DesktopLatestResponseDTO,
  GetDesktopLatestResponseDTO,
  GetLatestResponseDTO,
  LatestResponseDTO
} from './online-script.dto'
import { REQUEST } from '@nestjs/core'

@Controller('online-scripts')
@ApiTags('在线脚本')
export class OnlineScriptController {
  constructor(
    private readonly onlineScriptService: OnlineScriptService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Get('latest')
  @ApiOperation({ summary: '获取最新在线脚本' })
  @ApiOkResponse({ type: GetLatestResponseDTO })
  async getLatest(): Promise<LatestResponseDTO> {
    return await this.onlineScriptService.getLatest()
  }

  @Get('platform/latest')
  @ApiOperation({ summary: '获取最新在线版本' })
  @ApiOkResponse({ type: GetDesktopLatestResponseDTO })
  async getDesktopLatest(@Query() query: DesktopLatestRequest) {
    return await this.onlineScriptService.getDesktopLatest(query)
  }

  @Post('baidu')
  @ApiOperation({ summary: '生成百度上报' })
  @ApiOkResponse({ type: GetDesktopLatestResponseDTO })
  async postBaiduReport(@Body() body: BaiduReportRequest) {
    const origin = this.request.headers.origin as string

    return await this.onlineScriptService.postBaiduReport(body, origin)
  }
}
