import { Body, Controller, Delete, Get, Injectable, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { TopicsService } from './topics.service'
import { TopicsGroupService } from './topics-group.service'
import {
  PostTopicsGroupsRequest,
  PostTopicsRequest,
  TopicsDetailResponseDTO,
  TopicsGroupListResponseDTO,
  TopicsGroupResponseDTO,
  TopicsListResponseDTO
} from './topics.dto'

@Controller('topics')
@ApiTags('话题管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class TopicsController {
  constructor(
    private readonly topicsGroupService: TopicsGroupService,
    private readonly topicsService: TopicsService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取话题列表' })
  @ApiParam({ name: 'groupId', required: false, description: '分组ID 全部分组则不传' })
  @ApiParam({ name: 'size', required: false, description: '每页显示数量 默认10' })
  @ApiParam({ name: 'page', required: false, description: '分页页码 默认1' })
  @ApiOkResponse({ type: TopicsListResponseDTO, description: '操作成功' })
  async getMaterial(
    @Query('groupId') groupId: string,
    @Query('size', { transform: (value) => value || 10 }) size: number,
    @Query('page', { transform: (value) => value || 1 }) page: number
  ) {
    return await this.topicsService.getTopics(groupId, page, size)
  }

  @Post()
  @ApiOperation({ summary: '新增话题' })
  @ApiOkResponse({ type: TopicsDetailResponseDTO, description: '操作成功' })
  async postMaterial(@Body() body: PostTopicsRequest) {
    return await this.topicsService.postTopics(body)
  }

  @Delete(':topicId')
  @ApiOperation({ summary: '删除话题' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deleteMaterial(@Param('topicId') topicId: string) {
    return await this.topicsService.deleteTopics(topicId)
  }

  @Get('groups')
  @ApiOperation({ summary: '获取话题分组列表' })
  @ApiOkResponse({ type: TopicsGroupListResponseDTO, description: '操作成功' })
  async getGroups() {
    return await this.topicsGroupService.getTopicsGroupsAsync()
  }

  @Post('groups')
  @ApiOperation({ summary: '创建话题分组' })
  @ApiOkResponse({ type: TopicsGroupResponseDTO, description: '操作成功' })
  async postGroups(@Body() body: PostTopicsGroupsRequest) {
    return await this.topicsGroupService.postTopicsGroups(body)
  }

  @Put('groups/:groupId')
  @ApiOperation({ summary: '修改话题分组' })
  @ApiOkResponse({ type: TopicsGroupResponseDTO, description: '操作成功' })
  async putGroups(@Param('groupId') groupId: string, @Body() body: PostTopicsGroupsRequest) {
    return await this.topicsGroupService.putTopicsGroupsAsync(groupId, body)
  }

  @Delete('groups/:groupId')
  @ApiOperation({ summary: '删除话题分组' })
  @ApiOkResponse()
  async deleteGroups(@Param('groupId') groupId: string) {
    return await this.topicsGroupService.deleteTopicsGroupsAsync(groupId)
  }
}
