import { Module } from '@nestjs/common'
import { AdController } from './ad.controller'
import { AdService } from './ad.service'
import { AdMongoose } from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { AdminOssModule } from '../ali-oss/admin-oss.module'
import { WebhookModule } from '../webhook/webhook.module'

@Module({
  imports: [AdMongoose, AdminOssModule, WebhookModule, CommonModule],
  controllers: [AdController],
  providers: [AdService]
})
export class AdModule {}
