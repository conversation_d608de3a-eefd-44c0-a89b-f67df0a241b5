import { ConflictException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { MaterialLibraryGroupEntity } from '@yxr/mongo'
import { MaterialGroupDTO, PostMaterialGroupsRequest } from './material.dto'

@Injectable()
export class MaterialGroupService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(MaterialLibraryGroupEntity.name)
    private materialLibraryGroupModel: Model<MaterialLibraryGroupEntity>
  ) {}

  async postGroups(body: PostMaterialGroupsRequest) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const group = await this.materialLibraryGroupModel.findOne({
      name: body.name,
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (group) {
      throw new ConflictException('分组已存在')
    }

    const data = await this.materialLibraryGroupModel.create({
      name: body.name,
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    return {
      id: data._id.toString(),
      name: data.name,
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 分组列表
   */
  async getGroupsAsync(): Promise<MaterialGroupDTO[]> {
    const { teamId: currentTeamId } = this.request.session

    const data = await this.materialLibraryGroupModel
      .find({
        teamId: new Types.ObjectId(currentTeamId)
      })
      .sort({ createdAt: 'desc' })

    return data.map((item) => ({
      id: item._id.toString(),
      name: item.name
    }))
  }

  /**
   * 修改分组
   * @param groupId
   * @param body
   */
  async putGroupsAsync(
    groupId: string,
    body: PostMaterialGroupsRequest
  ): Promise<MaterialGroupDTO> {
    const { teamId: currentTeamId } = this.request.session
    let group = await this.materialLibraryGroupModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      _id: new Types.ObjectId(groupId)
    })

    if (!group) {
      throw new NotFoundException('分组不存在')
    }

    const hasGroupName = await this.materialLibraryGroupModel.findOne({
      name: body.name,
      _id: { $ne: groupId },
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (hasGroupName) {
      throw new ConflictException('分组名称已存在')
    }

    const data = await this.materialLibraryGroupModel.findOneAndUpdate(
      { _id: group._id },
      {
        $set: {
          name: body.name
        }
      }, // 使用 $set 只更新传入的字段
      { returnDocument: 'after', new: true } // 返回更新后的文档
    )

    return {
      id: data._id.toString(),
      name: data.name
    }
  }

  /**
   * 删除分组
   * @param groupId
   */
  async deleteGroupsAsync(groupId: string) {
    const { teamId: currentTeamId } = this.request.session
    const group = await this.materialLibraryGroupModel.findOne({
      _id: new Types.ObjectId(groupId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    //todo: 清空分组下面绑定的素材
    await this.materialLibraryGroupModel.deleteOne({
      _id: new Types.ObjectId(group._id)
    })
  }
}
