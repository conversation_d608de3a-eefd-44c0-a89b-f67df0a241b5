import { Controller, Get, Query } from '@nestjs/common'
import { ApiOkResponse, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger'
import {
  FindAllAccountsInputDto,
  FindAllAccountsOutputDto,
  FindAllAccountsOutputResponse
} from './webapp-accounts-query.dto'
import { WebappAccountsQueryService } from './webapp-accounts-query.service'

@Controller('webapp-platform-accounts')
@ApiTags('媒体账号管理')
export class WebappAccountsController {
  constructor(private readonly queryService: WebappAccountsQueryService) {}

  @Get()
  @ApiOperation({ summary: '查询发布记录' })
  @ApiOkResponse({ type: FindAllAccountsOutputResponse })
  @ApiQuery({ type: FindAllAccountsInputDto })
  findAll(@Query() input: FindAllAccountsInputDto): Promise<FindAllAccountsOutputDto> {
    return this.queryService.findAll(input)
  }

  // 设置代理
}

