import { BrowserEntity, BrowserFavoritesEntity, PlatformAccountEntity } from '@yxr/mongo'
import { BrowserFavoriteDTO, PostBrowserCollectRequest } from './browser.dto'
import { Document, Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { Inject, Injectable, NotFoundException } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { BrowserDeleteEvent, EventNames } from '@yxr/common'

@Injectable()
export class BrowserCollectService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(BrowserFavoritesEntity.name)
    private browserFavoritesModel: Model<BrowserFavoritesEntity>,
    private eventEmitter: EventEmitter2,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>
  ) {}

  /**
   * 浏览器收藏修改
   * @param body
   */
  async PostBrowserCollect(
    platformAccountId: string,
    body: PostBrowserCollectRequest
  ): Promise<BrowserFavoriteDTO> {
    const { session } = this.request

    const browser = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(session.teamId)
    })
    if (!browser) {
      throw new NotFoundException('浏览器空间未找到')
    }

    const collect = await this.browserFavoritesModel.findOne({
      websiteUrl: body.websiteUrl,
      platformAccountId: browser._id
    })

    let result: Document<unknown, {}, BrowserFavoritesEntity> &
      BrowserFavoritesEntity & { _id: Types.ObjectId }
    if (collect) {
      //修改
      result = await this.browserFavoritesModel.findOneAndUpdate(
        { _id: collect._id },
        {
          $set: {
            name: body.name
          }
        }, // 使用 $set 只更新传入的字段
        { returnDocument: 'after', new: true } // 返回更新后的文档
      )
    } else {
      //新增
      result = await this.browserFavoritesModel.create({
        browserId: browser._id,
        platformAccountId: new Types.ObjectId(browser._id),
        name: body.name,
        websiteUrl: body.websiteUrl
      })
    }

    return {
      id: result.id,
      name: result.name,
      websiteUrl: result.websiteUrl,
      browserId: result.browserId.toString()
    }
  }

  /**
   * 删除浏览器收藏
   * @param platformAccountId
   * @param collectId
   */
  async DelectBrowserCollect(platformAccountId: string, collectId: string) {
    const { session } = this.request

    await this.browserFavoritesModel.deleteOne({
      _id: new Types.ObjectId(collectId)
    })

    //触发网站空间关联数据变更事件
    await this.eventEmitter.emitAsync(
      EventNames.BrowserDeleteEvent,
      new BrowserDeleteEvent(platformAccountId, session.teamId, collectId)
    )
  }
}
