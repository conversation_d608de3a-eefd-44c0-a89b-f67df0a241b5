variables:
  HUOSHAN_DOCKER_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${HUOSHAN_CI_IMAGE_NAME}'
  HUOSHAN_DOCKER_SOCKET_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${HUOSHAN_CI_SOCKET_IMAGE_NAME}'
  HUOSHAN_DOCKER_ADMIN_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${HUOSHAN_CI_ADMIN_IMAGE_NAME}'
  DOCKER_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_IMAGE_NAME}'
  DOCKER_SOCKET_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_SOCKET_IMAGE_NAME}'
  DOCKER_ADMIN_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_ADMIN_IMAGE_NAME}'
  DOCKER_OVERSEAVICE_IMAGE: '${CI_DOCKER_REGISTRY}/${CI_DOCKER_NAMESPACES}/${CI_OVERSEAVICE_IMAGE_NAME}'

stages:
  - build-gateway-dev
  - deploy-gateway-dev

  - build-admin-dev
  - deploy-admin-dev

  - build-socket-dev
  - deploy-socket-dev

  - build-overseavice-dev
#  - deploy-overseavice-dev

  - build-gateway-test
  - deploy-gateway-test

  - build-admin-test
  - deploy-admin-test

  - build-socket-test
  - deploy-socket-test

  - build-overseavice-test
#  - deploy-overseavice-test

  - build-gateway-prod
  - deploy-gateway-prod

  - build-admin-prod
  - deploy-admin-prod

  - build-socket-prod
  - deploy-socket-prod

  - build-overseavice-prod
#  - deploy-overseavice-prod

# -------------------------- dev -------------------------------
development:build-gateway:
  image: docker:stable
  stage: build-gateway-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - huoshan
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $HUOSHAN_DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev
      docker push $HUOSHAN_DOCKER_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME --force

# development:deploy-gateway:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-gateway-dev
#   only:
#     changes:
#       - apps/gateway/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.dev
#     refs:
#       - huoshan
#   variables:
#     ENV_NAME: dev
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_IMAGE_NAME}-dev'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_IMAGE:$TAG_NAME -n default
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

# -------------------------------admin-dev-----------------------------
development:build-admin:
  image: docker:stable
  stage: build-admin-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - huoshan
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME --force

# development:deploy-admin:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-admin-dev
#   only:
#     changes:
#       - apps/admin/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.dev
#     refs:
#       - main
#   variables:
#     ENV_NAME: dev
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_ADMIN_IMAGE_NAME}-dev'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME -n default
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

# -------------------------------socket-dev-----------------------------
development:build-socket:
  image: docker:stable
  stage: build-socket-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - huoshan
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME --force

# development:deploy-socket:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-socket-dev
#   only:
#     changes:
#       - apps/socket/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.dev
#     refs:
#       - main
#   variables:
#     ENV_NAME: dev
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_SOCKET_IMAGE_NAME}-dev'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_SOCKET_IMAGE:$TAG_NAME -n default
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL



# -------------------------------overseavice-dev-----------------------------
development:build-overseavice:
  image: docker:stable
  stage: build-overseavice-dev
  variables:
    ENV_NAME: dev
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/overseavice/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.dev
    refs:
      - huoshan
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/overseavice/Dockerfile" -t $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=dev
      docker push $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME
      docker tag $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME
      docker push $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME
      docker rmi $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME --force

#development:deploy-overseavice:
#  image:
#    name: bitnami/kubectl:latest
#    entrypoint: ['']
#  stage: deploy-overseavice-dev
#  only:
#    changes:
#      - apps/overseavice/**/*
#      - packages/**/*
#      - .gitlab-ci.yml
#      - .env.dev
#    refs:
#      - main
#  variables:
#    ENV_NAME: dev
#    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#    CI_POD_DEV: '${CI_OVERSEAVICE_IMAGE_NAME}-dev'
#  script:
#    - echo "$KUBE_CONFIG" > /.kube/config
#    - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME -n default
#  environment:
#    name: Development
#    url: $CI_ENVIRONMENT_URL

# -------------------------- test -------------------------------

test:build-gateway:
  image: docker:stable
  stage: build-gateway-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - huoshan-pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $HUOSHAN_DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test
      docker push $HUOSHAN_DOCKER_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME --force
# test:deploy-gateway:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-gateway-test
#   only:
#     changes:
#       - apps/gateway/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.test
#     refs:
#       - huoshan-pre-production
#   variables:
#     ENV_NAME: test
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_IMAGE_NAME}-test'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

#     # ------------------------admin-test------------------------#

test:build-admin:
  image: docker:stable
  stage: build-admin-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - huoshan-pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME --force
# test:deploy-admin:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-admin-test
#   only:
#     changes:
#       - apps/admin/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.test
#     refs:
#       - pre-production
#   variables:
#     ENV_NAME: test
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_ADMIN_IMAGE_NAME}-test'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

test:build-socket:
  image: docker:stable
  stage: build-socket-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - huoshan-pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME --force
# test:deploy-socket:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-socket-test
#   only:
#     changes:
#       - apps/socket/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.test
#     refs:
#       - pre-production
#   variables:
#     ENV_NAME: test
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_SOCKET_IMAGE_NAME}-test'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_SOCKET_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

test:build-overseavice:
  image: docker:stable
  stage: build-overseavice-test
  variables:
    ENV_NAME: test
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/overseavice/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.test
    refs:
      - huoshan-pre-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/overseavice/Dockerfile" -t $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=test
      docker push $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME
      docker tag $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME
      docker push $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME
      docker rmi $DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME $DOCKER_OVERSEAVICE_IMAGE:$ENV_NAME --force

#test:deploy-overseavice:
#  image:
#    name: bitnami/kubectl:latest
#    entrypoint: ['']
#  stage: deploy-overseavice-test
#  only:
#    changes:
#      - apps/overseavice/**/*
#      - packages/**/*
#      - .gitlab-ci.yml
#      - .env.test
#    refs:
#      - pre-production
#  variables:
#    ENV_NAME: test
#    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#    CI_POD_DEV: '${CI_OVERSEAVICE_IMAGE_NAME}-test'
#  script:
#    - echo "$KUBE_CONFIG" > /.kube/config
#    - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_OVERSEAVICE_IMAGE:$TAG_NAME -n default
#    - kubectl get pods
#  environment:
#    name: Development
#    url: $CI_ENVIRONMENT_URL

# ------------------------------ prod --------------------------------

prod:build-gateway:
  image: docker:stable
  stage: build-gateway-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/gateway/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - huoshan-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/gateway/Dockerfile" -t $HUOSHAN_DOCKER_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod
      docker push $HUOSHAN_DOCKER_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_IMAGE:$ENV_NAME --force
# prod:deploy-gateway:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-gateway-prod
#   only:
#     changes:
#       - apps/gateway/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.prod
#     refs:
#       - production
#   variables:
#     ENV_NAME: prod
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_IMAGE_NAME}-prod'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

#     # ------------------------admin-prod------------------------#

prod:build-admin:
  image: docker:stable
  stage: build-admin-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/admin/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - huoshan-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/admin/Dockerfile" -t $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_ADMIN_IMAGE:$ENV_NAME --force
# prod:deploy-admin:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-admin-prod
#   only:
#     changes:
#       - apps/admin/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.prod
#     refs:
#       - production
#   variables:
#     ENV_NAME: prod
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_ADMIN_IMAGE_NAME}-prod'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$HUOSHAN_DOCKER_ADMIN_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL

prod:build-socket:
  image: docker:stable
  stage: build-socket-prod
  variables:
    ENV_NAME: prod
    TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
  only:
    changes:
      - apps/socket/**/*
      - packages/**/*
      - .gitlab-ci.yml
      - .env.prod
    refs:
      - huoshan-production
  script:
    - |
      docker login -u "$CI_DOCKER_USERNAME" -p "$CI_DOCKER_PASSWORD" $CI_DOCKER_REGISTRY >> /dev/null
      docker build --rm -f "apps/socket/Dockerfile" -t $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME . --build-arg ENV_TYPE=prod
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME
      docker tag $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker push $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME
      docker rmi $HUOSHAN_DOCKER_SOCKET_IMAGE:$TAG_NAME $HUOSHAN_DOCKER_SOCKET_IMAGE:$ENV_NAME --force

# prod:deploy-socket:
#   image:
#     name: bitnami/kubectl:latest
#     entrypoint: ['']
#   stage: deploy-socket-prod
#   only:
#     changes:
#       - apps/socket/**/*
#       - packages/**/*
#       - .gitlab-ci.yml
#       - .env.prod
#     refs:
#       - production
#   variables:
#     ENV_NAME: prod
#     TAG_NAME: '${ENV_NAME}-${CI_PIPELINE_ID}'
#     CI_POD_DEV: '${HUOSHAN_CI_SOCKET_IMAGE_NAME}-prod'
#   script:
#     - echo "$KUBE_CONFIG" > /.kube/config
#     - kubectl set image deployment/$CI_POD_DEV $CI_POD_DEV=$DOCKER_SOCKET_IMAGE:$TAG_NAME -n default
#     - kubectl get pods
#   environment:
#     name: Development
#     url: $CI_ENVIRONMENT_URL
