import { Module } from '@nestjs/common'
import { OnlineScriptController } from './online-script.controller'
import { OnlineScriptService } from './online-script.service'
import { AdminMongoose, OnlineScriptMongoose } from '@yxr/mongo'
import { WebhookModule } from '../webhook/webhook.module'
import { AdminOssModule } from '../ali-oss/admin-oss.module'
import { CommonModule } from '@yxr/common'

@Module({
  imports: [OnlineScriptMongoose, AdminMongoose, WebhookModule, AdminOssModule, CommonModule],
  controllers: [OnlineScriptController],
  providers: [OnlineScriptService]
})
export class OnlineScriptModule {}
