import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common'
import { OverseasProviderFactory, PublishTaskData, PublishResult, PublishTaskStatus, OverseasContext } from '@yxr/overseas'
import { CreatePublishTaskDto, PublishResultDto, GetPublishStatusDto, PublishTaskCallbackDto } from './content-publish.dto'
import axios from 'axios'

@Injectable()
export class ContentPublishService {
  private readonly logger = new Logger(ContentPublishService.name)

  constructor(
    private readonly overseasProviderFactory: OverseasProviderFactory
  ) {}

  /**
   * 创建发布任务
   */
  async createPublishTask(dto: CreatePublishTaskDto): Promise<PublishResultDto> {
    this.logger.log(`创建发布任务: ${dto.taskId}, 平台: ${dto.platform}`)

    try {
      // 获取平台发布提供者
      const publishProvider = await this.overseasProviderFactory.contentPublishProvider(dto.platform)

      // 构建上下文
      const context: OverseasContext = {
        accountOpenId: dto.accountOpenId,
        teamId: dto.teamId,
        userId: dto.userId,
        options: {
          credentials: dto.credentials
        }
      }

      // 验证发布内容
      const validation = await publishProvider.validateContent(context, dto.content)
      if (!validation.valid) {
        throw new BadRequestException(`内容验证失败: ${validation.errors?.join(', ')}`)
      }

      // 构建发布任务数据
      const taskData: PublishTaskData = {
        taskId: dto.taskId,
        taskSetId: dto.taskSetId,
        teamId: dto.teamId,
        userId: dto.userId,
        accountOpenId: dto.accountOpenId,
        platform: dto.platform,
        content: dto.content,
        publishAt: dto.publishAt ? new Date(dto.publishAt) : undefined,
        callbackUrl: dto.callbackUrl,
        maxRetries: dto.maxRetries || 3,
        createdAt: new Date()
      }

      // 执行发布
      const result = await publishProvider.publishContent(context, taskData)

      // 异步回调结果到国内服务
      this.sendCallbackAsync(dto.callbackUrl, result)

      return this.mapToPublishResultDto(result)
    } catch (error) {
      this.logger.error(`发布任务失败: ${dto.taskId}`, error)

      const errorResult: PublishResult = {
        taskId: dto.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'UNKNOWN_ERROR',
        completedAt: new Date()
      }

      // 异步回调错误结果
      this.sendCallbackAsync(dto.callbackUrl, errorResult)

      return this.mapToPublishResultDto(errorResult)
    }
  }

  /**
   * 批量创建发布任务
   */
  async batchCreatePublishTasks(tasks: CreatePublishTaskDto[]): Promise<PublishResultDto[]> {
    this.logger.log(`批量创建发布任务: ${tasks.length} 个任务`)

    const results: PublishResultDto[] = []

    // 并发执行发布任务
    const promises = tasks.map(async (task) => {
      try {
        return await this.createPublishTask(task)
      } catch (error) {
        this.logger.error(`批量发布任务失败: ${task.taskId}`, error)
        return this.mapToPublishResultDto({
          taskId: task.taskId,
          status: PublishTaskStatus.Failed,
          errorMessage: error.message,
          completedAt: new Date()
        })
      }
    })

    const batchResults = await Promise.allSettled(promises)

    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      } else {
        this.logger.error(`批量任务执行失败: ${tasks[index].taskId}`, result.reason)
        results.push(this.mapToPublishResultDto({
          taskId: tasks[index].taskId,
          status: PublishTaskStatus.Failed,
          errorMessage: result.reason?.message || '未知错误',
          completedAt: new Date()
        }))
      }
    })

    return results
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(dto: GetPublishStatusDto, platform: string, accountOpenId: string): Promise<PublishResultDto> {
    this.logger.log(`查询发布状态: ${dto.taskId}, 平台: ${platform}`)

    try {
      const publishProvider = await this.overseasProviderFactory.contentPublishProvider(platform)

      const context: OverseasContext = {
        accountOpenId: accountOpenId,
        options: {
          credentials: {} // 这里需要从数据库或缓存中获取认证信息
        }
      }

      const result = await publishProvider.getPublishStatus(context, dto.taskId, dto.platformContentId)
      return this.mapToPublishResultDto(result)
    } catch (error) {
      this.logger.error(`查询发布状态失败: ${dto.taskId}`, error)
      throw new InternalServerErrorException(`查询发布状态失败: ${error.message}`)
    }
  }

  /**
   * 异步发送回调到国内服务
   */
  private async sendCallbackAsync(callbackUrl: string, result: PublishResult): Promise<void> {
    // console.log(result)

    try {
      const callbackData: PublishTaskCallbackDto = {
        taskId: result.taskId,
        status: result.status,
        platformContentId: result.platformContentId,
        platformContentUrl: result.platformContentUrl,
        errorMessage: result.errorMessage,
        errorCode: result.errorCode,
        rawResponse: result.rawResponse,
        completedAt: result.completedAt.toISOString()
      }

      // console.log(callbackData)
      // 异步发送回调，不阻塞主流程
      setImmediate(async () => {
        try {

          // return await this.taskService.patchStatus(input.taskId, {
          //   deliveryToken: '',  // TODO: 需要根据实际情况设置
          //   documentId: '',
          //   publishId: '',
          //   openUrl: '',
          //   stageStatus: StageStatus.Doing,
          //   stages: TaskStages.Push,
          //   errorMessage: '',
          //   mediaType: ''
          // } as PatchStatusRequest)

          await axios.post(callbackUrl, callbackData, {
            timeout: 100,  // 30000, // TODO: 30秒超时
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'OverseasService/1.0'
            }
          })
          this.logger.log(`回调发送成功: ${result.taskId}`)
        } catch (error) {
          this.logger.error(`回调发送失败: ${result.taskId}`, error)
          // TODO: 可以考虑实现重试机制或将失败的回调存储到队列中
        }
      })
    } catch (error) {
      this.logger.error(`准备回调数据失败: ${result.taskId}`, error)
    }
  }

  /**
   * 将PublishResult映射为DTO
   */
  private mapToPublishResultDto(result: PublishResult): PublishResultDto {
    return {
      taskId: result.taskId,
      status: result.status,
      platformContentId: result.platformContentId,
      platformContentUrl: result.platformContentUrl,
      errorMessage: result.errorMessage,
      errorCode: result.errorCode,
      completedAt: result.completedAt
    }
  }

  /**
   * 获取平台支持的内容类型
   */
  async getSupportedContentTypes(platform: string): Promise<string[]> {
    try {
      const publishProvider = await this.overseasProviderFactory.contentPublishProvider(platform)
      return publishProvider.getSupportedContentTypes()
    } catch (error) {
      this.logger.error(`获取平台支持的内容类型失败: ${platform}`, error)
      return []
    }
  }

  /**
   * 获取平台内容限制
   */
  async getContentLimits(platform: string): Promise<any> {
    try {
      const publishProvider = await this.overseasProviderFactory.contentPublishProvider(platform)
      return publishProvider.getContentLimits()
    } catch (error) {
      this.logger.error(`获取平台内容限制失败: ${platform}`, error)
      return {}
    }
  }
}
