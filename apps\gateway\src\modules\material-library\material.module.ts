import { Module } from '@nestjs/common'
import { AuthorizationService } from '../../common/security/authorization.service'
import {
  MaterialLibraryGroupMongoose,
  MaterialLibraryMongoose,
  MemberMongoose,
  TeamMongoose
} from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { MaterialController } from './material.controller'
import { MaterialGroupService } from './material-group.service'
import { MaterialService } from './material.service'
import { TeamModule } from '../team/team.module'

@Module({
  imports: [
    TeamMongoose,
    MemberMongoose,
    MaterialLibraryGroupMongoose,
    MaterialLibraryMongoose,
    CommonModule,
    TeamModule
  ],
  controllers: [MaterialController],
  providers: [MaterialGroupService, MaterialService, AuthorizationService]
})
export class MaterialModule {}
