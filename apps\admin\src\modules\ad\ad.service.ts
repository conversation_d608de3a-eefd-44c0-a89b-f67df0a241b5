import { InjectModel } from '@nestjs/mongoose'
import {
  AdCreateRequestDTO,
  AdDTO,
  AdListRequestDTO,
  AdListResponse,
  PutAdEnabledRequestDTO
} from './ad.dto'
import { FilterQuery, Model, Types } from 'mongoose'
import { AdEntity } from '@yxr/mongo'
import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { AdminOssService } from '../ali-oss/admin-oss.service'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { AdTypeEmun } from 'packages/common'

@Injectable()
export class AdService {
  constructor(
    @InjectModel(AdEntity.name) private adModel: Model<AdEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly webhookService: WebhookService,
    private readonly adminOssService: AdminOssService
  ) {}

  /**
   * 获取广告列表
   * @param query
   * @returns
   */
  async getAds(query: AdListRequestDTO): Promise<AdListResponse> {
    const filter: FilterQuery<AdEntity> = {}

    if (query.name) {
      filter.name = { $regex: query.name, $options: 'i' }
    }
    if (query.enabled) {
      filter.enabled = query.enabled === 'true'
    }
    if (query.adType) {
      filter.adType = query.adType
    }

    const ads = await this.adModel
      .find(filter)
      .sort({ sort: -1, updatedAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const totalSize = await this.adModel.find(filter).countDocuments()

    return {
      page: query.page,
      size: query.size,
      totalSize: totalSize,
      totalPage: Math.ceil(totalSize / query.size),
      data: await Promise.all(
        ads.map(async (item) => ({
          id: item.id,
          name: item.name,
          adType: item.adType,
          popupType: item.popupType,
          adPath: `${process.env.OSS_DOWNLOAD_URL}/${item.adUrl}?v=${item.__v}`,
          adUrl: item.adUrl,
          adminName: item.adminName,
          sort: item.sort,
          isJumpTo: item.isJumpTo,
          jumpToUrl: item.jumpToUrl,
          enabled: item.enabled,
          isTimed: item.isTimed,
          expiredStartAt: item.expiredStartAt ? item.expiredStartAt.getTime() : 0,
          expiredEndAt: item.expiredEndAt ? item.expiredEndAt.getTime() : 0
        }))
      )
    }
  }

  /**
   * 创建广告
   * @param body
   */
  async createAd(body: AdCreateRequestDTO) {
    const { user } = this.request

    const createData: AdEntity = {
      adType: body.adType,
      adminName: 'name' in user ? user.name : '',
      sort: body.sort,
      name: body.name,
      enabled: body.enabled,
      isTimed: body.isTimed,
      isJumpTo: body.isJumpTo
    }

    if (createData.adType === AdTypeEmun.Popup) {
      //只允许上架一个
      const popupAd = await this.adModel.findOne({
        adType: AdTypeEmun.Popup,
        enabled: true
      })
      if (popupAd) {
        createData.enabled = false //有上架的新增默认未下架状态
      }
    }
    if (createData.isJumpTo === true) {
      if (!body.jumpToUrl) {
        throw new ForbiddenException('跳转地址不能为空')
      }
      createData.jumpToUrl = body.jumpToUrl
    }
    if (createData.isTimed === true) {
      if (!body.expiredStartAt || !body.expiredEndAt) {
        throw new ForbiddenException('有效时间不能为空')
      }
      createData.expiredStartAt = new Date(Number(body.expiredStartAt))
      createData.expiredEndAt = new Date(Number(body.expiredEndAt))
    }
    if (body.popupType) {
      createData.popupType = body.popupType
    }
    if (body.adUrl) {
      const buffer = Buffer.from(body.adUrl, 'base64')
      const envPrefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}`
      const name = `banner/${envPrefix}/${Date.now()}.png`
      const url = await this.adminOssService.uploadFile(buffer, name)
      createData.adUrl = url
    }

    await this.adModel.create(createData)

    if (createData.adType === AdTypeEmun.Banner) {
      await this.webhookService.sendToAll({
        event: WebhookEvents.BannerStatusUpdated
      })
    }
  }

  /**
   * 获取广告详情
   * @param adId
   * @returns
   */
  async getAdDetail(adId: string): Promise<AdDTO> {
    const ad = await this.adModel.findOne({
      _id: new Types.ObjectId(adId)
    })

    if (!ad) {
      throw new NotFoundException('广告未找到')
    }

    return {
      sort: ad.sort,
      name: ad.name,
      adUrl: ad.adUrl,
      adPath: `${process.env.OSS_DOWNLOAD_URL}/${ad.adUrl}?v=${ad.__v}`,
      isJumpTo: ad.isJumpTo,
      jumpToUrl: ad.jumpToUrl,
      enabled: ad.enabled,
      adType: ad.adType,
      popupType: ad.popupType,
      expiredStartAt: ad.expiredStartAt ? ad.expiredStartAt.getTime() : 0,
      expiredEndAt: ad.expiredEndAt ? ad.expiredEndAt.getTime() : 0
    }
  }

  /**
   * 更新广告
   * @param adId
   * @param body
   */
  async patchAdDetail(adId: string, body: AdCreateRequestDTO) {
    const { user } = this.request
    const ad = await this.adModel.findOne({
      _id: new Types.ObjectId(adId)
    })

    if (!ad) {
      throw new NotFoundException('广告未找到')
    }

    ad.sort = body.sort
    ad.name = body.name
    ad.isTimed = body.isTimed
    ad.isJumpTo = body.isJumpTo
    ad.adminName = 'name' in user ? user.name : ''
    if (ad.adType === AdTypeEmun.Popup && ad.enabled === false && body.enabled === true) {
      //只允许上架一个
      const popupAd = await this.adModel.findOne({
        adType: AdTypeEmun.Popup,
        enabled: true
      })
      if (popupAd) {
        throw new ForbiddenException('已有弹窗上架，无法同时上架2个')
      }
    }
    ad.enabled = body.enabled
    if (body.isJumpTo === true) {
      if (!body.jumpToUrl) {
        throw new ForbiddenException('跳转地址不能为空')
      }
      ad.jumpToUrl = body.jumpToUrl
    }
    if (body.isTimed === true) {
      if (!body.expiredStartAt || !body.expiredEndAt) {
        throw new ForbiddenException('有效时间不能为空')
      }
      ad.expiredStartAt = new Date(Number(body.expiredStartAt))
      ad.expiredEndAt = new Date(Number(body.expiredEndAt))
    }
    if (body.popupType) {
      ad.popupType = body.popupType
    }
    if (body.adUrl) {
      if (ad.adUrl) {
        await this.adminOssService.deleteOssObject(ad.adUrl)
      }
      const buffer = Buffer.from(body.adUrl, 'base64')
      const envPrefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}`
      const name = `banner/${envPrefix}/${Date.now()}.png`
      const url = await this.adminOssService.uploadFile(buffer, name)
      ad.adUrl = url
    }
    await ad.save()

    if (ad.adType === AdTypeEmun.Banner) {
      await this.webhookService.sendToAll({
        event: WebhookEvents.BannerStatusUpdated
      })
    }
  }

  /**
   * 上下架
   * @param adId
   * @param body
   */
  async putAdEnabled(adId: string, body: PutAdEnabledRequestDTO) {
    const { user } = this.request
    const ad = await this.adModel.findOne({
      _id: new Types.ObjectId(adId)
    })

    if (!ad) {
      throw new NotFoundException('广告未找到')
    }
    if (ad.adType === AdTypeEmun.Popup && ad.enabled === false && body.enabled === true) {
      //只允许上架一个
      const popupAd = await this.adModel.findOne({
        adType: AdTypeEmun.Popup,
        enabled: true
      })
      if (popupAd) {
        throw new ForbiddenException('已有弹窗上架，无法同时上架2个')
      }
    }
    ad.enabled = body.enabled
    ad.adminName = 'name' in user ? user.name : ''
    await ad.save()

    if (ad.adType === AdTypeEmun.Banner) {
      await this.webhookService.sendToAll({
        event: WebhookEvents.BannerStatusUpdated
      })
    }
  }

  /**
   * 删除广告
   * @param adId
   */
  async deleteAd(adId: string) {
    const ad = await this.adModel.findOne({
      _id: new Types.ObjectId(adId)
    })

    if (!ad) {
      throw new NotFoundException('广告未找到')
    }

    await this.adModel.deleteOne({
      _id: ad._id
    })

    if (ad.adUrl) {
      await this.adminOssService.deleteOssObject(ad.adUrl)
    }

    await this.webhookService.sendToAll({
      event: WebhookEvents.BannerStatusUpdated
    })
  }
}
