import { ApiProperty, ApiResponseProperty } from "@nestjs/swagger"
import { BaseResponseDTO } from "../../common/dto/BaseResponseDTO"

export class getOssSignatureDto {
  @ApiProperty({
    type: String,
    description: '资源上传地址, 客户端应该将二进制资源通过PUT的形式上传至此地址',
    example:
      'https://yixiaoer-lite-asserts.oss-cn-shanghai.aliyuncs.com/local/t-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v?OSSAccessKeyId=LTAI5tGLVT13SpqTufgPaLaz&Expires=1723696250&Signature=zWGnG9bkqfTMAHD5VDgTzpPthEA%3D',
    required: true
  })
  serviceUrl: string

  @ApiProperty({
    type: String,
    description: '资源存储KEY, 这个KEY应该作为相关业务资源存储的标识提交给业务接口',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: true
  })
  key: string
}

export class getOssResourceUrlResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: String
  })
  data: string
}

export class getOssSignatureResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: getOssSignatureDto
  })
  data: getOssSignatureDto
}
