import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import { DeviceLogsRequestBodyDTO } from './user-devices.dto'
import { REQUEST } from '@nestjs/core'
import { type FastifyRequest } from 'fastify'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { UserDeviceLogsEntity, UserDevicesEntity, UserEntity } from '@yxr/mongo'
import { VersionService } from '@yxr/common'

@Injectable()
export class UserDevicesService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(UserDevicesEntity.name) private userDevicesModel: Model<UserDevicesEntity>,
    @InjectModel(UserDeviceLogsEntity.name) private userDeviceLogsModel: Model<UserDeviceLogsEntity>
  ) {}

  async putUserDevices(phone: string, deviceId: string) {
    if (deviceId) {
      const user = await this.userModel.findOne({
        phone: phone
      })
      if (!user) {
        throw new NotFoundException('用户不存在')
      }
      const version = this.request.client?.version ?? ''
      const platform = this.request.client?.platform
      const numberVersion = version
        ? VersionService.versionToNumber(
            ...(VersionService.parseVersion(version) as [number, number, number])
          )
        : 0
      const userDevice = await this.userDevicesModel.findOne({
        userId: new Types.ObjectId(user.id),
        deviceId: deviceId
      })

      if (userDevice) {
        await this.userDevicesModel.updateOne(
          {
            _id: userDevice.id
          },
          {
            version: version,
            numberVersion: numberVersion,
            loginTime: new Date(),
            osType: platform,
            isActive: true
          }
        )
      } else {
        await this.userDevicesModel.insertOne({
          version: version,
          numberVersion: numberVersion,
          loginTime: new Date(),
          osType: platform,
          isActive: true,
          deviceId: deviceId,
          userId: new Types.ObjectId(user.id)
        })
      }
    }
  }

  /**
   * 生成设备日志
   * @param body
   */
  async postDeviceLogs(logId: string, body: DeviceLogsRequestBodyDTO) {
    const version = this.request.client?.version ?? ''
    const deviceLog = await this.userDeviceLogsModel.findById(new Types.ObjectId(logId))

    if (!deviceLog) {
      throw new ForbiddenException('设备日志不存在')
    }

    deviceLog.downloadLink = body.downloadLink
    if (version) {
      deviceLog.version = version
      deviceLog.numberVersion = VersionService.versionToNumber(
        ...(VersionService.parseVersion(version) as [number, number, number])
      )
    }
    await deviceLog.save()
  }
}
