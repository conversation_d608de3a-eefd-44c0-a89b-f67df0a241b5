import { Injectable, Logger, Scope } from '@nestjs/common'
import { AccountAuthProvider } from '../account-auth.provider'
import { OverseasContext, PlatformAccountInfo } from '../types'
import { Facebook<PERSON><PERSON> } from './facebook-api'

@Injectable({ scope: Scope.TRANSIENT })
export class FacebookAcco<PERSON><PERSON><PERSON>Provider implements AccountAuthProvider {
  logger = new Logger(FacebookAccountAuthProvider.name)

  private readonly clientId = process.env.FACEBOOK_CLIENT_ID || ''

  private readonly oauthCallbackUri = `${process.env.OVERSEAS_BASE_ADDRESS}auth/callback`

  constructor(private readonly api: FacebookApi) {}

  async generateAuthorizationUrl(state: string): Promise<string> {
    // https://developers.facebook.com/docs/permissions
    // https://www.facebook.com/v15.0/dialog/oauth?client_id=****************&scope=instagram_basic,instagram_content_publish,instagram_manage_comments,instagram_manage_insights,pages_show_list,pages_read_engagement,pages_manage_posts,read_insights,pages_manage_engagement,business_management,pages_read_user_content,pages_manage_metadata,instagram_manage_messages,pages_messaging&redirect_uri=https://www.upmee.cc/user/authorize&state=**************************************************************************&teamId=**********&upmeeId=**********
    // instagram_basic,instagram_content_publish,instagram_manage_comments,instagram_manage_insights,pages_show_list,pages_read_engagement,pages_manage_posts,read_insights,pages_manage_engagement,business_management,pages_read_user_content,pages_manage_metadata,instagram_manage_messages,pages_messaging
    const scope = [
      ...new Set([
        // Facebook 登录
        'public_profile',
        // 'email',

        // 发布权限
        // 'pages_show_list',
        // 'pages_read_engagement',
        // 'pages_manage_posts',

        // 公共主页权限
        // https://developers.facebook.com/docs/pages-api/getting-started
        'pages_manage_metadata',
        'pages_manage_posts',
        // 'pages_manage_read_engagement',  // Invalid Scope: pages_manage_read_engagement (Please check lower letter case or delimiter)
        'pages_show_list',

        //
        'pages_manage_engagement',
        'pages_manage_metadata',
        'pages_manage_posts',
        'pages_read_engagement',
        'pages_read_user_content',
        'pages_show_list',
        'publish_video',
        'business_management',

        // 其他必备权限
        // 'instagram_basic',
        // 'instagram_content_publish',
        // 'instagram_manage_comments',
        // 'instagram_manage_insights',
        // 'pages_show_list',
        // 'pages_read_engagement',
        // 'pages_manage_posts',
        // 'read_insights',
        // 'pages_manage_engagement',
        // 'business_management',
        // 'pages_read_user_content',
        // 'pages_manage_metadata',
        // 'instagram_manage_messages',
        'pages_messaging'
      ])
    ].join(',')

    // https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow
    const url = new URL('https://www.facebook.com/v21.0/dialog/oauth')
    url.searchParams.set('client_id', this.clientId)
    url.searchParams.set('redirect_uri', this.oauthCallbackUri)
    url.searchParams.set('response_type', 'code')
    url.searchParams.set('scope', scope)
    url.searchParams.set('state', state)

    return url.toString()
  }

  async exchangeAuthCodeForAccounts(context: OverseasContext, code: string, state: string): Promise<PlatformAccountInfo[]> {
    const output = await this.api.get_oauth_access_token(context, {
      code,
      redirect_uri: this.oauthCallbackUri
    })

    // Facebook 授权后实际上需要取得用户授权的公共主页视为账号
    const accounts = await this.api.get_user_accounts(context, output.access_token)

    // 获取公共主页详情
    const pageInfos = await Promise.all(
      accounts.data.map((page) => {
        return this.api.get_page(context, page.id, output.access_token)
      })
    )

    // 返回所有授权成功的公共主页
    return pageInfos.map((pageInfo) => ({
      openId: pageInfo.id,
      credentials: {
        page_access_token: pageInfo.access_token,
        user_access_token: output.access_token,
        expireAt: new Date(output.expires_in * 1000 + new Date().getTime())
      },
      avatar: pageInfo.picture.data.url,
      name: pageInfo.name,
      username: pageInfo.name // 目前不能确定, 先用 name 返回
    } as PlatformAccountInfo))
  }
}
