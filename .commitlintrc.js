const { execSync } = require('child_process');

const parseScope = function (path) {
  const fragments = path.split('/')

  if (fragments[0] === 'packages') {
    if(fragments.length > 2) {
      return `${fragments[0]}/${fragments[1]}`
    } else {
      return fragments[0]
    }
  }

  if (fragments[0] === 'apps') {
    if(fragments.length > 5) {
      return `${fragments[1]}/${fragments[4]}`
    }
    if (fragments.length > 3) {
      return `${fragments[1]}`
    }
  }

  return null
}

const types = [
  // [value, description, emoji]
  ['feat',    '新增功能 | 新增了一个功能, 实现了之前未曾实现的能力', ':sparkles:'],
  ['fix',     '修复缺陷 | 修复了一个缺陷', ':bug:'],
  ['docs',    '文档更新 | 只是修改了文档说明', ':memo:'],
  ['style',   '代码格式 | 修改代码的样式,例如调整缩进,空格,删除空行,更正拼写错误等不影响功能的代码变更。', ':lipstick:'],
  ['refactor','代码重构 | 重构代码，例如修改代码结构、变量名、函数名等但不修改功能逻辑（不包括修复缺陷、功能新增）', ':recycle:'],
  ['perf',    '性能提升 | 优化性能，例如提升代码的性能、减少内存占用等', ':zap:'],
  ['test',    '测试相关 | 修改测试用例，例如添加、删除、修改代码的测试用例', ':white_check_mark:'],
  ['build',   '构建相关 | 修改项目构建系统，例如修改依赖库、外部接口或者升级依赖的第三方软件包版本', ':package:'],
  ['ci',      '持续集成 | 修改持续集成流程，例如修改 Travis、Jenkins 等工作流配置', ':ferris_wheel:'],
  ['revert',  '代码回滚 | 代码回滚(恢复)到某一次提交, 需提供恢复到的提交 Hash', ':rewind:'],   // 如果提交恢复了之前的提交，它应该以恢复：开头，然后是恢复的提交的标头。在正文中，它应该说：这恢复了提交<hash>.，其中哈希是被恢复的提交的SHA。
  ['chore',   '其他修改 | 未涉及源文件、测试用例的其他杂项修改', ':hammer:'],
];

// 这里定义变更范围的描述, 在选择范围时就会现实对应的描述
const scope_descriptions = [
  // [value, description]
  ['admin',  '运营管理后台接口'],
  ['admin/app',  '运营管理后台接口/APP-DEMO'],
  ['admin/tenant',  '运营管理后台接口/租户管理模块'],
  ['admin/account',  '运营管理后台接口/账号管理模块'],
  ['gateway',  '业务服务接口'],
  ['gateway/app',  '业务服务接口/APP-DEMO'],
  ['gateway/tenant',  '业务服务接口/租户管理模块'],
  ['gateway/account',  '业务服务接口/账号管理模块'],
  ['packages/config',  '配置模块'],
  ['packages/mongo',  'MongoDB 模块'],
  ['packages/mysql',  'MySQL 模块'],
  ['packages/proto',  'Proto 模块'],
  ['packages/redis',  'Redis 模块'],
  ['packages/utils',  '工具函数模块'],
];

const stagedStatus = execSync('git status --porcelain || true')
  .toString().trim()
  .split('\n')
  .filter(x=> x[0] !== ' ' && x[0] !== '?');

// 从暂存变更中解析变更范围
const scopes = [...new Set(stagedStatus
  // .map(x => ({flag: x[0], path: x.slice(3), scope: parseScope(x.slice(3))}))
  .map(x => parseScope(x.slice(3)))
  .filter(x=> x !== null))]
  .map(x => [
    x,
    scope_descriptions.find(d => d[0] === x)?.[1] ?? ''
  ]);

const padWidth = Math.max(
  ...types.map(x => x[0].length),
  ...scopes.map(x => x[0].length)
) + 4;

/** @type {import('cz-git').UserConfig} */
module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // @see: https://commitlint.js.org/#/reference-rules
    'body-leading-blank': [2, 'always'],
    'footer-leading-blank': [1, 'always'],
    'header-max-length': [2, 'always', 108],
    'body-max-line-length': [2, 'always', 256],
    'subject-empty': [2, 'never'],
    'type-empty': [2, 'never'],
    'subject-case': [0],
    'type-enum': [2, 'always', types.map(([value]) => value) ],
  },
  prompt: {
    alias: { fd: 'docs: fix typos' },
    messages: {
      type: '请选择此次更改的类型:',
      scope: '请标注此次更改影响的范围 (可选):',
      customScope: '请输入此次更改影响的范围:',
      subject: '写一个简短的陈述式描述, 说明此次更改的内容:\n',
      body: '提供关于此次更改的详细描述 (可选). 使用 `|` 进行换行:\n',
      breaking: '列举不兼容的重大变更 (可选). 使用 `|` 进行换行:\n',
      footerPrefixesSelect: 'Select the ISSUES type of changeList by this change (optional):',
      customFooterPrefix: 'Input ISSUES prefix:',
      footer: '列举此次变更相关的 ISSUE (可选), 例如: #31, #34 :\n',
      generatingByAI: 'Generating your AI commit subject...',
      generatedSelectByAI: 'Select suitable subject by AI generated:',
      confirmCommit: '您确定提交此变更日志吗?'
    },
    types: types.map(([value, description, emoji]) => {
      return {value, name: `${(value + ':').padEnd(padWidth)}${description}`, emoji}
    }),
    scopes: scopes.map(([value, description]) => {
      return {value, name: `${(value + ':').padEnd(padWidth)}${description}`}
    }),
    enableMultipleScopes: true,
    customScopesAlias: `${'custom:'.padEnd(padWidth)}手动输入变更范围`,
    emptyScopesAlias: `${'empty:'.padEnd(padWidth)}不填写范围`,
    allowBreakingChanges: ['feat', 'fix', 'refactor'],
    breaklineNumber: 100,
    breaklineChar: '|',
    skipQuestions: ['footerPrefix','footer'],
    confirmColorize: true,
    // defaultBody: '',
    // defaultScope: '',
    // defaultSubject: ''
  }
}
