import { Controller, Get, Post } from '@nestjs/common'
import {
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags
} from '@nestjs/swagger'
import { PushService } from './push.service'
import { BaseForbiddenResponseDTO } from '../../common/dto/BaseResponseDTO'
import { TodayPushesCountDTO, TodayPushesCountResponseDTO } from './push.dto'

@Controller('pushes')
@ApiTags('发布管理')
@ApiHeader({ name: 'authorization', required: true })
export class PushController {
  constructor(private readonly pushService: PushService) {}

  @Get('today-count')
  @ApiOperation({ summary: '获取今日发布次数' })
  @ApiOkResponse({ type: TodayPushesCountResponseDTO })
  async getTodayPushesCount(): Promise<TodayPushesCountDTO> {
    return await this.pushService.getTodayPushesCount()
  }

  @Post()
  @ApiOperation({ summary: '创建一次发布', deprecated: true })
  @ApiOkResponse({ type: TodayPushesCountResponseDTO })
  @ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '发送量超限' })
  async createPush(): Promise<TodayPushesCountDTO> {
    return {
      count: 0,
      pushable: true
    }
  }
}
