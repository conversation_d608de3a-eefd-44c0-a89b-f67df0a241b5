/**
 * Twitter 内容发布功能测试
 * 这个文件用于测试 Twitter 内容发布的核心功能
 */

import { Test, TestingModule } from '@nestjs/testing'
import { Logger } from '@nestjs/common'
import { TwitterContentPublishProvider } from './twitter-content-publish.provider'
import { TwitterApi } from './twitter-api'
import {
  OverseasContext,
  PublishTaskData,
  PublishContentData,
  PublishContentType,
  PublishTaskStatus
} from '../types'

describe('TwitterContentPublishProvider', () => {
  let provider: TwitterContentPublishProvider
  let twitterApi: jest.Mocked<TwitterApi>

  const mockContext: OverseasContext = {
    platform: 'twitter',
    accountOpenId: 'test-account-id',
    teamId: 'test-team-id',
    userId: 'test-user-id',
    options: {
      credentials: {
        access_token: 'test-access-token'
      }
    }
  }

  beforeEach(async () => {
    const mockTwitterApi = {
      createTweet: jest.fn(),
      uploadMedia: jest.fn(),
      getTweet: jest.fn(),
      deleteTweet: jest.fn()
    }

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TwitterContentPublishProvider,
        {
          provide: TwitterApi,
          useValue: mockTwitterApi
        }
      ]
    }).compile()

    provider = module.get<TwitterContentPublishProvider>(TwitterContentPublishProvider)
    twitterApi = module.get(TwitterApi)

    // 禁用日志输出
    jest.spyOn(Logger.prototype, 'log').mockImplementation()
    jest.spyOn(Logger.prototype, 'error').mockImplementation()
  })

  describe('validateContent', () => {
    it('应该验证通过有效的文本内容', async () => {
      const content: PublishContentData = {
        type: PublishContentType.Text,
        text: '这是一条测试推文'
      }

      const result = await provider.validateContent(mockContext, content)

      expect(result.valid).toBe(true)
      expect(result.errors).toBeUndefined()
    })

    it('应该拒绝空文本内容', async () => {
      const content: PublishContentData = {
        type: PublishContentType.Text,
        text: ''
      }

      const result = await provider.validateContent(mockContext, content)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('Twitter发布必须包含文本内容')
    })

    it('应该拒绝超长文本内容', async () => {
      const content: PublishContentData = {
        type: PublishContentType.Text,
        text: 'a'.repeat(300) // 超过280字符限制
      }

      const result = await provider.validateContent(mockContext, content)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('文本内容超过最大长度限制 280 字符')
    })

    it('应该拒绝过多图片', async () => {
      const content: PublishContentData = {
        type: PublishContentType.Image,
        text: '测试图片推文',
        images: ['img1.jpg', 'img2.jpg', 'img3.jpg', 'img4.jpg', 'img5.jpg'] // 超过4张限制
      }

      const result = await provider.validateContent(mockContext, content)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('图片数量超过最大限制 4 张')
    })

    it('应该拒绝同时包含视频和图片', async () => {
      const content: PublishContentData = {
        type: PublishContentType.Mixed,
        text: '测试混合内容',
        videoUrl: 'video.mp4',
        images: ['img1.jpg']
      }

      const result = await provider.validateContent(mockContext, content)

      expect(result.valid).toBe(false)
      expect(result.errors).toContain('Twitter不支持同时发布视频和图片，请选择其中一种媒体类型')
    })
  })

  describe('publishContent', () => {
    const mockTaskData: PublishTaskData = {
      taskId: 'test-task-id',
      taskSetId: 'test-task-set-id',
      teamId: 'test-team-id',
      userId: 'test-user-id',
      accountOpenId: 'test-account-id',
      platform: 'twitter',
      content: {
        type: PublishContentType.Text,
        text: '测试推文内容'
      },
      callbackUrl: 'http://test-callback.com',
      createdAt: new Date()
    }

    it('应该成功发布文本内容', async () => {
      const mockResponse = {
        data: {
          id: '**********',
          text: '测试推文内容'
        }
      }

      twitterApi.createTweet.mockResolvedValue(mockResponse)

      const result = await provider.publishContent(mockContext, mockTaskData)

      expect(result.status).toBe(PublishTaskStatus.Success)
      expect(result.platformContentId).toBe('**********')
      expect(result.platformContentUrl).toBe('https://twitter.com/i/status/**********')
      expect(twitterApi.createTweet).toHaveBeenCalledWith(mockContext, {
        text: '测试推文内容'
      })
    })

    it('应该成功发布图片内容', async () => {
      const imageTaskData = {
        ...mockTaskData,
        content: {
          type: PublishContentType.Image,
          text: '测试图片推文',
          images: ['http://example.com/image1.jpg', 'http://example.com/image2.jpg']
        }
      }

      const mockUploadResponse = {
        media_id: 123456,
        media_id_string: '123456'
      }

      const mockTweetResponse = {
        data: {
          id: '**********',
          text: '测试图片推文'
        }
      }

      twitterApi.uploadMedia.mockResolvedValue(mockUploadResponse)
      twitterApi.createTweet.mockResolvedValue(mockTweetResponse)

      const result = await provider.publishContent(mockContext, imageTaskData)

      expect(result.status).toBe(PublishTaskStatus.Success)
      expect(twitterApi.uploadMedia).toHaveBeenCalledTimes(2)
      expect(twitterApi.createTweet).toHaveBeenCalledWith(mockContext, {
        text: '测试图片推文',
        media: { media_ids: ['123456', '123456'] }
      })
    })

    it('应该处理发布失败的情况', async () => {
      const error = new Error('Twitter API 错误')
      twitterApi.createTweet.mockRejectedValue(error)

      const result = await provider.publishContent(mockContext, mockTaskData)

      expect(result.status).toBe(PublishTaskStatus.Failed)
      expect(result.errorMessage).toBe('Twitter API 错误')
    })
  })

  describe('getSupportedContentTypes', () => {
    it('应该返回支持的内容类型', () => {
      const supportedTypes = provider.getSupportedContentTypes()

      expect(supportedTypes).toContain(PublishContentType.Text)
      expect(supportedTypes).toContain(PublishContentType.Image)
      expect(supportedTypes).toContain(PublishContentType.Video)
      expect(supportedTypes).toContain(PublishContentType.Mixed)
    })
  })

  describe('getContentLimits', () => {
    it('应该返回正确的内容限制', () => {
      const limits = provider.getContentLimits()

      expect(limits.maxTextLength).toBe(280)
      expect(limits.maxImageCount).toBe(4)
      expect(limits.maxVideoSize).toBe(512 * 1024 * 1024)
      expect(limits.maxVideoDuration).toBe(140)
      expect(limits.supportedImageFormats).toContain('jpg')
      expect(limits.supportedImageFormats).toContain('png')
      expect(limits.supportedVideoFormats).toContain('mp4')
    })
  })
})
