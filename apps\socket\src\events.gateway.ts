import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Inject, Logger } from '@nestjs/common'
import {
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect
} from '@nestjs/websockets'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Server, Socket } from 'socket.io'
import { genSocketRedisKey, getTeamOnlineUsersRedisKey, socketDeviceRoomName } from '@yxr/utils'
import { socketConnectedEventKey, socketEventEmitter } from './socket.event'

@WebSocketGateway(3003, {
  transports: ['websocket'],
  allowUpgrades: false,
  pingTimeout: 10000,
  pingInterval: 8000,
  cors: {
    origin: '*',
    credentials: true
  }
})
export class EventsGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  logger = new Logger('EventsGateway')

  /**
   * socket服务实例
   */
  @WebSocketServer()
  server: Server

  /**
   * 构造函数
   * @param cacheManager
   */
  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>) {}

  /**
   * 初始化
   */
  afterInit() {
    this.logger.log('Init')
  }

  /**
   * 处理断开连接
   * @param socket
   */
  async handleDisconnect(socket: Socket) {
    const { authorization } = socket.handshake.query as { authorization: string }
    if (!authorization) {
      return
    }

    const tokenKey = `session:au-${authorization}`
    const cache = await this.cacheManager.get<{ userId: string; teamId: string }>(tokenKey)

    // 退出登录的api接口会立即删除 tokenKey 缓存, 同时删除团队人员在线缓存, 会导致这里取不到 tokenKey 缓存
    if (cache && cache.userId) {
      await Promise.all([
        this.cacheManager.del(genSocketRedisKey(cache.userId)),
        //删除团队人员在线缓存
        this.cacheManager.store.client.zrem(getTeamOnlineUsersRedisKey(cache.teamId), cache.userId)
      ])

      // 通过事件发送到队列
      socketEventEmitter.emit(socketConnectedEventKey, {
        socketId: socket.id,
        type: 'disconnect',
        teamId: cache.teamId,
        userId: cache.userId
      })
    }
  }

  /**
   * 测试socket连接
   * @returns
   */
  @SubscribeMessage('each')
  doStuff() {
    return { event: 'each' }
  }

  /**
   * 发送消息
   * @param socketId
   * @param data
   * @returns
   */
  async send(list: Record<string, unknown>[]) {
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      const { socketId, data } = item
      if (socketId && typeof socketId === 'string') {
        this.server.to(socketId).emit('messages', data)
      }

      this.logger.debug(`send to ${socketId} ${JSON.stringify(data)}`)
    }

    return null
  }

  /**
   * 向所有在线客户端发送消息
   * @param data
   * @returns
   */
  async sendToAll(data: Record<string, unknown>) {
    this.server.emit('messages', data)

    this.logger.debug(`send to all clients: ${JSON.stringify(data)}`)

    return null
  }

  /**
   * 向指定设备发送消息
   * @param deviceType
   * @param data
   * @returns
   */
  async sendToDevice(deviceType: string, data: Record<string, unknown>) {
    if (deviceType === 'windows' || deviceType === 'macos') {
      this.server.to(socketDeviceRoomName(deviceType)).emit('messages', data)
      this.logger.debug(`send to deviceType-${deviceType}: ${JSON.stringify(data)}`)
    }
    return null
  }

  /**
   * 处理连接
   * @param socket
   * @returns
   */
  async handleConnection(socket: Socket) {
    try {
      const { authorization, deviceId, version } = socket.handshake.query as {
        authorization: string
        deviceId: string
        version: string
      }
      if (!authorization) {
        socket.disconnect() //断开连接
      }
      const tokenKey = `session:au-${authorization}`
      const cache = await this.cacheManager.get<{ userId: string; teamId: string }>(tokenKey)
      if (!cache?.userId) {
        socket.disconnect()
        return
      }

      // 将 socketId 以存入缓存
      await this.cacheManager.set(genSocketRedisKey(cache?.userId), socket.id, 0)

      //团队人员在线设置
      await this.cacheManager.store.client.zadd(
        getTeamOnlineUsersRedisKey(cache.teamId),
        1,
        cache?.userId
      )

      // 通过事件发送到队列
      socketEventEmitter.emit(socketConnectedEventKey, {
        socketId: socket.id,
        type: 'connected',
        teamId: cache?.teamId,
        userId: cache?.userId,
        deviceId: deviceId,
        version: version
      })

      // 获取设备类型
      const ua = socket.handshake.headers['user-agent'] || ''
      let deviceType = null
      if (/Windows/i.test(ua)) {
        deviceType = 'windows'
      } else if (/Macintosh|Mac OS/i.test(ua)) {
        deviceType = 'macos'
      }
      if (deviceType) {
        // 加入设备房间
        socket.join(socketDeviceRoomName(deviceType))
      }
    } catch (err) {
      console.error('Connection error:', err)
      socket.disconnect() // 断开连接
    }
  }
}
