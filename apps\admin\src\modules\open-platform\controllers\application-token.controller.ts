import { Body, Controller, Delete, Get, Param, Post } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse
} from '@nestjs/swagger'
import { ApplicationTokenService } from '../../../common/auth/services/application-token.service'
import { AdminOnly } from '../../../common/decorators/access-control.decorator'
import { Anonymous } from '../../../common/decorators/Anonymous'
import {
  BaseBadRequestDTO,
  BaseForbiddenResponseDTO,
  BaseNotFoundResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import {
  GenerateApplicationTokenRequestDto,
  RefreshTokenRequestDto,
  GenerateTokenResponseDto,
  RefreshTokenResponseDto,
  TokenValidationResultDto
} from '../dto/application-token.dto'

@ApiTags('开放平台-应用Token管理')
@Controller('open-platform/applications')
@ApiBearerAuth()
export class ApplicationTokenController {
  constructor(private readonly applicationTokenService: ApplicationTokenService) {}

  @Post('token')
  @Anonymous()
  @ApiOperation({
    summary: '生成应用Token',
    description: '基于应用ID和密钥生成JWT Token，用于API访问认证'
  })
  @ApiResponse({
    status: 201,
    description: 'Token生成成功',
    type: GenerateTokenResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或应用密钥错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '应用已被禁用',
    type: BaseForbiddenResponseDTO
  })
  async generateToken(@Body() generateTokenDto: GenerateApplicationTokenRequestDto) {
    const result = await this.applicationTokenService.generateToken(generateTokenDto)
    return result
  }

  @Post('token/refresh')
  @Anonymous()
  @ApiOperation({
    summary: '刷新应用Token',
    description: '使用当前Token刷新获取新的Token（仅在Token即将过期时可用）'
  })
  @ApiResponse({
    status: 201,
    description: 'Token刷新成功',
    type: RefreshTokenResponseDto
  })
  @ApiBadRequestResponse({
    description: 'Token无效或尚未到刷新时间',
    type: BaseBadRequestDTO
  })
  async refreshToken(@Body() refreshTokenDto: RefreshTokenRequestDto) {
    const result = await this.applicationTokenService.refreshToken(refreshTokenDto.token)
    return result
  }

  @Get(':applicationId/token')
  @AdminOnly()
  @ApiOperation({
    summary: '为应用生成Token（管理员操作）',
    description: '管理员为指定应用生成Token，无需提供密钥'
  })
  @ApiResponse({
    status: 200,
    description: 'Token生成成功',
    type: GenerateTokenResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '应用已被禁用',
    type: BaseForbiddenResponseDTO
  })
  async generateTokenByAdmin(@Param('applicationId') applicationId: string) {
    const result = await this.applicationTokenService.generateTokenByApplicationId(applicationId)
    return result
  }

  @Delete(':applicationId/tokens')
  @AdminOnly()
  @ApiOperation({
    summary: '撤销应用的所有Token（管理员操作）',
    description: '通过更新应用密钥来撤销所有已发放的Token'
  })
  @ApiResponse({
    status: 200,
    description: 'Token撤销成功'
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  async revokeAllTokens(@Param('applicationId') applicationId: string) {
    await this.applicationTokenService.revokeAllTokens(applicationId)
    return { message: '应用Token已全部撤销' }
  }

  @Post('token/validate')
  @Anonymous()
  @ApiOperation({
    summary: '验证Token有效性',
    description: '验证提供的Token是否有效，返回Token信息'
  })
  @ApiResponse({
    status: 200,
    description: 'Token验证结果',
    type: TokenValidationResultDto
  })
  async validateToken(@Body() body: { token: string }): Promise<TokenValidationResultDto> {
    try {
      const payload = await this.applicationTokenService.verifyToken(body.token)

      if (payload) {
        return {
          valid: true,
          appId: payload.appId,
          applicationId: payload.applicationId,
          userType: payload.userType,
          expiresAt: payload.expiresAt
        }
      } else {
        return {
          valid: false,
          error: 'Token无效或已过期'
        }
      }
    } catch (error) {
      return {
        valid: false,
        error: error.message || 'Token验证失败'
      }
    }
  }
}
