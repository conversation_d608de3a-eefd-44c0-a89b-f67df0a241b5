# 开放平台订单查询接口文档

## 概述

为 open-platform-order 控制器新增了订单查询接口，支持开放平台用户查询自己应用下的所有订单信息。

## 新增接口

### 订单查询接口

**接口路径**: `GET /open-platform/orders`

**权限控制**: `@OpenPlatformAccess()` - 仅允许开放平台用户访问

**请求参数**:
- `orderNumber` (查询参数, 可选): 订单号，支持模糊搜索
- `teamId` (查询参数, 可选): 团队ID，精确匹配
- `paymentStartTime` (查询参数, 可选): 支付开始时间（时间戳）
- `paymentEndTime` (查询参数, 可选): 支付结束时间（时间戳）
- `orderType` (查询参数, 可选): 订单类型（如：open_platform_account_points、open_platform_traffic）
- `page` (查询参数, 可选): 页码，默认1
- `size` (查询参数, 可选): 每页数量，默认10，最大100

**响应数据**:
```json
{
  "totalSize": 100,
  "page": 1,
  "size": 10,
  "totalPage": 10,
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "orderNo": "******************",
      "orderStatus": "paid",
      "orderType": "open_platform_account_points",
      "resourceType": "account_points",
      "teamId": "507f1f77bcf86cd799439012",
      "teamName": "我的团队",
      "userId": "507f1f77bcf86cd799439013",
      "totalAmount": 100.00,
      "payableAmount": 100.00,
      "payAmount": 100.00,
      "accountCapacity": 10,
      "trafficCount": null,
      "duration": 3,
      "startTime": *************,
      "endTime": *************,
      "trafficExpiredAt": null,
      "payTime": *************,
      "remark": "账号点数订单",
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

## 数据隔离机制

- **用户级隔离**: 通过 `openPlatformUserId` 字段确保只返回当前开放平台用户创建的订单
- **来源过滤**: 通过 `orderSource: 'open_platform'` 确保只返回开放平台订单
- **权限控制**: 使用 `@OpenPlatformAccess()` 装饰器确保只有开放平台用户可以访问

## 查询功能特点

1. **订单号搜索**: 支持订单号的模糊搜索，方便用户快速定位订单
2. **团队过滤**: 可以按团队ID过滤，查看特定团队的订单
3. **时间范围查询**: 支持按支付时间范围查询，便于财务对账
4. **订单类型过滤**: 可以按订单类型过滤，区分账号点数订单和流量订单
5. **分页支持**: 标准的分页功能，支持大量数据的高效查询
6. **关联数据**: 自动关联团队信息，提供完整的订单详情

## 订单类型说明

- `open_platform_account_points`: 账号点数订单
- `open_platform_traffic`: 流量订单
- `open_platform_create`: 开放平台开通订单（旧系统）
- `open_platform_renew`: 开放平台续费订单（旧系统）
- `open_platform_addon`: 开放平台增购订单（旧系统）

## 订单状态说明

- `pending`: 待支付
- `paid`: 已支付
- `cancelled`: 已取消
- `refunded`: 已退费

## 使用示例

### 查询所有订单
```bash
GET /open-platform/orders?page=1&size=10
Authorization: Bearer <open_platform_token>
```

### 按订单号搜索
```bash
GET /open-platform/orders?orderNumber=OP164099&page=1&size=10
Authorization: Bearer <open_platform_token>
```

### 按团队ID过滤
```bash
GET /open-platform/orders?teamId=507f1f77bcf86cd799439012&page=1&size=10
Authorization: Bearer <open_platform_token>
```

### 按时间范围查询
```bash
GET /open-platform/orders?paymentStartTime=*************&paymentEndTime=*************&page=1&size=10
Authorization: Bearer <open_platform_token>
```

### 按订单类型过滤
```bash
GET /open-platform/orders?orderType=open_platform_account_points&page=1&size=10
Authorization: Bearer <open_platform_token>
```

### 组合查询
```bash
GET /open-platform/orders?teamId=507f1f77bcf86cd799439012&orderType=open_platform_account_points&paymentStartTime=*************&page=1&size=10
Authorization: Bearer <open_platform_token>
```

## 实现特点

1. **遵循现有架构**: 采用 Controller + Service + DTO 架构模式
2. **统一权限控制**: 使用现有的访问控制装饰器系统
3. **完整的数据验证**: 包含请求参数验证和响应数据格式化
4. **高效查询**: 使用 MongoDB 的 populate 功能关联团队数据
5. **错误处理**: 包含完整的API文档和错误响应定义
6. **数据安全**: 确保用户只能查询自己的订单数据

## 注意事项

1. 该接口仅支持开放平台用户调用，需要使用开放平台用户的认证Token
2. 查询结果会自动按创建时间倒序排列，最新的订单在前
3. 时间参数使用时间戳格式（毫秒）
4. 分页参数中，size 最大值为100，防止单次查询数据量过大
5. 订单号搜索支持模糊匹配，可以输入部分订单号进行搜索
