/**
 * Twitter API 类型定义
 */

/**
 * Twitter OAuth2 令牌响应
 */
export interface TwitterOAuth2TokenResponse {
  token_type: string
  expires_in: number
  access_token: string
  refresh_token: string
  scope: string
}

/**
 * Twitter 用户信息响应
 */
export interface TwitterUserResponse {
  detail?: string
  title?: number
  type?: string
  errors?: unknown[]
  data: {
    id: string
    name: string
    username: string
    profile_image_url: string
    verified: boolean
    public_metrics: {
      followers_count: number
      following_count: number
      tweet_count: number
      like_count: number
    }
  }
}

/**
 * Twitter 推文创建响应
 */
export interface TwitterTweetResponse {
  data: {
    id: string
    text: string
  }
}

/**
 * Twitter 推文详情响应
 */
export interface TwitterTweetDetailResponse {
  data: {
    id: string
    text: string
    created_at: string
    author_id: string
    public_metrics: {
      retweet_count: number
      like_count: number
      reply_count: number
      quote_count: number
    }
  }
}

/**
 * Twitter 推文删除响应
 */
export interface TwitterDeleteTweetResponse {
  data: {
    deleted: boolean
  }
}

/**
 * Twitter 媒体上传响应（图片）
 */
export interface TwitterImageUploadResponse {
  media_id: number
  media_id_string: string
  size: number
  expires_after_secs: number
  image: {
    image_type: string
    w: number
    h: number
  }
}

/**
 * Twitter 媒体上传响应（视频初始化）
 */
export interface TwitterVideoInitResponse {
  media_id: number
  media_id_string: string
  expires_after_secs: number
}

/**
 * Twitter 推文创建参数
 */
export interface TwitterCreateTweetParams {
  text: string
  media?: { media_ids: string[] }
  reply?: { in_reply_to_tweet_id: string }
  quote_tweet_id?: string
  poll?: {
    options: string[]
    duration_minutes: number
  }
  geo?: {
    place_id: string
  }
  access_token?: string
}

/**
 * Twitter 用户字段选项
 */
export type TwitterUserFields = 
  | 'affiliation'
  | 'connection_status'
  | 'created_at'
  | 'description'
  | 'entities'
  | 'id'
  | 'is_identity_verified'
  | 'location'
  | 'most_recent_tweet_id'
  | 'name'
  | 'parody'
  | 'pinned_tweet_id'
  | 'profile_banner_url'
  | 'profile_image_url'
  | 'protected'
  | 'public_metrics'
  | 'receives_your_dm'
  | 'subscription'
  | 'subscription_type'
  | 'url'
  | 'username'
  | 'verified'
  | 'verified_followers_count'
  | 'verified_type'
  | 'withheld'

/**
 * Twitter 获取用户信息参数
 */
export interface TwitterGetUserMeParams {
  'user.fields'?: TwitterUserFields[]
}

/**
 * Twitter OAuth2 参数
 */
export interface TwitterOAuth2Params {
  code: string
  redirect_uri: string
  code_verifier: string
}

/**
 * Twitter API 端点
 */
export const TwitterApiEndpoints = {
  OAuth2Token: '/2/oauth2/token',
  UsersMe: '/2/users/me',
  CreateTweet: '/2/tweets',
  GetTweet: '/2/tweets',
  DeleteTweet: '/2/tweets',
  MediaUpload: '/1.1/media/upload.json'
} as const

/**
 * Twitter 上下文（扩展 OverseasContext）
 */
export interface TwitterContext {
  platform: 'twitter'
  accountOpenId?: string
  teamId?: string
  userId?: string
  options?: {
    credentials?: {
      access_token: string
      refresh_token?: string
      expires_at?: number
    }
    [key: string]: any
  }
}

/**
 * Twitter 媒体类型
 */
export type TwitterMediaType = 'image' | 'video'

/**
 * Twitter 视频上传命令
 */
export type TwitterVideoUploadCommand = 'INIT' | 'APPEND' | 'FINALIZE'

/**
 * Twitter 错误响应
 */
export interface TwitterErrorResponse {
  errors?: Array<{
    code: number
    message: string
    parameter?: string
  }>
  title?: string
  detail?: string
  type?: string
}

/**
 * Twitter API 通用响应包装器
 */
export interface TwitterApiResponse<T = any> {
  data?: T
  errors?: Array<{
    code: number
    message: string
  }>
  meta?: {
    result_count?: number
    next_token?: string
    previous_token?: string
  }
}
