import { Injectable, Logger } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { EventNames, PlatformMembersChangedEvent } from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { MemberEntity } from '@yxr/mongo'

@Injectable()
export class PlatformMembersChangedListener {
  logger = new Logger('PlatformMembersChangedListener')

  constructor(@InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>) {}

  @OnEvent(EventNames.PlatformMembersChangedEvent, { async: true })
  async handlePlatformMembersChangedEvent(payload: PlatformMembersChangedEvent) {
    // 这些成员的运营账号清单中添加账号
    if (payload.addMemberIds && payload.addMemberIds.length > 0) {
      const result = await this.memberModel.updateMany(
        {
          teamId: new Types.ObjectId(payload.teamId),
          userId: { $in: payload.addMemberIds.map((x) => new Types.ObjectId(x)) }
        },
        { $addToSet: { accounts: payload.platformAccountId } }
      )

      this.logger.debug(
        `批量添加成员运营的账号, teamId: ${payload.teamId} addMemberIds: ${payload.addMemberIds}, platformAccountId: ${payload.platformAccountId}, 更新记录数: ${result.modifiedCount}`
      )
    }

    // 这些成员的运营账号清单中删除账号
    if (payload.delMemberIds && payload.delMemberIds.length > 0) {
      const result = await this.memberModel.updateMany(
        {
          teamId: new Types.ObjectId(payload.teamId),
          userId: { $in: payload.delMemberIds.map((x) => new Types.ObjectId(x)) }
        },
        { $pull: { accounts: payload.platformAccountId } }
      )

      this.logger.debug(
        `批量删除成员运营的账号, teamId: ${payload.teamId} delMemberIds: ${payload.delMemberIds}, platformAccountId: ${payload.platformAccountId}, 更新记录数: ${result.modifiedCount}`
      )
    }
  }

  @OnEvent(EventNames.PlatformMembersChangedEvent2, { async: true })
  async handlePlatformMembersChangedEvent2(payload: PlatformMembersChangedEvent) {
    // 这些成员的运营账号清单中添加账号
    if (payload.addMemberIds && payload.addMemberIds.length > 0) {
      const result = await this.memberModel.updateMany(
        {
          teamId: new Types.ObjectId(payload.teamId),
          _id: { $in: payload.addMemberIds.map((x) => new Types.ObjectId(x)) }
        },
        { $addToSet: { accounts: payload.platformAccountId } }
      )

      this.logger.debug(
        `批量添加成员运营的账号, teamId: ${payload.teamId} addMemberIds: ${payload.addMemberIds}, platformAccountId: ${payload.platformAccountId}, 更新记录数: ${result.modifiedCount}`
      )
    }

    // 这些成员的运营账号清单中删除账号
    if (payload.delMemberIds && payload.delMemberIds.length > 0) {
      const result = await this.memberModel.updateMany(
        {
          teamId: new Types.ObjectId(payload.teamId),
          _id: { $in: payload.delMemberIds.map((x) => new Types.ObjectId(x)) }
        },
        { $pull: { accounts: payload.platformAccountId } }
      )

      this.logger.debug(
        `批量删除成员运营的账号, teamId: ${payload.teamId} delMemberIds: ${payload.delMemberIds}, platformAccountId: ${payload.platformAccountId}, 更新记录数: ${result.modifiedCount}`
      )
    }
  }
}
