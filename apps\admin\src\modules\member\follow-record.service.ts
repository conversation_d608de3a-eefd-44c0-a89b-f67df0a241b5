import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { CreateFollowRecordDTO, FollowRecordResponse } from './follow-record.dto'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { FollowRecordEntity, UserEntity } from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { AdminRole } from '@yxr/common'

@Injectable()
export class FollowRecordService {
  logger = new Logger('FollowRecordService')

  constructor(
    @InjectModel(FollowRecordEntity.name) private followRecordModel: Model<FollowRecordEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  async getFollowRecords(followUserId: string): Promise<FollowRecordResponse[]> {
    const result = await this.followRecordModel
      .find({
        userId: new Types.ObjectId(followUserId)
      })
      .sort({ createdAt: -1 })

    return result.map((item) => ({
      id: item._id.toString(),
      customerName: item.customerName,
      content: item.content,
      createdAt: item.createdAt.getTime()
    }))
  }

  async postFollowRecord(followUserId: string, body: CreateFollowRecordDTO) {
    const { user } = this.request
    if (user.role === AdminRole.Customer) {
      const followUser = await this.userModel.findOne({
        _id: new Types.ObjectId(followUserId)
      })
      if (followUser.customerId.toString() !== user.id.toString()) {
        throw new ForbiddenException('不是该用户的归属人，不能新增跟进')
      }
    }

    await this.followRecordModel.create({
      customerId: new Types.ObjectId(user.id),
      customerName: user.name,
      userId: new Types.ObjectId(followUserId),
      content: body.content
    })
  }
}
