# 开放平台退出登录功能实现

## 概述

在 `apps/admin/src/modules/open-platform/services/auth.service.ts` 中添加了完整的退出登录服务方法，支持清除用户认证Token缓存和相关会话信息，确保与现有的UnifiedTokenGuard认证机制完全兼容。

## 功能特性

### 1. 退出登录方法
- **方法名称**: `logout`
- **功能描述**: 处理开放平台用户的退出登录逻辑
- **返回类型**: `Promise<LogoutResponseDto>`

### 2. 支持的退出模式
- **当前设备退出**: 只清除当前Token，其他设备保持登录状态
- **所有设备退出**: 清除用户的所有Token，强制所有设备重新登录

### 3. 安全特性
- **身份验证**: 验证当前用户身份，确保只能退出自己的登录状态
- **操作日志**: 记录退出登录的操作日志
- **防重复退出**: 处理重复退出请求，避免错误

## 技术实现

### 1. Service层实现

**文件**: `apps/admin/src/modules/open-platform/services/auth.service.ts`

#### 1.1 主要方法
```typescript
/**
 * 退出登录
 * 清除用户的认证Token缓存和相关会话信息
 */
async logout(logoutDto: LogoutRequestDto = {}): Promise<LogoutResponseDto> {
  const { logoutAllDevices = false } = logoutDto
  const { session } = this.request

  // 验证当前用户身份
  if (!session || session.userType !== UserType.OPEN_PLATFORM) {
    throw new ForbiddenException('无效的用户会话')
  }

  const userId = session.userId
  let clearedTokensCount = 0

  if (logoutAllDevices) {
    // 退出所有设备
    clearedTokensCount = await this.clearAllUserTokens(userId)
  } else {
    // 只退出当前设备
    const currentToken = this.request.authorization
    if (currentToken) {
      clearedTokensCount = await this.clearCurrentToken(userId, currentToken)
    }
  }

  return {
    success: true,
    message: logoutAllDevices ? '已退出所有设备' : '退出登录成功',
    clearedTokensCount
  }
}
```

#### 1.2 清除所有Token（退出所有设备）
```typescript
private async clearAllUserTokens(userId: string): Promise<number> {
  // 清除开放平台用户的主要缓存键
  const userIdKey = `open_platform_user:${userId}`
  const oldAuthorization = await this.cacheManager.get<string>(userIdKey)

  if (oldAuthorization) {
    await Promise.all([
      this.cacheManager.del(userIdKey),
      this.cacheManager.del(`open_platform:${oldAuthorization}`),
      this.cacheManager.del(`unified:${oldAuthorization}`)
    ])
  }

  // 清除gateway-user相关Token
  const gatewayUserIdKey = `session:ui-${userId}:open-platform`
  const gatewayToken = await this.cacheManager.get<string>(gatewayUserIdKey)

  if (gatewayToken) {
    await Promise.all([
      this.cacheManager.del(gatewayUserIdKey),
      this.cacheManager.del(gatewayToken)
    ])
  }
}
```

#### 1.3 清除当前Token（只退出当前设备）
```typescript
private async clearCurrentToken(userId: string, currentToken: string): Promise<number> {
  // 清除当前Token相关的缓存
  const cacheKeysToDelete = [
    `open_platform:${currentToken}`,
    `unified:${currentToken}`,
    `session:au-${currentToken}`
  ]

  // 检查并清除存在的缓存键
  for (const key of cacheKeysToDelete) {
    const exists = await this.cacheManager.get(key)
    if (exists) {
      await this.cacheManager.del(key)
    }
  }

  // 清除用户ID映射（如果当前Token是最新的）
  const userIdKey = `open_platform_user:${userId}`
  const currentUserToken = await this.cacheManager.get<string>(userIdKey)
  
  if (currentUserToken === `open_platform:${currentToken}` || 
      currentUserToken === `unified:${currentToken}`) {
    await this.cacheManager.del(userIdKey)
  }
}
```

### 2. Controller层实现

**文件**: `apps/admin/src/modules/open-platform/controllers/auth.controller.ts`

```typescript
@Post('logout')
@OpenPlatformAccess()
@ApiOperation({
  summary: '退出登录',
  description: '清除用户的认证Token缓存，支持退出当前设备或所有设备'
})
@ApiOkResponse({
  description: '退出登录成功',
  type: LogoutOkResponseDto
})
async logout(@Body() logoutDto: LogoutRequestDto) {
  const result = await this.authService.logout(logoutDto)
  return result
}
```

### 3. DTO定义

**文件**: `apps/admin/src/modules/open-platform/dto/auth.dto.ts`

#### 3.1 请求DTO
```typescript
export class LogoutRequestDto {
  @ApiProperty({
    description: '是否退出所有设备',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean({ message: '退出所有设备必须是布尔值' })
  logoutAllDevices?: boolean = false
}
```

#### 3.2 响应DTO
```typescript
export class LogoutResponseDto {
  @ApiResponseProperty({
    example: true
  })
  success: boolean

  @ApiResponseProperty({
    example: '退出登录成功'
  })
  message: string

  @ApiResponseProperty({
    example: 1
  })
  clearedTokensCount: number
}
```

## 缓存键管理

### 1. 开放平台Token缓存键
- `open_platform_user:${userId}` - 用户ID到Token的映射
- `open_platform:${token}` - Token到用户信息的映射
- `unified:${token}` - 统一Token缓存

### 2. Gateway-User Token缓存键
- `session:ui-${userId}:open-platform` - 用户ID到Token键的映射
- `session:au-${token}` - Token到会话信息的映射

### 3. 清除策略
- **退出当前设备**: 只清除当前Token相关的缓存键
- **退出所有设备**: 清除用户所有相关的缓存键

## API接口文档

### 退出登录接口

**接口路径**: `POST /open-platform/auth/logout`

**权限控制**: `@OpenPlatformAccess()` - 仅开放平台用户可访问

**请求参数**:
```json
{
  "logoutAllDevices": false
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "退出登录成功",
  "clearedTokensCount": 3
}
```

### 使用示例

#### 退出当前设备
```bash
POST /open-platform/auth/logout
Authorization: Bearer <open_platform_token>
Content-Type: application/json

{
  "logoutAllDevices": false
}
```

#### 退出所有设备
```bash
POST /open-platform/auth/logout
Authorization: Bearer <open_platform_token>
Content-Type: application/json

{
  "logoutAllDevices": true
}
```

## 安全考虑

### 1. 身份验证
- 验证当前用户会话的有效性
- 确保只有开放平台用户可以调用退出接口
- 防止跨用户的退出操作

### 2. 操作日志
- 记录退出登录的操作日志
- 包含用户ID、退出模式、清除的Token数量
- 便于安全审计和问题排查

### 3. 错误处理
- 处理缓存操作失败的情况
- 提供友好的错误提示
- 防止敏感信息泄露

## 兼容性保证

### 1. UnifiedTokenGuard兼容
- 完全兼容现有的统一Token认证机制
- 支持多种Token缓存格式
- 不影响其他认证流程

### 2. 向后兼容
- 不影响现有的登录、注册功能
- 保持现有API接口不变
- 支持渐进式部署

### 3. 多端支持
- 支持Web端、移动端等多种客户端
- 兼容不同的Token生成方式
- 统一的退出登录体验

## 监控和调试

### 1. 日志记录
- 退出登录操作的详细日志
- 缓存清除操作的执行结果
- 错误情况的堆栈信息

### 2. 性能监控
- 监控缓存操作的执行时间
- 跟踪Token清除的成功率
- 统计退出登录的使用情况

### 3. 错误处理
- 详细的错误分类和处理
- 用户友好的错误提示
- 开发者调试信息

## 后续优化建议

1. **Token黑名单**: 考虑实现Token黑名单机制，提高安全性
2. **批量操作**: 优化大量Token清除时的性能
3. **通知机制**: 实现退出登录时的实时通知
4. **审计日志**: 增强安全审计和合规性支持
5. **自动清理**: 实现过期Token的自动清理机制
