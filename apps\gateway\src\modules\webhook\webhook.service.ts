import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { OrderEntity } from '@yxr/mongo'
import { Client, ClientGrpc, Transport } from '@nestjs/microservices'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Socket, socketConfig } from '@yxr/proto'
import { Cache } from 'cache-manager'
import { WebhookEvents } from './constant'
import { genSocketRedisKey, getTeamOnlineUsersRedisKey } from '@yxr/utils'
import { SocketBody, WebhookBody } from './types'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { OrderStatus, PayType } from '@yxr/common'
import { OrderManagerService } from '@yxr/order'
import { eventKey, orderEventEmitter } from '../order/order.event'

@Injectable()
export class WebhookService implements OnModuleInit {
  logger = new Logger('WebhookService')

  @Client({
    transport: Transport.GRPC,
    options: {
      ...socketConfig,
      channelOptions: {
        'grpc.keepalive_time_ms': 10 * 1000,
        'grpc.keepalive_timeout_ms': 5 * 1000,
        'grpc.keepalive_permit_without_calls': 1,
        'grpc.service_config': JSON.stringify({
          methodConfig: [
            {
              name: [{ service: 'Socket' }],
              timeout: '10s',
              retryPolicy: {
                maxAttempts: 3,
                initialBackoff: '0.5s',
                maxBackoff: '10s',
                backoffMultiplier: 1.5,
                retryableStatusCodes: ['UNAVAILABLE', 'INTERNAL']
              }
            }
          ]
        })
      }
    }
  })
  private readonly client: ClientGrpc

  socketService: Socket

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>,
    private readonly orderManagerService: OrderManagerService
  ) {}

  onModuleInit() {
    this.logger.log('WebhookService init')
    this.socketService = this.client.getService<Socket>('Socket')
  }

  /**
   * 发送socket消息
   * @param body
   * @deprecated 请使用 grpchook 方法
   * @returns
   */
  async webhook(body: WebhookBody[]): Promise<void> {
    if (body.length <= 0) {
      return
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []
    for (const e of body) {
      switch (e.event) {
        case WebhookEvents.NoticeCreate:
        case WebhookEvents.AccountUpdate:
        case WebhookEvents.TeamExit:
        case WebhookEvents.TeamRoleChange:

        default:
          break
      }

      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(e.user_id))
      if (socketId) {
        dataList.push({
          socketId,
          data: { ...e }
        })
      }
    }

    if (dataList.length) {
      try {
        this.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {
            // console.log('Received hero:');
            // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
          },
          error: (err) => {
            console.error('Error occurred:', err)
          },
          complete: () => {
            // console.log('gRPC request completed.');
          }
        })
      } catch (error) {
        this.logger.error('socketService error', error)
      }
    }
  }

  /**
   * 发送socket消息
   * @param body
   * @returns
   */
  async grpchook(userIds: string[], teamId: string, body: SocketBody): Promise<void> {
    let onlineUsers: string[] = []
    if (teamId) {
      //发送团队信息过滤未在团队用户
      onlineUsers = await this.cacheManager.store.client.zrange(
        getTeamOnlineUsersRedisKey(teamId),
        0,
        -1
      )
    } else {
      onlineUsers = userIds
    }

    const onlineUserIds =
      userIds != null ? userIds.filter((value) => onlineUsers.includes(value)) : onlineUsers
    if (onlineUserIds.length <= 0) {
      return
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []
    for (const e of onlineUserIds) {
      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(e))
      if (socketId) {
        dataList.push({
          socketId,
          data: { ...body }
        })
      }
    }

    if (dataList.length) {
      try {
        this.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {
            // console.log('Received hero:');
            // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
          },
          error: (err) => {
            console.error('Error occurred:', err)
          },
          complete: () => {
            // console.log('gRPC request completed.');
          }
        })
      } catch (error) {
        this.logger.error('socketService error', error)
      }
    }
  }

  async wechatPayWebhook(data: any) {
    try {
      const attach = 'lite-' + process.env.NODE_ENV
      if (data.trade_state === 'SUCCESS' && data.attach === attach) {
        const order = await this.orderModel.findOne({
          orderNo: data.return_no
        })
        if (order.orderStatus === OrderStatus.Paid) {
          return 'SUCCESS'
        }
        // 支付成功，进行相应的业务处理
        const receiptAmount = Number(data.total_fee / 100)
        if (order.payableAmount === receiptAmount) {
          const gmtPayment = this.convertToUTC(data.time_end)
          await this.orderModel.findByIdAndUpdate(order.id, {
            payTime: gmtPayment,
            orderStatus: OrderStatus.Paid,
            payAmount: receiptAmount,
            payType: PayType.WechatPay
          })

          await this.orderManagerService.handleCompletedOrder(order.orderNo)
          orderEventEmitter.emit(eventKey, { orderNo: order.orderNo, type: 'close' })
          await this.grpchook(null, order.teamId.toString(), {
            event: WebhookEvents.TeamVersionUpgrade
          })
          return 'SUCCESS'
        }
      }
      return 'FAIL'
    } catch (error) {
      this.logger.debug(error)
      return 'FAIL'
    }
  }

  async alipayWebhook(data: any) {
    try {
      const attach = 'lite-' + process.env.NODE_ENV
      if (data.trade_status === 'TRADE_SUCCESS') {
        // 验证通过，处理支付成功的逻辑
        const orderNo = data.out_trade_no
        const receiptAmount = Number(data.receipt_amount) // 实付金额
        const gmtPayment = new Date()

        const order = await this.orderModel.findOne({
          orderNo
        })
        if (!order) {
          this.logger.debug('订单不存在')
          return 'success'
        }
        if (order.orderStatus === OrderStatus.Paid) {
          return 'success'
        }

        // 支付成功，进行相应的业务处理
        if (order.payableAmount === receiptAmount) {
          await this.orderModel.findByIdAndUpdate(new Types.ObjectId(order.id), {
            payTime: gmtPayment,
            orderStatus: OrderStatus.Paid,
            payAmount: receiptAmount,
            payType: PayType.Alipay
          })

          await this.orderManagerService.handleCompletedOrder(order.orderNo)
          orderEventEmitter.emit(eventKey, { orderNo: order.orderNo, type: 'close' })
          await this.grpchook(null, order.teamId.toString(), {
            event: WebhookEvents.TeamVersionUpgrade
          })
        }
      }

      return 'fail'
    } catch (error) {
      return 'fail'
    }
  }

  convertToUTC(dateStr: string): Date {
    const year = parseInt(dateStr.slice(0, 4), 10)
    const month = parseInt(dateStr.slice(4, 6), 10) - 1 // 月份从0开始
    const day = parseInt(dateStr.slice(6, 8), 10)
    const hour = parseInt(dateStr.slice(8, 10), 10)
    const minute = parseInt(dateStr.slice(10, 12), 10)
    const second = parseInt(dateStr.slice(12, 14), 10)

    // 创建东八区的 Date 对象
    const localDate = new Date(year, month, day, hour, minute, second)
    // 创建 UTC 日期对象
    const utcDate = new Date(localDate.getTime() - 8 * 60 * 60 * 1000)
    return utcDate
  }
}
