/**
 * 海外平台执行业务方法时的上下文信息
 */
export interface OverseasContext {
  platform: string // 平台名称（如：facebook, instagram, twitter, tiktok, youtube）
  accountOpenId?: string
  teamId?: string
  userId?: string
  options?: Record<string, any> // 附加信息(部分平台需要的额外信息)
}

/**
 * 海外平台标准的授权访问凭证
 */
export type AuthorizationAccessToken = {
  /** 授权过程中拿到的账号唯一识别符 */
  openId: string

  /** 账号访问令牌 */
  accessToken: string

  /** 访问令牌过期时间 */
  expireAt: number

  /** 访问令牌类型, 通常是 beare */
  tokenType: string

  /** 授权类型 */
  scopes: string[],

  /** 平台原始授权信息(接口返回的原始信息, 存在这个字段的原因是有可能部分平台需要特定的授权凭证) */
  raw: unknown
}

/**
 * 平台侧账号信息
 */
export interface PlatformAccountInfo {
  /** 账号唯一识别符 */
  openId: string

  /**
   * 平台方认证凭证, 不同的平台可能结构不一致
   */
  credentials: Record<string, any>

  /** 平台用户头像 */
  avatar?: string

  /**
   * 平台用户昵称
   * 通常是平台内显示的昵称, 在平台内是可以重复的
   */
  name: string

  /**
   * 平台用户名
   * 通常是平台内唯一的标识符, 在平台内可以更改或者不能更改
   */
  username?: string

  /**
   * 是否通过了平台方的认证
   */
  isVerified?: boolean
}

/**
 * 发布内容类型枚举
 */
export enum PublishContentType {
  /** 文本内容 */
  Text = 'text',
  /** 图片内容 */
  Image = 'image',
  /** 视频内容 */
  Video = 'video',
  /** 图文混合内容 */
  Mixed = 'mixed'
}

/**
 * 发布任务状态枚举
 */
export enum PublishTaskStatus {
  /** 待发布 */
  Pending = 'pending',
  /** 发布中 */
  Publishing = 'publishing',
  /** 发布成功 */
  Success = 'success',
  /** 发布失败 */
  Failed = 'failed',
  /** 审核中 */
  Reviewing = 'reviewing',
  /** 审核通过 */
  Approved = 'approved',
  /** 审核拒绝 */
  Rejected = 'rejected'
}

/**
 * 发布内容数据结构
 */
export interface PublishContentData {
  /** 内容类型 */
  type: PublishContentType

  /** 文本内容 */
  text?: string

  /** 标题 */
  title?: string

  /** 描述 */
  description?: string

  /** 图片URL列表 */
  images?: string[]

  /** 视频URL */
  videoUrl?: string

  /** 视频封面URL */
  videoCover?: string

  /** 标签列表 */
  tags?: string[]

  /** 位置信息 */
  location?: {
    name: string
    latitude?: number
    longitude?: number
  }

  /** 平台特定的额外参数 */
  platformSpecific?: Record<string, any>
}

/**
 * 发布任务数据结构
 */
export interface PublishTaskData {
  /** 任务ID */
  taskId: string

  /** 任务集ID */
  taskSetId: string

  /** 团队ID */
  teamId: string

  /** 用户ID */
  userId: string

  /** 平台账号OpenID */
  accountOpenId: string

  /** 平台名称 */
  platform: string

  /** 发布内容 */
  content: PublishContentData

  /** 发布时间（可选，用于定时发布） */
  publishAt?: Date

  /** 回调URL */
  callbackUrl: string

  /** 重试次数 */
  retryCount?: number

  /** 最大重试次数 */
  maxRetries?: number

  /** 创建时间 */
  createdAt: Date
}

/**
 * 发布结果数据结构
 */
export interface PublishResult {
  /** 任务ID */
  taskId: string

  /** 发布状态 */
  status: PublishTaskStatus

  /** 平台返回的内容ID */
  platformContentId?: string

  /** 平台返回的内容URL */
  platformContentUrl?: string

  /** 错误信息 */
  errorMessage?: string

  /** 错误代码 */
  errorCode?: string

  /** 平台原始响应 */
  rawResponse?: any

  /** 完成时间 */
  completedAt: Date
}
