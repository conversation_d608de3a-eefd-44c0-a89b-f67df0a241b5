import { forward<PERSON><PERSON>, <PERSON>du<PERSON> } from '@nestjs/common'
import { PlatformAccountController } from './platform-account.controller'
import { PlatformAccountService } from './platform-account.service'
import {
  PlatformAccountMongoose,
  ContentMongoose,
  UserMongoose,
  TeamMongoose,
  MemberMongoose,
  GroupMongoose,
  BrowserMongoose,
  BrowserFavoritesMongoose,
  PlatformAccountOverviewMongoose,
  ContentStatisticMongoose,
  TaskMongoose,
  PlatformAccountStatusLogMongoose,
  PlatformAccountSummaryMongoose,
  HistoryStatisticMongoose,
  PlatformAccountCookieMongoose
} from '@yxr/mongo'
import { MemberAccountsChangedListener } from './member-accounts.changed.listeners'
import { TeamModule } from '../team/team.module'
import { WebhookModule } from '../webhook/webhook.module'
import { GroupController } from './group.controller'
import { GroupService } from './group.service'
import { AuthorizationService } from '../../common/security/authorization.service'
import { BrowserModule } from '../browser/browser.module'
import { ContentController } from './content.controller'
import { ContentService } from './content.service'
import { ContentStatisticEventService } from './content.event'
import { WebappAccountsController } from './webapp-accounts.controller'
import { WebappAccountsQueryService } from './webapp-accounts-query.service'
import { CommonModule } from '@yxr/common'
import { WechatIpadModule } from '../wechat-ipad/wechat-ipad.module'
import { PlatformAccountCloudService } from './platform-account-cloud.service'
import { PlatformAccountStatisticService } from './platform-account-statistic.service'
import { HuoshanModule } from '@yxr/huoshan'
import { KuaidailiModule } from '../kuaidaili/kuaidaiali.module'
import { PlatformAccountReportService } from './platform-account-report.service'
import { WxThirdPlatformModule } from '../wx-third-platform/wx-third-platform.module'

@Module({
  imports: [
    HistoryStatisticMongoose,
    PlatformAccountMongoose,
    BrowserMongoose,
    UserMongoose,
    ContentMongoose,
    TeamMongoose,
    TaskMongoose,
    MemberMongoose,
    GroupMongoose,
    BrowserFavoritesMongoose,
    PlatformAccountOverviewMongoose,
    ContentStatisticMongoose,
    PlatformAccountStatusLogMongoose,
    PlatformAccountSummaryMongoose,
    PlatformAccountCookieMongoose,
    HuoshanModule,
    TeamModule,
    BrowserModule,
    WebhookModule,
    CommonModule,
    WechatIpadModule,
    KuaidailiModule,
    forwardRef(() => WxThirdPlatformModule)
  ],
  controllers: [
    PlatformAccountController,
    GroupController,
    ContentController,
    WebappAccountsController
  ],
  providers: [
    PlatformAccountService,
    GroupService,
    MemberAccountsChangedListener,
    AuthorizationService,
    ContentService,
    ContentStatisticEventService,
    WebappAccountsQueryService,
    PlatformAccountCloudService,
    PlatformAccountStatisticService,
    PlatformAccountReportService
  ],
  exports: [PlatformAccountService]
})
export class PlatformAccountModule {}
