export class AccountDTO {
  platformAccountId: string
  platformAuthorId: string
  cookie: string
  localStorage?: string
}

export class ProxyDTO {
  serverAdd: string
  serverPort: string
}

export class TaskDTO {
  videoPath: string
  cover: string
  taskId: string
  platform: string
  proxy?: ProxyDTO
  platformAccount: AccountDTO
}

export class TaskCloudPushDTO {
  callBackData?: {
    teamId: string
    userId: string
  }
  publishType: string
  formData: unknown
  data: TaskDTO[]
}

export class CloudBaseDTO {
  callBackData?: {
    teamId: string
    userId?: string
    platformAccountId?: string,
    taskId?: string
  }
  platform: string
  platformAccountId: string
  cookie?: string
}

export class ContentDeleteDTO extends CloudBaseDTO {
  docId?: string
  platformContentType?: string
  isDraft?: boolean
}

export class AuditStatusDTO extends CloudBaseDTO {
  taskId?: string
  publishId?: string
}

export class AccountMusicDTO extends CloudBaseDTO {
  keyWord: string
  nextPage: string
  categoryId?: string
  categoryName?: string
}

export class AccountLocationDTO extends CloudBaseDTO {
  keyWord: string
  nextPage: string
}
