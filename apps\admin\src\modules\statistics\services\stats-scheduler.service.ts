import { Injectable, Logger } from '@nestjs/common'
import { Cron, CronExpression } from '@nestjs/schedule'
import { TeamStatsService } from './team-stats.service'

/**
 * 统计数据定时任务服务
 * 负责定时执行团队和应用的日统计任务
 */
@Injectable()
export class StatsSchedulerService {
  private readonly logger = new Logger(StatsSchedulerService.name)

  constructor(private readonly teamStatsService: TeamStatsService) {}

  /**
   * 每天凌晨00:30执行统计任务
   */
  @Cron('30 0 * * *', {
    name: 'daily-stats-calculation',
    timeZone: 'Asia/Shanghai'
  })
  async handleDailyStatsCalculation(): Promise<void> {
    this.logger.log('开始执行每日统计任务')

    try {
      // 统计昨天的数据
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)

      // 先统计团队数据
      await this.teamStatsService.calculateTeamDailyStats(yesterday)

      // 再统计应用数据（基于团队数据）
      await this.teamStatsService.calculateAppDailyStats(yesterday)

      this.logger.log(`每日统计任务完成: ${yesterday.toISOString().split('T')[0]}`)
    } catch (error) {
      this.logger.error(`每日统计任务失败: ${error.message}`, error.stack)
    }
  }

  /**
   * 手动执行指定日期的统计任务
   */
  async manualCalculateStats(date: Date): Promise<{
    success: boolean
    message: string
    date: string
  }> {
    this.logger.log(`手动执行统计任务: ${date.toISOString().split('T')[0]}`)

    try {
      // 先统计团队数据
      await this.teamStatsService.calculateTeamDailyStats(date)

      // 再统计应用数据
      await this.teamStatsService.calculateAppDailyStats(date)

      const result = {
        success: true,
        message: '统计任务执行成功',
        date: date.toISOString().split('T')[0]
      }

      this.logger.log(`手动统计任务完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const result = {
        success: false,
        message: `统计任务执行失败: ${error.message}`,
        date: date.toISOString().split('T')[0]
      }

      this.logger.error(`手动统计任务失败: ${JSON.stringify(result)}`)
      return result
    }
  }

  /**
   * 批量补充历史统计数据
   */
  async backfillStats(startDate: Date, endDate: Date): Promise<{
    success: boolean
    message: string
    processedDays: number
    failedDays: number
  }> {
    this.logger.log(
      `开始批量补充统计数据: ${startDate.toISOString().split('T')[0]} 到 ${endDate.toISOString().split('T')[0]}`
    )

    let processedDays = 0
    let failedDays = 0
    const currentDate = new Date(startDate)

    try {
      while (currentDate <= endDate) {
        try {
          this.logger.log(`处理日期: ${currentDate.toISOString().split('T')[0]}`)

          // 统计团队数据
          await this.teamStatsService.calculateTeamDailyStats(new Date(currentDate))

          // 统计应用数据
          await this.teamStatsService.calculateAppDailyStats(new Date(currentDate))

          processedDays++
          this.logger.log(`日期处理完成: ${currentDate.toISOString().split('T')[0]}`)
        } catch (error) {
          failedDays++
          this.logger.error(
            `日期处理失败: ${currentDate.toISOString().split('T')[0]}, error=${error.message}`
          )
        }

        // 移动到下一天
        currentDate.setDate(currentDate.getDate() + 1)

        // 添加小延迟，避免数据库压力过大
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      const result = {
        success: failedDays === 0,
        message: `批量补充完成: 成功${processedDays}天, 失败${failedDays}天`,
        processedDays,
        failedDays
      }

      this.logger.log(`批量补充统计数据完成: ${JSON.stringify(result)}`)
      return result
    } catch (error) {
      const result = {
        success: false,
        message: `批量补充失败: ${error.message}`,
        processedDays,
        failedDays
      }

      this.logger.error(`批量补充统计数据失败: ${JSON.stringify(result)}`)
      return result
    }
  }

  /**
   * 获取统计任务状态
   */
  async getStatsStatus(): Promise<{
    lastCalculatedDate: string | null
    totalTeamRecords: number
    totalAppRecords: number
    todayOverview: any
  }> {
    try {
      // 获取最近的统计日期
      const [lastTeamStats, lastAppStats] = await Promise.all([
        this.teamStatsService['teamDailyStatsModel']
          .findOne({}, {}, { sort: { date: -1 } })
          .exec(),
        this.teamStatsService['appDailyStatsModel']
          .findOne({}, {}, { sort: { date: -1 } })
          .exec()
      ])

      const lastCalculatedDate = lastTeamStats?.date || lastAppStats?.date

      // 获取记录总数
      const [totalTeamRecords, totalAppRecords] = await Promise.all([
        this.teamStatsService['teamDailyStatsModel'].countDocuments(),
        this.teamStatsService['appDailyStatsModel'].countDocuments()
      ])

      // 获取今日概览
      const today = new Date()
      const todayOverview = await this.teamStatsService.getStatsOverview(today)

      return {
        lastCalculatedDate: lastCalculatedDate ? lastCalculatedDate.toISOString().split('T')[0] : null,
        totalTeamRecords,
        totalAppRecords,
        todayOverview
      }
    } catch (error) {
      this.logger.error(`获取统计状态失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 清理过期的统计数据（保留最近90天）
   */
  @Cron('0 2 1 * *', {
    name: 'cleanup-old-stats',
    timeZone: 'Asia/Shanghai'
  })
  async cleanupOldStats(): Promise<void> {
    this.logger.log('开始清理过期统计数据')

    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - 90) // 保留90天

      const [teamDeleted, appDeleted] = await Promise.all([
        this.teamStatsService['teamDailyStatsModel'].deleteMany({
          date: { $lt: cutoffDate }
        }),
        this.teamStatsService['appDailyStatsModel'].deleteMany({
          date: { $lt: cutoffDate }
        })
      ])

      this.logger.log(
        `过期数据清理完成: 团队记录删除${teamDeleted.deletedCount}条, ` +
        `应用记录删除${appDeleted.deletedCount}条`
      )
    } catch (error) {
      this.logger.error(`清理过期数据失败: ${error.message}`, error.stack)
    }
  }
}
