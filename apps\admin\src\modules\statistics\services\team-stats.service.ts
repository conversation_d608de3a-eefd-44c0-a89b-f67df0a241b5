import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import {
  TeamDailyStatsEntity,
  OpenPlatformAppDailyStatsEntity,
  TrafficBillingEntity,
  PlatformAccountEntity,
  TeamEntity,
  OpenPlatformApplicationEntity
} from '@yxr/mongo'

/**
 * 团队统计服务
 * 负责团队和开放平台应用的日统计数据计算和存储
 */
@Injectable()
export class TeamStatsService {
  private readonly logger = new Logger(TeamStatsService.name)

  constructor(
    @InjectModel(TeamDailyStatsEntity.name)
    private teamDailyStatsModel: Model<TeamDailyStatsEntity>,
    @InjectModel(OpenPlatformAppDailyStatsEntity.name)
    private appDailyStatsModel: Model<OpenPlatformAppDailyStatsEntity>,
    @InjectModel(TrafficBillingEntity.name)
    private trafficBillingModel: Model<TrafficBillingEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>
  ) {}

  /**
   * 统计指定日期的团队数据
   */
  async calculateTeamDailyStats(date: Date): Promise<void> {
    this.logger.log(`开始统计团队日数据: ${date.toISOString().split('T')[0]}`)

    try {
      // 设置日期范围（当天的开始和结束）
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)
      
      const endOfDay = new Date(date)
      endOfDay.setHours(23, 59, 59, 999)

      // 获取所有团队
      const teams = await this.teamModel.find({}, { _id: 1 })
      
      this.logger.log(`找到 ${teams.length} 个团队需要统计`)

      let processedCount = 0
      let errorCount = 0

      // 逐个处理团队统计
      for (const team of teams) {
        try {
          await this.calculateSingleTeamStats(team._id, startOfDay, endOfDay)
          processedCount++
        } catch (error) {
          errorCount++
          this.logger.error(`团队统计失败: teamId=${team._id}, error=${error.message}`)
        }
      }

      this.logger.log(
        `团队日统计完成: 日期=${date.toISOString().split('T')[0]}, ` +
        `成功=${processedCount}, 失败=${errorCount}`
      )
    } catch (error) {
      this.logger.error(`团队日统计执行失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 统计单个团队的日数据
   */
  private async calculateSingleTeamStats(
    teamId: Types.ObjectId,
    startOfDay: Date,
    endOfDay: Date
  ): Promise<void> {
    // 统计流量使用量
    const trafficStats = await this.trafficBillingModel.aggregate([
      {
        $match: {
          teamId: teamId,
          createdAt: { $gte: startOfDay, $lte: endOfDay }
        }
      },
      {
        $group: {
          _id: null,
          totalTraffic: { $sum: '$traffic' }
        }
      }
    ])

    const trafficUsage = trafficStats.length > 0 ? trafficStats[0].totalTraffic : 0

    // 统计新增账号数量
    const accountStats = await this.platformAccountModel.aggregate([
      {
        $match: {
          teamId: teamId,
          createdAt: { $gte: startOfDay, $lte: endOfDay }
        }
      },
      {
        $count: 'totalAccounts'
      }
    ])

    const accountsAdded = accountStats.length > 0 ? accountStats[0].totalAccounts : 0

    // 更新或创建团队日统计记录
    await this.teamDailyStatsModel.findOneAndUpdate(
      {
        teamId: teamId,
        date: startOfDay
      },
      {
        $set: {
          trafficUsage,
          accountsAdded,
          updatedAt: new Date()
        }
      },
      {
        upsert: true,
        new: true
      }
    )

    this.logger.debug(
      `团队统计完成: teamId=${teamId}, 流量=${trafficUsage}字节, 新增账号=${accountsAdded}`
    )
  }

  /**
   * 统计指定日期的开放平台应用数据
   */
  async calculateAppDailyStats(date: Date): Promise<void> {
    this.logger.log(`开始统计应用日数据: ${date.toISOString().split('T')[0]}`)

    try {
      // 设置日期范围
      const startOfDay = new Date(date)
      startOfDay.setHours(0, 0, 0, 0)

      // 获取所有开放平台应用
      const applications = await this.applicationModel.find({}, { _id: 1 })
      
      this.logger.log(`找到 ${applications.length} 个应用需要统计`)

      let processedCount = 0
      let errorCount = 0

      // 逐个处理应用统计
      for (const app of applications) {
        try {
          await this.calculateSingleAppStats(app._id, startOfDay)
          processedCount++
        } catch (error) {
          errorCount++
          this.logger.error(`应用统计失败: appId=${app._id}, error=${error.message}`)
        }
      }

      this.logger.log(
        `应用日统计完成: 日期=${date.toISOString().split('T')[0]}, ` +
        `成功=${processedCount}, 失败=${errorCount}`
      )
    } catch (error) {
      this.logger.error(`应用日统计执行失败: ${error.message}`, error.stack)
      throw error
    }
  }

  /**
   * 统计单个应用的日数据
   */
  private async calculateSingleAppStats(
    applicationId: Types.ObjectId,
    date: Date
  ): Promise<void> {
    // 基于团队日统计数据汇总应用数据
    const appStats = await this.teamDailyStatsModel.aggregate([
      {
        $lookup: {
          from: 'teams',
          localField: 'teamId',
          foreignField: '_id',
          as: 'team'
        }
      },
      {
        $unwind: '$team'
      },
      {
        $match: {
          'team.sourceAppId': applicationId,
          date: date
        }
      },
      {
        $group: {
          _id: null,
          totalTrafficUsage: { $sum: '$trafficUsage' },
          totalAccountsAdded: { $sum: '$accountsAdded' },
          teamCount: { $sum: 1 }
        }
      }
    ])

    const stats = appStats.length > 0 ? appStats[0] : {
      totalTrafficUsage: 0,
      totalAccountsAdded: 0,
      teamCount: 0
    }

    // 更新或创建应用日统计记录
    await this.appDailyStatsModel.findOneAndUpdate(
      {
        applicationId: applicationId,
        date: date
      },
      {
        $set: {
          totalTrafficUsage: stats.totalTrafficUsage,
          totalAccountsAdded: stats.totalAccountsAdded,
          teamCount: stats.teamCount,
          updatedAt: new Date()
        }
      },
      {
        upsert: true,
        new: true
      }
    )

    this.logger.debug(
      `应用统计完成: appId=${applicationId}, ` +
      `总流量=${stats.totalTrafficUsage}字节, ` +
      `总新增账号=${stats.totalAccountsAdded}, ` +
      `团队数=${stats.teamCount}`
    )
  }

  /**
   * 获取团队统计数据
   */
  async getTeamStats(
    teamId: string,
    startDate: Date,
    endDate: Date
  ): Promise<TeamDailyStatsEntity[]> {
    return await this.teamDailyStatsModel
      .find({
        teamId: new Types.ObjectId(teamId),
        date: { $gte: startDate, $lte: endDate }
      })
      .sort({ date: 1 })
      .exec()
  }

  /**
   * 获取应用统计数据
   */
  async getAppStats(
    applicationId: string,
    startDate: Date,
    endDate: Date
  ): Promise<OpenPlatformAppDailyStatsEntity[]> {
    return await this.appDailyStatsModel
      .find({
        applicationId: new Types.ObjectId(applicationId),
        date: { $gte: startDate, $lte: endDate }
      })
      .sort({ date: 1 })
      .exec()
  }

  /**
   * 获取统计概览
   */
  async getStatsOverview(date: Date): Promise<{
    totalTeams: number
    totalApps: number
    totalTrafficUsage: number
    totalAccountsAdded: number
  }> {
    const startOfDay = new Date(date)
    startOfDay.setHours(0, 0, 0, 0)

    const [teamStats, appStats] = await Promise.all([
      this.teamDailyStatsModel.aggregate([
        { $match: { date: startOfDay } },
        {
          $group: {
            _id: null,
            totalTeams: { $sum: 1 },
            totalTrafficUsage: { $sum: '$trafficUsage' },
            totalAccountsAdded: { $sum: '$accountsAdded' }
          }
        }
      ]),
      this.appDailyStatsModel.countDocuments({ date: startOfDay })
    ])

    const teamStatsResult = teamStats.length > 0 ? teamStats[0] : {
      totalTeams: 0,
      totalTrafficUsage: 0,
      totalAccountsAdded: 0
    }

    return {
      totalTeams: teamStatsResult.totalTeams,
      totalApps: appStats,
      totalTrafficUsage: teamStatsResult.totalTrafficUsage,
      totalAccountsAdded: teamStatsResult.totalAccountsAdded
    }
  }
}
