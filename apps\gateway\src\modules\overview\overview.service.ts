import { Inject, Injectable } from '@nestjs/common'
import { HomeOverviewResponse, HomeOverviewTrendsResponse } from './overview.dto'
import { InjectModel } from '@nestjs/mongoose'
import {
  HistoryStatisticEntity,
  MemberEntity,
  PlatformAccountEntity,
  PlatformAccountSummaryEntity,
  PlatformAccountTrendEntity,
  TaskEntity
} from '@yxr/mongo'
import { FilterQuery, Model, Types } from 'mongoose'
import { FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import {
  CacheKeyService,
  HistoryStatisticTypeEnum,
  StatisticCommonService,
  TeamRoleNames
} from '@yxr/common'
import dayjs from 'dayjs'

@Injectable()
export class OverviewService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @InjectModel(HistoryStatisticEntity.name)
    private historyStatisticModel: Model<HistoryStatisticEntity>,
    @InjectModel(PlatformAccountTrendEntity.name)
    private platformAccountTrendModel: Model<PlatformAccountTrendEntity>,
    @InjectModel(TaskEntity.name)
    private taskModel: Model<TaskEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly statisticCommonService: StatisticCommonService
  ) {}

  async getHomeOverview(): Promise<HomeOverviewResponse> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    let overviewKey = CacheKeyService.getHomeOverviewKey(currentTeamId)
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      //需要隔离数据
      overviewKey = CacheKeyService.getHomeOverviewKey(currentTeamId, currentUserId)
    }
    const cache = await this.cacheManager.get<HomeOverviewResponse>(overviewKey)
    if (cache) {
      return cache
    }

    const overviewWhere: any = {
      $match: {
        teamId: new Types.ObjectId(currentTeamId)
      }
    }
    const filter: FilterQuery<HistoryStatisticEntity> = {
      teamId: new Types.ObjectId(currentTeamId),
      statisticType: HistoryStatisticTypeEnum.home
    }
    const homeStatisticData: HistoryStatisticEntity = {
      teamId: new Types.ObjectId(currentTeamId),
      statisticType: HistoryStatisticTypeEnum.home
    }

    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      //需要隔离数据
      const platformAccounts = await this.platformAccountModel
        .find({
          members: currentUserId,
          teamId: new Types.ObjectId(currentTeamId)
        })
        .select('_id')
        .lean()

      const platformAccountIds = platformAccounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
      overviewWhere.$match.platformAccountId = {
        $in: platformAccountIds
      }
      homeStatisticData.userId = new Types.ObjectId(currentUserId)
      filter.userId = new Types.ObjectId(currentUserId)
    }
    const fields = [
      'fansTotal',
      'readTotal',
      'playTotal',
      'commentsTotal',
      'likesTotal',
      'favoritesTotal'
    ]

    // 动态构建 $group 语句
    const groupStage: any = {
      _id: null
    }
    fields.forEach((item) => {
      groupStage[item] = { $sum: `$${item}` }
    })
    const overview = await this.platformAccountSummaryModel.aggregate([
      overviewWhere,
      {
        $group: groupStage
      }
    ])

    const homeStatistic = await this.historyStatisticModel.findOne(filter)
    const dataInfo = {}
    const incInfo = {}
    fields.forEach((item) => {
      if (!overview[0]) {
        dataInfo[item] = 0
        incInfo[item] = 0
      } else {
        dataInfo[item] = overview[0][item]
        const historyValue = homeStatistic?.historyStatistic?.[item] ?? overview[0][item]
        incInfo[item] = overview[0][item] - historyValue
      }
    })
    homeStatisticData.historyStatistic = dataInfo
    if (homeStatistic) {
      await this.historyStatisticModel.updateOne(
        {
          _id: new Types.ObjectId(homeStatistic.id)
        },
        homeStatisticData
      )
    } else {
      await this.historyStatisticModel.create(homeStatisticData)
    }
    const returnData: HomeOverviewResponse = {
      current: dataInfo,
      increments: incInfo,
      updatedAt: new Date().getTime()
    }
    await this.cacheManager.set(overviewKey, returnData, 1000 * 60 * 60 * 3) //设置3小时缓存

    return returnData
  }

  /**
   * 获取首页概览趋势数据
   * @returns
   */
  async getHomeOverviewTrends(): Promise<HomeOverviewTrendsResponse[]> {
    const { teamId: currentTeamId } = this.request.session

    let cacheKey = CacheKeyService.getTrendStatisticKey(
      currentTeamId,
      HistoryStatisticTypeEnum.home
    )
    const cacheData = await this.cacheManager.get<HomeOverviewTrendsResponse[]>(cacheKey)
    if (cacheData) {
      return cacheData
    }

    const fields = [
      'fansTotal',
      'readTotal',
      'playTotal',
      'commentsTotal',
      'likesTotal',
      'favoritesTotal'
    ]
    // 动态构建 $group 语句
    const groupStage: any = {
      _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
    }
    fields.forEach((item) => {
      groupStage[item] = { $sum: `$${item}` }
    })
    const date = dayjs().subtract(30, 'day').format('YYYY-MM-DD')
    const res = await this.platformAccountTrendModel.aggregate([
      {
        $match: {
          teamId: new Types.ObjectId(currentTeamId),
          createTime: {
            $gte: date
          }
        }
      },
      {
        $group: groupStage
      }
    ])

    let initData = this.statisticCommonService.generateDates(30)
    const data = initData.map((item) => {
      const dateInfo = res.find((i) => i._id === item.date)
      const playTotal = dateInfo?.playTotal ?? 0
      const readTotal = dateInfo?.readTotal ?? 0
      return {
        date: item.date,
        fansTotal: dateInfo?.fansTotal ?? 0,
        playTotal: playTotal + readTotal,
        commentsTotal: dateInfo?.commentsTotal ?? 0,
        likesTotal: dateInfo?.likesTotal ?? 0,
        favoritesTotal: dateInfo?.favoritesTotal ?? 0
      }
    })

    const now = new Date()
    const expiration = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    const taskOverdueToken = expiration.getTime() - now.getTime()
    await this.cacheManager.set(cacheKey, data, taskOverdueToken)

    return data
  }
}
