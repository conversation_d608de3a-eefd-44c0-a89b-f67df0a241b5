import { OverseasContext, PublishTaskData, PublishResult, PublishContentData } from './types'

/**
 * 内容发布提供者抽象类
 * 各平台内容发布相关的服务需要实现此接口
 */
export abstract class ContentPublishProvider {
  /**
   * 各平台的注册token
   * @param name 平台名称
   */
  static register_token(name: string): string {
    return `${ContentPublishProvider.name}:${name}`.toLowerCase()
  }

  /**
   * 验证发布内容是否符合平台要求
   * @param context 执行上下文
   * @param content 发布内容
   * @returns 验证结果，如果验证失败返回错误信息
   */
  abstract validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }>

  /**
   * 发布内容到平台
   * @param context 执行上下文
   * @param taskData 发布任务数据
   * @returns 发布结果
   */
  abstract publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult>

  /**
   * 查询发布状态（用于异步发布的状态查询）
   * @param context 执行上下文
   * @param taskId 任务ID
   * @param platformContentId 平台内容ID
   * @returns 发布状态
   */
  abstract getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult>

  /**
   * 删除已发布的内容
   * @param context 执行上下文
   * @param platformContentId 平台内容ID
   * @returns 删除结果
   */
  abstract deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }>

  /**
   * 获取平台支持的内容类型
   * @returns 支持的内容类型列表
   */
  abstract getSupportedContentTypes(): string[]

  /**
   * 获取平台内容限制（如文本长度、图片数量等）
   * @returns 内容限制配置
   */
  abstract getContentLimits(): {
    maxTextLength?: number
    maxImageCount?: number
    maxVideoSize?: number // bytes
    maxVideoDuration?: number // seconds
    supportedImageFormats?: string[]
    supportedVideoFormats?: string[]
  }
}
