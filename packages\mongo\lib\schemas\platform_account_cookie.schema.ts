import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { StringExpressionOperator, Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformAccountCookieEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  //账号ID
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: String,
    index: true,
    required: true
  })
  platformName: string

  @Prop({
    type: String,
    required: false
  })
  localStorage?: string

  @Prop({
    type: String,
    required: false
  })
  cookie?: string
}

export const PlatformAccountCookieSchema: ModelDefinition = {
  name: PlatformAccountCookieEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountCookieEntity)
}

export const PlatformAccountCookieMongoose = MongooseModule.forFeature([
  PlatformAccountCookieSchema
])
