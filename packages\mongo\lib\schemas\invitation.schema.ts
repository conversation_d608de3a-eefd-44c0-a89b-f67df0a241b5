import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { InvitationStatusEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class InvitationEntity {

  /**
   * 被邀请人Id
   */
  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: [String],
    required: true
  })
  roles: string[]

  @Prop({
    type: String,
    enum: {
      values: [InvitationStatusEnum.Pending, InvitationStatusEnum.Approved, InvitationStatusEnum.Rejected],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: InvitationStatusEnum.Pending
  })
  status: InvitationStatusEnum

  /**
   * 处理时间
   */
  @Prop({
    type: Date
  })
  handledAt?: Date

  /**
   * 邀请者
   */
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  invitedBy?: Types.ObjectId

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const InvitationSchema: ModelDefinition = {
  name: InvitationEntity.name,
  schema: SchemaFactory.createForClass(InvitationEntity)
}

export const InvitationMongoose = MongooseModule.forFeature([InvitationSchema])
