import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsDateString, IsOptional, IsString } from 'class-validator'
import { Transform } from 'class-transformer'

/**
 * 获取团队统计数据请求DTO
 */
export class GetTeamStatsRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '开始日期',
    example: '2024-01-01'
  })
  @IsDateString()
  startDate: string

  @ApiProperty({
    description: '结束日期',
    example: '2024-01-31'
  })
  @IsDateString()
  endDate: string
}

/**
 * 获取应用统计数据请求DTO
 */
export class GetAppStatsRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  applicationId: string

  @ApiProperty({
    description: '开始日期',
    example: '2024-01-01'
  })
  @IsDateString()
  startDate: string

  @ApiProperty({
    description: '结束日期',
    example: '2024-01-31'
  })
  @IsDateString()
  endDate: string
}

/**
 * 手动执行统计请求DTO
 */
export class ManualStatsRequestDto {
  @ApiProperty({
    description: '统计日期',
    example: '2024-01-01'
  })
  @IsDateString()
  date: string
}

/**
 * 批量补充统计请求DTO
 */
export class BackfillStatsRequestDto {
  @ApiProperty({
    description: '开始日期',
    example: '2024-01-01'
  })
  @IsDateString()
  startDate: string

  @ApiProperty({
    description: '结束日期',
    example: '2024-01-31'
  })
  @IsDateString()
  endDate: string
}

/**
 * 团队日统计响应DTO
 */
export class TeamDailyStatsResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '统计日期',
    example: '2024-01-01'
  })
  date: string

  @ApiProperty({
    description: '当日流量使用量（字节）',
    example: 1048576
  })
  trafficUsage: number

  @ApiProperty({
    description: '当日新增账号数量',
    example: 5
  })
  accountsAdded: number

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 应用日统计响应DTO
 */
export class AppDailyStatsResponseDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiProperty({
    description: '统计日期',
    example: '2024-01-01'
  })
  date: string

  @ApiProperty({
    description: '总流量使用量（字节）',
    example: ********
  })
  totalTrafficUsage: number

  @ApiProperty({
    description: '总新增账号数量',
    example: 50
  })
  totalAccountsAdded: number

  @ApiProperty({
    description: '团队数量',
    example: 10
  })
  teamCount: number

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 统计概览响应DTO
 */
export class StatsOverviewResponseDto {
  @ApiProperty({
    description: '团队总数',
    example: 100
  })
  totalTeams: number

  @ApiProperty({
    description: '应用总数',
    example: 20
  })
  totalApps: number

  @ApiProperty({
    description: '总流量使用量（字节）',
    example: *********
  })
  totalTrafficUsage: number

  @ApiProperty({
    description: '总新增账号数量',
    example: 500
  })
  totalAccountsAdded: number
}

/**
 * 统计状态响应DTO
 */
export class StatsStatusResponseDto {
  @ApiPropertyOptional({
    description: '最后统计日期',
    example: '2024-01-01'
  })
  lastCalculatedDate: string | null

  @ApiProperty({
    description: '团队统计记录总数',
    example: 1000
  })
  totalTeamRecords: number

  @ApiProperty({
    description: '应用统计记录总数',
    example: 200
  })
  totalAppRecords: number

  @ApiProperty({
    description: '今日概览',
    type: StatsOverviewResponseDto
  })
  todayOverview: StatsOverviewResponseDto
}

/**
 * 手动统计结果响应DTO
 */
export class ManualStatsResultResponseDto {
  @ApiProperty({
    description: '执行是否成功',
    example: true
  })
  success: boolean

  @ApiProperty({
    description: '执行结果消息',
    example: '统计任务执行成功'
  })
  message: string

  @ApiProperty({
    description: '统计日期',
    example: '2024-01-01'
  })
  date: string
}

/**
 * 批量补充结果响应DTO
 */
export class BackfillStatsResultResponseDto {
  @ApiProperty({
    description: '执行是否成功',
    example: true
  })
  success: boolean

  @ApiProperty({
    description: '执行结果消息',
    example: '批量补充完成: 成功30天, 失败0天'
  })
  message: string

  @ApiProperty({
    description: '处理成功的天数',
    example: 30
  })
  processedDays: number

  @ApiProperty({
    description: '处理失败的天数',
    example: 0
  })
  failedDays: number
}

/**
 * 获取团队统计数据响应DTO
 */
export class GetTeamStatsResponseDto {
  @ApiProperty({
    type: [TeamDailyStatsResponseDto]
  })
  data: TeamDailyStatsResponseDto[]
}

/**
 * 获取应用统计数据响应DTO
 */
export class GetAppStatsResponseDto {
  @ApiProperty({
    type: [AppDailyStatsResponseDto]
  })
  data: AppDailyStatsResponseDto[]
}

/**
 * 获取统计概览响应DTO
 */
export class GetStatsOverviewResponseDto {
  @ApiProperty({
    type: StatsOverviewResponseDto
  })
  data: StatsOverviewResponseDto
}

/**
 * 获取统计状态响应DTO
 */
export class GetStatsStatusResponseDto {
  @ApiProperty({
    type: StatsStatusResponseDto
  })
  data: StatsStatusResponseDto
}

/**
 * 手动执行统计响应DTO
 */
export class ManualStatsResponseDto {
  @ApiProperty({
    type: ManualStatsResultResponseDto
  })
  data: ManualStatsResultResponseDto
}

/**
 * 批量补充统计响应DTO
 */
export class BackfillStatsResponseDto {
  @ApiProperty({
    type: BackfillStatsResultResponseDto
  })
  data: BackfillStatsResultResponseDto
}
