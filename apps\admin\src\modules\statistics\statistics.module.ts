import { Module } from '@nestjs/common'
import { ScheduleModule } from '@nestjs/schedule'
import {
  TeamDailyStatsMongoose,
  OpenPlatformAppDailyStatsMongoose,
  TrafficBillingMongoose,
  PlatformAccountMongoose,
  TeamMongoose,
  OpenPlatformApplicationMongoose
} from '@yxr/mongo'
import { TeamStatsService } from './services/team-stats.service'
import { StatsSchedulerService } from './services/stats-scheduler.service'
import { StatsController } from './controllers/stats.controller'

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TeamDailyStatsMongoose,
    OpenPlatformAppDailyStatsMongoose,
    TrafficBillingMongoose,
    PlatformAccountMongoose,
    TeamMongoose,
    OpenPlatformApplicationMongoose
  ],
  controllers: [StatsController],
  providers: [TeamStatsService, StatsSchedulerService],
  exports: [TeamStatsService, StatsSchedulerService]
})
export class StatisticsModule {}
