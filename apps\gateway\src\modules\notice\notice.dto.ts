import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { NoticeTypesEnum } from '@yxr/common'
import { IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'

export class getNoticesNewestDTO {
  @ApiProperty({
    description: '最新消息的时间戳, 当在查询的时间戳之后存在新消息时返回最新一条消息的创建时间',
    type: Number,
    required: true
  })
  latest: number

  @ApiProperty({
    description: '是否有最新消息, 当在查询的时间戳之后存在新消息时返回 true',
    type: Boolean,
    required: true,
    example: true
  })
  hasNewest: boolean
}

export class getNoticesNewestResponseDTO {
  @ApiResponseProperty({
    type: getNoticesNewestDTO
  })
  data: getNoticesNewestDTO
}

export class NoticesDTO {
  @ApiProperty({
    description: '消息Id',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: true
  })
  id: string

  @ApiProperty({
    type: String,
    description: '消息标题',
    example: 'xxx邀请你加入xx团队'
  })
  title: string

  @ApiProperty({
    type: String,
    description: '消息内容',
    example: 'xxx邀请你加入xx团队'
  })
  content: string

  @ApiProperty({
    description: '消息来源的团队Id, 系统消息为 null',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: false
  })
  teamId: string

  @ApiProperty({
    type: String,
    description:
      '消息类型, 具体业务类型请参考 https://app.apifox.com/link/project/4626224/apis/doc-4964772',
    example: NoticeTypesEnum.Regular,
    required: true,
    enum: NoticeTypesEnum
  })
  type: NoticeTypesEnum

  @ApiProperty({
    type: Object,
    description: '消息通知业务参数',
    required: false
  })
  bizArgs: any

  @ApiProperty({
    type: Object,
    description: '消息通知业务状态',
    required: false
  })
  bizState: any

  @ApiProperty({
    description: '消息通知生成时间',
    type: Number,
    example: 1723520439658
  })
  createdAt: Number
}

export class NoticesPageResponse {
  @ApiResponseProperty({
    type: [NoticesDTO]
  })
  data: NoticesDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

/**
 * 消息通知列表响应实体
 */
export class NoticesPageResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: NoticesPageResponse
  })
  data: NoticesPageResponse
}

export class VersionQueryDTO {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: String,
    description: '系统类型, windows、macos、ios、android',
    example: 'windows',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '类型不能为空' })
  type: string
}

export class VersionsDTO {
  @ApiProperty({
    type: String,
    description: '版本id'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0'
  })
  version: string

  @ApiProperty({
    type: String,
    description: '公告内容',
    example: '优化发布体验'
  })
  notice: string

  @ApiProperty({
    type: Number,
    description: '创建时间'
  })
  createdAt: number
}

export class VersionsPageResponse {
  @ApiResponseProperty({
    type: [VersionsDTO]
  })
  data: VersionsDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class VersionsPageResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: VersionsPageResponse
  })
  data: VersionsPageResponse
}
