export class VersionService {
  // 工具函数
  static versionToNumber(major: number, minor: number, patch: number) {
    return major * 1_000_000 + minor * 1_000 + patch
  }

  static numberToVersion(versionNumber: number) {
    const major = Math.floor(versionNumber / 1_000_000)
    const minor = Math.floor((versionNumber % 1_000_000) / 1_000)
    const patch = versionNumber % 1_000
    return { major, minor, patch }
  }

  static parseVersion(version: string) {
    if(!version){
      return
    }
    const parts = version.split('.').map(Number)
    if (parts.length !== 3 || parts.some(isNaN)) {
      throw new Error(`错误的版本号: ${version}, 版本号必须为 x.y.z 形式, 且 x,y,z 必须为数字`)
    }
    return parts
  }

  static compareVersions(version1: any, version2: any) {
    const [major1, minor1, patch1] = this.parseVersion(version1)
    const [major2, minor2, patch2] = this.parseVersion(version2)

    if (major2 > major1) {
      return 1
    } else if (major2 < major1) {
      return -1
    } else {
      if (minor2 > minor1) {
        return 1
      } else if (minor2 < minor1) {
        return -1
      } else {
        if (patch2 > patch1) {
          return 1
        } else if (patch2 < patch1) {
          return -1
        } else {
          return 0
        }
      }
    }
  }
}
