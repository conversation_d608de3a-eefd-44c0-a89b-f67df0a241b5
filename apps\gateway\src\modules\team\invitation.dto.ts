import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import {
  IsBoolean,
  IsMobilePhone,
  isMobilePhone,
  IsNotEmpty,
  IsString,
  Length
} from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { MemberStatusEnum } from '@yxr/common'
import { ProposalTeamDetailsDto } from './proposal.dto'

// export class invitationListItemDTO {
//
// }

// export class invitationDetailsDto extends invitationListItemDTO {
//
// }

// export class invitationResponseDTO extends BaseResponseDTO {
//   @ApiResponseProperty({
//     type: invitationDetailsDto
//   })
//   data: invitationDetailsDto
// }
//
//
// export class invitationPagedListResponse {
//   @ApiResponseProperty({
//     type: [invitationListItemDTO]
//   })
//   data: invitationListItemDTO[]
//
//   @ApiResponseProperty({
//     type: Number,
//     example: 1
//   })
//   page: number
//
//   @ApiResponseProperty({
//     type: Number,
//     example: 10
//   })
//   size: number
//
//   @ApiResponseProperty({
//     type: Number,
//     example: 100
//   })
//   totalSize: number
//
//   @ApiResponseProperty({
//     type: Number,
//     example: 100
//   })
//   totalPage: number
// }

// export class getInvitationListOkResponseDTO  extends BaseResponseDTO{
//   @ApiResponseProperty({
//     type: invitationPagedListResponse
//   })
//   data: invitationPagedListResponse
// }

export class InvitationUserStatusDTO {
  @ApiProperty({
    description: '用户Id',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: true
  })
  id: string

  @ApiProperty({
    description: '用户昵称',
    example: '微笑的充电器',
    required: true
  })
  nickName: string

  @ApiProperty({
    description: '用户头像地址',
    example: 'https://static-lite.yixiaoer.cn/avatars/000025.png',
    required: true
  })
  avatarUrl: string

  @ApiProperty({
    description: '头像图片存储KEY',
    example: 'xxx.png',
    required: true
  })
  avatarKey: string

  @ApiProperty({
    type: String,
    description: '加入状态',
    example: MemberStatusEnum.Joined,
    required: true,
    enum: MemberStatusEnum
  })
  status: MemberStatusEnum
}

export class InvitationUserStatusResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: InvitationUserStatusDTO
  })
  data: InvitationUserStatusDTO
}

export class createInvitationRequestBodyDTO {
  @ApiProperty({
    description: '待邀请账号',
    example: '16853247856',
    required: true
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string
}

export class patchInvitationRequestBodyDTO {
  @ApiProperty({
    type: Boolean,
    description: '状态, true: 同意, false: 拒绝',
    required: true
  })
  @IsBoolean()
  approved: boolean
}
