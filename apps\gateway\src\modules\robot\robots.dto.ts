import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator'
import { ReportTypeEnum, RobotTypeEnum, SendingTimeTypeEnum } from '@yxr/common'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class RobotStatisticDTO {
  @ApiProperty({
    type: Number,
    description: '总粉丝数',
    example: 100
  })
  fansTotal?: number

  @ApiProperty({
    type: Number,
    description: '总播放量',
    example: 100
  })
  playTotal?: number

  @ApiProperty({
    type: Number,
    description: '阅读量',
    example: 100
  })
  readTotal?: number

  @ApiProperty({
    type: Number,
    description: '总评论量',
    example: 100
  })
  commentsTotal?: number

  @ApiProperty({
    type: Number,
    description: '总点赞量',
    example: 100
  })
  likesTotal?: number

  @ApiProperty({
    type: Number,
    description: '总收藏量',
    example: 100
  })
  favoritesTotal?: number
}

export class RobotOverviewResponse {
  @ApiProperty({
    description: '当前最新数据(根据平台来动态返回的)'
  })
  current: RobotStatisticDTO

  @ApiProperty({
    description: '上一次统计对比增量数据(根据平台来动态返回的)'
  })
  increments: RobotStatisticDTO
}

export class SendMessageTestRequest {
  @ApiProperty({
    type: String,
    enum: RobotTypeEnum,
    description: '发送渠道',
    example: 'feishu',
    required: true
  })
  @IsString({ message: '发送渠道不正确' })
  @IsEnum(RobotTypeEnum, {
    message: `发送渠道必须是以下值之一: ${Object.values(RobotTypeEnum)}`
  })
  robotType: RobotTypeEnum

  @ApiProperty({
    type: [String],
    description: '数据类型 day:日报,weekly:周报',
    example: 'day',
    enum: ReportTypeEnum,
    required: true
  })
  @IsString({ each: true, message: '数组中的每个数据类型必须是字符串' })
  @IsEnum(ReportTypeEnum, {
    each: true,
    message: `数据类型必须是以下值之一: ${Object.values(ReportTypeEnum)}`
  })
  reportType: string[]

  @ApiProperty({
    description: '推送地址',
    example: 'https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx',
    required: true
  })
  @IsString({ message: '推送地址不正确' })
  @IsNotEmpty({ message: '推送地址不能为空' })
  webhookUrl: string
}

export class PostRobotRequest {
  @ApiProperty({
    type: String,
    enum: RobotTypeEnum,
    description: '发送渠道',
    example: 'feishu',
    required: true
  })
  @IsString({ message: '发送渠道不正确' })
  @IsEnum(RobotTypeEnum, {
    message: `发送渠道必须是以下值之一: ${Object.values(RobotTypeEnum)}`
  })
  robotType: RobotTypeEnum

  @ApiProperty({
    type: [String],
    description: '数据类型 day:日报,weekly:周报',
    example: 'day',
    enum: ReportTypeEnum,
    required: true
  })
  @IsString({ each: true, message: '数组中的每个数据类型必须是字符串' })
  @IsEnum(ReportTypeEnum, {
    each: true,
    message: `数据类型必须是以下值之一: ${Object.values(ReportTypeEnum)}`
  })
  reportType: string[]

  @ApiProperty({
    description: '推送地址',
    example: 'https://open.feishu.cn/open-apis/bot/v2/hook/xxxxxxxx',
    required: true
  })
  @IsString({ message: '推送地址不正确' })
  @IsNotEmpty({ message: '推送地址不能为空' })
  webhookUrl: string

  @ApiProperty({
    type: [String],
    enum: SendingTimeTypeEnum,
    description: '发送时间:数据类型为周报时必填 day每日, monday:星期一,friday:星期五',
    example: 'monday',
    required: false
  })
  @IsString({ each: true, message: '发送时间类型不正确' })
  @IsEnum(SendingTimeTypeEnum, {
    each: true,
    message: `发送时间类型必须是以下值之一: ${Object.values(SendingTimeTypeEnum)}`
  })
  sendTimeType: string[]
}

export class RobotResponse {
  @ApiProperty({
    description: '团队id'
  })
  teamId: string

  @ApiProperty({
    description: '发送渠道',
    type: String,
    enum: RobotTypeEnum
  })
  robotType: RobotTypeEnum

  @ApiProperty({
    description: '数据类型',
    type: [String],
    enum: ReportTypeEnum
  })
  reportType: string[]

  @ApiProperty({
    description: '推送地址'
  })
  webhookUrl: string

  @ApiProperty({
    description: '发布时间类型',
    type: [String],
    enum: SendingTimeTypeEnum
  })
  sendTimeType: string[]

  @ApiProperty({
    description: '创建时间'
  })
  createdAt: number
}

export class RobotResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: RobotResponse
  })
  data: RobotResponse
}
