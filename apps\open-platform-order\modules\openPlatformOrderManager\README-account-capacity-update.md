# 账号点数更新逻辑修复说明

## 修复概述

在 `updateTeamVipExpiration` 方法中增加了账号点数的更新逻辑，确保团队的账号点数限制与VIP状态更新保持一致。

## 方法签名变更

### 修复前
```typescript
private async updateTeamVipExpiration(
  teamId: string,
  orderStartTime: Date,
  orderEndTime: Date,
  session: any
): Promise<void>
```

### 修复后
```typescript
private async updateTeamVipExpiration(
  teamId: string,
  orderStartTime: Date,
  orderEndTime: Date,
  accountCapacityLimit: number,
  session: any
): Promise<void>
```

## 核心业务逻辑

### 1. 非VIP团队处理
当团队当前不是VIP状态且订单立即生效时：
- 设置团队为VIP状态
- 更新团队过期时间为订单结束时间
- **新增**: 设置团队账号点数限制为订单的账号点数

```typescript
if (!isCurrentlyVip) {
  if (orderStartDay <= now) {
    await this.teamModel.findByIdAndUpdate(
      teamId,
      {
        isVip: true,
        expiredAt: orderEndTime,
        accountCapacityLimit: accountCapacityLimit, // 新增
        updatedAt: new Date()
      },
      { session }
    )
  }
}
```

### 2. VIP团队处理
当团队当前是VIP状态且存在时间重叠时：
- 更新团队过期时间为较晚的时间
- **新增**: 重新计算团队的总账号点数（考虑所有有效订单的叠加效果）

```typescript
if (hasOverlap) {
  const newExpiredAt = orderEndTime > currentExpiredAt ? orderEndTime : currentExpiredAt
  
  // 重新计算团队的总账号点数（包括新订单）
  const totalAccountPoints = await this.calculateTeamAccountPointsWithNewOrder(
    teamId, 
    orderStartTime, 
    orderEndTime, 
    accountCapacityLimit, 
    session
  )

  await this.teamModel.findByIdAndUpdate(
    teamId,
    {
      expiredAt: newExpiredAt,
      accountCapacityLimit: totalAccountPoints, // 新增
      updatedAt: new Date()
    },
    { session }
  )
}
```

## 新增辅助方法

### calculateTeamAccountPointsWithNewOrder
专门用于计算包含新订单的团队总账号点数：

```typescript
private async calculateTeamAccountPointsWithNewOrder(
  teamId: string,
  newOrderStartTime: Date,
  newOrderEndTime: Date,
  newOrderAccountCapacity: number,
  session: any
): Promise<number> {
  const now = new Date()
  
  // 查找所有现有的有效账号点数订单
  const existingAccountOrders = await this.orderModel.find({
    teamId: new Types.ObjectId(teamId),
    orderStatus: OrderStatus.Paid,
    resourceType: OpenPlatformOrderResourceType.AccountPoints,
    startTime: { $lte: now },
    endTime: { $gt: now }
  }).session(session)

  // 累加现有订单的账号点数
  let totalAccountPoints = existingAccountOrders.reduce((sum, order) => {
    return sum + (order.accountCapacity || 0)
  }, 0)

  // 检查新订单是否在当前时间有效
  if (newOrderStartTime <= now && newOrderEndTime > now) {
    totalAccountPoints += newOrderAccountCapacity
  }

  return totalAccountPoints
}
```

## 业务场景示例

### 场景1: 非VIP团队创建账号点数订单
```
团队状态: isVip=false, accountCapacityLimit=0
订单信息: 10个账号点数, 2024-01-15 到 2024-04-15
当前时间: 2024-01-15

执行结果:
- 设置团队为VIP: isVip=true
- 更新过期时间: expiredAt=2024-04-15
- 设置账号点数: accountCapacityLimit=10
```

### 场景2: VIP团队时间重叠，账号点数叠加
```
团队状态: isVip=true, accountCapacityLimit=5, expiredAt=2024-03-01
现有订单: 5个账号点数, 2024-01-01 到 2024-03-01
新订单: 8个账号点数, 2024-02-15 到 2024-05-15
当前时间: 2024-01-15

重叠检查: true (2024-02-15 < 2024-03-01 AND 2024-05-15 > 2024-01-15)

执行结果:
- 更新过期时间: expiredAt=2024-05-15 (较晚时间)
- 重新计算账号点数: 5 + 8 = 13
- 更新账号点数: accountCapacityLimit=13
```

### 场景3: VIP团队无时间重叠
```
团队状态: isVip=true, accountCapacityLimit=5, expiredAt=2024-02-01
新订单: 8个账号点数, 2024-03-01 到 2024-06-01
当前时间: 2024-01-15

重叠检查: false (2024-03-01 > 2024-02-01)

执行结果:
- 不更新过期时间和账号点数
- 保持原有状态
```

## 日志记录增强

### 状态检查日志
```typescript
this.logger.debug(
  `团队VIP状态检查: teamId=${teamId}, ` +
  `当前VIP状态=${isCurrentlyVip}, ` +
  `当前过期时间=${team.expiredAt?.toISOString() || 'null'}, ` +
  `当前账号点数=${team.accountCapacityLimit || 0}, ` +
  `订单时间段=${orderStartTime.toISOString()} - ${orderEndTime.toISOString()}, ` +
  `订单账号点数=${accountCapacityLimit}`
)
```

### 非VIP团队设置日志
```typescript
this.logger.log(
  `非VIP团队设置为VIP: teamId=${teamId}, ` +
  `订单立即生效, 新过期时间=${orderEndTime.toISOString()}, ` +
  `账号点数=${accountCapacityLimit}`
)
```

### VIP团队重叠处理日志
```typescript
this.logger.log(
  `VIP团队时间重叠处理: teamId=${teamId}, ` +
  `原过期时间=${currentExpiredAt.toISOString()}, ` +
  `订单结束时间=${orderEndTime.toISOString()}, ` +
  `新过期时间=${newExpiredAt.toISOString()}, ` +
  `原账号点数=${team.accountCapacityLimit || 0}, ` +
  `新账号点数=${totalAccountPoints}`
)
```

## 调用方更新

### 方法调用更新
```typescript
// 修复前
await this.updateTeamVipExpiration(createDto.teamId, createDto.startTime, endTime, session)

// 修复后
await this.updateTeamVipExpiration(createDto.teamId, createDto.startTime, endTime, createDto.accountCapacityLimit, session)
```

### DTO字段名称统一
```typescript
// Admin模块DTO更新
export class CreateAccountPointsOrderRequestDto {
  // 从 accountCount 改为 accountCapacityLimit
  accountCapacityLimit: number
}
```

## 数据一致性保证

### 1. 事务安全
- 所有数据库更新操作都在同一个session事务中执行
- VIP状态、过期时间、账号点数的更新保持原子性

### 2. 计算准确性
- 使用专门的方法计算包含新订单的总账号点数
- 考虑订单的有效时间范围
- 避免重复计算和遗漏

### 3. 业务规则一致性
- 账号点数更新与VIP状态更新遵循相同的业务规则
- 只有在订单生效或时间重叠时才进行更新

## 测试建议

### 1. 单元测试场景
- 非VIP团队立即生效订单的账号点数设置
- VIP团队时间重叠时的账号点数叠加计算
- VIP团队无时间重叠时的账号点数保持不变
- 边界情况：订单未来生效、团队不存在等

### 2. 集成测试场景
- 创建多个重叠订单验证账号点数累加
- 验证团队VIP状态与账号点数的一致性
- 测试订单生效时间对账号点数的影响

### 3. 性能测试
- 大量订单情况下的账号点数计算性能
- 数据库事务的执行效率

## 注意事项

1. **字段名称统一**: 确保所有相关代码使用 `accountCapacityLimit` 而不是 `accountCount`
2. **事务完整性**: 所有相关更新必须在同一个数据库事务中完成
3. **计算准确性**: 账号点数计算必须考虑订单的有效时间范围
4. **日志完整性**: 提供详细的日志记录便于问题排查和监控

这次修复确保了团队账号点数与VIP状态更新的一致性，提供了准确的账号点数叠加计算，并保持了良好的数据完整性和可追溯性。
