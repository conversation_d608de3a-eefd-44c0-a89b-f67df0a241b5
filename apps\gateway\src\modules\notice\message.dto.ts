import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { NoticeTypesEnum } from '@yxr/common'
import { Type } from 'class-transformer'
import { IsOptional } from 'class-validator'

export class MessageListRequestDTO {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class MessageDetail {
  @ApiProperty({
    description: '消息Id',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: true
  })
  id: string

  @ApiProperty({
    type: String,
    description: '消息标题',
    example: '系统标题'
  })
  title: string

  @ApiProperty({
    type: String,
    description: '消息内容',
    example: '系统内容'
  })
  content: string

  @ApiProperty({
    type: Boolean,
    description: '是否弹窗'
  })
  isPopUp: boolean

  @ApiProperty({
    type: String,
    description: '消息类型',
    example: NoticeTypesEnum.System,
    required: true,
    enum: NoticeTypesEnum
  })
  type: NoticeTypesEnum

  @ApiProperty({
    description: '消息通知生成时间',
    type: Number,
    example: 1723520439658
  })
  createdAt: Number
}

export class MessagePageResponse {
  @ApiResponseProperty({
    type: [MessageDetail]
  })
  data: MessageDetail[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

/**
 * 消息通知列表响应实体
 */
export class MessagePageResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MessagePageResponse
  })
  data: MessagePageResponse
}

export class MessageLatestResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: MessageDetail
  })
  data: MessageDetail
}
