import {
  MemberEntity,
  PlatformAccountEntity,
  PlatformAccountSummaryEntity
} from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types } from 'mongoose'
import { Inject, Injectable } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { createObjectCsvStringifier } from 'csv-writer'
import dayjs from 'dayjs'
import { StatisticCommonService, TeamRoleNames } from '@yxr/common'
import { AccountOverviewReportRequest } from './platform-account.dto'

@Injectable()
export class PlatformAccountReportService {
  constructor(
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(MemberEntity.name)
    private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly statisticCommonService: StatisticCommonService
  ) {}

  async exportAccountOverviews(filter: AccountOverviewReportRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const where: FilterQuery<PlatformAccountEntity> = {
      teamId: new Types.ObjectId(currentTeamId)
    }
    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      where.members = currentUserId
    }
    if (filter.name) {
      where.$or = [
        { platformAccountName: { $regex: filter.name, $options: 'i' } },
        { remark: { $regex: filter.name, $options: 'i' } }
      ]
    }
    if (filter.group) {
      where.groups = filter.group
    }
    if (filter.platform) {
      where.platformName = filter.platform
    }
    if (filter.loginStatus) {
      if (filter.loginStatus == 2) {
        where.status = { $gte: filter.loginStatus }
      } else {
        where.status = filter.loginStatus
      }
    }
    const accounts = await this.platformAccountModel
      .find(where)
      .select('_id platformAccountName')
      .lean()

    const accountIds = accounts.map((item) => item._id) // 获取当前用户的所有平台账号ID
    const results = await this.platformAccountSummaryModel
      .find({
        platformAccountId: { $in: accountIds }
      })
      .sort({ publishTime: -1 })
      .lean()

    const platformAccountMap = accounts.reduce((pcc, pitem) => {
      pcc[pitem._id.toString()] = pitem
      return pcc
    }, {})

    let exportDatas = []
    const fields = await this.statisticCommonService.getAccountOverviewField(filter.platform)
    //组装数据
    for (const element of results) {
      const exportData = {
        platformAccountName:
          platformAccountMap[element.platformAccountId.toString()]?.platformAccountName,
        platformName: element.platformName,
        loginStatus:
          platformAccountMap[element.platformAccountId.toString()]?.loginStatus == 1
            ? '有效'
            : '失效',
        updatedAt: element?.updatedAt
          ? dayjs(element.updatedAt).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
          : '-'
      }
      fields.forEach((item) => {
        exportData[item.key] = element[item.key]
      })
      exportDatas.push(exportData)
    }
    return exportDatas
  }

  async generateCsv(data: any[], headers: { id: string; title: string }[]): Promise<string> {
    // 创建 CSV 字符串生成器
    const csvStringifier = createObjectCsvStringifier({ header: headers })

    // 拼接 CSV 内容
    const headerString = csvStringifier.getHeaderString()
    const recordsString = csvStringifier.stringifyRecords(data)

    return headerString + recordsString
  }
}
