import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class TeamExpiredLogEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  customerId?: Types.ObjectId

  /**
   * VIP 到期时间
   */
  @Prop({
    type: Date
  })
  expiredAt?: Date

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const TeamExpiredLogSchema: ModelDefinition = {
  name: TeamExpiredLogEntity.name,
  schema: SchemaFactory.createForClass(TeamExpiredLogEntity)
}

export const TeamExpiredLogMongoose = MongooseModule.forFeature([TeamExpiredLogSchema])
