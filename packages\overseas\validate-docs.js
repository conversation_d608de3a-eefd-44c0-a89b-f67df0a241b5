#!/usr/bin/env node

/**
 * 文档验证脚本
 * 验证README.md中提到的所有文件路径是否存在
 */

const fs = require('fs')
const path = require('path')

const baseDir = __dirname

// 需要验证的文件路径列表
const filesToCheck = [
  'src/utils/axios-config.ts',
  'src/utils/error-handler.ts',
  'src/providers/utils.ts',
  'src/providers/types.ts',
  'src/providers/tiktok/tiktok-api.ts',
  'src/providers/tiktok/tiktok-error-handler.ts',
  'src/providers/tiktok/tiktok-api-types.ts',
  'src/providers/tiktok/tiktok-content-publish.provider.ts',
  'tests/utils/error-handler.spec.ts',
  'tests/utils/axios-config.spec.ts',
  'tests/providers/utils.spec.ts',
  'tests/providers/tiktok/tiktok-api.spec.ts',
  'tests/providers/tiktok/tiktok-error-handler.spec.ts',
  'README.md',
  'DIRECTORY.md',
  'PROXY_CONFIG_GUIDE.md',
  'ERROR_HANDLING_GUIDE.md'
]

console.log('🔍 验证文档中提到的文件路径...\n')

let allValid = true

filesToCheck.forEach(filePath => {
  const fullPath = path.join(baseDir, filePath)
  const exists = fs.existsSync(fullPath)

  if (exists) {
    console.log(`✅ ${filePath}`)
  } else {
    console.log(`❌ ${filePath} - 文件不存在`)
    allValid = false
  }
})

console.log('\n📋 验证结果:')
if (allValid) {
  console.log('✅ 所有文件路径都有效')
  console.log('📖 文档结构完整，可以安全使用')
} else {
  console.log('❌ 发现无效的文件路径')
  console.log('⚠️  请更新文档或检查文件是否存在')
  process.exit(1)
}

// 验证README.md的基本结构
console.log('\n🔍 验证README.md结构...')

try {
  const readmeContent = fs.readFileSync(path.join(baseDir, 'README.md'), 'utf8')

  const requiredSections = [
    '# 海外平台连接器 (Overseas Platform Connector)',
    '## 📋 目录',
    '## 概述',
    '## 核心架构',
    '## 快速开始',
    '## 平台集成',
    '## API调用',
    '## Webhook处理',
    '## 异常处理',
    '## 详细实现',
    '## 使用指南',
    '## 故障排除',
    '## 最佳实践',
    '## API参考'
  ]

  let missingSection = false
  requiredSections.forEach(section => {
    if (readmeContent.includes(section)) {
      console.log(`✅ ${section}`)
    } else {
      console.log(`❌ 缺少章节: ${section}`)
      missingSection = true
    }
  })

  if (!missingSection) {
    console.log('✅ README.md结构完整')
  } else {
    console.log('❌ README.md结构不完整')
    allValid = false
  }

} catch (error) {
  console.log('❌ 无法读取README.md文件')
  allValid = false
}

console.log('\n🎉 文档验证完成!')
if (allValid) {
  console.log('📚 文档已准备就绪，可以开始使用')
} else {
  console.log('⚠️  请修复上述问题后重新验证')
  process.exit(1)
}
