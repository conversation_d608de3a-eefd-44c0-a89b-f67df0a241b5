/* eslint-disable no-continue */
import { Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { Client, ClientGrpc, Transport } from '@nestjs/microservices'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Socket, socketConfig } from '@yxr/proto'
import { Cache } from 'cache-manager'
import { WebhookEvents } from './constant'
import { genSocketRedisKey, getTeamOnlineUsersRedisKey } from '@yxr/utils'
import { SocketBody, WebhookBody } from './types'

@Injectable()
export class WebhookService implements OnModuleInit {
  logger = new Logger('WebhookService')

  @Client({
    transport: Transport.GRPC,
    options: {
      ...socketConfig,
      channelOptions: {
        'grpc.keepalive_time_ms': 10 * 1000,
        'grpc.keepalive_timeout_ms': 5 * 1000,
        'grpc.keepalive_permit_without_calls': 1,
        'grpc.service_config': JSON.stringify({
          methodConfig: [
            {
              name: [{ service: 'Socket' }],
              timeout: '10s',
              retryPolicy: {
                maxAttempts: 3,
                initialBackoff: '0.5s',
                maxBackoff: '10s',
                backoffMultiplier: 1.5,
                retryableStatusCodes: ['UNAVAILABLE', 'INTERNAL']
              }
            }
          ]
        })
      }
    }
  })
  private readonly client: ClientGrpc

  socketService: Socket

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>) {}

  onModuleInit() {
    this.logger.log('WebhookService init')
    this.socketService = this.client.getService<Socket>('Socket')
  }

  /**
   * 发送socket消息
   * @param body
   * @deprecated 请使用 grpchook 方法
   * @returns
   */
  async webhook(body: WebhookBody[]): Promise<void> {
    if (body.length <= 0) {
      return
    }

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []
    for (const e of body) {
      switch (e.event) {
        case WebhookEvents.NoticeCreate:
        case WebhookEvents.AccountUpdate:
        case WebhookEvents.TeamExit:
        case WebhookEvents.TeamRoleChange:

        default:
          break
      }

      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(e.user_id))
      if (socketId) {
        dataList.push({
          socketId,
          data: { ...e }
        })
      }
    }

    if (dataList.length) {
      try {
        this.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {
            // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
          },
          error: (err) => {},
          complete: () => {}
        })
      } catch (error) {
        this.logger.error('socketService error', error)
      }
    }
  }

  /**
   * 发送socket消息
   * @param body
   * @returns
   */
  async grpchook(userIds: string[], teamId: string, body: SocketBody): Promise<void> {
    let onlineUsers: string[] = []
    if (teamId) {
      //发送团队信息过滤未在团队用户
      onlineUsers = await this.cacheManager.store.client.zrange(
        getTeamOnlineUsersRedisKey(teamId),
        0,
        -1
      )
    } else {
      onlineUsers = userIds
    }

    this.logger.debug('onlineUsers:', onlineUsers)

    const onlineUserIds =
      userIds != null ? userIds.filter((value) => onlineUsers.includes(value)) : onlineUsers
    if (onlineUserIds.length <= 0) {
      return
    }
    this.logger.debug('onlineUserIds:', onlineUserIds)

    const dataList: { socketId: string; data: Record<string, unknown> }[] = []
    for (const e of onlineUserIds) {
      const socketId = await this.cacheManager.get<string>(genSocketRedisKey(e))
      if (socketId) {
        dataList.push({
          socketId,
          data: { ...body }
        })
      }
    }

    this.logger.debug('dataList:', dataList)

    if (dataList.length) {
      try {
        this.socketService.send({ list: JSON.stringify(dataList) }).subscribe({
          next: () => {
            // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
          },
          error: (err) => {
            this.logger.error('Error occurred:', err)
          },
          complete: () => {
            this.logger.log('gRPC request completed.')
          }
        })
      } catch (error) {
        this.logger.error('socketService error', error)
      }
    }
  }

  async sendToAll(message: SocketBody) {
    try {
      this.socketService.sendToAll({ data: JSON.stringify(message) }).subscribe({
        next: () => {
          // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
        },
        error: (err) => {
          this.logger.error('Error occurred:', err)
        },
        complete: () => {
          this.logger.log('gRPC request completed.')
        }
      })
    } catch (error) {
      this.logger.error('socketService error', error)
    }
  }

  async sendToDevice(deviceType: string, message: SocketBody) {
    try {
      this.socketService
        .sendToDevice({ deviceType: deviceType, data: JSON.stringify(message) })
        .subscribe({
          next: () => {
            // 在这里处理 gRPC 响应逻辑，如保存到数据库或触发其他操作
          },
          error: (err) => {
            this.logger.error('Error occurred:', err)
          },
          complete: () => {
            this.logger.log('gRPC request completed.')
          }
        })
    } catch (error) {
      this.logger.error('socketService error', error)
    }
  }
}
