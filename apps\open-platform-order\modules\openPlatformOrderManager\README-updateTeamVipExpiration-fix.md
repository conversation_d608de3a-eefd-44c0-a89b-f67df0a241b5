# updateTeamVipExpiration 方法修复说明

## 修复概述

修复了 `updateTeamVipExpiration` 方法中的业务逻辑错误，按照正确的业务规则重新实现了VIP团队过期时间的更新逻辑。

## 业务规则

### 1. 非VIP团队处理
- **条件判断**: `!team.isVip` 或 `!team.expiredAt` 或 `team.expiredAt <= 当前时间`
- **立即生效**: 如果订单开始日期 <= 当前日期，则立即设置为VIP
- **未来生效**: 如果订单开始日期 > 当前日期，则暂不更新VIP状态

### 2. VIP团队处理
- **条件判断**: `team.isVip = true` 且 `team.expiredAt > 当前时间`
- **重叠检查**: 判断订单时间段与团队VIP时间是否重叠
- **重叠条件**: `订单开始时间 < 团队过期时间` AND `订单结束时间 > 当前时间`
- **时间更新**: 如果重叠，取两个过期时间中较晚的一个

## 方法签名变更

### 修复前
```typescript
private async updateTeamVipExpiration(
  teamId: string,
  newExpiration: Date,
  session: any
): Promise<void>
```

### 修复后
```typescript
private async updateTeamVipExpiration(
  teamId: string,
  orderStartTime: Date,
  orderEndTime: Date,
  session: any
): Promise<void>
```

## 核心逻辑实现

### 1. VIP状态判断
```typescript
const now = new Date()
now.setHours(0, 0, 0, 0) // 设置为当天0点，便于日期比较

const orderStartDay = new Date(orderStartTime)
orderStartDay.setHours(0, 0, 0, 0)

// 判断团队当前是否为VIP状态
const isCurrentlyVip = team.isVip && team.expiredAt && team.expiredAt > now
```

### 2. 非VIP团队处理
```typescript
if (!isCurrentlyVip) {
  if (orderStartDay <= now) {
    // 订单立即生效，设置为VIP
    await this.teamModel.findByIdAndUpdate(
      teamId,
      {
        isVip: true,
        expiredAt: orderEndTime,
        updatedAt: new Date()
      },
      { session }
    )
  } else {
    // 订单未来生效，暂不更新VIP状态
    this.logger.debug('订单未来生效，暂不更新VIP状态')
  }
}
```

### 3. VIP团队重叠处理
```typescript
else {
  const currentExpiredAt = team.expiredAt!
  
  // 检查时间重叠
  const hasOverlap = orderStartTime < currentExpiredAt && orderEndTime > now
  
  if (hasOverlap) {
    // 取较晚的过期时间
    const newExpiredAt = orderEndTime > currentExpiredAt ? orderEndTime : currentExpiredAt
    
    await this.teamModel.findByIdAndUpdate(
      teamId,
      {
        expiredAt: newExpiredAt,
        updatedAt: new Date()
      },
      { session }
    )
  }
}
```

## 业务场景示例

### 场景1: 非VIP团队，订单当天生效
```
团队状态: isVip=false, expiredAt=null
订单时间: 2024-01-15 到 2024-04-15
当前时间: 2024-01-15

结果: 立即设置为VIP，过期时间=2024-04-15
```

### 场景2: 非VIP团队，订单未来生效
```
团队状态: isVip=false, expiredAt=null
订单时间: 2024-01-20 到 2024-04-20
当前时间: 2024-01-15

结果: 暂不更新VIP状态，等待订单生效日期
```

### 场景3: VIP团队，时间重叠
```
团队状态: isVip=true, expiredAt=2024-03-01
订单时间: 2024-02-15 到 2024-05-15
当前时间: 2024-01-15

重叠检查: 2024-02-15 < 2024-03-01 AND 2024-05-15 > 2024-01-15 = true
结果: 更新过期时间为2024-05-15（较晚的时间）
```

### 场景4: VIP团队，时间不重叠
```
团队状态: isVip=true, expiredAt=2024-02-01
订单时间: 2024-03-01 到 2024-06-01
当前时间: 2024-01-15

重叠检查: 2024-03-01 < 2024-02-01 = false
结果: 不更新过期时间
```

## 日志记录

### 详细的状态检查日志
```typescript
this.logger.debug(
  `团队VIP状态检查: teamId=${teamId}, ` +
  `当前VIP状态=${isCurrentlyVip}, ` +
  `当前过期时间=${team.expiredAt?.toISOString() || 'null'}, ` +
  `订单时间段=${orderStartTime.toISOString()} - ${orderEndTime.toISOString()}`
)
```

### 操作结果日志
```typescript
// 非VIP团队设置为VIP
this.logger.log(
  `非VIP团队设置为VIP: teamId=${teamId}, ` +
  `订单立即生效, 新过期时间=${orderEndTime.toISOString()}`
)

// VIP团队重叠处理
this.logger.log(
  `VIP团队时间重叠处理: teamId=${teamId}, ` +
  `原过期时间=${currentExpiredAt.toISOString()}, ` +
  `订单结束时间=${orderEndTime.toISOString()}, ` +
  `新过期时间=${newExpiredAt.toISOString()}`
)
```

## 边界情况处理

### 1. 团队不存在
```typescript
if (!team) {
  this.logger.warn(`团队不存在: teamId=${teamId}`)
  return
}
```

### 2. 日期为null的情况
```typescript
const isCurrentlyVip = team.isVip && team.expiredAt && team.expiredAt > now
```

### 3. 时间比较精度
```typescript
const now = new Date()
now.setHours(0, 0, 0, 0) // 统一设置为当天0点

const orderStartDay = new Date(orderStartTime)
orderStartDay.setHours(0, 0, 0, 0) // 统一设置为当天0点
```

## 调用方式更新

### 修复前的调用
```typescript
await this.updateTeamVipExpiration(createDto.teamId, endTime, session)
```

### 修复后的调用
```typescript
await this.updateTeamVipExpiration(createDto.teamId, createDto.startTime, endTime, session)
```

## 影响的文件

1. **主要修复文件**:
   - `apps/open-platform-order/modules/openPlatformOrderManager/openPlatformOrderManager.service.ts`
   - `apps/admin/src/modules/open-platform/services/new-order.service.ts`

2. **方法调用更新**:
   - 两个文件中的 `createAccountPointsOrder` 方法都已更新调用方式

## 测试建议

### 1. 单元测试场景
- 非VIP团队立即生效订单
- 非VIP团队未来生效订单
- VIP团队时间重叠订单
- VIP团队时间不重叠订单
- 边界情况（团队不存在、日期为null等）

### 2. 集成测试场景
- 创建账号点数订单后验证团队VIP状态
- 多个订单的时间重叠处理
- 订单生效时间的准确性验证

### 3. 性能测试
- 大量订单创建时的VIP状态更新性能
- 数据库事务的正确性验证

## 注意事项

1. **事务安全**: 所有数据库更新操作都在传入的session事务中执行
2. **日期精度**: 统一使用当天0点进行日期比较，避免时分秒的影响
3. **日志完整**: 提供详细的调试和操作日志，便于问题排查
4. **向后兼容**: 方法签名变更需要同步更新所有调用点

这次修复确保了VIP团队过期时间更新逻辑的正确性，符合业务需求，并提供了完整的日志记录和错误处理机制。
