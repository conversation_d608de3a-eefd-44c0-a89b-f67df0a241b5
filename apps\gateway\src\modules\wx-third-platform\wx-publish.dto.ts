import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength
} from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Express } from 'express'
export enum WxMediaType {
  image = 'image',
  voice = 'voice',
  video = 'video',
  thumb = 'thumb'
}

export class WxArticle {
  @ApiProperty({
    description: '标题',
    required: true,
    type: String
  })
  @IsNotEmpty({ message: '标题不能为空' })
  @IsString({ message: '标题格式不匹配' })
  title: string

  @ApiProperty({
    description: '作者',
    required: false,
    type: String
  })
  @IsString({ message: '作者格式不匹配' })
  author?: string

  @ApiProperty({
    description:
      '图文消息的摘要,仅有单图文消息才有摘要,多图文此处为空。如果本字段为没有填写,则默认抓取正文前54个字。',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '作者格式不匹配' })
  digest?: string

  @ApiProperty({
    description:
      '图文消息的具体内容,支持HTML标签,必须少于2万字符,小于1M,且此处会去除JS,涉及图片url必须来源 "上传图文消息内的图片获取URL"接口获取。外部图片url将被过滤',
    required: true,
    type: String
  })
  @IsNotEmpty({ message: '内容不能为空' })
  @IsString({ message: '内容格式不匹配' })
  @MaxLength(20000, { message: '内容不能超过2万字符' })
  content: string

  @ApiProperty({
    description: '图文消息的原文地址,即点击“阅读原文”后的URL',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '原文地址格式不匹配' })
  contentSourceUrl?: string

  @ApiProperty({
    description: '图文消息的封面图片素材',
    required: true,
    type: String
  })
  @IsString({ message: '封面图片素材格式不匹配' })
  thumbUrl: string

  @ApiProperty({
    description: '是否打开评论,0不打开(默认),1打开',
    required: false,
    type: Number
  })
  @IsInt({ message: '打开评论关开格式错误' })
  needOpenComment?: number

  @ApiProperty({
    description: '是否粉丝才可评论,0所有人可评论(默认),1粉丝才可评论',
    required: false,
    type: Number
  })
  @IsInt({ message: '打开评论关开格式错误' })
  onlyFansCanComment?: number

  @ApiProperty({
    description:
      '封面裁剪为2.35:1规格的坐标字段。以原始图片（thumb_media_id）左上角（0,0），右下角（1,1）建立平面坐标系，经过裁剪后的图片，其左上角所在的坐标即为（X1,Y1）,右下角所在的坐标则为（X2,Y2），用分隔符_拼接为X1_Y1_X2_Y2，每个坐标值的精度为不超过小数点后6位数字。示例见下图，图中(X1,Y1) 等于（0.1945,0）,(X2,Y2)等于（1,0.5236），所以请求参数值为0.1945_0_1_0.5236。',
    required: false,
    type: String
  })
  @IsString({ message: '封面裁剪格式错误' })
  picCrop235?: string

  @ApiProperty({
    description:
      '封面裁剪为1:1规格的坐标字段，裁剪原理同pic_crop_235_1，裁剪后的图片必须符合规格要求',
    required: false,
    type: String
  })
  @IsInt({ message: '封面裁剪格式错误' })
  picCrop1?: string
}

export class WxPublishCreateRequest {
  @ApiProperty({
    description: '微信账号Ids',
    required: true,
    type: String
  })
  @IsArray({ message: '账号格式不匹配' })
  platformAccountIds: string[]

  @ApiProperty({
    description: '任务集发布类型',
    example: 'verticalVideo',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '发布类型不能为空' })
  publishType: string

  @ApiProperty({
    description: '文章被判定为转载时，且原创文允许转载时，将继续进行群发操作',
    example: 0,
    default: 0,
    required: false
  })
  @IsNumber()
  sendIgnoreReprint: number

  @ApiProperty({
    type: Number,
    description: '是否群发',
    example: '默认为0不群发, 1群发',
    default: 0,
    required: true
  })
  @IsNumber()
  sendAll: number

  @ApiProperty({
    description: '任务集封面存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsString()
  @IsNotEmpty({ message: '任务集封面不能为空' })
  coverKey: string

  @ApiProperty({
    description: '描述',
    example: '任务集描述',
    required: false
  })
  @IsOptional()
  desc: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  @IsOptional()
  descRich: unknown

  @ApiProperty({
    description: '发布内容',
    required: true,
    type: [WxArticle]
  })
  @IsNotEmpty({ message: '发布内容不能为空' })
  @IsArray({ message: '发布内容格式不匹配' })
  articles: WxArticle[]
}

export class WxPublishPreviewRequest {
  @ApiProperty({
    type: [String],
    description: '预览账号',
    example: "['chenjing']",
    required: true
  })
  @IsArray()
  @IsNotEmpty({ message: '微信账号不能为空' })
  towxname: string[]

  @ApiProperty({
    description: '发布内容',
    required: true,
    type: [WxArticle]
  })
  @IsNotEmpty({ message: '发布内容不能为空' })
  @IsArray({ message: '发布内容格式不匹配' })
  articles: WxArticle[]
}

export class WxUploadimgResponse {
  @ApiResponseProperty({
    example:
      'http://mmbiz.qpic.cn/mmbiz/gLO17UPS6FS2xsypf378iaNhWacZ1G1UplZYWEYfwvuU6Ont96b1roYsCNFwaRrSaKTPCUdBK9DgEHicsKwWCBRQ/0',
    type: String
  })
  url: string
}

export class WxMaterialResponse {
  @ApiResponseProperty({
    example: '新增的永久素材的media_id',
    type: String
  })
  media_id: string

  @ApiResponseProperty({
    example:
      'http://mmbiz.qpic.cn/mmbiz/gLO17UPS6FS2xsypf378iaNhWacZ1G1UplZYWEYfwvuU6Ont96b1roYsCNFwaRrSaKTPCUdBK9DgEHicsKwWCBRQ/0',
    type: String
  })
  url: string
}

export class WxUploadimgResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WxUploadimgResponse
  })
  data: WxUploadimgResponse
}

export class WxMaterialRequest {
  @ApiProperty({
    type: String,
    enum: [WxMediaType],
    description: '媒体文件类型，分别有图片（image）、语音（voice）、视频（video）和缩略图（thumb）',
    example: WxMediaType.image,
    required: true
  })
  @IsEnum(WxMediaType, {
    message: `媒体文件类型格式不匹配`
  })
  @IsString()
  type: WxMediaType

  @ApiProperty({
    description: '素材的标题',
    required: false,
    default: 'title',
    type: String
  })
  @IsOptional()
  @IsString({ message: '素材标题格式不匹配' })
  title?: string

  @ApiProperty({
    description: '素材的描述',
    required: false,
    default: 'introduction',
    type: String
  })
  @IsOptional()
  @IsString({ message: '素材描述格式不匹配' })
  introduction?: string
}

export class WxMaterialResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WxMaterialResponse
  })
  data: WxMaterialResponse
}
