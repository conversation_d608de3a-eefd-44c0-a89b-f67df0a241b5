import { Module } from '@nestjs/common'
import { OrderManagerService } from './orderManager.service'
import {
  ContractMongoose,
  InterestMongoose,
  MemberMongoose,
  OrderMongoose,
  PlatformAccountMongoose,
  RefundMongoose,
  UserMongoose,
  TeamMongoose,
  TeamExpiredLogMongoose
} from '@yxr/mongo'

@Module({
  imports: [
    OrderMongoose,
    MemberMongoose,
    TeamMongoose,
    InterestMongoose,
    RefundMongoose,
    UserMongoose,
    ContractMongoose,
    PlatformAccountMongoose,
    TeamExpiredLogMongoose
  ],
  providers: [OrderManagerService],
  exports: [OrderManagerService]
})
export class OrderManagerModule {}
