import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator'
import { Type } from 'class-transformer'
import { OverviewDateTypeEnum } from '@yxr/common'

export class OverviewQueryDTO {
  @ApiProperty({
    description: '日期类型',
    enum: OverviewDateTypeEnum,
    example: OverviewDateTypeEnum.Thirty,
    required: false
  })
  @IsEnum(OverviewDateTypeEnum)
  @IsOptional()
  dateType?: OverviewDateTypeEnum
}

export class TeamStatisticListRequest {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '到期时间开始时间',
    example: 0,
    required: false
  })
  // @IsNotEmpty({ message: '请选择开始时间' })
  @IsOptional()
  startTime: number

  @ApiProperty({
    type: Number,
    description: '到期时间结束时间',
    example: 0,
    required: false
  })
  // @IsNotEmpty({ message: '请选择结束时间' })
  @IsOptional()
  endTime: number

  @ApiProperty({
    type: String,
    description: '客服ID'
  })
  @IsOptional()
  customerId: string
}

export class OverviewTpye {
  @ApiResponseProperty({
    type: Number
  })
  count: number

  @ApiResponseProperty({
    type: Date
  })
  date: string
}

export class DataStatistic {
  @ApiResponseProperty({
    type: Number
  })
  registerCount: number

  @ApiResponseProperty({
    type: Number
  })
  appUserPublishCount: number

  @ApiResponseProperty({
    type: Number
  })
  teamPublishCount: number

  @ApiResponseProperty({
    type: Number
  })
  publishCount: number

  @ApiResponseProperty({
    type: Date
  })
  date: string
}

export class DataScaleDTO {
  @ApiProperty({
    description: '视频发布数',
    example: '100',
    type: Number
  })
  videoPublishNum: number

  @ApiProperty({
    example: '100',
    description: '图文发布数',
    type: Number
  })
  textPublishNum: number

  @ApiProperty({
    example: '100',
    description: '文章发布数',
    type: Number
  })
  articlePublishNum: number

  @ApiProperty({
    example: '100',
    description: '一键发布数',
    type: Number
  })
  oneClickPublishNum: number

  @ApiProperty({
    example: '100',
    description: 'app发布数',
    type: Number
  })
  appPublishNum: number

  @ApiProperty({
    example: '100',
    description: '浏览器发布数',
    type: Number
  })
  browserPublishNum: number

  @ApiProperty({
    example: '100',
    description: '媒体账号数量',
    type: Number
  })
  platformAccountNum: number

  @ApiProperty({
    example: '100',
    description: '空间网站数量',
    type: Number
  })
  browserNum: number
}

export class RegisterScaleDTO {
  @ApiProperty({
    example: '100',
    description: 'ios注册数',
    type: Number
  })
  iosRegisterNum: number

  @ApiProperty({
    example: '100',
    description: 'windows注册数',
    type: Number
  })
  windowsRegisterNum: number

  @ApiProperty({
    example: '100',
    description: 'mac注册数',
    type: Number
  })
  macRegisterNum: number

  @ApiProperty({
    example: '100',
    description: '安卓注册数',
    type: Number
  })
  androidRegisterNum: number

  @ApiProperty({
    example: '100',
    description: '未知注册数（包含历史注册数据和未识别注册数据）',
    type: Number
  })
  otherRegisterNum: number
}

export class Overview {
  @ApiResponseProperty({
    type: [OverviewTpye]
  })
  data: OverviewTpye[]
}

export class DataStatisticDTO {
  @ApiResponseProperty({
    type: [DataStatistic]
  })
  data: DataStatistic[]
}

export class OverviewDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Overview
  })
  data: Overview
}

export class DataStatisticResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: DataStatisticDTO
  })
  data: DataStatisticDTO
}

export class AccountStatisticTrend {
  @ApiResponseProperty({
    type: Date
  })
  date: string

  @ApiResponseProperty({
    type: Number
  })
  incrementAccountTotal: number
}

export class AccountStatistic {
  @ApiResponseProperty({
    type: String
  })
  platformName: string

  @ApiResponseProperty({
    type: [AccountStatisticTrend]
  })
  trendData: AccountStatisticTrend[]
}

export class AccountStatisticDTO {
  @ApiResponseProperty({
    type: [AccountStatistic]
  })
  data: AccountStatistic[]
}

export class AccountStatisticResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AccountStatisticDTO
  })
  data: AccountStatisticDTO
}

export class DataScaleResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: DataScaleDTO
  })
  data: DataScaleDTO
}

export class AccountScaleDTO {
  @ApiProperty({
    example: '抖音',
    description: '平台名称',
    type: String
  })
  platformName: string

  @ApiProperty({
    example: '100',
    description: '账号数',
    type: Number
  })
  count: number
}

export class PlatformPublishScaleDTO {
  @ApiProperty({
    example: '抖音',
    description: '平台名称',
    type: String
  })
  platformName: string

  @ApiProperty({
    example: '100',
    description: '成功发布数',
    type: Number
  })
  successPublishTotal: number

  @ApiProperty({
    example: '100',
    description: '失败发布数',
    type: Number
  })
  failPublishTotal: number
}

export class AccountScaleResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [AccountScaleDTO]
  })
  data: AccountScaleDTO[]
}

export class PlatformPublishScaleResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [PlatformPublishScaleDTO]
  })
  data: PlatformPublishScaleDTO[]
}

export class TeamStatistic {
  @ApiProperty({
    type: String,
    description: '日期'
  })
  createTime: string

  @ApiProperty({
    type: Number,
    description: '注册团队数'
  })
  registerTeamCount: number

  @ApiProperty({
    type: Number,
    description: '付费团队数'
  })
  paidTeamCount: number

  @ApiProperty({
    type: Number,
    description: '转化率'
  })
  conversionRate: number

  @ApiProperty({
    type: Number,
    description: '过期团队数'
  })
  expiredTeamCount: number

  @ApiProperty({
    type: Number,
    description: '续费订单数'
  })
  renewTeamCount: number

  @ApiProperty({
    type: Number,
    description: '续费率'
  })
  renewRate: number
}

export class TeamStatisticDTO {
  @ApiResponseProperty({
    type: [TeamStatistic]
  })
  data: TeamStatistic[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number

  @ApiProperty({
    type: Number,
    description: '注册团队数',
    example: 100
  })
  registerTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '过期团队数',
    example: 100
  })
  expiredTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '付费团队数',
    example: 100
  })
  paidTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '续费团队数',
    example: 100
  })
  renewTeamTotal: number

  @ApiProperty({
    type: Number,
    description: '转化率',
    example: 100
  })
  conversionRateTotal: number

  @ApiProperty({
    type: Number,
    description: '续费率',
    example: 100
  })
  renewRateTotal: number
}

export class TeamStatisticResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamStatisticDTO
  })
  data: TeamStatisticDTO
}

export class OnlineScaleDTO {
  @ApiProperty({
    example: '抖音',
    description: '平台名称',
    type: String
  })
  platformName: string

  @ApiProperty({
    example: "{'1 天内':0,'1-3 天':0,'3-7 天':0,'7-14 天':0,'14-30 天':0,'30 天以上':0}",
    description: '数据对象',
    type: Object
  })
  stats: Record<string, number>
}

export class OnlineScaleResponse extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [OnlineScaleDTO]
  })
  data: OnlineScaleDTO[]
}
