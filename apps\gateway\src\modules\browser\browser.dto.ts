import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString } from 'class-validator'

export class BrowserFavoriteDTO {
  @ApiProperty({
    description: '收藏id',
    type: String
  })
  id: string

  @ApiProperty({
    description: '空间ID',
    type: String
  })
  browserId: string

  @ApiProperty({
    description: '收藏名称',
    type: String
  })
  name: string

  @ApiProperty({
    description: '收藏地址',
    type: String
  })
  websiteUrl: string
}

/**
 * 浏览器收藏和修改
 */
export class PostBrowserCollectRequest {
  @ApiProperty({
    type: String,
    example: '我的收藏夹',
    required: true
  })
  @IsString()
  @IsOptional()
  name: string

  @ApiProperty({
    type: String,
    example: 'http://www.baidu.com',
    required: true
  })
  @IsString()
  @IsOptional()
  websiteUrl?: string
}
