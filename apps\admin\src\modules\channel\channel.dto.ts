import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsBoolean, IsNotEmpty, IsOptional, IsString, Length } from 'class-validator'
import { Type } from 'class-transformer'

export class ChannelDetail {
  @ApiProperty({
    type: String,
    description: '渠道ID'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '渠道账号',
    default: '渠道账号'
  })
  username: string

  @ApiProperty({
    type: String,
    description: '渠道名称',
    default: '渠道名称'
  })
  channelName: string

  @ApiProperty({
    type: String,
    description: '渠道码',
    default: '10001'
  })
  channelCode: string

  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  enabled: boolean

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: false
  })
  giftDays: number

  @ApiProperty({
    type: Number,
    description: '创建时间',
    required: false
  })
  createdAt: number
}

export class ChannelListDetail {
  @ApiProperty({
    type: String,
    description: '渠道ID'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '渠道名称',
    default: '渠道名称'
  })
  channelName: string

  @ApiProperty({
    type: String,
    description: '渠道码',
    default: '10001'
  })
  channelCode: string

  @ApiProperty({
    type: Number,
    description: '用户数'
  })
  userCount: number

  @ApiProperty({
    type: Number,
    description: '订单数'
  })
  orderCount: number

  @ApiProperty({
    type: Number,
    description: '下单总金额数'
  })
  orderTotalAmount: number

  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  enabled: boolean

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: false
  })
  giftDays: number

  @ApiProperty({
    type: Number,
    description: '创建时间',
    required: false
  })
  createdAt: number
}

export class ChannelCreateRequestDTO {
  @ApiProperty({
    type: String,
    description: '渠道名称',
    default: '渠道名称'
  })
  @IsString()
  @IsNotEmpty({ message: '渠道名称不能为空' })
  channelName: string

  @ApiProperty({
    type: String,
    description: '渠道码',
    default: '10001'
  })
  @IsString()
  @IsNotEmpty({ message: '渠道码不能为空' })
  channelCode: string

  @ApiProperty({ description: '用户名', required: true })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  username: string

  @ApiProperty({ description: '密码', required: true })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: false
  })
  @IsOptional()
  giftDays: number
}

export class ChannelPatchRequestDTO {
  @ApiProperty({
    type: String,
    description: '渠道名称',
    default: '渠道名称'
  })
  @IsOptional()
  @IsString()
  channelName: string

  @ApiProperty({ description: '密码', required: true })
  @IsOptional()
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: false
  })
  @IsOptional()
  giftDays: number
}

export class PutChannelEnabledRequestDTO {
  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  @IsBoolean()
  enabled: boolean
}

export class ChannelListRequestDTO {
  @ApiProperty({
    type: String,
    description: '渠道名称',
    default: '渠道名称'
  })
  @IsString()
  @IsOptional()
  channelName: string

  @ApiProperty({
    type: String,
    description: '渠道码',
    default: '10001'
  })
  @IsString()
  @IsOptional()
  channelCode: string

  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class ChannelDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ChannelDetail
  })
  data: ChannelDetail
}

export class ChannelListResponse {
  @ApiResponseProperty({
    type: [ChannelListDetail]
  })
  data: ChannelListDetail[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class ChannelListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ChannelListResponse
  })
  data: ChannelListResponse
}
