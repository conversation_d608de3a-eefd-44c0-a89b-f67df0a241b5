import { Injectable, Logger } from '@nestjs/common'
import { ContentPublishProvider } from '../content-publish.provider'
import { 
  OverseasContext, 
  PublishTaskData, 
  PublishResult, 
  PublishContentData, 
  PublishContentType,
  PublishTaskStatus 
} from '../types'
import { YoutubeApi } from './youtube-api'

@Injectable()
export class YoutubeContentPublishProvider extends ContentPublishProvider {
  private readonly logger = new Logger(YoutubeContentPublishProvider.name)

  constructor(private readonly youtubeApi: YoutubeApi) {
    super()
  }

  /**
   * 验证发布内容是否符合YouTube要求
   */
  async validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    // 检查内容类型支持
    const supportedTypes = this.getSupportedContentTypes()
    if (!supportedTypes.includes(content.type)) {
      errors.push(`不支持的内容类型: ${content.type}`)
    }

    // 检查文本长度
    const limits = this.getContentLimits()
    if (content.title && content.title.length > 100) {
      errors.push(`标题超过最大长度限制 100 字符`)
    }

    if (content.description && content.description.length > limits.maxTextLength) {
      errors.push(`描述超过最大长度限制 ${limits.maxTextLength} 字符`)
    }

    // YouTube主要是视频平台
    if (content.type !== PublishContentType.Video && content.type !== PublishContentType.Mixed) {
      errors.push('YouTube主要支持视频内容发布')
    }

    if (!content.videoUrl) {
      errors.push('YouTube发布必须包含视频内容')
    }

    if (!content.title || content.title.trim().length === 0) {
      errors.push('YouTube发布必须包含标题')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 发布内容到YouTube
   */
  async publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult> {
    this.logger.log(`开始发布到YouTube: 任务=${taskData.taskId}, 账号=${context.accountOpenId}`)

    try {
      const result = await this.publishVideoContent(context, taskData)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Success,
        platformContentId: result.id,
        platformContentUrl: `https://www.youtube.com/watch?v=${result.id}`,
        rawResponse: result,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`YouTube发布失败: 任务=${taskData.taskId}`, error)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'YOUTUBE_PUBLISH_ERROR',
        rawResponse: error.response?.data,
        completedAt: new Date()
      }
    }
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult> {
    try {
      const video = await this.youtubeApi.getVideo(context, platformContentId)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Success,
        platformContentId: platformContentId,
        platformContentUrl: `https://www.youtube.com/watch?v=${platformContentId}`,
        rawResponse: video,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`查询YouTube发布状态失败: 任务=${taskId}`, error)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        completedAt: new Date()
      }
    }
  }

  /**
   * 删除已发布的内容
   */
  async deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      await this.youtubeApi.deleteVideo(context, platformContentId)
      return { success: true }
    } catch (error) {
      this.logger.error(`删除YouTube内容失败: ${platformContentId}`, error)
      return { 
        success: false, 
        errorMessage: error.message 
      }
    }
  }

  /**
   * 获取支持的内容类型
   */
  getSupportedContentTypes(): string[] {
    return [
      PublishContentType.Video,
      PublishContentType.Mixed
    ]
  }

  /**
   * 获取内容限制
   */
  getContentLimits() {
    return {
      maxTextLength: 5000, // YouTube描述限制
      maxImageCount: 0,    // YouTube不支持纯图片
      maxVideoSize: 256 * 1024 * 1024 * 1024, // 256GB
      maxVideoDuration: 12 * 60 * 60, // 12小时
      supportedImageFormats: [],
      supportedVideoFormats: ['mp4', 'mov', 'avi', 'wmv', 'flv', 'webm', 'mkv']
    }
  }

  /**
   * 发布视频内容
   */
  private async publishVideoContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { videoUrl, title, description, videoCover, tags } = taskData.content
    
    return await this.youtubeApi.uploadVideo(context, {
      video_url: videoUrl,
      snippet: {
        title: title || '未命名视频',
        description: description || '',
        tags: tags || [],
        categoryId: '22', // People & Blogs
        defaultLanguage: 'zh-CN',
        defaultAudioLanguage: 'zh-CN'
      },
      status: {
        privacyStatus: 'public', // public, unlisted, private
        embeddable: true,
        license: 'youtube',
        publicStatsViewable: true
      },
      thumbnail_url: videoCover
    })
  }
}
