import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { RechargeType, RechargeStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_application_recharges',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformApplicationRechargeEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  /**
   * 开放平台用户ID - 用于数据隔离，确保每个开放平台用户只能访问自己的充值记录
   */
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true,
    ref: 'OpenPlatformUserEntity'
  })
  openPlatformUserId: Types.ObjectId

  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true
  })
  rechargeOrderNo: string

  @Prop({
    type: String,
    enum: RechargeType,
    required: true
  })
  rechargeType: RechargeType

  @Prop({
    type: Number,
    required: true,
    min: 0
  })
  rechargeAmount: number

  @Prop({
    type: Number,
    required: true,
    min: 0
  })
  virtualCoinAmount: number

  @Prop({
    type: Number,
    required: true,
    min: 0,
    default: 0
  })
  paymentAmount: number

  @Prop({
    type: String,
    enum: RechargeStatus,
    required: true,
    default: RechargeStatus.PENDING
  })
  rechargeStatus: RechargeStatus

  @Prop({
    type: Date,
    required: false
  })
  rechargeTime?: Date

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  remark?: string

  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  operatorId?: Types.ObjectId

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const OpenPlatformApplicationRechargeSchema: ModelDefinition = {
  name: OpenPlatformApplicationRechargeEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformApplicationRechargeEntity)
}

export const OpenPlatformApplicationRechargeMongoose = MongooseModule.forFeature([OpenPlatformApplicationRechargeSchema])
