import crypto from 'crypto'
import { Date } from 'mongoose'

export class Cryptography {
  // Convert from host byte order to network byte order
  static hostToNetworkOrder(value: number) {
    const buffer = Buffer.alloc(4)
    buffer.writeUInt32BE(value, 0)
    return buffer.readUInt32BE(0)
  }

  // AES 解密
  static aesDecrypt(
    input: string,
    encodingAESKey: string,
    appidCallback: { (appid: any): void; (appid: any): void; (arg0: string): void }
  ) {
    const key = Buffer.from(encodingAESKey + '=', 'base64')
    const iv = key.subarray(0, 16) // first 16 bytes of the key
    const btmpMsg = Cryptography.aesDecryptWithIv(input, iv, key)
    const len = Cryptography.networkToHostOrder(btmpMsg.readUInt32BE(16))

    const bMsg = btmpMsg.subarray(20, 20 + len)
    const bAppid = btmpMsg.subarray(20 + len)

    const oriMsg = bMsg.toString('utf8')
    const appid = bAppid.toString('utf8')

    appidCallback(appid) // returning appid via callback

    return oriMsg
  }

  // AES 加密
  static aesEncrypt(
    input: string | { [Symbol.toPrimitive](hint: 'string'): string },
    encodingAESKey: string,
    appid: string | { [Symbol.toPrimitive](hint: 'string'): string }
  ) {
    const key = Buffer.from(encodingAESKey + '=', 'base64')
    const iv = key.subarray(0, 16) // first 16 bytes of the key
    const randCode = Cryptography.createRandCode(16)
    const bRand = Buffer.from(randCode, 'utf8')
    const bAppid = Buffer.from(appid, 'utf8')
    const btmpMsg = Buffer.from(input, 'utf8')
    const bMsgLen = Buffer.alloc(4)
    bMsgLen.writeUInt32BE(Cryptography.hostToNetworkOrder(btmpMsg.length), 0)

    const bMsg = Buffer.concat([bRand, bMsgLen, btmpMsg, bAppid])

    return Cryptography.aesEncryptWithIv(bMsg, iv, key)
  }

  // Generate Random Code (similar to CreateRandCode)
  static createRandCode(codeLen = 16) {
    const codeSerial =
      '2,3,4,5,6,7,a,c,d,e,f,h,i,j,k,m,n,p,r,s,t,A,C,D,E,F,G,H,J,K,M,N,P,Q,R,S,U,V,W,X,Y,Z'
    const arr = codeSerial.split(',')
    let code = ''
    const rand = new Random()
    for (let i = 0; i < codeLen; i++) {
      const randValue = rand.nextInt(0, arr.length - 1)
      code += arr[randValue]
    }
    return code
  }

  // AES Decrypt with IV and Key
  static aesDecryptWithIv(
    input: string,
    iv: Buffer | crypto.BinaryLike,
    key: Buffer | crypto.CipherKey
  ) {
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
    decipher.setAutoPadding(false) //不自动填充
    let decrypted = decipher.update(input, 'base64')
    decrypted = Buffer.concat([decrypted, decipher.final()])
    return decrypted
  }

  // AES Encrypt with IV and Key
  static aesEncryptWithIv(
    input: Buffer | crypto.BinaryLike,
    iv: Buffer | crypto.BinaryLike,
    key: Buffer | crypto.CipherKey
  ) {
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
    let encrypted = cipher.update(input)
    encrypted = Buffer.concat([encrypted, cipher.final()])
    return encrypted.toString('base64')
  }

  // Network to Host Order
  static networkToHostOrder(value: number) {
    return value
  }
}

// Random number generator to replicate C# Random
class Random {
  private seed: number
  constructor() {
    this.seed = Date.now()
  }

  nextInt(min: number, max: number) {
    const rand = this.seed * 9301 + (49297 % 233280)
    this.seed = rand
    return Math.floor((rand / 233280) * (max - min) + min)
  }
}
