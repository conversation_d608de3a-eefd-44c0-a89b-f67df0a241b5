import { BadRequestException, Injectable } from '@nestjs/common'
import { TosService } from '@yxr/huoshan'
import { getOssSignatureDto } from './admin-oss.dto'

@Injectable()
export class AdminOssService {
  private deskTopBucketName = 'yixiaoer-lite-desktop-download'
  constructor(private readonly tosService: TosService) {}

  async getListObjects(name: string) {
    try {
      const getListObjects = await this.tosService.getFileList(name, this.deskTopBucketName)
      return getListObjects
    } catch (error) {
      throw new BadRequestException(error)
    }
  }

  async getUploadSignatureUrl(key: string): Promise<getOssSignatureDto> {
    const signatureUrl = await this.tosService.getUploadSignatureUrl(key,null, this.deskTopBucketName)
    return {
      serviceUrl: signatureUrl,
      key: key
    }
  }

  /**
   * 上传文件到oss
   * @param fileString
   * @returns
   */
  async uploadFile(buffer: Buffer, name: string) {
    try {
      await this.tosService.uploadFile(buffer, name)

      return name
    } catch (error) {
      throw new BadRequestException(error)
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await this.tosService.deleteOssObject(key)
    } catch (error) {
      throw new BadRequestException('Oss文件删除失败 info:' + error)
    }
  }
}
