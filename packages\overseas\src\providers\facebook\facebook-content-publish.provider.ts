import { Injectable, Logger } from '@nestjs/common'
import { ContentPublishProvider } from '../content-publish.provider'
import { 
  OverseasContext, 
  PublishTaskData, 
  PublishResult, 
  PublishContentData, 
  PublishContentType,
  PublishTaskStatus 
} from '../types'
import { FacebookApi } from './facebook-api'

@Injectable()
export class FacebookContentPublishProvider extends ContentPublishProvider {
  private readonly logger = new Logger(FacebookContentPublishProvider.name)

  constructor(private readonly facebookApi: FacebookApi) {
    super()
  }

  /**
   * 验证发布内容是否符合Facebook要求
   */
  async validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    // 检查内容类型支持
    const supportedTypes = this.getSupportedContentTypes()
    if (!supportedTypes.includes(content.type)) {
      errors.push(`不支持的内容类型: ${content.type}`)
    }

    // 检查文本长度
    const limits = this.getContentLimits()
    if (content.text && content.text.length > limits.maxTextLength) {
      errors.push(`文本内容超过最大长度限制 ${limits.maxTextLength} 字符`)
    }

    // 检查图片数量
    if (content.images && content.images.length > limits.maxImageCount) {
      errors.push(`图片数量超过最大限制 ${limits.maxImageCount} 张`)
    }

    // 检查必需内容
    if (content.type === PublishContentType.Text && !content.text) {
      errors.push('文本类型内容必须包含文本')
    }

    if (content.type === PublishContentType.Image && (!content.images || content.images.length === 0)) {
      errors.push('图片类型内容必须包含至少一张图片')
    }

    if (content.type === PublishContentType.Video && !content.videoUrl) {
      errors.push('视频类型内容必须包含视频URL')
    }

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 发布内容到Facebook
   */
  async publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult> {
    this.logger.log(`开始发布到Facebook: 任务=${taskData.taskId}, 账号=${context.accountOpenId}`)

    try {
      let result: any

      switch (taskData.content.type) {
        case PublishContentType.Text:
          result = await this.publishTextContent(context, taskData)
          break
        case PublishContentType.Image:
          result = await this.publishImageContent(context, taskData)
          break
        case PublishContentType.Video:
          result = await this.publishVideoContent(context, taskData)
          break
        case PublishContentType.Mixed:
          result = await this.publishMixedContent(context, taskData)
          break
        default:
          throw new Error(`不支持的内容类型: ${taskData.content.type}`)
      }

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Success,
        platformContentId: result.id,
        platformContentUrl: result.permalink_url || `https://facebook.com/${result.id}`,
        rawResponse: result,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`Facebook发布失败: 任务=${taskData.taskId}`, error)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'FACEBOOK_PUBLISH_ERROR',
        rawResponse: error.response?.data,
        completedAt: new Date()
      }
    }
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult> {
    try {
      const post = await this.facebookApi.getPost(context, platformContentId)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Success,
        platformContentId: platformContentId,
        platformContentUrl: post.permalink_url || `https://facebook.com/${platformContentId}`,
        rawResponse: post,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`查询Facebook发布状态失败: 任务=${taskId}`, error)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        completedAt: new Date()
      }
    }
  }

  /**
   * 删除已发布的内容
   */
  async deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      await this.facebookApi.deletePost(context, platformContentId)
      return { success: true }
    } catch (error) {
      this.logger.error(`删除Facebook内容失败: ${platformContentId}`, error)
      return { 
        success: false, 
        errorMessage: error.message 
      }
    }
  }

  /**
   * 获取支持的内容类型
   */
  getSupportedContentTypes(): string[] {
    return [
      PublishContentType.Text,
      PublishContentType.Image,
      PublishContentType.Video,
      PublishContentType.Mixed
    ]
  }

  /**
   * 获取内容限制
   */
  getContentLimits() {
    return {
      maxTextLength: 63206, // Facebook文本限制
      maxImageCount: 10,    // Facebook相册最大图片数
      maxVideoSize: 4 * 1024 * 1024 * 1024, // 4GB
      maxVideoDuration: 240 * 60, // 240分钟
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
      supportedVideoFormats: ['mp4', 'mov', 'avi', 'mkv', 'webm', '3gp']
    }
  }

  /**
   * 发布纯文本内容
   */
  private async publishTextContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const message = taskData.content.text
    return await this.facebookApi.createPost(context, { message })
  }

  /**
   * 发布图片内容
   */
  private async publishImageContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { images, text } = taskData.content
    
    if (images.length === 1) {
      // 单张图片
      return await this.facebookApi.createPhotoPost(context, {
        url: images[0],
        caption: text
      })
    } else {
      // 多张图片（相册）
      return await this.facebookApi.createAlbumPost(context, {
        photos: images.map(url => ({ url })),
        message: text
      })
    }
  }

  /**
   * 发布视频内容
   */
  private async publishVideoContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { videoUrl, text, videoCover } = taskData.content
    
    return await this.facebookApi.createVideoPost(context, {
      file_url: videoUrl,
      description: text,
      thumb: videoCover
    })
  }

  /**
   * 发布混合内容
   */
  private async publishMixedContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { text, images, videoUrl } = taskData.content
    
    if (videoUrl) {
      // 如果有视频，优先发布视频
      return await this.publishVideoContent(context, taskData)
    } else if (images && images.length > 0) {
      // 否则发布图片
      return await this.publishImageContent(context, taskData)
    } else {
      // 最后发布文本
      return await this.publishTextContent(context, taskData)
    }
  }
}
