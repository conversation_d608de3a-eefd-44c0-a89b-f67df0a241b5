import { registerAs } from '@nestjs/config'

export const BaseConfigRegister = registerAs('app', () => {
  return {
    /**
     * 客户端ID
     */
    clientSecret: process.env.CLIENT_SECRET,

    /**
     * 客户端密钥
     */
    clientKey: process.env.CLIENT_KEY,

    /**
     * 应用名称
     */
    language: 'zh',
    /**
     * 应用端口, host
     */
    http: {
      host: '0.0.0.0',
      port: 3000
    },
    /**
     * 同步间隔
     */
    syncInterval: '',
    /**
     * 时区
     */
    timezone: 'Asia/Shanghai',
    /**
     * 短信accessKeyId
     */
    smsAccessKeyId: 'LTAI5tMJQUPHUGvsKrsPcYmk',

    /**
     * 短信accessKeySecret
     */
    smsAccessKeySecret: '******************************',

    /**
     * 短信endpoint
     */
    smsEndpoint: 'dysmsapi.aliyuncs.com',

    /**
     * 短信签名name
     */
    smsSignName: '长沙草儿绽放科技',

    /**
     * 短信模板code
     */
    smsTemplateCode: 'SMS_222866265',

    /**
     * 会话令牌过期时间
     */
    overdueToken: 1000 * 60 * 60 * 24 * 7, // 7天

    /**
     * 短信验证码过期时间
     */
    smsCodeTime: 1000 * 60 * 5, // 5分钟

    /**
     * cors配置
     */
    cors: {
      allowMethod: '*',
      allowOrigin: '*',
      allowHeader: '*'
    },

    alipay: {
      appId: '2021002134645499',
      privateKey:
        'MIIEpAIBAAKCAQEAwUGFY0few+HjhAovkYf20U7UWY2vjw2m/GBddFaqtZjtY3DPURBbJDcpCd9sSYCLFbNKaXVKvlOsQg184CH0BUtyjhK5gdGZ0bl01/8c4tYS4C4mCtxiuz5+eitH0q8ER7qoiOnyZpSmv9zpsl+FzILBSq9Ny8MnLzXAbteJ5Z4M9cbpanJtverdoBXnTPfC+y6xv85DGQV1QMx1OLtxbZJ2bMtsVHhjjRXkHuCjDBNeSBwchywm/kJzqENaKVrD8dXpALnYzSXxdHVUaL08wjRoorFLePNhZejhnYzEl9F8yAdj7QBQDwaAZAetUlDMNhm4go0mKoHvi25bz5ymNwIDAQABAoIBABDO5g8QfSuERgmB6Uk7Dhh+RrHTROWoRlHE/9vSfd51gpAXi7B9P7ASrBsTt5Nc/rvQcDRj4zBSjrTf/3BvA45CVaBNuJy14i7/dk4i/hwsik+9M8nWCTBH6zAs+34zgqfKcEYYMuEBJM+jIyBEdpqm9LDONnH2cz4D6sy5KmEyCvUDlHAWUp770p4vP+USKnkVn1hStPvzhCW0JlskfzVYc+SjIG9ghq3q88ksdZ71qT5H0PsKclsX0tm4YULvuNkBsQ+m9WaJfhUMxY7bWW0AP73Jf/5lPwQo8QGXI2oOQ8DqWUA4NsIA0oNJUdIU+k0xnkhnwWop4bhPAR/kvUECgYEA+VFE+h/Y07VCx+d8EY4rhqWX3oXS2D821aesoXiWaNOxDZOfvslxkLUM/Cg+RBgPLn28SpFMeeMprQBjrmGpomJpfHnF0JcgvyqrHhEKRqpBSup5Oi1kmOksckLxb6tIvJnzMiBJo6ssvd1dFj5tQfOUF+G5xXa1D6Eo6FIRipkCgYEAxm+Tp6BZKmm8rC1J+jMeLRkLCe6d8O3nxRcLhiyGTD7+RwZ8eMseQ+m/LGdxYX6O3d+GelfO5/Yru0S98VXADTon4n8MPMfx8jgOXJ/glFlESL5zc5FitKmmdyUQmPUU+bRcrOxyF+Z5cdvT3Bb/MFZ3dWvq1H1l8OInkKSfiU8CgYEAvMfkyuBCdkvR/TwynbhhDNtmW4BJXqS/CJimBr4gZc+lSPp3AyfVbhUpd+WhAD6XgESExpGurrxrWivpTql0oRiTd0Vdm8xTZ20PlPyiI0/XkLpqgLrOaVpl5T77QesYcHbBxzrbh6qdfkaXrbgnUsD1af/pgrOX9fW52/AIiEECgYBkf0exgnUGFOz36ivoKhFjV6aOjimxRlu2JniwqEYPwWt4PyG073HRhUVL0MvMS5NBbR4nQedI9RSq0CK+YeZVDJn8jMl+0uiG+rrTUCkmGJuLl+DSq9PomW70MnJYdO79k8nHzivuTMw+zM1cTs1JBtGTR2dz3FpPh8nv6F/f4wKBgQCUC1xLlCMpTdgdJb5E+V9AYsgadmIbsaEC9BHMb551QhTZB3ziKcOZwHjku2t6s5q4hd6gdIIa/oEKqjt0E2oK0iXyl7ijJbZbG87c5fYpUFoH5GefT5fW+R6q7C+ebRoQ95opn56SBpulGVhfJ0CG7SgUtNmP7E7hxuJ8ITk7mw==',
      alipayPublicKey:
        'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo7MsNzWtjllREARTQ2yFlrjZSHVAJXroZ/5FmPUNDyWPd9V26UlVvJ+LiTZ/4CNKFt+7O7vEFdlYYlAcyHyTQA5RYvJNIJx3e0x2HSZC8aD2rKofTTSkbCEDil1bTzyEpNHOg7cyQx+L0/tdOwA3R1a1wn8/nAB708c3AnTk3mS/eJIpxG948PlaHi37WfZqlCT2cmRkqDhYZ/C9tYfG8Vlmxh/TFjafhvEowL9/8v+0/eIeG7iS3OraRr9aeir/mPFlCQm3WYrlcjyhUNXGG3xXuZzEgFdtPucshPIpBfdyL7mUsDIFEqt5rLJN9bCXcwKReJruE8yqUhOmPmoFVQIDAQAB'
    },

    wechatPayInfo: {
      appId: 'wx9006cd2865fbd221',
      appSecret: '3f2e6923c83984843b8b9b9c55be61ce',
      mchId: '1607882447',
      key: 'CEZFcaoerzhanfangcoozf2022caoer0'
    },

    /**
     * rateLimit配置
     */
    rateLimit: {
      resetTime: '1 minute',
      maxRequestPerId: 30
    },

    /**
     * 购买后的二维码
     */
    serviceCodeUrl:
      'https://yixiaoer-lite-asserts.oss-cn-shanghai.aliyuncs.com/service-qr-code-DWjeU5gc.jpg',

    jwtSecret: 'hFyoLoQajKukdYHiXPPzxnFjVRsxpE17iQr1ML6AvjhpJvAmXgE4r7XLHeD1vKLGBJYDwfkEVn02Wfu2hZ'
  }
})

export type BaseConfig = ReturnType<typeof BaseConfigRegister>
