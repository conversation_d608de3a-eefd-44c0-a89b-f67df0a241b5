import { ApiProperty, ApiPropertyOptional, ApiResponseProperty } from '@nestjs/swagger'
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min, IsMongoId } from 'class-validator'
import { Transform } from 'class-transformer'
import { RechargeType, RechargeStatus } from '@yxr/common'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'


/**
 * 创建充值订单请求DTO
 */
export class CreateRechargeRequestDto {
  @ApiProperty({
    description: '手机号码',
    example: '***********'
  })
  @IsNotEmpty({ message: '应用ID不能为空' })
  @IsString()
  phone: string

  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty({ message: '应用ID不能为空' })
  @IsString()
  applicationId: string

  @ApiProperty({
    description: '充值类型',
    enum: RechargeType,
    example: RechargeType.BANK_TRANSFER
  })
  @IsNotEmpty({ message: '充值类型不能为空' })
  @IsEnum(RechargeType, { message: '充值类型不正确' })
  rechargeType: RechargeType

  @ApiProperty({
    description: '充值金额',
    example: 10000,
    minimum: 1
  })
  @IsNotEmpty({ message: '充值金额不能为空' })
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Min(1, { message: '充值金额必须大于0' })
  @Transform(({ value }) => parseInt(value))
  virtualCoinAmount: number

  @ApiProperty({
    description: '充值金额',
    example: 10000,
    minimum: 1
  })
  @IsNotEmpty({ message: '充值金额不能为空' })
  @IsNumber({}, { message: '充值金额必须是数字' })
  @Transform(({ value }) => parseInt(value))
  paymentAmount: number

  @ApiPropertyOptional({
    description: '备注信息',
    example: '应用充值'
  })
  @IsOptional()
  @IsString({ message: '备注信息必须是字符串' })
  remark?: string
}

/**
 * 更新充值状态请求DTO
 */
export class UpdateRechargeStatusRequestDto {
  @ApiPropertyOptional({
    description: '备注信息',
    example: '充值成功'
  })
  @IsOptional()
  @IsString({ message: '备注信息必须是字符串' })
  remark?: string
}

/**
 * 查询充值记录请求DTO
 */
export class GetRechargeListRequestDto {
  @ApiPropertyOptional({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsMongoId({ message: '应用ID格式不正确' })
  applicationId?: string

  @ApiPropertyOptional({
    description: '充值类型',
    enum: RechargeType
  })
  @IsOptional()
  @IsEnum(RechargeType, { message: '充值类型不正确' })
  rechargeType?: RechargeType

  @ApiPropertyOptional({
    description: '充值状态',
    enum: RechargeStatus
  })
  @IsOptional()
  @IsEnum(RechargeStatus, { message: '充值状态不正确' })
  rechargeStatus?: RechargeStatus

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Transform(({ value }) => parseInt(value) || 10)
  size?: number = 10
}

/**
 * 充值记录响应DTO
 */
export class RechargeRecordResponseDto {
  @ApiProperty({
    description: '充值记录ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiProperty({
    description: '应用名称',
    example: '测试应用'
  })
  applicationName: string

  @ApiProperty({
    description: '开放平台用户phone',
    example: '***********'
  })
  phone: string

  @ApiProperty({
    description: '开放平台用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  openPlatformUserId: string

  @ApiProperty({
    description: '充值订单号',
    example: 'RC202312010001'
  })
  rechargeOrderNo: string

  @ApiProperty({
    description: '充值类型',
    example: RechargeType.BANK_TRANSFER
  })
  rechargeType: string

  @ApiProperty({
    description: '充值金额（单位：分）',
    example: 10000
  })
  rechargeAmount: number

  @ApiProperty({
    description: '虚拟币金额（单位：分）',
    example: 10000
  })
  virtualCoinAmount: number

  @ApiProperty({
    description: '实际到账金额（单位：分）',
    example: 10000
  })
  paymentAmount: number

  @ApiProperty({
    description: '充值状态',
    example: RechargeStatus.SUCCESS
  })
  rechargeStatus: string

  @ApiPropertyOptional({
    description: '充值时间',
    example: *************
  })
  rechargeTime?: number

  @ApiPropertyOptional({
    description: '备注信息',
    example: '充值成功'
  })
  remark?: string

  @ApiPropertyOptional({
    description: '操作员ID',
    example: '507f1f77bcf86cd799439011'
  })
  operatorId?: string

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 充值记录列表响应DTO
 */
export class RechargeListResponseDto {
  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 10
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 10
  })
  totalPage: number

  @ApiProperty({
    description: '充值记录列表',
    type: [RechargeRecordResponseDto]
  })
  data: RechargeRecordResponseDto[]
}


export class RechargeListDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: RechargeListResponseDto
  })
  data: RechargeListResponseDto
}


/**
 * 应用余额响应DTO
 */
export class ApplicationBalanceResponseDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  applicationId: string

  @ApiProperty({
    description: '总余额（单位：分）',
    example: 100000
  })
  totalBalance: number

  @ApiProperty({
    description: '冻结余额（单位：分）',
    example: 0
  })
  frozenBalance: number

  @ApiProperty({
    description: '可用余额（单位：分）',
    example: 100000
  })
  availableBalance: number

  @ApiProperty({
    description: '累计充值金额（单位：分）',
    example: 200000
  })
  totalRecharge: number

  @ApiProperty({
    description: '累计消费金额（单位：分）',
    example: 100000
  })
  totalConsumption: number

  @ApiPropertyOptional({
    description: '最后充值时间',
    example: *************
  })
  lastRechargeTime?: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}
