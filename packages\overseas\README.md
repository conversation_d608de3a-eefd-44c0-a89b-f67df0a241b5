# 海外平台连接器 (Overseas Platform Connector)

## 📋 目录

- [概述](#概述)
- [核心架构](#核心架构)
- [快速开始](#快速开始)
- [平台集成](#平台集成)
- [API调用](#api调用)
- [Webhook处理](#webhook处理)
- [异常处理](#异常处理)
- [使用指南](#使用指南)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)
- [API参考](#api参考)

## 概述

### 🎯 项目目标

海外平台连接器是一个统一的海外社交媒体平台集成解决方案，主要职责包括：

1. **统一接口封装**: 抹平不同平台的API差异，提供统一的调用接口
2. **Webhook消息解析**: 统一处理各平台的Webhook消息，转换为标准数据结构
3. **统一异常处理**: 标准化的错误处理、重试机制和监控集成

### 🚀 核心优势

- **平台差异抹平**: 统一的接口设计，屏蔽底层平台差异
- **开发效率提升**: 简化海外平台集成的复杂度
- **可扩展架构**: 易于添加新的海外平台支持
- **生产就绪**: 完整的错误处理、监控和日志记录

### 📊 支持平台

| 平台 | API调用 | Webhook处理 | 账号授权 | 内容发布 |
|------|---------|-------------|----------|----------|
| TikTok | ✅ 完整支持 | 🔄 开发中 | ✅ 完整支持 | ✅ 完整支持 |
| Facebook | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 |
| Instagram | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 |
| Twitter | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 |
| YouTube | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 | 🔄 计划中 |

### 🏗️ 核心功能模块

1. **API调用模块**: 统一的平台API调用接口
2. **Webhook处理模块**: 平台消息解析和标准化
3. **账号授权模块**: OAuth流程和凭证管理
4. **内容发布模块**: 跨平台内容发布能力
5. **异常处理模块**: 统一的错误处理和重试机制

## 核心架构

### 🏗️ 系统架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                    海外平台连接器 (Overseas Connector)            │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │  API调用    │  │ Webhook处理 │  │  账号授权   │  │  内容发布   │ │
│  │             │  │             │  │             │  │             │ │
│  │ • 统一接口  │  │ • 消息解析  │  │ • OAuth流程 │  │ • 跨平台发布│ │
│  │ • 参数转换  │  │ • 数据标准化│  │ • 凭证管理  │  │ • 状态同步  │ │
│  │ • 响应处理  │  │ • 事件分发  │  │ • 权限验证  │  │ • 结果回调  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        统一异常处理层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │  错误转换   │  │  重试机制   │  │  监控集成   │  │  日志记录   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        平台适配层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │
│  │   TikTok    │  │  Facebook   │  │ Instagram   │  │   Twitter   │ │
│  │             │  │             │  │             │  │             │ │
│  │ • API封装   │  │ • API封装   │  │ • API封装   │  │ • API封装   │ │
│  │ • 错误处理  │  │ • 错误处理  │  │ • 错误处理  │  │ • 错误处理  │ │
│  │ • 类型定义  │  │ • 类型定义  │  │ • 类型定义  │  │ • 类型定义  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 📁 项目结构

```
packages/overseas/src/
├── utils/                           # 核心工具
│   ├── axios-config.ts              # 统一axios配置和拦截器
│   └── error-handler.ts             # 异常处理核心逻辑
├── providers/                       # 平台提供者
│   ├── types.ts                     # 通用类型定义
│   ├── utils.ts                     # 通用工具函数
│   ├── account-auth.provider.ts     # 账号授权提供者基类
│   ├── content-publish.provider.ts  # 内容发布提供者基类
│   └── tiktok/                      # TikTok平台实现
│       ├── tiktok-api.ts            # TikTok API调用封装
│       ├── tiktok-api-types.ts      # TikTok API类型定义
│       ├── tiktok-error-handler.ts  # TikTok错误处理器
│       ├── tiktok-account-auth.provider.ts    # TikTok账号授权
│       ├── tiktok-content-publish.provider.ts # TikTok内容发布
│       └── providers.ts             # TikTok提供者导出
├── models/                          # 数据模型
├── constants.ts                     # 常量定义
├── index.ts                         # 主入口文件
├── overseas-provider.factory.ts     # 提供者工厂
└── overseas.module.ts               # NestJS模块
```

### 🔧 核心组件

#### 1. 统一拦截器 (`axios-config.ts`)
```typescript
interface InterceptorConfig {
  context: OverseasContext
  businessErrorChecker?: BusinessErrorChecker
  enableRetry?: boolean
  enableBusinessErrorCheck?: boolean
  retryConfig?: Partial<RetryConfig>
}
```

#### 2. 业务错误检查器接口
```typescript
interface BusinessErrorChecker {
  hasBusinessError: (response: AxiosResponse) => boolean
  handleBusinessError: (response: AxiosResponse, context: OverseasContext) => Promise<never>
}
```

#### 3. 统一错误类 (`RemoteApiError`)
```typescript
class RemoteApiError extends Error {
  platform: string
  errorCode: RemoteApiErrorCodes
  category: ErrorCategory
  severity: ErrorSeverity
  isRetryable: boolean
  localizedMessage: string
  // ... 更多属性
}
```

## 快速开始

### 🚀 基础使用

海外平台连接器提供了统一的接口来处理不同平台的集成需求。

#### 1. 平台连接器初始化

```typescript
import { OverseasModule } from '@/packages/overseas'
import { TiktokApi } from '@/packages/overseas/providers/tiktok'

// 在NestJS模块中导入
@Module({
  imports: [OverseasModule],
  providers: [TiktokApi],
})
export class AppModule {}
```

#### 2. TikTok API调用示例

```typescript
import { TiktokApi } from './providers/tiktok/tiktok-api'
import { createOverseasContext } from './providers/utils'
import { RemoteApiError } from './utils/error-handler'

// 创建上下文
const context = createOverseasContext('tiktok', {
  accountOpenId: 'your-account-id',
  teamId: 'your-team-id',
  userId: 'your-user-id',
  options: {
    credentials: {
      access_token: 'your-access-token'
    }
  }
})

// 创建API实例
const tiktokApi = new TiktokApi()

try {
  // 上传视频 - 自动错误处理和重试
  const result = await tiktokApi.uploadVideo(context, {
    video_url: 'https://example.com/video.mp4',
    caption: '测试视频',
    privacy_level: 'PUBLIC_TO_EVERYONE'
  })

  console.log('上传成功:', result.data.video_id)
} catch (error) {
  if (error instanceof RemoteApiError) {
    console.error('上传失败:', error.localizedMessage)
    console.log('错误代码:', error.errorCode)
    console.log('是否可重试:', error.isRetryable)
  }
}

// 注意：当前uploadVideo使用兼容的旧架构，但错误处理机制相同
```

#### 2. 环境配置

```bash
# 开发环境 - 启用代理
OVERSEAS_USE_PROXY=true
OVERSEAS_PROXY_URL=http://127.0.0.1:7897
OVERSEAS_REQUEST_TIMEOUT=30000

# 生产环境 - 直连
OVERSEAS_USE_PROXY=false
OVERSEAS_REQUEST_TIMEOUT=30000

# TikTok配置
TIKTOK_CLIENT_KEY=your_client_key
TIKTOK_CLIENT_SECRET=your_client_secret
```

### ⚡ 立即修复生产问题

如果遇到 `ReferenceError: safeApiCall is not defined` 错误：

1. **检查导入**: 确保 `tiktok-api.ts` 包含正确的导入
2. **重启服务**: 部署修复后重启海外服务
3. **验证功能**: 尝试发布测试内容验证修复效果

### 🔄 当前迁移状态

**TikTok平台迁移进度**:
- ✅ `oauth2_token` - 已使用新的统一拦截器架构
- ✅ `getAccountOverview` - 已使用新的统一拦截器架构
- 🔄 `uploadVideo` - 使用兼容的旧架构（`safeApiCall`）
- 🔄 `getVideoInfo` - 使用兼容的旧架构（`safeApiCall`）
- 🔄 `deleteVideo` - 使用兼容的旧架构（`safeApiCall`）

**兼容性说明**: 旧架构方法已修复导入问题，可正常使用。新旧架构可以共存，逐步迁移不影响生产环境。

## 平台集成

### 🔌 平台连接器架构

每个平台都有独立的连接器实现，包含以下核心组件：

#### 1. API调用层 (`*-api.ts`)
```typescript
// 平台API封装示例
export class TiktokApi {
  // OAuth授权
  async oauth2_token(context: OverseasContext, params: TikTokOAuth2Params)

  // 账号信息
  async getAccountOverview(context: OverseasContext, accessToken: string, openId: string)

  // 内容操作
  async uploadVideo(context: OverseasContext, params: TikTokVideoUploadParams)
  async getVideoInfo(context: OverseasContext, videoId: string)
  async deleteVideo(context: OverseasContext, videoId: string)
}
```

#### 2. 账号授权提供者 (`*-account-auth.provider.ts`)
```typescript
// 统一的账号授权接口
export class TikTokAccountAuthProvider implements AccountAuthProvider {
  async authorize(params: AuthorizeParams): Promise<AuthorizeResult>
  async refreshToken(context: OverseasContext): Promise<RefreshTokenResult>
  async revokeAccess(context: OverseasContext): Promise<void>
}
```

#### 3. 内容发布提供者 (`*-content-publish.provider.ts`)
```typescript
// 统一的内容发布接口
export class TikTokContentPublishProvider implements ContentPublishProvider {
  async publish(task: PublishTask): Promise<PublishResult>
  async getPublishStatus(taskId: string): Promise<PublishStatus>
  async cancelPublish(taskId: string): Promise<void>
}
```

#### 4. 错误处理器 (`*-error-handler.ts`)
```typescript
// 平台特定的错误处理
export class TikTokBusinessErrorChecker implements BusinessErrorChecker {
  hasBusinessError(response: AxiosResponse): boolean
  handleBusinessError(response: AxiosResponse, context: OverseasContext): Promise<never>
}
```

### 🌐 添加新平台支持

添加新平台只需要实现以下接口：

```typescript
// 1. 创建平台目录
mkdir src/providers/facebook

// 2. 实现核心组件
- facebook-api.ts              # API调用封装
- facebook-api-types.ts        # 类型定义
- facebook-error-handler.ts    # 错误处理
- facebook-account-auth.provider.ts    # 账号授权
- facebook-content-publish.provider.ts # 内容发布
- providers.ts                 # 导出文件

// 3. 注册到工厂
// overseas-provider.factory.ts
case 'facebook':
  return new FacebookContentPublishProvider()
```

## API调用

### 📡 统一API调用接口

海外平台连接器提供了统一的API调用方式，屏蔽了不同平台的差异：

#### 1. 上下文管理
```typescript
// 创建平台上下文
const context = createOverseasContext('tiktok', {
  accountOpenId: 'account-id',
  teamId: 'team-id',
  userId: 'user-id',
  options: {
    credentials: {
      access_token: 'access-token',
      refresh_token: 'refresh-token'
    }
  }
})
```

#### 2. API调用模式
```typescript
// 统一的调用模式
try {
  const result = await platformApi.someMethod(context, params)
  // 处理成功结果
} catch (error) {
  if (error instanceof RemoteApiError) {
    // 统一的错误处理
    console.error(`${error.platform} API调用失败:`, error.localizedMessage)
  }
}
```

#### 3. 批量操作支持
```typescript
import { batchApiCalls } from './providers/utils'

// 批量API调用
const tasks = accounts.map(account =>
  () => tiktokApi.getAccountOverview(account.context, account.token, account.openId)
)

const results = await batchApiCalls(tasks, 3) // 并发度为3
```

## Webhook处理

### 🔔 Webhook消息处理架构

海外平台连接器提供统一的Webhook消息处理能力：

#### 1. 消息解析器
```typescript
// 平台特定的Webhook解析器
export class TikTokWebhookParser implements WebhookParser {
  parseMessage(rawPayload: any): StandardWebhookMessage {
    // 将TikTok原始消息转换为标准格式
    return {
      platform: 'tiktok',
      eventType: this.mapEventType(rawPayload.event),
      accountId: rawPayload.account_id,
      data: this.transformData(rawPayload),
      timestamp: new Date(rawPayload.timestamp)
    }
  }
}
```

#### 2. 事件分发器
```typescript
// 统一的事件分发
export class WebhookEventDispatcher {
  async dispatch(message: StandardWebhookMessage): Promise<void> {
    switch (message.eventType) {
      case 'video.published':
        await this.handleVideoPublished(message)
        break
      case 'account.updated':
        await this.handleAccountUpdated(message)
        break
      // ... 其他事件类型
    }
  }
}
```

#### 3. 标准化数据结构
```typescript
// 统一的Webhook消息格式
interface StandardWebhookMessage {
  platform: string           // 平台标识
  eventType: string          // 事件类型
  accountId: string          // 账号ID
  data: Record<string, any>  // 事件数据
  timestamp: Date            // 事件时间
  signature?: string         // 签名验证
}
```

## 异常处理

### 🛡️ 统一异常处理机制

异常处理是海外平台连接器的核心功能之一，提供：

#### 1. 错误分类和转换
```typescript
// 统一的错误类型
enum RemoteApiErrorCodes {
  AccessTokenInvalid = 'ACCESS_TOKEN_INVALID',
  AccessTokenExpired = 'ACCESS_TOKEN_EXPIRED',
  RateLimitExceeded = 'RATE_LIMIT_EXCEEDED',
  ContentViolation = 'CONTENT_VIOLATION',
  // ... 更多错误类型
}

// 平台错误到标准错误的映射
// TikTok: code 40001 -> SCOPE_NOT_AUTHORIZED
// Facebook: error.code 190 -> ACCESS_TOKEN_INVALID
// Twitter: error.code 88 -> RATE_LIMIT_EXCEEDED
```

#### 2. 智能重试机制
```typescript
// 自动重试配置
interface RetryConfig {
  maxRetries: number        // 最大重试次数
  baseDelay: number        // 基础延迟时间
  maxDelay: number         // 最大延迟时间
  backoffMultiplier: number // 退避倍数
}

// 指数退避算法
const delay = Math.min(
  baseDelay * Math.pow(backoffMultiplier, retryCount),
  maxDelay
)
```

#### 3. 错误监控和日志
```typescript
// 结构化错误日志
const errorInfo = {
  platform: error.platform,
  errorCode: error.errorCode,
  category: error.category,
  severity: error.severity,
  isRetryable: error.isRetryable,
  context: context,
  timestamp: new Date()
}
```

## 详细实现

### 🔍 TikTok错误处理实现

#### 错误码映射表

| TikTok错误码 | 标准错误类型 | 描述 | 可重试 |
|-------------|-------------|------|--------|
| 40001 | `SCOPE_NOT_AUTHORIZED` | 权限范围不足 | ❌ |
| 40102-40105 | `ACCESS_TOKEN_INVALID` | 访问令牌无效 | ❌ |
| 40103 | `ACCESS_TOKEN_EXPIRED` | 访问令牌过期 | ❌ |
| 40000,40002,40006,40010,40011,40051,40053 | `REQUEST_PARAMETERS_INCORRECT` | 请求参数错误 | ❌ |
| 50001,50002 | `SERVER_ERROR` | 服务器错误 | ✅ |
| 60001 | `RATE_LIMIT_EXCEEDED` | 限流 | ✅ |
| 60002 | `QUOTA_EXCEEDED` | 配额超限 | ❌ |
| 70001 | `CONTENT_VIOLATION` | 内容违规 | ❌ |
| 70002 | `MEDIA_UPLOAD_FAILED` | 媒体上传失败 | ✅ |
| 80001 | `ACCOUNT_SUSPENDED` | 账号被暂停 | ❌ |
| 90001 | `FEATURE_NOT_AVAILABLE` | 功能不可用 | ❌ |

#### 实现代码

```typescript
export class TikTokBusinessErrorChecker implements BusinessErrorChecker {
  hasBusinessError(response: AxiosResponse<TikTokApiResponse>): boolean {
    return response.data && response.data.code !== 0
  }

  async handleBusinessError(response: AxiosResponse<TikTokApiResponse>, context: OverseasContext): Promise<never> {
    const data = response.data
    let errorCode: RemoteApiErrorCodes

    // 根据TikTok错误码映射到标准错误类型
    switch (data.code) {
      case 40001:
        errorCode = RemoteApiErrorCodes.ScopeNotAuthorized
        break
      case 40102:
      case 40104:
      case 40105:
        errorCode = RemoteApiErrorCodes.AccessTokenInvalid
        break
      // ... 更多映射
      default:
        errorCode = RemoteApiErrorCodes.UnknownRemoteApiError
    }

    throw new RemoteApiError(
      context.platform,
      errorCode,
      { 
        message: data.message,
        tiktokErrorCode: data.code,
        logId: data.request_id
      },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )
  }
}
```

### 🔄 重试机制实现

#### 配置选项

```typescript
interface RetryConfig {
  maxRetries: number        // 最大重试次数，默认3次
  baseDelay: number        // 基础延迟时间，默认1000ms
  maxDelay: number         // 最大延迟时间，默认30000ms
  backoffMultiplier: number // 退避倍数，默认2
}
```

#### 重试算法

```typescript
// 指数退避算法
const delay = Math.min(
  baseDelay * Math.pow(backoffMultiplier, retryCount),
  maxDelay
)

// 重试条件判断
const shouldRetry = (
  error instanceof RemoteApiError && 
  error.isRetryable && 
  retryCount < maxRetries
)
```

### 🌐 代理配置

海外平台连接器支持通过HTTP代理访问各个海外平台的API：

```bash
# 环境变量配置
OVERSEAS_USE_PROXY=true                    # 启用代理
OVERSEAS_PROXY_URL=http://127.0.0.1:7897   # 代理服务器地址
OVERSEAS_REQUEST_TIMEOUT=30000             # 请求超时时间
```

```typescript
// 代理配置实现
export function getProxyConfig(): ProxyConfig {
  const enabled = process.env.OVERSEAS_USE_PROXY === 'true'

  return {
    enabled,
    url: process.env.OVERSEAS_PROXY_URL || 'http://127.0.0.1:7897',
    timeout: parseInt(process.env.OVERSEAS_REQUEST_TIMEOUT || '30000', 10)
  }
}
```

**📖 详细配置指南**: 请参考 [代理配置指南](./PROXY_CONFIG_GUIDE.md) 获取完整的配置说明、故障排除方法和性能优化建议。

**🛡️ 异常处理详情**: 请参考 [异常处理指南](./ERROR_HANDLING_GUIDE.md) 获取详细的异常处理机制说明和最佳实践。

## 使用指南

### 📝 完整API调用示例

#### OAuth授权流程

```typescript
const tiktokApi = new TiktokApi()

// 1. OAuth授权
const tokenResponse = await tiktokApi.oauth2_token(context, {
  auth_code: 'authorization_code_from_callback',
  redirect_uri: 'your_redirect_uri'
})

// 2. 更新上下文中的访问令牌
context.options.credentials = {
  access_token: tokenResponse.data.access_token,
  refresh_token: tokenResponse.data.refresh_token
}

// 3. 获取账号信息
const accountInfo = await tiktokApi.getAccountOverview(
  context,
  tokenResponse.data.access_token,
  tokenResponse.data.open_id
)

console.log('账号信息:', accountInfo.data)
```

#### 内容发布流程

```typescript
// 1. 上传视频
const uploadResult = await tiktokApi.uploadVideo(context, {
  video_url: 'https://your-cdn.com/video.mp4',
  caption: '这是一个测试视频 #测试 #API',
  privacy_level: 'PUBLIC_TO_EVERYONE',
  disable_comment: false,
  disable_duet: false,
  disable_stitch: false,
  is_ai_generated: false,
  upload_to_draft: false
})

const videoId = uploadResult.data.video_id
console.log('视频上传成功:', videoId)

// 2. 查询视频状态
const videoInfo = await tiktokApi.getVideoInfo(context, videoId)
console.log('视频信息:', videoInfo.data)

// 3. 如需删除视频
// await tiktokApi.deleteVideo(context, videoId)
```

### 🔧 自定义错误处理

```typescript
import { createOverseasAxiosInstance, InterceptorConfig } from '../utils/axios-config'

// 创建自定义业务错误检查器
const customErrorChecker: BusinessErrorChecker = {
  hasBusinessError: (response) => {
    // 自定义错误检查逻辑
    return response.data.status === 'error'
  },
  
  handleBusinessError: async (response, context) => {
    // 自定义错误处理逻辑
    throw new RemoteApiError(
      context.platform,
      RemoteApiErrorCodes.UnknownRemoteApiError,
      { customError: response.data.error },
      extractRequestInfo(response),
      extractResponseInfo(response),
      context
    )
  }
}

// 使用自定义错误检查器
const interceptorConfig: InterceptorConfig = {
  context,
  businessErrorChecker: customErrorChecker,
  enableRetry: true,
  retryConfig: {
    maxRetries: 5,
    baseDelay: 2000
  }
}

const customAxios = createOverseasAxiosInstance(
  'https://api.example.com',
  interceptorConfig
)
```

## 故障排除

### 🚨 常见问题

#### 1. `ReferenceError: safeApiCall is not defined`

**原因**: 缺少必要的导入语句

**解决方案**:
```typescript
// 在 tiktok-api.ts 文件顶部添加
import { safeApiCall, createPlatformSpecificAxiosInstance, checkBusinessError } from '../utils'
```

#### 2. 代理连接失败

**症状**: 请求超时或连接被拒绝

**解决方案**:
```bash
# 检查代理配置
echo $OVERSEAS_USE_PROXY
echo $OVERSEAS_PROXY_URL

# 验证代理服务是否运行
curl -x http://127.0.0.1:7897 https://www.google.com
```

#### 3. TikTok API返回40001错误

**原因**: 权限范围不足

**解决方案**:
1. 检查应用权限配置
2. 重新进行OAuth授权
3. 确认账号类型支持相关功能

#### 4. 重试次数过多

**症状**: 请求耗时过长

**解决方案**:
```typescript
// 调整重试配置
const retryConfig = {
  maxRetries: 2,        // 减少重试次数
  baseDelay: 500,       // 减少基础延迟
  maxDelay: 10000       // 减少最大延迟
}
```

### 🔍 调试技巧

#### 1. 启用详细日志

```typescript
// 在开发环境中启用详细日志
console.log('请求上下文:', context)
console.log('API响应:', response.data)
console.log('错误详情:', error.getFullErrorInfo())
```

#### 2. 监控重试行为

```typescript
// 监听重试事件
axios.interceptors.response.use(
  response => response,
  async error => {
    const retryCount = error.config.__retryCount || 0
    console.log(`重试第${retryCount + 1}次:`, error.message)
    // ... 重试逻辑
  }
)
```

#### 3. 验证网络连接

```bash
# 测试直连
curl -v https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/

# 测试代理连接
curl -v -x http://127.0.0.1:7897 https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/
```

## 最佳实践

### 💡 开发建议

#### 1. 错误处理策略

```typescript
// ✅ 推荐：细粒度错误处理
try {
  const result = await tiktokApi.uploadVideo(context, params)
  return { success: true, data: result.data }
} catch (error) {
  if (error instanceof RemoteApiError) {
    switch (error.errorCode) {
      case RemoteApiErrorCodes.AccessTokenExpired:
        // 刷新令牌后重试
        return await refreshTokenAndRetry()
      case RemoteApiErrorCodes.RateLimitExceeded:
        // 延迟后重试
        return await delayAndRetry()
      case RemoteApiErrorCodes.ContentViolation:
        // 内容违规，不重试
        return { success: false, reason: 'content_violation' }
      default:
        // 其他错误
        return { success: false, error: error.localizedMessage }
    }
  }
  throw error // 重新抛出非RemoteApiError
}
```

#### 2. 上下文管理

```typescript
// ✅ 推荐：集中管理上下文
class OverseasContextManager {
  private contexts = new Map<string, OverseasContext>()
  
  createContext(accountId: string, platform: string, credentials: any): OverseasContext {
    const context = createOverseasContext(platform, {
      accountOpenId: accountId,
      options: { credentials }
    })
    this.contexts.set(accountId, context)
    return context
  }
  
  updateCredentials(accountId: string, credentials: any) {
    const context = this.contexts.get(accountId)
    if (context) {
      context.options.credentials = credentials
    }
  }
}
```

#### 3. 批量操作

```typescript
// ✅ 推荐：使用批量API调用
import { batchApiCalls } from './providers/utils'

const uploadTasks = videos.map(video => 
  () => tiktokApi.uploadVideo(context, video)
)

const results = await batchApiCalls(uploadTasks, 3) // 并发度为3

results.forEach((result, index) => {
  if (result.success) {
    console.log(`视频${index + 1}上传成功:`, result.data)
  } else {
    console.error(`视频${index + 1}上传失败:`, result.error.message)
  }
})
```

### 🔒 安全建议

#### 1. 凭证管理

```typescript
// ✅ 推荐：安全的凭证存储
class SecureCredentialStore {
  private encrypt(data: string): string {
    // 使用加密算法加密敏感数据
    return encryptedData
  }
  
  private decrypt(encryptedData: string): string {
    // 解密数据
    return decryptedData
  }
  
  storeCredentials(accountId: string, credentials: any) {
    const encrypted = this.encrypt(JSON.stringify(credentials))
    // 存储到安全位置
  }
}
```

#### 2. 请求限流

```typescript
// ✅ 推荐：客户端限流
class RateLimiter {
  private requests = new Map<string, number[]>()
  
  async checkLimit(platform: string, limit: number, window: number): Promise<boolean> {
    const now = Date.now()
    const requests = this.requests.get(platform) || []
    
    // 清理过期请求
    const validRequests = requests.filter(time => now - time < window)
    
    if (validRequests.length >= limit) {
      return false // 超出限制
    }
    
    validRequests.push(now)
    this.requests.set(platform, validRequests)
    return true
  }
}
```

### 📊 监控和日志

#### 1. 结构化日志

```typescript
// ✅ 推荐：结构化错误日志
const logger = {
  logApiError: (error: RemoteApiError, context: OverseasContext) => {
    console.error('API调用失败', {
      timestamp: new Date().toISOString(),
      platform: error.platform,
      errorCode: error.errorCode,
      category: error.category,
      severity: error.severity,
      isRetryable: error.isRetryable,
      accountId: context.accountOpenId,
      teamId: context.teamId,
      userId: context.userId,
      requestInfo: error.requestInfo,
      responseInfo: error.responseInfo
    })
  }
}
```

#### 2. 性能监控

```typescript
// ✅ 推荐：API性能监控
class ApiMetrics {
  private metrics = new Map<string, { count: number, totalTime: number, errors: number }>()
  
  recordApiCall(platform: string, duration: number, success: boolean) {
    const key = platform
    const current = this.metrics.get(key) || { count: 0, totalTime: 0, errors: 0 }
    
    current.count++
    current.totalTime += duration
    if (!success) current.errors++
    
    this.metrics.set(key, current)
  }
  
  getMetrics(platform: string) {
    const metrics = this.metrics.get(platform)
    if (!metrics) return null
    
    return {
      totalCalls: metrics.count,
      averageTime: metrics.totalTime / metrics.count,
      errorRate: metrics.errors / metrics.count,
      successRate: (metrics.count - metrics.errors) / metrics.count
    }
  }
}
```

## API参考

### 🔧 核心接口

#### `createOverseasAxiosInstance`

创建配置了统一拦截器的axios实例。

```typescript
function createOverseasAxiosInstance(
  baseURL: string,
  interceptorConfig: InterceptorConfig,
  options?: AxiosRequestConfig
): AxiosInstance
```

**参数**:
- `baseURL`: API基础URL
- `interceptorConfig`: 拦截器配置
- `options`: 额外的axios配置选项

**返回**: 配置好的axios实例

#### `createOverseasContext`

创建海外平台上下文对象。

```typescript
function createOverseasContext(
  platform: string,
  options?: Partial<Omit<OverseasContext, 'platform'>>
): OverseasContext
```

**参数**:
- `platform`: 平台名称 ('tiktok', 'facebook', 'instagram', 'twitter', 'youtube')
- `options`: 上下文选项

**返回**: 海外平台上下文对象

#### `RemoteApiError`

统一的API错误类。

```typescript
class RemoteApiError extends Error {
  constructor(
    platform: string,
    errorCode: RemoteApiErrorCodes,
    details: Record<string, any>,
    requestInfo: RequestInfo,
    responseInfo: ResponseInfo,
    context: OverseasContext,
    customMessage?: string
  )
  
  // 属性
  platform: string
  errorCode: RemoteApiErrorCodes
  category: ErrorCategory
  severity: ErrorSeverity
  isRetryable: boolean
  localizedMessage: string
  
  // 方法
  getFullErrorInfo(): FullErrorInfo
}
```

### 📚 类型定义

#### `InterceptorConfig`

```typescript
interface InterceptorConfig {
  context: OverseasContext
  retryConfig?: Partial<RetryConfig>
  businessErrorChecker?: BusinessErrorChecker
  enableRetry?: boolean
  enableBusinessErrorCheck?: boolean
}
```

#### `OverseasContext`

```typescript
interface OverseasContext {
  platform: string
  accountOpenId?: string
  teamId?: string
  userId?: string
  options?: Record<string, any>
}
```

#### `RetryConfig`

```typescript
interface RetryConfig {
  maxRetries: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
}
```

#### `BusinessErrorChecker`

```typescript
interface BusinessErrorChecker {
  hasBusinessError: (response: AxiosResponse) => boolean
  handleBusinessError: (response: AxiosResponse, context: OverseasContext) => Promise<never>
}
```

---

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请：

1. 查阅本文档的[故障排除](#故障排除)部分
2. 检查相关的错误日志和调试信息
3. 联系开发团队获取技术支持

**文档版本**: v1.0.0  
**最后更新**: 2025年1月  
**维护团队**: 海外平台开发组
