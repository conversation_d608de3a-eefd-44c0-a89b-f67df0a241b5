import { ApiProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class captchaVerifyRequestDTO {
  @ApiProperty({
    type: String,
    description: '系统类型, windows-x86、windows-x64、macos-x64、macos-arm64、android',
    example: 'windows-x86',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '类型不能为空' })
  type: string

  @ApiProperty({
    type: Boolean,
    description: '是否是渠道下载默认是false',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isChannel: boolean = false

  @ApiProperty({
    type: String,
    description: '验证参数',
    example: '123123213',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '验证参数不能为空' })
  captchaVerifyParam: string
}

export class OssResourceUrlResponse {
  @ApiProperty({
    type: String,
    description: '资源访问地址',
    example:
      'https://yixiaoer-lite-asserts.oss-cn-shanghai.aliyuncs.com/local/t-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v?OSSAccessKeyId=LTAI5tGLVT13SpqTufgPaLaz&Expires=1723696250&Signature=zWGnG9bkqfTMAHD5VDgTzpPthEA%3D',
    required: true
  })
  downloadUrl: string

  @ApiProperty({
    type: String,
    description: '验证结果',
    example: false
  })
  captchaVerifyResult: boolean
}

export class OssResourceUrlResponseDTO extends BaseResponseDTO {
  @ApiProperty({
    type: OssResourceUrlResponse,
    description: '资源访问地址',
    required: true
  })
  data: OssResourceUrlResponse
}
