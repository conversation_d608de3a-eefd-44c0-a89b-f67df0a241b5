import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { OpenPlatformPermissions, OpenPlatformStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_app_authorizations',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformAppAuthorizationEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  channelUserId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  authorizedBy: Types.ObjectId

  @Prop({
    type: [String],
    enum: OpenPlatformPermissions.All,
    required: true,
    default: [OpenPlatformPermissions.VIEW_APPLICATION, OpenPlatformPermissions.GENERATE_TOKEN]
  })
  permissions: string[]

  @Prop({
    type: Number,
    enum: OpenPlatformStatus,
    required: true,
    default: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

// 创建复合索引确保同一应用对同一渠道商只能有一个授权记录
const schema = SchemaFactory.createForClass(OpenPlatformAppAuthorizationEntity);
schema.index(
  { applicationId: 1, channelUserId: 1 }, 
  { unique: true }
);

export const OpenPlatformAppAuthorizationSchema: ModelDefinition = {
  name: OpenPlatformAppAuthorizationEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformAppAuthorizationEntity)
}

export const OpenPlatformAppAuthorizationMongoose = MongooseModule.forFeature([OpenPlatformAppAuthorizationSchema])
