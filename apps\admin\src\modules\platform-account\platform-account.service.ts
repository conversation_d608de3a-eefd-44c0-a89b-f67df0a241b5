import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountEntity, PlatformAccountOverviewEntity, TeamEntity } from '@yxr/mongo'
import { PlatformAccountListRequest, PlatformAccountListResponse } from './platform-account.dto'
import { Injectable } from '@nestjs/common'

@Injectable()
export class PlatformAccountService {
  constructor(
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformAccountOverviewEntity.name)
    private platformAccountOverviewModel: Model<PlatformAccountOverviewEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>
  ) {}

  /**
   * 获取媒体账号列表
   * @param filter
   */
  async getPlatformAccountListAsync(
    filter: PlatformAccountListRequest
  ): Promise<PlatformAccountListResponse> {
    const re = {
      stateCode: 0,
      page: filter.page,
      size: filter.size,
      totalSize: 0,
      totalPage: 0,
      data: []
    }
    const page = filter.page
    const size = filter.size
    const where: any = {
      $match: {}
    }

    const teamWhere: any = {
      $match: {}
    }

    //根据团队查
    let teamIds = []
    if (filter.teamName) {
      teamWhere.$match.$or = [
        { name: { $regex: filter.teamName, $options: 'i' } },
        { code: { $regex: filter.teamName, $options: 'i' } }
      ]
      const teamIdsPre = await this.teamModel.aggregate([
        teamWhere,
        {
          $project: {
            _id: 1
          }
        }
      ])
      if (teamIdsPre?.length > 0) {
        teamIds = teamIdsPre.map((item) => item._id)
        if (teamIds?.length > 0) {
          where.$match.teamId = { $in: teamIds }
        }
      } else {
        return re
      }
    }

    //根据媒体号名称/备注名称模糊匹配
    if (filter.platformAccountName) {
      where.$match.$or = [
        { platformAccountName: { $regex: filter.platformAccountName, $options: 'i' } },
        { remark: { $regex: filter.platformAccountName, $options: 'i' } }
      ]
    }

    //根据平台名称
    if (filter.platformName) {
      where.$match.platformName = filter.platformName
    }

    if (filter.createStartTime && filter.createEndTime) {
      where.$match.createdAt = {
        $gt: new Date(Number(filter.createStartTime)),
        $lte: new Date(Number(filter.createEndTime))
      }
    }

    if (filter.loginStatus > 0) {
      where.$match.status = filter.loginStatus
    } else {
      where.$match.status = {
        $in: [1, 2, 4]
      }
    }

    const currentTimestamp = Date.now()
    const dayTimeMill = 1000 * 60 * 60 * 24
    // 提取 $match 中的内容作为查询条件
    const countQuery = { ...where.$match }

    // 确保 teamId 是 ObjectId 类型
    if (countQuery.teamId && countQuery.teamId.$in) {
      countQuery.teamId.$in = countQuery.teamId.$in.map((id) => new Types.ObjectId(id))
    }
    const totalCount = await this.platformAccountModel.countDocuments(countQuery)

    const result = await this.platformAccountModel.aggregate([
      where,
      { $sort: { createdAt: -1 } },
      { $skip: (filter.page - 1) * filter.size },
      { $limit: filter.size },
      {
        $lookup: {
          from: 'teamentities',
          localField: 'teamId',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                _id: 0,
                id: '$_id',
                name: 1,
                code: 1
              }
            }
          ],
          as: 'teamDetails'
        }
      },
      {
        // 展开联查结果数组
        $unwind: {
          path: '$teamDetails',
          preserveNullAndEmptyArrays: true // 保留没有匹配结果的文档
        }
      },
      {
        $facet: {
          counts: [{ $count: 'total' }],
          items: [
            {
              $project: {
                _id: 0,
                id: '$_id',
                platformAccountName: 1,
                teamCode: '$teamDetails.code',
                teamName: '$teamDetails.name',
                platformName: 1,
                createdAt: { $toLong: '$createdAt' },
                updatedAt: { $toLong: '$updatedAt' },
                loginStatusUpdatedAt: { $toLong: '$loginStatusUpdatedAt' },
                status: 1,
                cloudCheckTime: 1,
                parentId: 1,
                remark: 1
              }
            },
            {
              // 添加 days 字段
              $addFields: {
                onLineDays: {
                  $cond: {
                    if: { $eq: ['$status', 1] },
                    then: {
                      $divide: [
                        { $subtract: [currentTimestamp, { $toLong: '$loginStatusUpdatedAt' }] },
                        dayTimeMill
                      ]
                    },
                    else: 0
                  }
                }
              }
            }
          ]
        }
      }
    ])

    const platformAccountIds = result[0]?.items.map((item) => new Types.ObjectId(item.id))
    const accountOverviews = await this.platformAccountOverviewModel
      .find({
        platformAccountId: { $in: platformAccountIds }
      })
      .select('platformAccountId cloudUpdatedAt')
      .lean()

    const data = result[0]?.items.map((item: any) => {
      const overview = accountOverviews.find(
        (c) => c.platformAccountId.toString() === item.id.toString()
      )
      return {
        ...item,
        cloudUpdatedAt: overview ? overview.cloudUpdatedAt : null,
        parentId: item.parentId?.toString()
      }
    })

    const totalSize = totalCount

    return {
      stateCode: 0,
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }
}
