import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common'
import {
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { OrderService } from './order.service'
import {
  OrderDetailResponseDTO,
  OrderInterestResponseDTO,
  OrderListRequestDTO,
  orderPriceRequestDTO,
  OrderRequestCreateOrderDTO,
  OrderResponseCreateOrderDTO,
  OrderResponseOrderListDTO,
  OrderStatusRequestDTO,
  UpgradeOrderRequest,
  VipRenewDTO
} from './order.dto'
import { BaseUnauthorizedResponseDTO } from '../../common/dto/BaseResponseDTO'
import { OrderPriceDetailResponseDTO } from '../order/order.dto'
import { AdminAndOpenPlatformAccess, AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('orders')
@AdminAndOpenPlatformAccess()
@ApiTags('订单管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  @ApiOperation({ summary: '订单列表' })
  @ApiOkResponse({ type: OrderResponseOrderListDTO })
  getOrders(@Query() query: OrderListRequestDTO) {
    return this.orderService.getOrders(query)
  }

  @Post()
  @ApiOperation({ summary: '创建订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  createOrder(@Body() body: OrderRequestCreateOrderDTO) {
    return this.orderService.createOrder(body)
  }

  @Get(':orderNo')
  @ApiOperation({ summary: '订单详情' })
  @ApiOkResponse({ type: OrderDetailResponseDTO })
  getOrderDetail(@Param('orderNo') orderNo: string) {
    return this.orderService.getOrderDetail(orderNo)
  }

  @Post('upgrade')
  @ApiOperation({ summary: '升级订单' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  createUpgradeOrder(@Body() body: UpgradeOrderRequest) {
    return this.orderService.createUpgradeOrder(body)
  }

  @Post('renew')
  @ApiOperation({ summary: '续费vip' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  renewVip(@Body() body: VipRenewDTO) {
    return this.orderService.renewOrder(body)
  }

  @Patch(':orderNo/status')
  @ApiOperation({ summary: '对公转账开通' })
  @ApiOkResponse({ type: OrderResponseCreateOrderDTO })
  async putOrderStatus(@Param('orderNo') orderNo: string, @Body() body: OrderStatusRequestDTO) {
    return this.orderService.putOrderStatus(orderNo, body)
  }

  @Patch(':orderNo/cancel')
  @ApiOperation({ summary: '订单取消' })
  @ApiOkResponse({ description: '操作成功' })
  async putOrderCancel(@Param('orderNo') orderNo: string) {
    return this.orderService.putOrderCancel(orderNo)
  }

  @Get('interest')
  @ApiOperation({ summary: '获取订单VIP规格' })
  @ApiOkResponse({ type: OrderInterestResponseDTO })
  getInterest() {
    return this.orderService.getInterest()
  }

  @Post('price')
  @ApiOperation({ summary: '订单计算金额' })
  @ApiOkResponse({ type: OrderPriceDetailResponseDTO })
  calculateOrderPrice(@Body() body: orderPriceRequestDTO) {
    return this.orderService.calculateOrderPrice(body)
  }
}
