/**
 * TikTok API相关类型定义
 */

/**
 * TikTok API通用响应格式
 */
export interface TikTokApiResponse<T = any> {
  code: number
  message: string
  request_id?: string
  data?: T
}

/**
 * OAuth2令牌响应
 */
export interface TikTokOAuth2TokenResponse {
  access_token: string
  token_type: string
  scope: string
  expires_in: number
  refresh_token: string
  refresh_token_expires_in: number
  open_id: string
}

/**
 * 账号概览响应
 */
export interface TikTokAccountOverviewResponse {
  is_business_account: boolean
  profile_image: string
  username: string
  profile_deep_link: string
  display_name: string
  bio_description: string
  is_verified: boolean
  following_count: number
  followers_count: number
  total_likes: number
  videos_count: number
  metrics: TikTokAccountMetric[]
}

/**
 * 账号指标数据
 */
export interface TikTokAccountMetric {
  date: string
  video_views: number
  likes: number
  comments: number
  shares: number
  engaged_audience: number
}

/**
 * 视频上传响应
 */
export interface TikTokVideoUploadResponse {
  video_id: string
  share_url: string
  embed_link: string
  unique_id: string
  title: string
  video_description: string
  duration: number
  cover_image_url: string
  embed_html: string
  create_time: number
  is_top_video: boolean
  reach: number
  like_count: number
  comment_count: number
  share_count: number
  view_count: number
}

/**
 * 视频信息响应
 */
export interface TikTokVideoInfoResponse {
  video_id: string
  share_url: string
  embed_link: string
  unique_id: string
  title: string
  video_description: string
  duration: number
  cover_image_url: string
  create_time: number
  like_count: number
  comment_count: number
  share_count: number
  view_count: number
}

/**
 * 视频删除响应
 */
export interface TikTokVideoDeleteResponse {
  video_id: string
}

/**
 * 视频上传参数
 */
export interface TikTokVideoUploadParams {
  video_url: string
  caption: string
  is_brand_organic?: boolean
  is_branded_content?: boolean
  disable_comment?: boolean
  disable_duet?: boolean
  disable_stitch?: boolean
  thumbnail_offset?: number
  is_ai_generated?: boolean
  privacy_level: 'PUBLIC_TO_EVERYONE' | 'MUTUAL_FOLLOW_FRIENDS' | 'SELF_ONLY'
  upload_to_draft?: boolean
}

/**
 * OAuth2授权参数
 */
export interface TikTokOAuth2Params {
  auth_code: string
  redirect_uri: string
}

/**
 * TikTok API端点枚举
 */
export enum TikTokApiEndpoints {
  OAuth2Token = '/open_api/v1.3/tt_user/oauth2/token/',
  AccountOverview = '/open_api/v1.3/tt_user/info/',
  VideoUpload = '/open_api/v1.3/business/video/publish/',
  VideoList = '/open_api/v1.3/post/list/',
  VideoDelete = '/open_api/v1.3/post/delete/'
}

/**
 * TikTok隐私级别枚举
 */
export enum TikTokPrivacyLevel {
  PublicToEveryone = 'PUBLIC_TO_EVERYONE',
  MutualFollowFriends = 'MUTUAL_FOLLOW_FRIENDS',
  SelfOnly = 'SELF_ONLY'
}

/**
 * TikTok API配置
 */
export interface TikTokApiConfig {
  clientId: string
  clientSecret: string
  baseUrl: string
}

/**
 * TikTok上下文扩展
 */
export interface TikTokContext {
  platform: 'tiktok'
  accountOpenId: string
  teamId?: string
  userId?: string
  options?: {
    credentials?: {
      access_token: string
      refresh_token?: string
    }
    [key: string]: any
  }
}

/**
 * 兼容旧代码的类型定义
 * @deprecated 请使用上面的强类型定义
 */
export interface ApiCommonResponse {
  code: number
  message: string
  request_id?: string
  data?: any
}

/**
 * 视频列表查询参数
 */
export interface TikTokVideoListParams {
  fields: string[]
  cursor?: number
  max_count?: number
  video_id?: string
}

/**
 * 视频列表响应
 */
export interface TikTokVideoListResponse {
  videos: TikTokVideoInfoResponse[]
  cursor: number
  has_more: boolean
}

/**
 * 业务账号信息查询参数
 */
export interface TikTokBusinessAccountParams {
  business_id: string
  start_date?: string
  end_date?: string
  fields?: string[]
}

/**
 * 错误响应类型
 */
export interface TikTokErrorResponse {
  code: number
  message: string
  request_id?: string
  data?: null
}

/**
 * 类型守卫：检查是否为TikTok错误响应
 */
export function isTikTokErrorResponse(response: any): response is TikTokErrorResponse {
  return response && typeof response.code === 'number' && response.code !== 0
}

/**
 * 类型守卫：检查是否为TikTok成功响应
 */
export function isTikTokSuccessResponse<T>(response: any): response is TikTokApiResponse<T> {
  return response && typeof response.code === 'number' && response.code === 0
}
