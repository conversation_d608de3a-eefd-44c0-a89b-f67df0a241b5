import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { OrderEntity, TeamEntity } from '@yxr/mongo'
import { OrderStatus, OpenPlatformOrderResourceType } from '@yxr/common'

/**
 * 权益计算服务
 * 负责计算团队在新订单系统下的权益
 */
@Injectable()
export class OpenPlatformBenefitCalculationService {
  private readonly logger = new Logger(OpenPlatformBenefitCalculationService.name)

  constructor(
    @InjectModel(OrderEntity.name)
    private orderModel: Model<OrderEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>
  ) {}

  /**
   * 计算团队当前权益
   */
  async calculateTeamBenefits(teamId: string): Promise<{
    teamId: string
    teamName: string
    vipExpiredAt: Date | null
    accountPoints: {
      current: number
      activeOrders: Array<{
        orderId: string
        orderNo: string
        accountCount: number
        startTime: Date
        endTime: Date
        isActive: boolean
      }>
    }
    traffic: {
      accumulated: number // KB
      accumulatedGB: number
      validRecords: Array<{
        amount: number
        amountGB: number
        expiredAt: Date
        createdAt: Date
      }>
      expiredAmount: number
    }
    calculatedAt: Date
  }> {
    this.logger.debug(`计算团队权益: teamId=${teamId}`)

    // 获取团队信息
    const team = await this.teamModel.findById(teamId)
    if (!team) {
      throw new Error('团队不存在')
    }

    const now = new Date()

    // 计算账号点数权益
    const accountBenefits = await this.calculateAccountPointsBenefits(teamId, now)

    // 计算流量权益
    const trafficBenefits = await this.calculateTrafficBenefits(team, now)

    const result = {
      teamId,
      teamName: team.name,
      vipExpiredAt: team.expiredAt || null,
      accountPoints: accountBenefits,
      traffic: trafficBenefits,
      calculatedAt: now
    }

    this.logger.debug(
      `团队权益计算完成: teamId=${teamId}, ` +
      `账号点数=${accountBenefits.current}, ` +
      `累积流量=${trafficBenefits.accumulatedGB}GB`
    )

    return result
  }

  /**
   * 计算账号点数权益（考虑重叠订单叠加）
   */
  private async calculateAccountPointsBenefits(
    teamId: string,
    now: Date
  ): Promise<{
    current: number
    activeOrders: Array<{
      orderId: string
      orderNo: string
      accountCount: number
      startTime: Date
      endTime: Date
      isActive: boolean
    }>
  }> {
    // 查找所有账号点数订单
    const accountOrders = await this.orderModel.find({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Paid,
      resourceType: OpenPlatformOrderResourceType.AccountPoints
    }).sort({ startTime: 1 })

    let currentAccountPoints = 0
    const activeOrders = []

    for (const order of accountOrders) {
      const isActive = order.startTime <= now && order.endTime > now
      
      if (isActive) {
        currentAccountPoints += order.accountCapacity || 0
      }

      activeOrders.push({
        orderId: order._id.toString(),
        orderNo: order.orderNo,
        accountCapacity: order.accountCapacity || 0,
        startTime: order.startTime,
        endTime: order.endTime,
        isActive
      })
    }

    return {
      current: currentAccountPoints,
      activeOrders
    }
  }

  /**
   * 计算流量权益
   */
  private async calculateTrafficBenefits(
    team: any,
    now: Date
  ): Promise<{
    accumulated: number
    accumulatedGB: number
    validRecords: Array<{
      amount: number
      amountGB: number
      expiredAt: Date
      createdAt: Date
    }>
    expiredAmount: number
  }> {
    const trafficRecords = team.trafficRecords || []
    const validRecords = []
    let expiredAmount = 0

    for (const record of trafficRecords) {
      if (record.expiredAt > now) {
        // 未过期的流量
        validRecords.push({
          amount: record.amount,
          amountGB: Number((record.amount / 1024 / 1024).toFixed(2)),
          expiredAt: record.expiredAt,
          createdAt: record.createdAt
        })
      } else {
        // 已过期的流量
        expiredAmount += record.amount
      }
    }

    const accumulated = team.accumulatedTraffic || 0

    return {
      accumulated,
      accumulatedGB: Number((accumulated / 1024 / 1024).toFixed(2)),
      validRecords,
      expiredAmount
    }
  }

  /**
   * 批量计算多个团队的权益概览
   */
  async calculateMultipleTeamsBenefits(teamIds: string[]): Promise<Array<{
    teamId: string
    teamName: string
    accountPoints: number
    trafficGB: number
    vipExpiredAt: Date | null
    activeOrdersCount: number
  }>> {
    const results = []

    for (const teamId of teamIds) {
      try {
        const benefits = await this.calculateTeamBenefits(teamId)
        results.push({
          teamId: benefits.teamId,
          teamName: benefits.teamName,
          accountPoints: benefits.accountPoints.current,
          trafficGB: benefits.traffic.accumulatedGB,
          vipExpiredAt: benefits.vipExpiredAt,
          activeOrdersCount: benefits.accountPoints.activeOrders.filter(o => o.isActive).length
        })
      } catch (error) {
        this.logger.error(`计算团队权益失败: teamId=${teamId}, error=${error.message}`)
        // 添加错误记录，但不中断整个批量计算
        results.push({
          teamId,
          teamName: '计算失败',
          accountPoints: 0,
          trafficGB: 0,
          vipExpiredAt: null,
          activeOrdersCount: 0
        })
      }
    }

    return results
  }

  /**
   * 获取团队权益变化历史
   */
  async getTeamBenefitsHistory(
    teamId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Array<{
    date: Date
    accountPoints: number
    trafficGB: number
    orderEvents: Array<{
      type: 'account_start' | 'account_end' | 'traffic_add' | 'traffic_expire'
      orderNo?: string
      amount: number
      description: string
    }>
  }>> {
    // 获取时间范围内的所有订单事件
    const accountOrders = await this.orderModel.find({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Paid,
      resourceType: OpenPlatformOrderResourceType.AccountPoints,
      $or: [
        { startTime: { $gte: startDate, $lte: endDate } },
        { endTime: { $gte: startDate, $lte: endDate } }
      ]
    }).sort({ startTime: 1 })

    const trafficOrders = await this.orderModel.find({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Paid,
      resourceType: OpenPlatformOrderResourceType.Traffic,
      startTime: { $gte: startDate, $lte: endDate }
    }).sort({ startTime: 1 })

    // 构建事件时间线
    const events = []

    // 添加账号订单事件
    for (const order of accountOrders) {
      if (order.startTime >= startDate && order.startTime <= endDate) {
        events.push({
          date: order.startTime,
          type: 'account_start' as const,
          orderNo: order.orderNo,
          amount: order.accountCapacity || 0,
          description: `账号点数订单开始: +${order.accountCapacity}个账号`
        })
      }

      if (order.endTime >= startDate && order.endTime <= endDate) {
        events.push({
          date: order.endTime,
          type: 'account_end' as const,
          orderNo: order.orderNo,
          amount: -(order.accountCapacity || 0),
          description: `账号点数订单结束: -${order.accountCapacity}个账号`
        })
      }
    }

    // 添加流量订单事件
    for (const order of trafficOrders) {
      events.push({
        date: order.startTime,
        type: 'traffic_add' as const,
        orderNo: order.orderNo,
        amount: order.trafficCount || 0,
        description: `流量订单: +${order.trafficCount}GB流量`
      })
    }

    // 按日期排序
    events.sort((a, b) => a.date.getTime() - b.date.getTime())

    // 按天聚合事件
    const dailyHistory = new Map()

    for (const event of events) {
      const dateKey = event.date.toISOString().split('T')[0]
      
      if (!dailyHistory.has(dateKey)) {
        dailyHistory.set(dateKey, {
          date: new Date(dateKey),
          accountPoints: 0,
          trafficGB: 0,
          orderEvents: []
        })
      }

      const dayData = dailyHistory.get(dateKey)
      dayData.orderEvents.push(event)

      // 累计当日变化
      if (event.type === 'account_start' || event.type === 'account_end') {
        dayData.accountPoints += event.amount
      } else if (event.type === 'traffic_add') {
        dayData.trafficGB += event.amount
      }
    }

    return Array.from(dailyHistory.values()).sort((a, b) => a.date.getTime() - b.date.getTime())
  }
}
