import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ModelDefinition, MongooseModule } from '@nestjs/mongoose'
import { Number, Types } from 'mongoose'
import { OpenPlatformStatus, ApplicationType, WebhookStatus } from '@yxr/common'

@Schema({
  collection: 'open_platform_applications',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformApplicationEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index: true
  })
  userId: Types.ObjectId

  @Prop({
    type: String,
    required: true,
    maxlength: 100
  })
  name: string

  @Prop({
    type: String,
    required: true,
    unique: true,
    index: true
  })
  appId: string

  @Prop({
    type: String,
    required: true
  })
  secretKey: string

  @Prop({
    type: Number,
    required: true,
    default: 1
  })
  accountPrice: number


  @Prop({
    type: Number,
    required: true,
    default: 0.5
  })
  trafficPrice: number

  @Prop({
    type: Number,
    required: true,
    default: 49
  })
  packagePrice: number

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  description?: string

  @Prop({
    type: Number,
    enum: OpenPlatformStatus,
    required: true,
    default: OpenPlatformStatus.ACTIVE
  })
  status: OpenPlatformStatus

  @Prop({
    type: String,
    enum: ApplicationType,
    required: true,
    index: true,
    default: ApplicationType.TECHNICAL_SERVICE
  })
  applicationType: ApplicationType

  @Prop({
    type: String,
    required: false,
    maxlength: 500
  })
  webhookUrl?: string

  @Prop({
    type: String,
    enum: WebhookStatus,
    required: true,
    default: WebhookStatus.NOT_SET
  })
  webhookStatus: WebhookStatus

  @Prop({
    type: Date,
    required: false
  })
  webhookVerifiedAt?: Date

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const OpenPlatformApplicationSchema: ModelDefinition = {
  name: OpenPlatformApplicationEntity.name,
  schema: SchemaFactory.createForClass(OpenPlatformApplicationEntity)
}

export const OpenPlatformApplicationMongoose = MongooseModule.forFeature([OpenPlatformApplicationSchema])
