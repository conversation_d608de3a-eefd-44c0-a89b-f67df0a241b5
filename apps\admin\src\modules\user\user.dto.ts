import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsIn, IsNotEmpty, IsNumber, IsOptional, IsString, Length } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Type } from 'class-transformer'

export class getAdminUsersQueryDto {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class AdminUserDto {
  @ApiProperty({
    type: String,
    description: '用户ID',
    example: '66fa0294fad22d8795577ba8'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '姓名',
    example: '张三'
  })
  name: string

  @ApiProperty({
    type: String,
    description: '账号',
    example: 'admin'
  })
  username: string

  @ApiProperty({
    type: Number,
    description: '角色：0超管，1管理员，2客服',
    example: 1
  })
  role: number

  @ApiProperty({
    type: String,
    description: '客服二维码地址',
    example: 'https://static-lite.yixiaoer.cn/ads/test/1736849285244.png'
  })
  qrCode: string

  @ApiProperty({
    description: '创建时间',
    required: true
  })
  createdAt: number

  @ApiProperty({
    description: '状态0不可分配 1可以分配',
    type: Number,
    default: 0
  })
  status: number
}

export class GetAdminUsersResponse {
  @ApiResponseProperty({
    type: [AdminUserDto]
  })
  data: AdminUserDto[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class AdminUserResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: GetAdminUsersResponse
  })
  data: GetAdminUsersResponse
}
export class GetAdminUserResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AdminUserDto
  })
  data: AdminUserDto
}
export class patchAdminUserRequestDto {
  @ApiProperty({
    description: '姓名',
    example: '王大锤',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '姓名不能为空' })
  name: string

  @ApiProperty({
    description: '二维码(二进制的Base64编码字符串)',
    example: 1,
    required: false,
    type: String
  })
  @IsOptional()
  qrCode: string

  @ApiProperty({
    description: '非客服可以不传，状态0不可分配 1可以分配',
    type: Number,
    required: false,
    default: 0
  })
  @IsOptional()
  status: number
}

export class resetPasswordRequestDto {
  @ApiProperty({
    description: '密码',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string
}
export class putStatusRequestDto {
  @ApiProperty({
    description: '状态0不可分配 1可以分配',
    type: Number,
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '状态不能为空' })
  status: number
}

export class createAdminUserRequestDto {
  @ApiProperty({
    description: '用户名',
    required: true
  })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @Length(4, 18, { message: '请输入4-18位用户名' })
  username: string

  @ApiProperty({
    description: '姓名',
    example: '王大锤',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '姓名不能为空' })
  name: string

  @ApiProperty({
    description: '密码',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string

  @ApiProperty({
    description: '添加身份 1管理员 2客服',
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty({ message: '请选择身份' })
  @IsIn([1, 2], { message: '身份只能是 1 或 2' })
  role: number

  @ApiProperty({
    description: '二维码(二进制的Base64编码字符串)',
    example: 1,
    required: false,
    type: String
  })
  @IsOptional()
  qrCode: string

  @ApiProperty({
    description: '状态0不可分配 1可以分配',
    type: Number,
    required: false,
    default: 0
  })
  @IsOptional()
  status: number
}

export class UserLoginRequestDto {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: true
  })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @Length(4, 18, { message: '请输入4-18位用户名' })
  username: string

  @ApiProperty({
    description: '密码',
    example: 'admin1234',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string

  @ApiProperty({
    description: '二次验证码',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString({ message: '密码格式不正确' })
  token: string
}

export class UserLoginResphoneDTO {
  /**
   * @description
   * 账号名称
   */
  @ApiProperty({
    type: String,
    description: '账号名称',
    example: '张三'
  })
  name?: string

  /**
   * @description
   * 唯一凭证
   */
  @ApiProperty({
    type: String,
    format: 'nanoid',
    example: '1300120012DE89D1DE89D'
  })
  authorization: string

  @ApiProperty({
    type: Number,
    description: '角色：0超管，1管理员，2客服',
    example: 0
  })
  role?: number

  @ApiProperty({
    type: String,
    description: '是否上验证',
    example: true
  })
  verifyMfa: boolean

  @ApiProperty({
    type: String,
    description: '是否是初始化mfa',
    example: true
  })
  initMfa: boolean

  @ApiProperty({
    type: String,
    description: '验证mfa的二维码'
  })
  qrcode: string

  @ApiProperty({
    type: String,
    description: '首次绑定mfa的密钥'
  })
  secret: string
}

export class UserLoginOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: UserLoginResphoneDTO
  })
  data: UserLoginResphoneDTO
}

export class UserDeleteResponseDTO extends BaseResponseDTO {}

export class CustomerResponse {
  @ApiProperty({
    type: String,
    description: '用户ID',
    example: '66fa0294fad22d8795577ba8'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '姓名',
    example: '张三'
  })
  name: string
}

export class CustomerResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [CustomerResponse]
  })
  data: CustomerResponse[]
}

export class BindMfaRequestDTO {
  @ApiProperty({
    description: '用户名',
    example: 'admin',
    required: true
  })
  @IsString({ message: '用户名格式不正确' })
  @IsNotEmpty({ message: '用户名不能为空' })
  @Length(4, 18, { message: '请输入4-18位用户名' })
  username: string

  @ApiProperty({
    description: '密码',
    example: '123456',
    required: true
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码格式不正确' })
  @Length(6, 24, { message: '请输入6-24位密码' })
  password: string

  @ApiProperty({
    description: '密钥',
    required: true
  })
  @IsString()
  @IsOptional()
  secret: string

  @ApiProperty({
    description: '验证码',
    example: '201555',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '验证码不能为空' })
  token: string
}
