import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { OrderType } from '@yxr/common'

export class OrderPriceResponseDto {
  @ApiProperty({
    type: Number,
    description: '产品金额',
    example: 1
  })
  orderAmount: number

  @ApiProperty({
    type: Number,
    description: '折扣金额',
    example: 1
  })
  discountAmount: number

  @ApiProperty({
    type: Number,
    description: '续费到期日期',
    example: '1735783254'
  })
  expiredTime: number

  @ApiProperty({
    type: String,
    description: '升级公式',
    example: '10*3/30*1=1'
  })
  tips: string

  @ApiProperty({
    type: String,
    description: '升级公式(中文 )',
    example: '升级权益包金额/30*剩余时长'
  })
  tipsCn: string
}

export class PostCalculateOrderPriceRequestDTO {
  @ApiProperty({
    description: '用户id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  userId: string

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    enum: OrderType,
    description: '订单类型(open:开通,upgrade:升级,renew:续费,gift:赠送)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderType)
  orderType: OrderType

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    description: '天数数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  days: number
}

export class OrderBaseRequestDTO {
  @ApiProperty({
    description: '用户id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  userId: string

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isCorporateTransfer?: boolean

  @ApiProperty({
    type: Boolean,
    description: '是否为付款',
    required: false
  })
  @IsBoolean()
  @IsOptional()
  isPay: boolean = true

  @ApiProperty({
    description: '创建人',
    required: false
  })
  @IsString()
  @IsOptional()
  creatorId?: string

  @ApiProperty({
    description: '创建人昵称',
    required: false
  })
  @IsString()
  @IsOptional()
  creatorName?: string

  @ApiProperty({
    description: '备注',
    required: false
  })
  @IsString()
  @IsOptional()
  remark?: string

  @ApiProperty({
    description: '待付款金额',
    required: false
  })
  @IsNumber()
  @IsOptional()
  payAmount?: number

  @ApiProperty({
    description: '客户端IP',
    required: false
  })
  @IsString()
  @IsOptional()
  clientIP?: string

  @ApiProperty({
    description: '开放平台应用id',
    example: '1',
    required: false
  })
  @IsString()
  applicationId?: string

  @ApiProperty({
    description: '开放平台用户id',
    example: '1',
    required: false
  })
  @IsString()
  openPlatformUserId?: string
}

export class PostCreateOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  days: number
}

export class PostUpgradeOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number
}

export class PostRenewOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    description: '月份数量',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: '1',
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  days: number
}

export class PostGiftOrderRequestDTO extends OrderBaseRequestDTO {
  @ApiProperty({
    type: Number,
    description: '赠送天数',
    required: true
  })
  @IsNumber()
  giftDays: number
}

export class PostRefundRequestDTO {
  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '实际退费金额',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  actualRefundAmount: number

  @ApiProperty({
    description: '备注',
    required: false
  })
  @IsString()
  @IsOptional()
  remark: string
}
