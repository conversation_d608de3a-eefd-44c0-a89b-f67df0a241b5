import { Module } from '@nestjs/common'
import { KuaidailiController } from './kuaidaili.controller'
import { KuaidailiService } from './kuaidaili.service'
import { PlatformAccountMongoose, PlatformProxyMongoose, TeamMongoose } from '@yxr/mongo'
import { CommonModule } from '@yxr/common'

@Module({
  imports: [TeamMongoose, PlatformProxyMongoose, PlatformAccountMongoose, CommonModule],
  controllers: [KuaidailiController],
  providers: [KuaidailiService]
})
export class KuaidaialiModule {}
