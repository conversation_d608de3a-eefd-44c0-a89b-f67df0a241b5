import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'

export class OnlineScriptDetailsDto {
  @ApiProperty({
    type: String,
    description: '脚本类型, rpa/spider',
    example: 'rpa',
    required: false
  })
  type: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0',
    required: true
  })
  version: string

  @ApiProperty({
    type: Number,
    description: '发布时间',
    required: true
  })
  createdAt: number
}

export class OnlineScriptListResponseDTO {
  @ApiResponseProperty({
    type: [OnlineScriptDetailsDto]
  })
  data: OnlineScriptDetailsDto[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class GetOnlineScriptListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OnlineScriptListResponseDTO
  })
  data: OnlineScriptListResponseDTO
}

export class CreateOnlineScriptResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OnlineScriptDetailsDto
  })
  data: OnlineScriptDetailsDto
}

export class CreateOnlineScriptRequest {
  @ApiProperty({
    type: String,
    description: '脚本类型, rpa/spider',
    example: 'rpa',
    required: false
  })
  @IsString()
  @IsOptional()
  type: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0',
    required: true
  })
  @IsString()
  version: string

  @ApiProperty({
    type: String,
    description: '脚本文件二进制的Base64编码字符串',
    required: true
  })
  @IsString()
  scripts: string
}

export class GetOnlineScriptListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: String,
    description: '脚本类型, rpa/spider',
    example: 'rpa',
    required: false
  })
  @IsString()
  @IsOptional()
  type: string
}

//创建版本
export class CreateDesktopAppRequest {
  @ApiProperty({
    type: String,
    description: '桌面应用类型, windows/macos/ios/android',
    example: 'windows',
    required: true
  })
  @IsString({ message: '桌面应用类型格式不正确' })
  @IsNotEmpty({ message: '桌面应用类型不能为空' })
  type: string

  @ApiProperty({
    type: Boolean,
    required: true,
    default: false
  })
  @IsBoolean()
  @IsNotEmpty({ message: '强制更新类型不能为空' })
  isForce: boolean

  @ApiProperty({
    type: String,
    description: '文件地址，前端直传后的文件地址',
    required: false
  })
  @IsString()
  scripts?: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0',
    required: true
  })
  @IsString({ message: '版本号格式不正确' })
  @IsNotEmpty({ message: '版本号不能为空' })
  version: string

  @ApiProperty({
    type: String,
    description: '版本公告',
    example: '发版公告',
    required: true
  })
  @IsString({ message: '版本公告格式不正确' })
  @IsNotEmpty({ message: '版本公告不能为空' })
  notice: string

  @ApiProperty({
    type: String,
    description: 'IOS发版地址',
    example: 'IOS发版地址',
    required: false
  })
  @IsOptional()
  requestUrl?: string
}

export class GetDesktopAppLatestRequest {
  @ApiProperty({
    type: String,
    description: '版本类型, windows/macos/ios/android',
    example: 'windows',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '版本类型不能为空' })
  type: string
}

//版本详情
export class DesktopAppDetailsDto {
  @ApiProperty({
    type: String,
    description: '桌面应用类型, windows/macos/ios/android',
    example: 'windows',
    required: true
  })
  type: string

  @ApiProperty({
    type: String,
    description: '发布人名称',
    example: '发布人',
    required: true
  })
  publishName: string

  @ApiProperty({
    type: String,
    description: '版本号',
    example: '1.0.0',
    required: true
  })
  version: string

  @ApiProperty({
    type: String,
    description: '发版公告',
    example: '发版公告'
  })
  notice: string

  @ApiProperty({
    type: Boolean,
    description: '是否强制更新',
    example: 'false'
  })
  isForce: boolean

  @ApiProperty({
    type: Number,
    description: '发布时间',
    required: true
  })
  createdAt: number

  @ApiProperty({
    type: Number,
    description: 'ios下载地址',
    required: true
  })
  requestUrl: string
}

/**
 * 桌面应用列表
 */
export class DesktopAppListResponseDTO {
  @ApiResponseProperty({
    type: [DesktopAppDetailsDto]
  })
  data: DesktopAppDetailsDto[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

/**
 * 获取桌面应用列表返回
 */
export class GetDesktopAppListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: DesktopAppListResponseDTO
  })
  data: DesktopAppListResponseDTO
}

/**
 * 创建桌面应用列表返回
 */
export class CreateDesktopAppResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: DesktopAppDetailsDto
  })
  data: DesktopAppDetailsDto
}

/**
 * 获取桌面应用列表参数
 */
export class GetDesktopAppListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: Date,
    description: '发布时间开始时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  publishStartTime: Date

  @ApiProperty({
    type: Date,
    description: '发布时间结束时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  publishEndTime: Date

  @ApiProperty({
    type: String,
    description: '版本号查询',
    example: '1.0.0',
    required: false
  })
  @IsOptional()
  version: string

  @ApiProperty({
    type: String,
    description: '设备类型',
    example: 'macos',
    required: false
  })
  @IsOptional()
  type: string
}

/**
 * 更新桌面应用列表参数
 */
export class PutDesktopAppRequest {
  @ApiProperty({
    type: Boolean,
    description: '是否强制更新',
    example: 'false',
    required: true
  })
  @IsBoolean()
  isForce: boolean

  @ApiProperty({
    type: String,
    description: '发版公告',
    example: '发版公告'
  })
  @IsString()
  notice: string

  @ApiProperty({
    type: String,
    description: '文件二进制的Base64编码字符串',
    required: false
  })
  @IsString()
  @IsOptional()
  scripts?: string
}
