import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Type } from 'class-transformer'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator'
import { AdTypeEmun, PopupTypeEmun } from '@yxr/common'

export class AdDTO {
  @ApiProperty({
    type: Number,
    description: '排序',
    default: 1
  })
  sort: number

  @ApiProperty({
    type: String,
    description: '名称',
    default: 'banner图片名称'
  })
  name: string

  @ApiProperty({
    type: String,
    description: '广告图ossKey'
  })
  adUrl: string

  @ApiProperty({
    type: String,
    description: '广告图访问地址'
  })
  adPath?: string

  @ApiProperty({
    type: Boolean,
    description: '是否跳转',
    default: false
  })
  isJumpTo: boolean

  @ApiProperty({
    type: String,
    description: '跳转地址'
  })
  jumpToUrl: string

  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  enabled: boolean

  @ApiProperty({
    type: Number,
    description: '有效开始时间',
    required: false
  })
  expiredStartAt: number

  @ApiProperty({
    type: Number,
    description: '有效结束时间',
    required: false
  })
  expiredEndAt: number

  @ApiProperty({
    type: String,
    enum: AdTypeEmun,
    description: '广告类型banner广告,home_popup首页弹框',
    required: true
  })
  adType: AdTypeEmun

  @ApiProperty({
    type: String,
    enum: PopupTypeEmun,
    description: '弹框方案everyday每天一次,threeday三天一次',
    required: false
  })
  popupType: PopupTypeEmun
}

export class AdCreateRequestDTO {
  @ApiProperty({
    type: Number,
    description: '排序',
    default: 1
  })
  @IsNumber()
  sort: number = 1

  @ApiProperty({
    type: String,
    description: '名称',
    default: 'banner图片名称'
  })
  @IsString()
  name: string

  @ApiProperty({
    type: String,
    description: '缩略图'
  })
  @IsString()
  @IsOptional()
  adUrl: string

  @ApiProperty({
    type: Boolean,
    description: '是否跳转',
    default: false
  })
  @IsBoolean()
  isJumpTo: boolean

  @ApiProperty({
    type: String,
    description: '跳转地址'
  })
  @IsOptional()
  jumpToUrl: string

  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  @IsBoolean()
  enabled: boolean

  @ApiProperty({
    type: Boolean,
    description: '是否时间永久有效',
    default: false
  })
  @IsBoolean()
  isTimed: boolean

  @ApiProperty({
    type: Number,
    description: '有效开始时间',
    required: false
  })
  @IsOptional()
  expiredStartAt: number

  @ApiProperty({
    type: Number,
    description: '有效结束时间',
    required: false
  })
  @IsOptional()
  expiredEndAt: number

  @ApiProperty({
    type: String,
    enum: AdTypeEmun,
    description: '广告类型banner广告,home_popup首页弹框',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '广告类型不能为空' })
  adType: AdTypeEmun

  @ApiProperty({
    type: String,
    enum: PopupTypeEmun,
    description: '弹框方案everyday每天一次,threeday三天一次',
    required: false
  })
  @ValidateIf((o) => o.adType === AdTypeEmun.Popup)
  @IsNotEmpty({ message: '弹框方案不能为空' })
  @IsString({ message: '弹框方案格式错误' })
  popupType: PopupTypeEmun
}

export class PutAdEnabledRequestDTO {
  @ApiProperty({
    type: Boolean,
    description: '上架状态',
    default: false
  })
  @IsBoolean()
  enabled: boolean
}

export class AdListRequestDTO {
  @ApiProperty({
    type: Boolean,
    required: false,
    description: '状态筛选',
    example: 'false'
  })
  @IsString()
  @IsOptional()
  enabled: string

  @ApiProperty({
    type: String,
    enum: AdTypeEmun,
    description: '广告类型banner广告,home_popup首页弹框',
    required: true
  })
  @IsString()
  @IsOptional()
  adType: AdTypeEmun

  @ApiProperty({
    type: String,
    required: false,
    description: '广告名称',
    example: 'banner广告'
  })
  @IsString()
  @IsOptional()
  name: string

  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class AdDetail extends AdDTO {
  @ApiProperty({
    type: String,
    description: '操作人',
    default: '管理人'
  })
  adminName: string
}

export class AdDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AdDTO
  })
  data: AdDTO
}

export class AdListResponse {
  @ApiResponseProperty({
    type: [AdDetail]
  })
  data: AdDetail[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class AdListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: AdListResponse
  })
  data: AdListResponse
}
