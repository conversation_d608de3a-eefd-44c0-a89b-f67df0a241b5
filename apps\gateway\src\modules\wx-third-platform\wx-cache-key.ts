/**
 * 微信相关缓存key 管理
 */
export class WxCacheKey {
  //微信第三方平台票据
  static readonly ComponentVerifyTicketTime = 1000 * 60 * 60 * 11 // 11小时
  static readonly ComponentVerifyTicket = 'wx-component-verify-ticket'

  //预授权码
  static readonly PreAuthCodeTime = 1000 * 60 * 25 // 25分钟
  private static readonly PreAuthCode = 'wx-pre-auth-code'

  //第三方接口凭证
  static readonly AccessTokenTime = 1000 * 6600 // 1小时50分钟
  static readonly ComponentAccessToken = 'wx-component-access-token'
  private static readonly AccessToken = 'access-token'

  //获取预授权码缓存key
  static getPreAuthCode(userId: string) {
    return WxCacheKey.PreAuthCode + '-' + userId
  }

  //授权账号接口凭证缓存key
  static getAccessToken(authorizerAppid: string) {
    return WxCacheKey.AccessToken + '-' + authorizerAppid
  }
}
