import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { MaterialTypeEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class CommonTopicsEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  groupId?: Types.ObjectId

  @Prop({
    type: String,
    required: true
  })
  name: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const CommonTopicsSchema: ModelDefinition = {
  name: CommonTopicsEntity.name,
  schema: SchemaFactory.createForClass(CommonTopicsEntity)
}

export const CommonTopicsMongoose = MongooseModule.forFeature([CommonTopicsSchema])
