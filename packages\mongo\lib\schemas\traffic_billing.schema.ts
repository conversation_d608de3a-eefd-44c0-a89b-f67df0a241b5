import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { OrderSource, OrderStatus, OrderType, PayType, SalesType } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class TrafficBillingEntity {
  /**
   * 用户id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  /**
   * 团队id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    unique: true,
    required: true
  })
  taskId: string

  //已使用网络流量
  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  useNetworkTraffic?: number

  /**
   * 创建时间
   */
  @Prop({
    type: Date
  })
  createdAt?: Date

  /**
   * 更新时间
   */
  @Prop({
    type: Date
  })
  updatedAt?: Date

}

export const TrafficBillingSchema: ModelDefinition = {
  name: TrafficBillingEntity.name,
  schema: SchemaFactory.createForClass(TrafficBillingEntity)
}

export const TrafficBillingMongoose = MongooseModule.forFeature([TrafficBillingSchema])
