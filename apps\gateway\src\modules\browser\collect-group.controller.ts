import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import {
  GroupsDetailResponseDTO,
  GroupsListResponseDTO,
  PostGroupsRequest,
  PutCollectGroupItemsRequest
} from './collect-group.dto'
import { CollectGroupService } from './collect-group.service'

@Controller('collect-groups')
@ApiTags('浏览器管理/收藏分组管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class CollectGroupController {
  constructor(private readonly collectGroupService: CollectGroupService) {}

  @Post()
  @ApiOperation({ summary: '创建分组' })
  @ApiOkResponse({ type: GroupsDetailResponseDTO })
  async postGroups(@Body() body: PostGroupsRequest) {
    return await this.collectGroupService.postGroupsAsync(body)
  }

  @Get()
  @ApiOperation({ summary: '分组列表' })
  @ApiOkResponse({ type: GroupsListResponseDTO })
  @ApiQuery({ name: 'page', required: false, type: Number, description: '页码 <默认 1>' })
  @ApiQuery({ name: 'size', required: false, type: Number, description: '每页数量 <默认 10>' })
  @ApiQuery({ name: 'name', required: false, type: String, description: '分组名称' })
  async getGroups(
    @Query('name') name: string,
    @Query('size', { transform: (value) => value || 10 }) size: number,
    @Query('page', { transform: (value) => value || 1 }) page: number
  ) {
    return await this.collectGroupService.getGroupsAsync(name, page, size)
  }

  @Patch(':groupId')
  @ApiOperation({ summary: '更新分组' })
  @ApiOkResponse({ type: GroupsDetailResponseDTO })
  async patchGroups(@Param('groupId') groupId: string, @Body() body: PostGroupsRequest) {
    return await this.collectGroupService.patchGroupsAsync(groupId, body)
  }

  @Get(':groupId')
  @ApiOperation({ summary: '获取分组详情' })
  @ApiOkResponse({ type: GroupsDetailResponseDTO })
  async getGroupDetail(@Param('groupId') groupId: string) {
    return await this.collectGroupService.getGroupDetailAsync(groupId)
  }

  @Delete(':groupId')
  @ApiOperation({ summary: '删除分组' })
  @ApiOkResponse()
  async deleteGroups(@Param('groupId') groupId: string) {
    await this.collectGroupService.deleteGroupsAsync(groupId)
  }

  @Patch(':groupId/set_favorites')
  @ApiOperation({ summary: '设置分组收藏' })
  @ApiOkResponse({ type: GroupsDetailResponseDTO })
  @ApiBody({ type: [PutCollectGroupItemsRequest] })
  async putCollectGroupItems(
    @Param('groupId') groupId: string,
    @Body() body: PutCollectGroupItemsRequest[]
  ) {
    return await this.collectGroupService.putCollectGroupItems(groupId, body)
  }
}
