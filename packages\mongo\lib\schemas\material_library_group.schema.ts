import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class MaterialLibraryGroupEntity {
  /**
   * 分组名称
   */
  @Prop({
    type: String,
    required: true
  })
  name: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const MaterialLibraryGroupSchema: ModelDefinition = {
  name: MaterialLibraryGroupEntity.name,
  schema: SchemaFactory.createForClass(MaterialLibraryGroupEntity)
}

export const MaterialLibraryGroupMongoose = MongooseModule.forFeature([MaterialLibraryGroupSchema])
