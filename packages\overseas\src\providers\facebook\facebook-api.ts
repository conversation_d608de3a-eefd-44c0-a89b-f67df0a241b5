import { Injectable, Logger, Scope } from '@nestjs/common'
import { OverseasContext } from '../types'
import { _ensureSuccessfulInvoke } from '../utils'
import { createAxiosInstance } from '../../utils/axios-config'

export type PageTaskEnum =
  | 'ADVERTISE' // 创建广告 | 创建公共主页隐藏帖 | 如果公共主页已与 Instagram 帐户关联，可创建广告
  | 'ANALYZE' // 查看公共主页的成效分析 | 查看发布了帖子或评论的公共主页管理员
  | 'CREATE_CONTENT' // 以公共主页身份在该公共主页上发布内容
  | 'MANAGE' // 分配和管理公共主页任务
  | 'MANAGE_LEADS' // 查看和管理潜在客户
  | 'MESSAGING' // 以公共主页身份发送消息
  | 'MODERATE' // 以公共主页身份回复对公共主页帖子的评论 | 删除对公共主页帖子的评论 | 如果公共主页已与 Instagram 帐户关联，可从 Facebook 将内容发布到 Instagram、回复和删除评论、发送私信、同步企业联系方式以及创建广告。
  | 'VIEW_MONETIZATION_INSIGHTS' // 查看变现成效分析

@Injectable({ scope: Scope.TRANSIENT })
export class FacebookApi {
  logger = new Logger(FacebookApi.name)

  private readonly clientId = process.env.FACEBOOK_CLIENT_ID

  private readonly clientSecret = process.env.FACEBOOK_CLIENT_SECRET

  private readonly api_version = 'v22.0'

  async get_oauth_access_token(context: OverseasContext, params: {
    code: string
    redirect_uri: string
  }) {
    // https://developers.facebook.com/docs/facebook-login/guides/advanced/manual-flow#confirm

    return await _ensureSuccessfulInvoke<{
      access_token: string
      expires_in: number
      token_type: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.get(`/${this.api_version}/oauth/access_token`, {
        params: {
          ...params,
          grant_type: 'authorization_code',
          client_id: this.clientId,
          client_secret: this.clientSecret
        }
      })
    })
  }


  async get_user_accounts(context: OverseasContext, access_token: string, user_id: string = 'me') {
    const response = await _ensureSuccessfulInvoke<{
      data: Array<{
        /**
         * 公共主页的 ID
         */
        id: string
        /**
         * 公共主页的短期访问令牌
         */
        access_token: string
        /**
         * 公共主页的 名称
         */
        name: string
        category: string
        category_list: Array<{ id: string; name: string }>
        tasks: Array<PageTaskEnum>
      }>
      paging: { cursors: { before: string; after: string } }
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.get(`/v21.0/${user_id}/accounts`, {
        headers: { Authorization: `Bearer ${access_token}` }
      })
    })

    this.logger.debug(`[facebook.api.get_user_accounts]: ${JSON.stringify(response, null, 2)}`)
    return response
  }

  async get_page(context: OverseasContext, page_id: string, access_token: string) {
    const response = await _ensureSuccessfulInvoke<{
      name: string
      id: string
      access_token: string
      picture: {
        data: {
          height: number
          is_silhouette: false
          url: string
          width: number
        }
      }
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.get(`/${this.api_version}/${page_id}`, {
        params: {
          fields: 'name,id,picture,access_token',
          access_token: access_token
        }
      })
    })

    this.logger.debug(`[facebook.api.get_page]: ${JSON.stringify(response, null, 2)}`)
    return response
  }

  /**
   * 创建文本帖子
   */
  async createPost(context: OverseasContext, params: {
    message: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const pageId = this.getPageIdFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
      permalink_url?: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.post(`/${this.api_version}/${pageId}/feed`, {
        message: params.message
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 创建图片帖子
   */
  async createPhotoPost(context: OverseasContext, params: {
    url: string
    caption?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const pageId = this.getPageIdFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
      post_id: string
      permalink_url?: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.post(`/${this.api_version}/${pageId}/photos`, {
        url: params.url,
        caption: params.caption
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 创建相册帖子（多张图片）
   */
  async createAlbumPost(context: OverseasContext, params: {
    photos: Array<{ url: string; caption?: string }>
    message?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const pageId = this.getPageIdFromContext(context)

    // 先创建相册
    const album = await _ensureSuccessfulInvoke<{
      id: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.post(`/${this.api_version}/${pageId}/albums`, {
        name: 'Published Photos',
        message: params.message
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })

    // 上传图片到相册
    const photoPromises = params.photos.map(photo =>
      _ensureSuccessfulInvoke<{
        id: string
      }>(context, () => {
        const axios = createAxiosInstance('https://graph.facebook.com')
        return axios.post(`/${this.api_version}/${album.id}/photos`, {
          url: photo.url,
          caption: photo.caption
        }, {
          headers: { Authorization: `Bearer ${accessToken}` }
        })
      })
    )

    await Promise.all(photoPromises)

    return {
      id: album.id,
      permalink_url: `https://facebook.com/${album.id}`
    }
  }

  /**
   * 创建视频帖子
   */
  async createVideoPost(context: OverseasContext, params: {
    file_url: string
    description?: string
    thumb?: string
    access_token?: string
  }) {
    const accessToken = params.access_token || this.getAccessTokenFromContext(context)
    const pageId = this.getPageIdFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
      permalink_url?: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.post(`/${this.api_version}/${pageId}/videos`, {
        file_url: params.file_url,
        description: params.description,
        thumb: params.thumb
      }, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 获取帖子信息
   */
  async getPost(context: OverseasContext, postId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      id: string
      message?: string
      permalink_url?: string
      created_time: string
      updated_time: string
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.get(`/${this.api_version}/${postId}`, {
        params: {
          fields: 'id,message,permalink_url,created_time,updated_time'
        },
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 删除帖子
   */
  async deletePost(context: OverseasContext, postId: string, access_token?: string) {
    const accessToken = access_token || this.getAccessTokenFromContext(context)

    return await _ensureSuccessfulInvoke<{
      success: boolean
    }>(context, () => {
      const axios = createAxiosInstance('https://graph.facebook.com')
      return axios.delete(`/${this.api_version}/${postId}`, {
        headers: { Authorization: `Bearer ${accessToken}` }
      })
    })
  }

  /**
   * 从上下文中获取访问令牌
   */
  private getAccessTokenFromContext(context: OverseasContext): string {
    // 假设访问令牌存储在context.options中
    const credentials = context.options?.credentials
    if (!credentials?.access_token) {
      throw new Error('缺少Facebook访问令牌')
    }
    return credentials.access_token
  }

  /**
   * 从上下文中获取页面ID
   */
  private getPageIdFromContext(context: OverseasContext): string {
    const credentials = context.options?.credentials
    if (!credentials?.page_id) {
      throw new Error('缺少Facebook页面ID')
    }
    return credentials.page_id
  }
}
