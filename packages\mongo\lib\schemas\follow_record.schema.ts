import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class FollowRecordEntity {
  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  customerId: Types.ObjectId

  /**
   * 分配客服名称
   */
  @Prop({
    type: String,
    required: true
  })
  customerName: string

  /**
   * 用户ID
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  /**
   * 跟进内容
   */
  @Prop({
    type: String
  })
  content: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const FollowRecordSchema: ModelDefinition = {
  name: FollowRecordEntity.name,
  schema: SchemaFactory.createForClass(FollowRecordEntity)
}

export const FollowRecordMongoose = MongooseModule.forFeature([FollowRecordSchema])
