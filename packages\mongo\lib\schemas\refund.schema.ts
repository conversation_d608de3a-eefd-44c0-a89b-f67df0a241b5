import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class RefundEntity {
  /**
   * 退款编号
   */
  @Prop({
    type: String,
    required: true
  })
  refundNo: string

  /**
   * 团队id
   */
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  teamId: Types.ObjectId

  /**
   * 操作人账号
   */
  @Prop({
    type: String,
    required: false
  })
  username: string

  /**
   * 应退金额
   */
  @Prop({
    type: Number,
    required: true
  })
  refundableAmount: number

  /**
   * 实退金额
   */
  @Prop({
    type: Number,
    required: true
  })
  actualAmount: number

  @Prop({
    type: String
  })
  remark?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Types.Map
  })
  orderInfo: unknown
}

export const RefundSchema: ModelDefinition = {
  name: RefundEntity.name,
  schema: SchemaFactory.createForClass(RefundEntity)
}

export const RefundMongoose = MongooseModule.forFeature([RefundSchema])
