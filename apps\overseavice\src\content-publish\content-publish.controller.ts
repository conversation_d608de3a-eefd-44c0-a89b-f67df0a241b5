import { Controller, Post, Get, Body, Param, Query, HttpCode, HttpStatus } from '@nestjs/common'
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger'
import { ContentPublishService } from './content-publish.service'
import { 
  CreatePublishTaskDto, 
  PublishResultDto, 
  GetPublishStatusDto, 
  BatchCreatePublishTaskDto 
} from './content-publish.dto'

@Controller(['content-publish', 'vd/content-publish', 'vt/content-publish', 'vp/content-publish'])
@ApiTags('海外平台内容发布')
export class ContentPublishController {
  constructor(private readonly contentPublishService: ContentPublishService) {}

  @Post('tasks')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '创建发布任务' })
  @ApiResponse({ 
    status: 200, 
    description: '发布任务创建成功',
    type: PublishResultDto
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误' 
  })
  @ApiResponse({ 
    status: 500, 
    description: '服务器内部错误' 
  })
  async createPublishTask(@Body() dto: CreatePublishTaskDto): Promise<PublishResultDto> {
    return await this.contentPublishService.createPublishTask(dto)
  }

  @Post('tasks/batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批量创建发布任务' })
  @ApiResponse({ 
    status: 200, 
    description: '批量发布任务创建成功',
    type: [PublishResultDto]
  })
  @ApiResponse({ 
    status: 400, 
    description: '请求参数错误' 
  })
  @ApiResponse({ 
    status: 500, 
    description: '服务器内部错误' 
  })
  async batchCreatePublishTasks(@Body() dto: BatchCreatePublishTaskDto): Promise<PublishResultDto[]> {
    return await this.contentPublishService.batchCreatePublishTasks(dto.tasks)
  }

  @Get('tasks/:taskId/status')
  @ApiOperation({ summary: '查询发布任务状态' })
  @ApiParam({ name: 'taskId', description: '任务ID' })
  @ApiQuery({ name: 'platform', description: '平台名称' })
  @ApiQuery({ name: 'accountOpenId', description: '账号OpenID' })
  @ApiQuery({ name: 'platformContentId', description: '平台内容ID' })
  @ApiResponse({ 
    status: 200, 
    description: '查询成功',
    type: PublishResultDto
  })
  @ApiResponse({ 
    status: 404, 
    description: '任务不存在' 
  })
  @ApiResponse({ 
    status: 500, 
    description: '服务器内部错误' 
  })
  async getPublishStatus(
    @Param('taskId') taskId: string,
    @Query('platform') platform: string,
    @Query('accountOpenId') accountOpenId: string,
    @Query('platformContentId') platformContentId: string
  ): Promise<PublishResultDto> {
    const dto: GetPublishStatusDto = {
      taskId,
      platformContentId
    }
    return await this.contentPublishService.getPublishStatus(dto, platform, accountOpenId)
  }

  @Get('platforms/:platform/content-types')
  @ApiOperation({ summary: '获取平台支持的内容类型' })
  @ApiParam({ name: 'platform', description: '平台名称' })
  @ApiResponse({ 
    status: 200, 
    description: '查询成功',
    type: [String]
  })
  async getSupportedContentTypes(@Param('platform') platform: string): Promise<string[]> {
    return await this.contentPublishService.getSupportedContentTypes(platform)
  }

  @Get('platforms/:platform/content-limits')
  @ApiOperation({ summary: '获取平台内容限制' })
  @ApiParam({ name: 'platform', description: '平台名称' })
  @ApiResponse({ 
    status: 200, 
    description: '查询成功'
  })
  async getContentLimits(@Param('platform') platform: string): Promise<any> {
    return await this.contentPublishService.getContentLimits(platform)
  }
}
