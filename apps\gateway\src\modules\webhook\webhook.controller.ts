import { <PERSON>, HttpC<PERSON>, Inject, Logger, Post, Req, Re<PERSON> } from '@nestjs/common'
import { FastifyReply, FastifyRequest } from 'fastify'
import { WebhookService } from './webhook.service'
import { WebhookBody } from './types'
import { REQUEST } from '@nestjs/core'
import { TlsService } from '@yxr/huoshan'

@Controller()
export class WebhookController {
  logger = new Logger('WebhookController')

  constructor(
    private readonly webhookService: WebhookService,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly loggerService: TlsService,
  ) {}

  @Post(process.env.WEBHOOK_URL)
  async webhook(@Req() req: FastifyRequest) {
    const { body } = req as { body: WebhookBody }
    const { session } = this.request

    body.user_id = session.userId

    if (typeof body.content === 'string') {
      body.content = JSON.parse(body.content)
    }
    const dataList: <PERSON>hookBody[] = []
    dataList.push(body)
    await this.webhookService.webhook(dataList)
  }

  @Post(process.env.ALIPAY_WEBHOOK_URL)
  @HttpCode(200)
  async alipayWebhook(@Req() req: FastifyRequest, @Res() res: FastifyReply) {
    const { body } = req as { body: WebhookBody; headers: Record<string, string> }
    await this.loggerService.info(this.request, '支付宝支付回调信息：', { body: JSON.stringify(body) })
    const result = await this.webhookService.alipayWebhook(body)
    await this.loggerService.info(this.request, '支付宝返回结果', { result: result })
    res.status(200).send(result)
  }

  @Post(process.env.WECHATPAY_WEBHOOK_URL)
  @HttpCode(200)
  async wechatPayWebhook(@Req() req: FastifyRequest, @Res() res: FastifyReply) {
    const { body } = req as { body: WebhookBody; headers: Record<string, string> }
    await this.loggerService.info(this.request, '微信支付回调信息：', { body: JSON.stringify(body) })
    const result = await this.webhookService.wechatPayWebhook(body)
    await this.loggerService.info(this.request, '微信支付返回结果', { result: result })
    res.status(200).send(result)
  }
}
