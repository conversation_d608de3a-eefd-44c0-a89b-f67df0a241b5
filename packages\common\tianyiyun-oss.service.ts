import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common'

import { getOssSignatureDto } from '../../apps/gateway/src/modules/storage/storage.dto'
import { nanoid } from 'nanoid'
import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  HeadObjectCommand,
  PutObjectCommand,
  S3Client
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { TeamBucketNamesEnum } from './enums'
import axios from 'axios'

@Injectable()
export class TianyiyunOssService {
  private teamBucketDirs = {
    [TeamBucketNamesEnum.Assets]: 'as',
    [TeamBucketNamesEnum.Attachments]: 'at',
    [TeamBucketNamesEnum.SiteSpaces]: 'sp',
    [TeamBucketNamesEnum.MaterialLibrary]: 'ml',
    [TeamBucketNamesEnum.WechatPublish]: 'wxp',
    [TeamBucketNamesEnum.CloudPublish]: 'yfb'
  }

  private oSSbucket = process.env.OSS_TIANYIYUN_BUCKET
  private expiresIn = 2 * 60 // 60 分钟（单位：秒）
  private s3Client: S3Client
  constructor() {
    this.s3Client = new S3Client({
      region: process.env.OSS_TIANYIYUN_REGION,
      credentials: {
        accessKeyId: process.env.OSS_TIANYIYUN_ACCESS_Key_ID,
        secretAccessKey: process.env.OSS_TIANYIYUN_SECRET_ACCESS_Key
      },
      endpoint: process.env.OSS_TIANYIYUN_ENDPOINT,
      forcePathStyle: false //必须设为 false，否则路径格式会错误
    })
  }

  private calculateDir(teamId: string, bucket: TeamBucketNamesEnum) {
    const bucketDir = this.teamBucketDirs[bucket]
    if (!bucketDir) throw new BadRequestException(`bucket \'${bucket}\' is not valid`)

    // 根据不同环境添加不同的前缀, 生产环境不添加前缀
    const prefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}/`
    return `${bucketDir}/${prefix}t-${teamId}/`
  }

  private async TianyiyunOssClient(
    key: string,
    method: string,
    internalNetwork: boolean,
    expiresIn: number = this.expiresIn
  ): Promise<string> {
    // 配置 AWS S3 客户端
    let endpoint = process.env.OSS_TIANYIYUN_ENDPOINT
    if (internalNetwork && method === 'Get') {
      endpoint = process.env.OSS_TIANYIYUN_INTERNEL_ENDPOINT //内网地址
    }

    let objectCommand = new PutObjectCommand({
      Bucket: this.oSSbucket,
      Key: key
    })
    switch (method) {
      case 'Get':
        objectCommand = new GetObjectCommand({
          Bucket: this.oSSbucket,
          Key: key
        })

        break
    }
    try {
      this.s3Client = new S3Client({
        region: process.env.OSS_TIANYIYUN_REGION,
        credentials: {
          accessKeyId: process.env.OSS_TIANYIYUN_ACCESS_Key_ID,
          secretAccessKey: process.env.OSS_TIANYIYUN_SECRET_ACCESS_Key
        },
        endpoint: endpoint,
        forcePathStyle: false
      })

      // 生成预签名 URL
      let signedUrl = await getSignedUrl(
        this.s3Client,
        objectCommand,
        { expiresIn: expiresIn } // 过期时间
      )

      return signedUrl
    } catch (err) {
      console.error('* Status: Fail')
      console.error('* Error:\n', err)
    }
  }

  /**
   * 获取资源直传地址
   */
  async getUploadSignatureUrl(key: string): Promise<string> {
    return await this.TianyiyunOssClient(key, 'Put', false)
  }

  /**
   * 获取资源直传地址
   */
  async getOssUploadUrl(
    teamId: string,
    bucket: TeamBucketNamesEnum,
    fileKey: string | undefined
  ): Promise<getOssSignatureDto> {
    const startsWith = this.calculateDir(teamId, bucket)
    fileKey = fileKey ?? nanoid().toLocaleLowerCase()
    const key = `${startsWith}${fileKey}`
    // 配置 AWS S3 客户端
    const signatureUrl = await this.TianyiyunOssClient(key, 'Put', false)

    return {
      serviceUrl: signatureUrl,
      key: key
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      const objectCommand = new DeleteObjectCommand({
        Bucket: this.oSSbucket,
        Key: key
      })
      await this.s3Client.send(objectCommand)
    } catch (error) {
      Logger.error('文件删除失败 info:', error)
    }
  }

  /**
   * 批量删除OSS指定文件
   * @param key
   */
  async deleteMultiOssObject(key: string[]) {
    try {
      const objectCommand = new DeleteObjectsCommand({
        Bucket: this.oSSbucket,
        Delete: {
          Objects: key.map((k) => ({ Key: k }))
        }
      })
      await this.s3Client.send(objectCommand)
    } catch (error) {
      Logger.error('文件删除失败 info:', error)
    }
  }
  /**
   * 获取资源访问地址
   * @param param0
   * @returns
   */
  async getOssAccessUrl(
    // teamId,
    // bucket,
    fileKey: string,
    internalNetwork: boolean,
    expiresIn?: number
  ): Promise<string> {
    // const startsWith = this.calculateDir(teamId, bucket)
    fileKey = fileKey ?? nanoid().toLocaleLowerCase()
    // const key = `${startsWith}${fileKey}`
    const key = `${fileKey}`

    return await this.TianyiyunOssClient(key, 'Get', internalNetwork, expiresIn)
  }

  /**
   * 获取资源的元数据
   * @param key
   */
  async headFileInfo(key: string) {
    try {
      let contentLength = 0
      if (TianyiyunOssService.isValidUrl(key)) {
        // 发送HEAD请求获取文件大小和类型信息
        const headResponse = await axios({
          method: 'head',
          url: key,
          timeout: 5000 // 设置超时时间
        })

        // 获取文件大小
        contentLength = parseInt(headResponse.headers['content-length'] || '0', 10)
      } else {
        // 生成预签名 URL
        const objectCommand = new HeadObjectCommand({
          Bucket: this.oSSbucket,
          Key: key
        })
        const headData = await this.s3Client.send(objectCommand)
        contentLength = headData.ContentLength
      }
      if (contentLength <= 0) {
        throw new NotFoundException('无法识别上传文件')
      }
      return contentLength
    } catch (error) {
      Logger.error('天翼云资源获取失败:', error)
    }
  }

  /**
   * 检查URL是否有效
   * @param url 需要检查的URL
   * @returns 是否有效
   */
  static isValidUrl(url: string): boolean {
    try {
      // 检查URL格式
      const urlObj = new URL(url)
      // 检查协议是否为http或https
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
    } catch (error) {
      // URL格式无效
      return false
    }
  }
}
