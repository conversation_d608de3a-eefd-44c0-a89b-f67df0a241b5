import { ApiProperty, ApiPropertyOptional, ApiResponseProperty } from '@nestjs/swagger'
import { IsDateString, IsNumber, IsOptional, IsString, Min, IsBoolean, IsNotEmpty, ValidateIf } from 'class-validator'
import { Transform } from 'class-transformer'
import { OpenPlatformOrderResourceType } from '@yxr/common'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

/**
 * 创建账号点数订单请求DTO
 */
export class CreateAccountPointsOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '账号点数数量',
    example: 10,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  accountCapacityLimit: number

  @ApiProperty({
    description: '时长（月）',
    example: 3,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  duration: number

  @ApiProperty({
    description: '订单开始日期',
    example: '2024-01-01'
  })
  @IsDateString()
  startTime: string

  @ApiPropertyOptional({
    description: '备注',
    example: '账号点数订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 创建流量订单请求DTO
 */
export class CreateTrafficOrderRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  teamId: string

  @ApiProperty({
    description: '流量数量（GB）',
    example: 100,
    minimum: 1
  })
  @IsNumber()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  trafficCount: number

  @ApiPropertyOptional({
    description: '备注',
    example: '流量订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 新订单响应DTO
 */
export class NewOrderResponseDto {
  @ApiProperty({
    description: '订单ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '订单编号',
    example: 'OP*************123'
  })
  orderNo: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiProperty({
    description: '订单状态',
    example: 'paid'
  })
  orderStatus: string

  @ApiProperty({
    description: '订单类型',
    example: 'open_platform_account_points'
  })
  orderType: string

  @ApiProperty({
    description: '资源类型',
    enum: OpenPlatformOrderResourceType,
    example: OpenPlatformOrderResourceType.AccountPoints
  })
  resourceType: OpenPlatformOrderResourceType

  @ApiProperty({
    description: '账号点数数量（仅账号点数订单）',
    example: 10
  })
  accountCount: number

  @ApiProperty({
    description: '流量数量（GB，仅流量订单）',
    example: 100
  })
  trafficCount: number

  @ApiProperty({
    description: '时长（月）',
    example: 3
  })
  duration: number

  @ApiProperty({
    description: '开始时间（时间戳）',
    example: *************
  })
  startTime: number

  @ApiProperty({
    description: '结束时间（时间戳）',
    example: *************
  })
  endTime: number

  @ApiPropertyOptional({
    description: '流量过期时间（时间戳，仅流量订单）',
    example: *************
  })
  trafficExpiredAt?: number

  @ApiPropertyOptional({
    description: '备注',
    example: '订单备注'
  })
  remark?: string

  @ApiProperty({
    description: '创建时间（时间戳）',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间（时间戳）',
    example: *************
  })
  updatedAt: number
}

/**
 * 团队账号点数查询响应DTO
 */
export class TeamAccountPointsResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '当前团队账号点数',
    example: 25
  })
  accountCapacityLimit: number

  @ApiProperty({
    description: 'VIP过期时间（时间戳）',
    example: *************
  })
  vipExpiredAt: number
}

/**
 * 创建开放平台权益包订单请求DTO
 */
export class CreateOpenPlatformOrderRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string

  @ApiProperty({
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '权益包数量',
    example: 1,
    minimum: 1
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  interestCount: number

  @ApiPropertyOptional({
    description: '月份数量',
    example: 1,
    minimum: 1
  })
  @ValidateIf((o) => !o.days) // 当 days 为空时，month必填
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  month?: number

  @ApiPropertyOptional({
    description: '自定义天数',
    example: 30,
    minimum: 1
  })
  @ValidateIf((o) => !o.month) // 当 month 为空时，days必填
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  @Transform(({ value }) => parseInt(value))
  days?: number

  @ApiProperty({
    description: '实付金额（元）',
    example: 100.00,
    minimum: 0
  })
  @ValidateIf((o) => o.isPay === true) // 当 isPay 为true时，payAmount必须大于0
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01, { message: '实付金额必须大于0' })
  @Transform(({ value }) => parseFloat(value))
  payAmount: number

  @ApiProperty({
    description: '是否需要支付',
    example: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiPropertyOptional({
    description: '备注',
    example: '权益包订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}

/**
 * 开放平台订单响应DTO
 */
export class OpenPlatformOrderResponseDto {
  @ApiResponseProperty({
    example: 'EX123KIO112'
  })
  orderNo: string
}

/**
 * 创建开放平台订单响应DTO
 */
export class CreateOpenPlatformOrderResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OpenPlatformOrderResponseDto
  })
  data: OpenPlatformOrderResponseDto
}
