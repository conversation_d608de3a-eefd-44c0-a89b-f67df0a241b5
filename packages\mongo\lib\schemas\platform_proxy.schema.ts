import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'

@Schema({
  timestamps: true,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class PlatformProxyEntity {
  @Prop({
    type: String,
    required: true,
    index: true
  })
  platformName: string

  //ip地址
  @Prop({
    type: String,
    index: true,
    required: true
  })
  proxyIp: string

  @Prop({
    type: String,
    required: true
  })
  proxyUser: string

  @Prop({
    type: String,
    required: true
  })
  proxyPwd: string

  // 省份编码
  @Prop({
    type: String,
    index: true,
    required: true
  })
  regionId: string

  // 当前占用数
  @Prop({
    type: Number,
    required: true
  })
  currLogin: number

  // 最大占用数
  @Prop({
    type: Number,
    required: true
  })
  maxLogin: number

  // 是否可用
  @Prop({
    type: Boolean,
    required: true,
    default: false
  })
  enabled: boolean
}

export const PlatformProxySchema: ModelDefinition = {
  name: PlatformProxyEntity.name,
  schema: SchemaFactory.createForClass(PlatformProxyEntity)
}

export const PlatformProxyMongoose = MongooseModule.forFeature([PlatformProxySchema])
