import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, Model, Types } from 'mongoose'
import {
  AdminEntity,
  CustomerAssignRecordEntity,
  MemberEntity,
  TeamEntity,
  UserEntity
} from '@yxr/mongo'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { getCustomerQrRedisKey } from '@yxr/utils'
import { AdminRole, TeamRoleNames } from '@yxr/common'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class CustomerService {
  logger = new Logger('CustomerService')

  constructor(
    @InjectConnection() private readonly connection: Connection,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(CustomerAssignRecordEntity.name)
    private customerAssignRecordModel: Model<CustomerAssignRecordEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly loggerService: TlsService
  ) {}

  async assignAsync(userId: string) {
    const qrcode = await this.assignStaffToUser(userId)
    return qrcode
  }

  // 分配客服给新用户
  async assignStaffToUser(userId: string) {
    const staff = await this.userModel.findOne({
      _id: new Types.ObjectId(userId)
    })
    if (staff?.customerId) {
      const qrCodeCache = await this.cacheManager.get(
        getCustomerQrRedisKey(staff?.customerId.toString())
      )
      if (qrCodeCache) {
        return `${process.env.OSS_DOWNLOAD_URL}/${qrCodeCache}`
      }
      //已分配客服
      const customer = await this.adminModel.findOne({
        _id: new Types.ObjectId(staff.customerId)
      })
      await this.cacheManager.set(
        getCustomerQrRedisKey(staff?.customerId.toString()),
        customer.qrCode,
        60 * 60 * 24
      )
      return `${process.env.OSS_DOWNLOAD_URL}/${customer.qrCode}`
    }

    const session = await this.connection.startSession()
    session.startTransaction()
    try {
      // 获取最后一个分配的用户
      const lastAssignedUser = await this.customerAssignRecordModel
        .findOne()
        .sort({ createdAt: -1 })

      // 获取下一个客服
      const nextStaff = await this.getNextStaff(
        lastAssignedUser ? lastAssignedUser.customerId.toString() : null
      )
      if (!nextStaff) {
        throw new Error('获取客服失败')
      }
      // 创建新用户并分配客服
      await this.customerAssignRecordModel.create(
        [
          {
            userId: new Types.ObjectId(userId),
            customerId: new Types.ObjectId(nextStaff._id)
          }
        ],
        { session }
      )

      await this.userModel.updateOne(
        {
          _id: new Types.ObjectId(userId)
        },
        {
          customerId: new Types.ObjectId(nextStaff._id)
        },
        { session }
      )

      //自己创建的团队
      const members = await this.memberModel
        .find({
          userId: new Types.ObjectId(userId),
          roles: { $in: [TeamRoleNames.MASTER] }
        })
        .select('teamId')
        .lean()
      if (members) {
        const teamIds = members.map((item) => new Types.ObjectId(item.teamId))

        await this.teamModel.updateMany(
          {
            _id: { $in: teamIds }
          },
          {
            customerId: new Types.ObjectId(nextStaff._id)
          },
          { session }
        )
      }

      // 提交事务
      await session.commitTransaction()
      return `${process.env.OSS_DOWNLOAD_URL}/${nextStaff.qrCode}`
    } catch (error) {
      // 如果出错，回滚事务
      await session.abortTransaction()
      await this.loggerService.error(null, '分配客服失败', { error: error })
    } finally {
      await session.endSession()
    }
  }

  // 获取下一个客服
  async getNextStaff(lastAssignedStaffId: string) {
    const staff = await this.adminModel
      .find({ role: AdminRole.Customer, status: 1 })
      .sort({ createdAt: -1 }) // 按顺序取活跃的客服
    if (!staff.length) {
      throw new ForbiddenException('没有可用的客服人员')
    }

    // 如果是第一次分配, 或者已经分配了所有客服, 从第一个客服开始
    if (!lastAssignedStaffId) {
      return staff[0]
    }

    // 根据上一个分配的客服ID，找下一个客服
    const lastAssignedIndex = staff.findIndex(
      (staff) => staff._id.toString() === lastAssignedStaffId.toString()
    )
    // 如果没有找到上一个分配的客服（可能是下架了），从第一个客服开始
    const nextStaffIndex = lastAssignedIndex === -1 ? 0 : (lastAssignedIndex + 1) % staff.length
    return staff[nextStaffIndex]
  }
}
