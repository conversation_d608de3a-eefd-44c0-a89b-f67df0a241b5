import { Controller, Get, Inject, Query, Res } from '@nestjs/common'
import { FastifyReply } from 'fastify'
import { nanoid } from 'nanoid'
import { AccountAuthService } from './account-auth.service'

@Controller(['vd/auth', 'vt/auth', 'vp/auth'])
export class AccountAuthController {

  constructor(private readonly service: AccountAuthService) {
  }

  @Get('url')
  async getAuthorizationUrl(@Query('platform') platform: string, @Query('teamId') teamId: string, @Query('userId') userId: string): Promise<{
    authorizationUrl: string,
    state: string
  }> {
    return await this.service.getAuthorizationUrl(platform, teamId, userId)
  }

  /**
   * 账号授权页面的中转地址
   * @param to base64编码后的平台授权页面地址(需要将 redirect_url 设为本服务的 callback 地址)
   * @param res
   */
  @Get('redirect')
  async authorizationRedirect(@Query('to') to: string, @Res() res: FastifyReply) {
    const url = Buffer.from(to, 'base64').toString('utf-8')
    res.redirect(url, 302)
  }

  /**
   * 平台侧授权回调地址
   * @param code
   * @param state
   * @param res
   */
  @Get('callback')
  async authorizationCallback(@Query('code') code: string, @Query('state') state: string, @Res() res: FastifyReply) {
    console.log('authorizationCallback', code, state)

    const nonce = nanoid()

    try {
      await this.service.authorization(code, state)

      res
        // .header('Content-Security-Policy', `script-src 'self' 'nonce-${nonce}';`)
        .type('text/html')
        .status(200).send(`
      <html lang="zh-CN">
      <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>授权成功</title>
      </head>
      <body>
      <script nonce="${nonce}" type="text/javascript">
        // window.opener?.accountAuthorized('aaaaa')
        window.close();
      </script>
      <noscript>您的账号授权成功, 请关闭此页面</noscript>
      <p>您的账号授权成功, 请关闭此页面</p>
      </body>
      </html>
      `)
    } catch (err) {
      // this.logger.error(err)

      res
        // .header('Content-Security-Policy', `script-src 'self' 'nonce-${nonce}';`)
        .type('text/html')
        .status(500).send(`
      <html lang="zh-CN">
      <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title>授权失败</title>
      </head>
      <body>
      <p>${err.message}</p>
      </body>
      </html>
      `)
    }
  }
}
