import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  UserEntity,
  TeamEntity,
  MemberEntity,
  OrderEntity,
  TrafficBillingEntity,
  OpenPlatformApplicationEntity
} from '@yxr/mongo'
import { DataIsolationService } from '../../../common/services/data-isolation.service'
import {
  CreateGatewayUserRequestDto,
  CreateOpenPlatformGatewayUserRequestDto,
  UpdateGatewayUserRequestDto,
  GetGatewayUsersRequestDto,
  GatewayUserResponseDto,
  GatewayUsersListResponseDto,
  GetAppUsersRequestDto,
  GetAppTeamsRequestDto,
  AppUserResponseDto,
  AppTeamResponseDto,
  AppUsersListResponseDto,
  AppTeamsListResponseDto,
  OrderResponseDto,
  GetOrdersRequestDto,
  OrdersListResponseDto
} from '../dto/gateway-user.dto'
import { customAlphabet, nanoid } from 'nanoid'
import { MemberStatusEnum, TeamFeatures, TeamRoleNames, UserType } from 'packages/common'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { RootConfigMap } from '@yxr/config'
import { ConfigService } from '@nestjs/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { UserUtils } from '@yxr/common'
import crypto from 'crypto'

@Injectable()
export class GatewayUserService extends DataIsolationService {
  private readonly logger = new Logger(GatewayUserService.name)

  nanoidCode = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 6)

  nanoPhone = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 11)

  hashPassword(password: string) {
    const salt = crypto.randomBytes(16).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex')
    return { salt, hash }
  }

  constructor(
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @InjectModel(OrderEntity.name)
    private orderModel: Model<OrderEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(REQUEST) request: FastifyRequest
  ) {
    super(request)
  }

  /**
   * 创建Gateway用户
   * 优化逻辑：
   * 1. 当存在latestTeamId时：将用户加入现有团队，设置为管理员角色
   * 2. 当不存在latestTeamId时：为用户创建新团队，设置为团队主人
   */
  async createUser(createUserDto: CreateGatewayUserRequestDto): Promise<GatewayUserResponseDto> {
    const { uniqueId, teamId } = createUserDto

    const nickName = UserUtils.generateRandomNickName()
    const avatar = UserUtils.generateRandomAvatarUrl()
    const phone = this.nanoPhone()

    const session = await this.userModel.db.startSession()
    session.startTransaction()

    try {
      let targetTeamId: Types.ObjectId
      let isNewTeam = false

      // 条件判断：处理团队分配逻辑
      if (teamId) {
        // 验证现有团队是否存在且用户有权限访问
        const teamFilter = this.applyDataIsolation({ _id: new Types.ObjectId(teamId) })
        const existingTeam = await this.teamModel.findOne(teamFilter, null, { session })

        if (!existingTeam) {
          throw new NotFoundException('指定团队不存在或无权限访问')
        }

        // 检查团队成员数量限制
        if (existingTeam.memberCount >= existingTeam.memberCountLimit) {
          throw new BadRequestException('团队成员数量已达上限')
        }

        targetTeamId = new Types.ObjectId(teamId)
        this.logger.log(`用户将加入现有团队: ${teamId}`)
      } else {
        // 创建新团队
        const code = this.nanoidCode()
        const newTeamData = {
          name: '未命名团队',
          logo: 'avatars/team-default.png',
          code: code,
          accountCountLimit: TeamFeatures.DefaultAccountCountLimit,
          accountCount: 0,
          memberCountLimit: TeamFeatures.DefaultMemberCountLimit,
          memberCount: 1, // 初始成员数为1（创建者）
          enabled: true,
          isVip: false,
          isDeleted: false
        }

        // 应用数据隔离到团队数据
        const isolatedTeamData = this.applyDataIsolationForCreate(newTeamData)
        const [newTeam] = await this.teamModel.create([isolatedTeamData], { session })

        targetTeamId = newTeam._id
        isNewTeam = true
        this.logger.log(`为用户创建新团队: ${newTeam._id}`)
      }

      // 准备用户数据
      let userData: Partial<UserEntity> = {
        phone,
        nickName,
        latestTeamId: targetTeamId, // 正确设置团队ID
        avatar,
        registrationSource: 'open_platform_app',
        openUniqueId: uniqueId
      }

      // 应用数据隔离
      userData = this.applyDataIsolationForCreate(userData)

      // 创建用户
      const [newUser] = await this.userModel.create([userData], { session })

      // 创建团队成员关系 - 统一设置为管理员角色
      const memberRole = isNewTeam ? TeamRoleNames.MASTER : TeamRoleNames.ADMIN
      await this.memberModel.create<MemberEntity>(
        [
          {
            userId: newUser._id,
            teamId: targetTeamId,
            roles: [memberRole], // 统一设置为管理员级别角色
            remark: nickName,
            accounts: [],
            status: MemberStatusEnum.Joined
          }
        ],
        { session }
      )

      // 更新团队成员统计（仅当加入现有团队时）
      if (!isNewTeam) {
        await this.teamModel.findByIdAndUpdate(
          targetTeamId,
          { $inc: { memberCount: 1 } },
          { session }
        )
      }

      await session.commitTransaction()

      this.logger.log(
        `Gateway用户创建成功: ${phone}, ID: ${newUser._id}, ` +
          `团队: ${targetTeamId}, 角色: ${memberRole}, 新团队: ${isNewTeam}`
      )

      return this.formatUserResponse(newUser)
    } catch (error) {
      await session.abortTransaction()
      this.logger.error(`Gateway用户创建失败: ${error.message}`, error.stack)

      // 根据错误类型抛出更具体的异常
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error
      }
      throw new BadRequestException('用户创建失败，请稍后重试')
    } finally {
      await session.endSession()
    }
  }

  /**
   * 创建OpenPlatform Gateway用户（用户名+密码方式）
   * 使用OpenPlatformAccess权限控制
   */
  async createOpenPlatformUser(
    createUserDto: CreateOpenPlatformGatewayUserRequestDto
  ): Promise<GatewayUserResponseDto> {
    const { applicationId, phone, password, nickname, teamId } = createUserDto
    const { session } = this.request

    // 验证用户权限
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建用户')
    }

    // 验证应用权限
    const application = await this.applicationModel
      .findOne({
        _id: new Types.ObjectId(applicationId),
        userId: new Types.ObjectId(session.userId)
      })
      .lean()

    if (!application) {
      throw new ForbiddenException('无权限访问该应用')
    }

    // 生成唯一的手机号（使用用户名作为基础）
    const nickName = UserUtils.generateRandomNickName()
    const avatar = UserUtils.generateRandomAvatarUrl()

    // 检查手机号是否已存在（在该应用下）
    const existingUser = await this.userModel
      .findOne({
        phone,
        source: 'open_platform_app',
        sourceAppId: applicationId
      })
      .lean()

    if (existingUser) {
      throw new BadRequestException('用户已存在')
    }

    const dbSession = await this.userModel.db.startSession()
    let targetTeamId: Types.ObjectId
    let isNewTeam = false

    try {
      dbSession.startTransaction()

      // 处理团队逻辑
      if (teamId) {
        // 验证指定的团队是否存在且属于该应用
        const team = await this.teamModel
          .findOne({
            _id: new Types.ObjectId(teamId),
            source: 'open_platform_app',
            sourceAppId: applicationId,
            isDeleted: false
          })
          .session(dbSession)

        if (!team) {
          throw new NotFoundException('指定的团队不存在或无权限访问')
        }
        targetTeamId = team._id
      } else {
        // 创建新团队
        const code = this.generateTeamCode()
        const newTeamData = {
          name: '未命名团队',
          logo: 'avatars/team-default.png',
          code: code,
          accountCountLimit: TeamFeatures.DefaultAccountCapacityLimit,
          accountCount: 0,
          memberCountLimit: TeamFeatures.DefaultMemberCountLimit,
          memberCount: 1,
          enabled: true,
          isVip: false,
          isDeleted: false,
          interestCount: 0,
          source: 'open_platform_app',
          sourceAppId: applicationId,
          openPlatformUserId: new Types.ObjectId(session.userId)
        }

        const [newTeam] = await this.teamModel.create([newTeamData], { session: dbSession })
        targetTeamId = newTeam._id
        isNewTeam = true
        this.logger.log(`为OpenPlatform用户创建新团队: ${newTeam._id}`)
      }

      // 加密密码
      const { salt, hash: hashedPassword } = this.hashPassword(password)

      // 准备用户数据
      const userData: Partial<UserEntity> = {
        phone,
        password: hashedPassword,
        salt,
        nickName: nickName,
        avatar,
        latestTeamId: targetTeamId,
        source: 'open_platform_app',
        sourceAppId: applicationId,
        openPlatformUserId: new Types.ObjectId(session.userId)
      }

      // 创建用户
      const [newUser] = await this.userModel.create([userData], { session: dbSession })

      // 创建团队成员关系
      const memberRole = isNewTeam ? TeamRoleNames.MASTER : TeamRoleNames.ADMIN
      const memberData = {
        userId: newUser._id,
        teamId: targetTeamId,
        roles: [memberRole],
        accounts: [],
        status: MemberStatusEnum.Joined
      }

      await this.memberModel.create([memberData], { session: dbSession })

      // 更新团队成员数
      await this.teamModel.updateOne(
        { _id: targetTeamId },
        { $inc: { memberCount: 1 } },
        { session: dbSession }
      )

      await dbSession.commitTransaction()

      this.logger.log(
        `OpenPlatform Gateway用户创建成功: ${nickName}, ID: ${newUser._id}, ` +
          `应用: ${applicationId}, 团队: ${targetTeamId}, 角色: ${memberRole}, 新团队: ${isNewTeam}`
      )

      return this.formatUserResponse(newUser)
    } catch (error) {
      await dbSession.abortTransaction()
      this.logger.error(`OpenPlatform Gateway用户创建失败: ${error.message}`, error.stack)

      // 根据错误类型抛出更具体的异常
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException ||
        error instanceof ForbiddenException
      ) {
        throw error
      }
      throw new BadRequestException('用户创建失败，请稍后重试')
    } finally {
      await dbSession.endSession()
    }
  }

  /**
   * 更新Gateway用户
   */
  async updateUser(
    userId: string,
    updateUserDto: UpdateGatewayUserRequestDto
  ): Promise<GatewayUserResponseDto> {
    const { nickName, avatar, latestTeamId, channelCode } = updateUserDto

    // 构建查询条件（应用数据隔离）
    const filter = this.applyDataIsolation({ _id: new Types.ObjectId(userId) })

    // 查找用户
    const user = await this.userModel.findOne(filter)
    if (!user) {
      throw new NotFoundException('用户不存在或无权限访问')
    }

    // 如果更新团队ID，验证团队是否存在且有权限访问
    if (latestTeamId) {
      const teamFilter = this.applyDataIsolation({ _id: new Types.ObjectId(latestTeamId) })
      const team = await this.teamModel.findOne(teamFilter)
      if (!team) {
        throw new NotFoundException('团队不存在或无权限访问')
      }
    }

    // 准备更新数据
    const updateData: Partial<UserEntity> = {}
    if (nickName !== undefined) updateData.nickName = nickName
    if (avatar !== undefined) updateData.avatar = avatar
    if (latestTeamId !== undefined) updateData.latestTeamId = new Types.ObjectId(latestTeamId)
    if (channelCode !== undefined) updateData.channelCode = channelCode

    // 更新用户
    const updatedUser = await this.userModel.findOneAndUpdate(filter, updateData, { new: true })

    if (!updatedUser) {
      throw new NotFoundException('用户更新失败')
    }

    this.logger.log(`Gateway用户更新成功: ${updatedUser.phone}, ID: ${userId}`)

    return this.formatUserResponse(updatedUser)
  }

  /**
   * 删除Gateway用户
   */
  async deleteUser(userId: string): Promise<void> {
    // 构建查询条件（应用数据隔离）
    const filter = this.applyDataIsolation({ _id: new Types.ObjectId(userId) })

    // 查找并删除用户
    const user = await this.userModel.findOneAndDelete(filter)
    if (!user) {
      throw new NotFoundException('用户不存在或无权限访问')
    }

    this.logger.log(`Gateway用户删除成功: ${user.phone}, ID: ${userId}`)
  }

  /**
   * 获取Gateway用户详情
   */
  async getUserById(userId: string): Promise<GatewayUserResponseDto> {
    // 构建查询条件（应用数据隔离）
    const filter = this.applyDataIsolation({ _id: new Types.ObjectId(userId) })

    const user = await this.userModel.findOne(filter)
    if (!user) {
      throw new NotFoundException('用户不存在或无权限访问')
    }

    return this.formatUserResponse(user)
  }

  /**
   * 查询Gateway用户列表
   */
  async getUsers(query: GetGatewayUsersRequestDto): Promise<GatewayUsersListResponseDto> {
    const { phone, nickName, channelCode, page = 1, size = 10 } = query

    // 构建基础查询条件
    let filter: any = {}

    if (phone) {
      filter.phone = { $regex: phone, $options: 'i' }
    }
    if (nickName) {
      filter.nickName = { $regex: nickName, $options: 'i' }
    }
    if (channelCode) {
      filter.channelCode = channelCode
    }

    // 应用数据隔离
    filter = this.applyDataIsolation(filter)

    const skip = (page - 1) * size

    // 执行查询
    const [users, totalSize] = await Promise.all([
      this.userModel.find(filter).sort({ createdAt: -1 }).skip(skip).limit(size).lean(),
      this.userModel.countDocuments(filter)
    ])

    const data = users.map((user) => this.formatUserResponse(user))

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  async generateAccessToken(userId: string, teamId: string) {
    const user = await this.userModel.findById(userId)
    if (!user) {
      throw new NotFoundException('用户不存在')
    }

    // 判断用户是否是开放平台用户
    if (!user.openPlatformUserId) {
      throw new BadRequestException('用户不是开放平台用户')
    }

    // 判断用户是否在团队中
    const member = await this.memberModel.findOne({
      userId: new Types.ObjectId(userId),
      teamId: new Types.ObjectId(teamId)
    })
    if (!member) {
      throw new BadRequestException('用户不属于指定团队')
    }

    return await this.generateAuthorization(user, userId, teamId)
  }

  /**
   * 格式化用户响应数据
   */
  private formatUserResponse(user: any): GatewayUserResponseDto {
    return {
      id: user._id.toString(),
      nickName: user.nickName,
      uniqueId: user.openUniqueId,
      teamId: user.latestTeamId?.toString() || '',
      avatar: user.avatar,
      channelCode: user.channelCode,
      registrationSource: user.registrationSource,
      source: user.source || 'gateway',
      sourceAppId: user.sourceAppId,
      openPlatformUserId: user.openPlatformUserId?.toString(),
      createdAt: user.createdAt?.getTime() || Date.now(),
      updatedAt: user.updatedAt?.getTime() || Date.now()
    }
  }

  /**
   * 获取应用下的用户列表
   */
  async getAppUsers(appId: string, query: GetAppUsersRequestDto): Promise<AppUsersListResponseDto> {
    const { page = 1, size = 10 } = query

    // 构建基础查询条件
    let filter: any = {
      source: 'open_platform_app',
      sourceAppId: appId
    }

    const skip = (page - 1) * size

    // 执行查询
    const [users, totalSize] = await Promise.all([
      this.userModel.find(filter).sort({ createdAt: -1 }).skip(skip).limit(size).lean(),
      this.userModel.countDocuments(filter)
    ])

    const data = users.map((user) => this.formatAppUserResponse(user))

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data
    }
  }

  /**
   * 获取应用下的团队列表
   */
  async getAppTeams(appId: string, query: GetAppTeamsRequestDto): Promise<AppTeamsListResponseDto> {
    const { teamName, isVip, page = 1, size = 10 } = query

    // 构建基础查询条件
    let filter: any = {
      source: 'open_platform_app',
      sourceAppId: appId,
      isDeleted: false
    }

    if (teamName) {
      filter.$or = [
        { name: { $regex: teamName, $options: 'i' } },
        { code: { $regex: teamName, $options: 'i' } }
      ]
    }
    if (isVip !== undefined) {
      filter.isVip = isVip
    }

    const skip = (page - 1) * size

    // 使用聚合查询获取团队信息和统计数据
    const aggregationPipeline = [
      { $match: filter },
      {
        // 关联成员信息统计实际成员数
        $lookup: {
          from: 'memberentities',
          localField: '_id',
          foreignField: 'teamId',
          as: 'members'
        }
      },
      {
        // 添加计算字段
        $addFields: {
          // 实际成员数（只统计已加入的成员）
          actualMemberCount: {
            $size: {
              $filter: {
                input: '$members',
                cond: { $eq: ['$$this.status', 'joined'] }
              }
            }
          }
        }
      },
      {
        // 移除不需要的关联数据
        $project: {
          members: 0
        }
      },
      { $sort: { createdAt: -1 as const } }
    ]

    // 执行聚合查询
    const [teamsResult, totalSize] = await Promise.all([
      this.teamModel.aggregate([...aggregationPipeline, { $skip: skip }, { $limit: size }]),
      this.teamModel.aggregate([{ $match: filter }, { $count: 'total' }])
    ])

    const teams = teamsResult || []
    const total = totalSize.length > 0 ? totalSize[0].total : 0

    const data = teams.map((team) => this.formatAppTeamResponse(team))

    return {
      totalSize: total,
      page,
      size,
      totalPage: Math.ceil(total / size),
      data
    }
  }

  /**
   * 查询应用下的订单列表
   */
  async getOrders(appId: string, query: GetOrdersRequestDto): Promise<OrdersListResponseDto> {
    const {
      orderNo,
      code,
      paymentStartTime,
      paymentEndTime,
      orderType,
      page = 1,
      size = 10
    } = query

    const application = await this.applicationModel.findById(appId)

    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 构建基础查询条件 - 只查询当前开放平台用户的订单
    let filter: any = {
      sourceAppId: appId
    }

    // 订单号查询（支持模糊搜索）
    if (orderNo) {
      filter.orderNo = { $regex: orderNo, $options: 'i' }
    }

    // 团队Code过滤 - 需要先通过code查找团队ID
    if (code) {
      const team = await this.teamModel.findOne({ code: code }).lean()
      if (team) {
        filter.teamId = team._id
      } else {
        // 如果找不到对应的团队，返回空结果
        return {
          totalSize: 0,
          page,
          size,
          totalPage: 0,
          data: []
        }
      }
    }

    // 支付时间范围查询
    if (paymentStartTime || paymentEndTime) {
      filter.payTime = {}
      if (paymentStartTime) {
        filter.payTime.$gte = new Date(paymentStartTime)
      }
      if (paymentEndTime) {
        filter.payTime.$lte = new Date(paymentEndTime)
      }
    }

    // 订单类型过滤
    if (orderType) {
      filter.orderType = orderType
    }

    const skip = (page - 1) * size

    try {
      // 使用聚合查询关联团队信息
      const aggregationPipeline = [
        { $match: filter },
        {
          $lookup: {
            from: 'teamentities',
            localField: 'teamId',
            foreignField: '_id',
            as: 'teamInfo'
          }
        },
        {
          $addFields: {
            teamName: {
              $ifNull: [{ $arrayElemAt: ['$teamInfo.name', 0] }, '未知团队']
            },
            code: {
              $ifNull: [{ $arrayElemAt: ['$teamInfo.code', 0] }, '']
            }
          }
        },
        {
          $project: {
            teamInfo: 0 // 移除临时的 teamInfo 字段
          }
        },
        { $sort: { createdAt: -1 as const } }
      ]

      // 获取总数
      const countPipeline = [{ $match: filter }, { $count: 'total' }]

      const [countResult, orders] = await Promise.all([
        this.orderModel.aggregate(countPipeline),
        this.orderModel.aggregate([...aggregationPipeline, { $skip: skip }, { $limit: size }])
      ])

      const totalSize = countResult.length > 0 ? countResult[0].total : 0
      const data = orders.map((order) =>
        this.formatOrderResponse({
          ...order,
          applicationName: application.name,
          appId: application.appId
        })
      )

      return {
        totalSize,
        page,
        size,
        totalPage: Math.ceil(totalSize / size),
        data
      }
    } catch (error) {
      this.logger.error(`查询订单失败: ${error.message}`, error.stack)
      throw new ForbiddenException(`查询订单失败: ${error.message}`)
    }
  }

  /**
   * 格式化订单响应数据
   */
  private formatOrderResponse(order: any): OrderResponseDto {
    // 处理团队信息，聚合查询后团队名称直接在 teamName 字段中
    const teamId = order.teamId.toString()
    const teamName = order.teamName || '未知团队'

    return {
      id: order._id.toString(),
      orderNo: order.orderNo,
      orderStatus: order.orderStatus,
      orderType: order.orderType,
      resourceType: order.resourceType,
      teamId,
      teamName,
      applicationName: order.applicationName,
      appId: order.appId,
      code: order.code,
      userId: order.userId.toString(),
      totalAmount: order.totalAmount || 0,
      payableAmount: order.payableAmount || 0,
      payAmount: order.payAmount || 0,
      accountCapacity: order.accountCapacity,
      trafficCount: order.trafficCount,
      duration: order.duration,
      startTime: order.startTime?.getTime(),
      endTime: order.endTime?.getTime(),
      trafficExpiredAt: order.trafficExpiredAt?.getTime(),
      payTime: order?.payTime ? order.payTime.getTime() : 0,
      remark: order.remark,
      createdAt: order.createdAt?.getTime() || Date.now(),
      updatedAt: order.updatedAt?.getTime() || Date.now()
    }
  }

  /**
   * 格式化应用用户响应数据
   */
  private formatAppUserResponse(user: any): AppUserResponseDto {
    return {
      id: user._id.toString(),
      phone: user.phone,
      nickName: user.nickName,
      avatar: user.avatar,
      teamId: user.latestTeamId?.toString() || '',
      channelCode: user.channelCode,
      createdAt: user.createdAt?.getTime() || Date.now(),
      updatedAt: user.updatedAt?.getTime() || Date.now()
    }
  }

  /**
   * 格式化应用团队响应数据
   */
  private formatAppTeamResponse(team: any): AppTeamResponseDto {
    return {
      id: team._id.toString(),
      name: team.name,
      code: team.code,
      logo: team.logo,
      // 使用聚合查询计算的实际成员数，如果没有则使用团队记录的成员数
      memberCount:
        team.actualMemberCount !== undefined ? team.actualMemberCount : team.memberCount || 0,
      // 使用聚合查询计算的流量统计，保留两位小数
      networkTraffic: Math.round((team.networkTraffic || 0) * 100) / 100,
      useNetworkTraffic: Math.round((team.useNetworkTraffic || 0) * 100) / 100,
      isVip: team.isVip || false,
      expiredAt: team.expiredAt?.getTime(),
      accountCapacityLimit: team.accountCapacityLimit || 0,
      accountCapacity: team.accountCapacity || 0,
      createdAt: team.createdAt?.getTime() || Date.now(),
      updatedAt: team.updatedAt?.getTime() || Date.now()
    }
  }

  private async generateAuthorization(user: UserEntity, userId: string, teamId: string) {
    const userIdKey = `session:ui-${userId}:open-platform`

    // 用户登录场景中 request 中是取不到 token 的, 需要使用配对的缓存键来获取
    const oldToken = await this.cacheManager.store.client.get(userIdKey)
    if (oldToken) {
      // 向已登录该账号发其他设备登录消息
      this.logger.debug(`10秒后移除旧 token: ${oldToken}`)
      await this.cacheManager.store.client.expire(oldToken, 10) // 延期删除老的token
    }

    // 生成新 token
    const token = nanoid()
    const tokenKey = `session:au-${token}`
    const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')

    await Promise.all([
      this.cacheManager.set(userIdKey, tokenKey, overdueToken),
      this.cacheManager.set(
        tokenKey,
        {
          user: user, // 保留和旧设计不合理的兼容, 当 request 中的 user 可以安全删除时, 可以删除
          userId: userId,
          teamId: teamId
        },
        overdueToken
      )
    ])

    return { token, expiresIn: overdueToken / 1000 }
  }

  /**
   * 生成团队代码
   */
  private generateTeamCode(): string {
    const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const nanoidCustom = customAlphabet(alphabet, 6)
    return nanoidCustom()
  }

  /**
   * 生成唯一的手机号（基于用户名和应用ID）
   */
  private generateUniquePhone(username: string, applicationId: string): string {
    // 使用用户名和应用ID的哈希值生成唯一的11位手机号
    const crypto = require('crypto')
    const hash = crypto.createHash('md5').update(`${username}_${applicationId}`).digest('hex')

    // 取前10位数字，前面加上1构成11位手机号
    const digits = hash.replace(/[^0-9]/g, '').substring(0, 10)
    const phone = '1' + digits.padEnd(10, '0')

    return phone
  }
}
