import { Module } from '@nestjs/common'
import {
  ContentMongoose,
  ContentStatisticMongoose,
  PlatformAccountMongoose,
  PlatformAccountOverviewMongoose,
  PlatformAccountSummaryMongoose,
  TaskMongoose,
  TeamComponentMongoose,
  TeamMongoose,
  TrafficBillingMongoose
} from '@yxr/mongo'
import { HuoshanModule } from '@yxr/huoshan'
import { OpenPlatformService } from './open-platform.service'
import { OpenPlatformController } from './open-platform.controller'
import { CommonModule } from '@yxr/common'
import { WxThirdPlatformModule } from '../wx-third-platform/wx-third-platform.module'

@Module({
  imports: [
    TaskMongoose,
    ContentMongoose,
    TeamMongoose,
    TrafficBillingMongoose,
    TeamComponentMongoose,
    PlatformAccountOverviewMongoose,
    PlatformAccountMongoose,
    PlatformAccountSummaryMongoose,
    ContentStatisticMongoose,
    HuoshanModule,
    CommonModule,
    WxThirdPlatformModule
  ],
  controllers: [OpenPlatformController],
  providers: [OpenPlatformService],
  exports: [OpenPlatformService]
})
export class OpenPlatformModule {}
