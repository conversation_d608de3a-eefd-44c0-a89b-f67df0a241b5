import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { PublishChannel } from '@yxr/common'

export enum MediaType {
  /// <summary>
  /// 视频
  /// </summary>
  Video = 1
}

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class ContentEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: String,
    unique: true,
    required: true
  })
  taskSetId: string

  @Prop({
    type: Types.ObjectId,
    index: true,
    unique: false,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  superId?: string

  @Prop({
    type: String,
    required: false
  })
  superLockId?: string

  @Prop({
    type: String,
    required: false
  })
  platformAvatar?: string

  @Prop({
    type: String,
    required: true
  })
  platformName: string

  @Prop({
    type: String,
    required: true
  })
  platformAccountName: string

  @Prop({
    type: String,
    required: false
  })
  phone: string

  @Prop({
    type: String,
    required: false
  })
  nickName: string

  @Prop({
    type: Types.ObjectId,
    required: true
  })
  platformAccountId: Types.ObjectId

  @Prop({
    type: String,
    required: false
  })
  title?: string

  @Prop({
    type: String,
    required: false
  })
  cover?: string

  @Prop({
    type: String
  })
  desc?: string

  @Prop({
    type: Types.Map
  })
  descRich?: unknown

  @Prop({
    type: String
  })
  videoUrl?: string

  @Prop({
    type: String
  })
  content: string

  @Prop({
    type: String,
    default: null
  })
  publishType: string

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isAppContent: boolean

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isDraft?: boolean

  @Prop({
    type: String
  })
  video?: string

  @Prop({
    type: Number,
    required: true,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  videoSize?: number

  @Prop({
    type: Types.Map
  })
  publishArgs: unknown

  @Prop({
    type: String,
    enum: PublishChannel,
    default: PublishChannel.local
  })
  publishChannel: PublishChannel

  /**
   * 微信草稿箱ID
   */
  @Prop({
    type: String,
    required: false
  })
  mediaId?: string

  /**
   * 是否群发 0不群发 1群发
   */
  @Prop({
    type: Number,
    default: 0,
    required: false
  })
  sendAll?: number

  @Prop({
    type: Number,
    default: 0,
    required: false
  })
  sendIgnoreReprint?: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const ContentSchema: ModelDefinition = {
  name: ContentEntity.name,
  schema: SchemaFactory.createForClass(ContentEntity)
}

export const ContentMongoose = MongooseModule.forFeature([ContentSchema])
