import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { SocketModule } from './socket.module';
import { Logger } from '@nestjs/common';
import { RedisIoAdapter } from './events.redis-adapter';
import { socketConfig } from '@yxr/proto'

async function bootstrap() {
 
  const logger = new Logger('Socket')
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(SocketModule, {
    transport: Transport.GRPC,
    options: {
      url: '0.0.0.0:3002',
      package: 'socket',
      protoPath: socketConfig.protoPath
    }
  })

  const redisIoAdapter = new RedisIoAdapter(app)
  await redisIoAdapter.connectToRedis()
  app.useWebSocketAdapter(redisIoAdapter)

  await app.listen()

  logger.log('Socket service is running')
  logger.log(`gRPC:${socketConfig.url}`)

}
bootstrap();
