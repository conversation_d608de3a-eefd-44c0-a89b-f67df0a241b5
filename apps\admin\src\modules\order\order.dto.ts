import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min, ValidateIf } from 'class-validator'
import { OrderSource, OrderStatus, OrderType, PayType } from '@yxr/common'
import { Type } from 'class-transformer'

export class orderResponseCreateOrder {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  orderNo: string
}

export class OrderResponseCreateOrderDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: orderResponseCreateOrder
  })
  data: orderResponseCreateOrder
}

export class OrderRequestCreateOrderDTO {
  @ApiProperty({
    description: '权益包id',
    required: true,
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '团队id',
    required: true,
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @ValidateIf((o) => !o.days) // 当 month 为false时，days必填
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: 1,
    required: true
  })
  @ValidateIf((o) => !o.month) // 当 month 为false时，days必填
  @IsNotEmpty()
  @IsNumber()
  @Min(1,{message: '自定义天数必须大于0'})
  days: number

  @ApiProperty({
    description: '实付金额 isPay为true时必填且大于0',
    type: Number,
    example: 100,
    required: true
  })
  @ValidateIf((o) => o.isPay === true) // 当 isPay 为true时，payAmount必须大于0
  @IsNotEmpty()
  @IsNumber()
  @Min(0.01,{message: '实付金额必须大于0'})
  payAmount: number

  @ApiProperty({
    type: Boolean,
    required: true,
    description: '是否赠送',
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class OrderStatusRequestDTO {
  @ApiProperty({
    type: Number,
    description: '实付金额',
    required: true
  })
  @IsNumber()
  payAmount: number

  @ApiProperty({
    type: String,
    description: '备注',
    required: false
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class UpgradeOrderRequest {
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '实付金额 isPay为true时必填且大于0',
    type: Number,
    example: 100,
    required: true
  })
  @ValidateIf((o) => o.isPay === true) // 当 isPay 为true时，payAmount必须大于0
  @IsNotEmpty()
  @IsNumber()
  @Min(0.01,{message: '实付金额必须大于0'})
  payAmount: number

  @ApiProperty({
    type: Boolean,
    required: false
  })
  @IsNotEmpty()
  @IsBoolean()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class VipRenewDTO {
  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @ValidateIf((o) => !o.days) // 当 month 为false时，days必填
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    description: '自定义天数',
    example: 1,
    required: true
  })
  @ValidateIf((o) => !o.month) // 当 month 为false时，days必填
  @IsNotEmpty()
  @IsNumber()
  @Min(1,{message: '自定义天数必须大于0'})
  days: number

  @ApiProperty({
    description: '实付金额 isPay为true时必填且大于0',
    type: Number,
    example: 100,
    required: true
  })
  @ValidateIf((o) => o.isPay === true) // 当 isPay 为true时，payAmount必须大于0
  @IsNotEmpty()
  @IsNumber()
  @Min(0.01,{message: '实付金额必须大于0'})
  payAmount: number

  @ApiProperty({
    type: Boolean
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class GiftVipDTO {
  @ApiProperty({
    type: String,
    required: true
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '赠送天数',
    example: 1,
    required: true
  })
  @IsNumber()
  giftDays: number

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class OrderListRequestDTO {
  @ApiProperty({
    type: String,
    required: false,
    description: '订单号',
    example: '202501020856EMW1JCKX'
  })
  @IsString()
  @IsOptional()
  orderNo: string

  @ApiProperty({
    type: String,
    required: false,
    description: '团队名称和编号',
    example: '0SHZ3D或坚强的油条'
  })
  @IsString()
  @IsOptional()
  teamName: string

  @ApiProperty({
    type: String,
    required: false,
    description: '手机号',
    example: '18800000888'
  })
  @IsString()
  @IsOptional()
  phone: string

  @ApiProperty({
    type: String,
    required: false,
    example: OrderStatus.Pending,
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    enum: OrderStatus
  })
  @IsString()
  @IsOptional()
  orderStatus: OrderStatus

  @ApiProperty({
    type: String,
    required: false,
    example: OrderType.Create,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)',
    enum: OrderType
  })
  @IsString()
  @IsOptional()
  orderType: OrderType

  @ApiProperty({
    type: String,
    required: false,
    example: PayType.Alipay,
    description:
      '支付方式(wechatPay:微信支付,alipay:支付宝支付,corporateTransfer:对公转账,other:其他)',
    enum: PayType
  })
  @IsString()
  @IsOptional()
  payType: PayType

  @ApiProperty({
    type: String,
    required: false,
    description: '创建开始时间'
  })
  @IsString()
  @IsOptional()
  createStartTime: string

  @ApiProperty({
    type: String,
    required: false,
    description: '创建结束时间'
  })
  @IsString()
  @IsOptional()
  createEndTime: string

  @ApiProperty({
    type: Number,
    required: false,
    description: '支付开始时间'
  })
  @IsString()
  @IsOptional()
  payStartTime: number

  @ApiProperty({
    type: Number,
    required: false,
    description: '支付结束时间'
  })
  @IsString()
  @IsOptional()
  payEndTime: number

  @ApiProperty({
    type: String,
    required: false,
    description: '归属人ID'
  })
  @IsString()
  @IsOptional()
  customerId: string

  @ApiProperty({
    type: Number,
    required: false,
    description: '实付金额大于'
  })
  @IsNumber()
  @IsOptional()
  payAmount: number

  @ApiProperty({
    type: String,
    required: false,
    description: '渠道码'
  })
  @IsString()
  @IsOptional()
  channelCode: string

  @ApiProperty({
    type: Number,
    required: false,
    description: '销售类型(1:新购,2:复购)'
  })
  @IsOptional()
  @Type(() => Number)
  salesType: number

  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10
}

export class OrderListDetail {
  @ApiProperty({
    type: String,
    example: '订单号'
  })
  orderNo: string

  @ApiProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiProperty({
    type: String,
    example: '团队编号'
  })
  code: string

  @ApiProperty({
    type: String,
    example: '手机号'
  })
  phone: string

  @ApiProperty({
    type: String,
    enum: OrderStatus,
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    example: OrderStatus.Pending
  })
  orderStatus: OrderStatus

  @ApiProperty({
    type: String,
    enum: OrderType,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)',
    example: OrderType.Create
  })
  orderType: OrderType

  @ApiProperty({
    type: String,
    enum: PayType,
    description:
      '支付方式(wechatPay:微信支付,alipay:支付宝支付,corporateTransfer:对公转账,other:其他)',
    example: PayType.Alipay
  })
  payType: PayType

  @ApiProperty({
    type: Number,
    example: '支付金额'
  })
  totalAmount: number

  @ApiProperty({
    type: Number,
    example: '0（赠送订单） 1新购 2复购'
  })
  salesType: number

  @ApiProperty({
    type: String,
    description: '客服名称'
  })
  customerName: string

  @ApiProperty({
    description: '订单创建时间',
    type: Number
  })
  createdAt: number

  @ApiProperty({
    description: '支付时间',
    type: Number
  })
  payTime: number
}

export class orderResponseListOrder {
  @ApiResponseProperty({
    type: [OrderListDetail]
  })
  data: OrderListDetail[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiProperty({
    type: Number,
    description: '订单列表总支付金额',
    example: 1
  })
  totalAmount: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class OrderResponseOrderListDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: orderResponseListOrder
  })
  data: orderResponseListOrder
}

export class OrderDetailResponse {
  @ApiProperty({
    type: String,
    example: '订单号'
  })
  orderNo: string

  @ApiProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiProperty({
    type: String,
    example: '团队编号'
  })
  code: string

  @ApiProperty({
    type: String,
    enum: OrderStatus,
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    example: OrderStatus.Pending
  })
  orderStatus: OrderStatus

  @ApiProperty({
    type: String,
    enum: OrderType,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)',
    example: OrderType.Create
  })
  orderType: OrderType

  @ApiProperty({
    type: String,
    enum: OrderSource,
    description: '订单来源(online:线上,system:系统)',
    example: OrderSource.Online
  })
  orderSource: OrderSource

  @ApiProperty({
    type: String,
    enum: PayType,
    description:
      '支付方式(wechatPay:微信支付,alipay:支付宝支付,corporateTransfer:对公转账,other:其他)',
    example: PayType.WechatPay
  })
  payType: PayType

  @ApiProperty({
    type: Number,
    example: '支付金额'
  })
  totalAmount: number

  @ApiProperty({
    type: Number,
    example: '应付金额'
  })
  payableAmount: number

  @ApiProperty({
    type: Number,
    example: '实付金额'
  })
  payAmount: number

  @ApiProperty({
    description: '订单创建时间',
    type: Number
  })
  createdAt: number

  @ApiProperty({
    description: '支付时间',
    type: Number
  })
  payTime?: number

  @ApiProperty({
    description: '到期时间',
    type: Number
  })
  expiredAt: number

  @ApiProperty({
    description: '权益包数量',
    type: Number
  })
  interestCount: number

  @ApiProperty({
    description: '购买月份',
    type: Number
  })
  vipMonth: number

  @ApiProperty({
    description: '赠送月份',
    type: Number
  })
  freeMonth: number

  @ApiProperty({
    description: '赠送天数',
    type: Number
  })
  days: number

  @ApiProperty({
    description: '是否是免费',
    type: Boolean
  })
  isFree: boolean

  @ApiProperty({
    type: String,
    example: '创建人'
  })
  creatorName: string

  @ApiProperty({
    type: String,
    description: '备注'
  })
  remark: string
}

export class OrderDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderDetailResponse
  })
  data: OrderDetailResponse
}

class OrderInterestVipOftenDTO {
  @ApiProperty({
    type: Number,
    description: '月份数',
    example: 100
  })
  mount: number

  @ApiProperty({
    type: Number,
    description: '赠送月份数',
    example: 100
  })
  present: number
}

export class OrderInterestData {
  @ApiProperty({
    type: String,
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  id: string

  /** @deprecated 已废弃, 请使用 platformAccountCapacity 代替 */
  @ApiProperty({
    type: Number,
    example: 100,
    deprecated: true
  })
  platformAccountCount: number  // 变更为账号点数

  @ApiProperty({
    type: Number,
    example: 100
  })
  platformAccountCapacity: number

  @ApiProperty({
    type: Number,
    description: '素材库容量大小',
    example: 10
  })
  capacityLimit: number

  @ApiProperty({
    type: Boolean,
    description: '是否app发布',
    example: false
  })
  appPublish: boolean

  @ApiProperty({
    type: Number,
    example: 10
  })
  memberCount: number

  @ApiProperty({
    type: Number,
    example: 100
  })
  price: number

  @ApiProperty({
    type: [OrderInterestVipOftenDTO]
  })
  vipOften: OrderInterestVipOftenDTO[]
}

export class OrderInterestResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInterestData
  })
  data: OrderInterestData
}

export class orderPriceRequestDTO {
  @ApiProperty({
    enum: OrderType,
    description: '订单类型(create:开通,upgrade:升级,renew:续费,gift赠送)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderType)
  orderType: OrderType

  @ApiProperty({
    description: '团队id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    description: '天数数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  days: number
}

export class OrderPriceResponseDto {
  @ApiProperty({
    type: Number,
    description: '产品金额',
    example: 1
  })
  orderAmount: number

  @ApiProperty({
    type: Number,
    description: '折扣金额',
    example: 1
  })
  discountAmount: number

  @ApiProperty({
    type: Number,
    description: '续费到期日期',
    example: '1735783254'
  })
  expiredTime: number

  @ApiProperty({
    type: String,
    description: '升级公式',
    example: '10*3/30*1=1'
  })
  tips: string

  @ApiProperty({
    type: String,
    description: '升级公式(中文 )',
    example: '升级权益包金额/30*剩余时长'
  })
  tipsCn: string
}

export class OrderPriceDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderPriceResponseDto
  })
  data: OrderPriceResponseDto
}
