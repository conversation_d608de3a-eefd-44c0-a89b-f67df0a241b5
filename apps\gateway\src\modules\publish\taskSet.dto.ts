import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  isString,
  IsString,
  ValidateIf
} from 'class-validator'
import { Transform, Type } from 'class-transformer'
import { PublishChannel, TaskSetStatusEnum, UploadStatusEnum } from '@yxr/common'
import { TaskDetailResponse } from './task.dto'
import { TaskCloudPushDTO } from './taskCloudPush.dto'

export class PublishAccountDTO {
  @ApiProperty({
    description: '媒体账号id',
    example: '67fb2f1735eeb3cf31db3d65',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '媒体账号不能为空' })
  platformAccountId: string

  @ApiProperty({
    description: '封面地址(上传到天翼云)',
    example: '67fb2f1735eeb3cf31db3d65',
    required: false
  })
  @ValidateIf((o) => o._parent?.publishType === 'video')
  @IsNotEmpty({ message: '媒体账号不能为空' })
  coverKey: string

  @ApiProperty({
    description: '视频地址(上传到天翼云)',
    example: '67fb2f1735eeb3cf31db3d65',
    required: false
  })
  @ValidateIf((o) => o._parent?.publishType === 'video')
  @IsNotEmpty({ message: '媒体账号不能为空' })
  videoKey: string

  @ApiProperty({
    description: '视频地址(上传到天翼云)',
    example: '67fb2f1735eeb3cf31db3d65',
    required: false
  })
  @IsOptional()
  /**
   * 超级编导视频id
   */
  superId?: string

  @ApiProperty({
    description: '视频地址(上传到天翼云)',
    example: '67fb2f1735eeb3cf31db3d65',
    required: false
  })
  @IsOptional()
  /**
   * 超级编导锁id
   */
  superLockId?: string
}

export class TaskSetCreateRequest {
  @ApiProperty({
    description: '任务集ID',
    example: '',
    required: true
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty({ message: '任务集ID不能为空' })
  taskSetId: string

  @ApiProperty({
    description: '任务集发布类型',
    example: 'verticalVideo',
    required: true
  })
  @IsOptional()
  publishType: string

  @ApiProperty({
    type: String,
    description: '发布类型',
    required: false
  })
  @IsString()
  @IsOptional()
  mediaType: string

  @ApiProperty({
    description: '任务集封面存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsOptional()
  coverKey: string

  @ApiProperty({
    description: '描述',
    example: '任务集描述',
    required: false
  })
  @IsOptional()
  desc: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  @IsOptional()
  descRich: unknown

  @ApiProperty({
    type: [String],
    description: '账号媒体ID数组',
    example: "['抖音号,视频号']",
    required: true
  })
  @IsArray({ message: '发布平台格式不匹配' })
  @IsNotEmpty({ message: '发布平台不能为空' })
  platforms: string[]

  @ApiProperty({
    type: [PublishAccountDTO],
    description: '账号媒体数组信息',
    required: true
  })
  @IsArray({ message: '账号媒体格式不匹配' })
  @IsNotEmpty({ message: '账号媒体不能为空' })
  @IsOptional()
  platformAccounts: PublishAccountDTO[]

  @ApiProperty({
    type: Number,
    description: '是否定时任务',
    default: false
  })
  @IsOptional()
  isTimed: number

  @ApiProperty({
    type: Boolean,
    description: '是否为草稿',
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isDraft: boolean

  @ApiProperty({
    description: '发布参数(动态结构, 服务端不做任何处理, 会原封不动的下发给客户端用于发布)',
    example: {
      title: '测试标题',
      content: '测试内容',
      cover: 'https://xxxxx.png',
      tags: ['测试标签'],
      category: '测试分类',
      video: {
        url: 'https://xxxxx.mp4',
        cover: 'https://xxxxx.png'
      }
    },
    required: false
  })
  @IsOptional()
  publishArgs: unknown

  @ApiProperty({
    type: String,
    enum: Object.values(PublishChannel),
    description: '发布渠道本机发布:local,云端:cloud',
    example: PublishChannel.local,
    required: false
  })
  @IsOptional()
  @IsEnum(PublishChannel)
  publishChannel: PublishChannel = PublishChannel.local

  @ApiProperty({
    type: Boolean,
    required: false,
    description: '是否app发布, app发布为true',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isAppContent: boolean = false
}

export class TaskSetUpdateRequest {
  @ApiProperty({
    type: String,
    enum: Object.values(UploadStatusEnum),
    description: 'waiting 等待中 doing上传或下载中 successful上传或下载成功 failed上传或下载失败',
    example: UploadStatusEnum.Waiting,
    required: true
  })
  @IsNotEmpty({ message: '上传状态不能为空' })
  @IsEnum(UploadStatusEnum)
  uploadStatus: UploadStatusEnum

  @ApiProperty({
    type: String,
    description: '上传进度传输，客户端显示',
    example: '11/32',
    required: false
  })
  @IsNotEmpty({ message: '上传状态不能为空' })
  @IsOptional()
  progress: string

  @ApiProperty({
    description: '任务集封面存储Key',
    example: 't-66b311ed3d4f465e690c0805/as/zvtt_wiqvpv644mvx4l4v',
    required: false
  })
  @IsOptional()
  coverKey: string

  @ApiProperty({
    type: [PublishAccountDTO],
    description: '账号媒体数组信息',
    required: true
  })
  @IsArray({ message: '账号媒体格式不匹配' })
  @IsNotEmpty({ message: '账号媒体不能为空' })
  @IsOptional()
  platformAccounts: PublishAccountDTO[]

  @ApiProperty({
    description: '发布参数(动态结构, 服务端不做任何处理, 会原封不动的下发给客户端用于发布)',
    example: {
      title: '测试标题',
      content: '测试内容',
      cover: 'https://xxxxx.png',
      tags: ['测试标签'],
      category: '测试分类',
      video: {
        url: 'https://xxxxx.mp4',
        cover: 'https://xxxxx.png'
      }
    },
    required: false
  })
  @IsOptional()
  publishArgs: unknown
}

export class TaskSetListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '查询此时间戳之后是否有新消息 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  time: number = 0

  @ApiProperty({
    type: Number,
    description: '发布时间开始时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishStartTime: number = 0

  @ApiProperty({
    type: Number,
    description: '发布时间结束时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishEndTime: number = 0

  @ApiProperty({
    title: '任务集状态',
    description:
      '任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功), allfailed(全部发布失败)',
    type: String,
    enum: TaskSetStatusEnum,
    example: TaskSetStatusEnum.Publishing
  })
  @IsOptional()
  taskSetStatus?: TaskSetStatusEnum

  @ApiProperty({
    type: String,
    description: '发布类型',
    required: false
  })
  @IsOptional()
  publishType: string

  @ApiProperty({
    type: [String],
    description: '运营人用户ID',
    example: ['66a8b11e5f8d230bc3140f1c', '66a9e2bf52e68e8300599a4f'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => (isString(value) ? [value] : value))
  userIds: string[]
}

export class MemberTaskSetListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '查询此时间戳之后是否有新消息 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  time: number = 0
}

export class ExportTaskSetsRequest {
  @ApiProperty({
    type: Number,
    description: '发布时间开始时间 <默认 0>',
    example: 0,
    required: true
  })
  @IsNotEmpty({ message: '发布时间开始时间不能为空' })
  @Type(() => Number)
  publishStartTime: number = 0

  @ApiProperty({
    type: Number,
    description: '发布时间结束时间 <默认 0>',
    example: 0,
    required: true
  })
  @IsNotEmpty({ message: '发布时间结束时间不能为空' })
  @Type(() => Number)
  publishEndTime: number = 0

  @ApiProperty({
    type: String,
    description: '发布类型',
    required: true
  })
  @IsNotEmpty({ message: '发布类型不能为空' })
  publishType: string

  @ApiProperty({
    type: String,
    description: '运营人用户ID(管理员角色不需传入，传入也会被忽略)',
    example: '66a8b11e5f8d230bc3140f1c',
    required: false
  })
  @IsOptional()
  @IsString()
  userId: string
}

export class TaskSetDetailResponse {
  @ApiProperty({
    description: '任务集的taskIdentityId',
    required: true
  })
  id: string

  @ApiProperty({
    type: [String],
    example: "['抖音号,视频号']",
    description: '账号媒体',
    required: true
  })
  platforms: string[]

  @ApiResponseProperty({
    type: String,
    example: '发布人'
  })
  nickName: string

  @ApiResponseProperty({
    type: String,
    example: '发布人Id'
  })
  userId: string

  @ApiResponseProperty({
    type: Boolean,
    example: 'true'
  })
  isAppContent: boolean

  @ApiProperty({
    description: '封面',
    example: 'https://xxxxxx.png',
    required: false
  })
  @IsOptional()
  coverKey: string

  @ApiProperty({
    description: '封面',
    example: 'https://xxxxxx.png',
    required: false
  })
  @IsOptional()
  coverUrl: string

  @ApiProperty({
    description: '描述',
    example: '视频描述',
    required: false
  })
  desc: string

  @ApiProperty({
    description: '结构化描述信息（可以是富文本）',
    example: [
      { text: '刚刚堵塞斑斑驳驳河钢股份', type: 'text' },
      { text: '#个vv', type: 'tag' }
    ],
    required: false
  })
  descRich: unknown

  @ApiProperty({
    title: '任务集状态',
    description:
      '任务集状态: publishing(发布中), allsuccessful(全部发布成功), partialsuccessful(部分发布成功), allfailed(全部发布失败)',
    type: String,
    enum: TaskSetStatusEnum,
    example: TaskSetStatusEnum.Publishing
  })
  taskSetStatus: TaskSetStatusEnum

  @ApiProperty({
    type: String,
    description: '编辑类型'
  })
  publishType: string

  @ApiProperty({
    description: '任务集创建时间',
    required: true
  })
  createdAt: number

  @ApiProperty({
    type: Number,
    description: '任务数'
  })
  taskCount: number

  @ApiProperty({
    type: Boolean,
    description: '是否是草稿'
  })
  isDraft: boolean

  @ApiProperty({
    type: Number,
    description: '任务总耗时(秒)'
  })
  totalTaskDuration: number

  @ApiProperty({
    type: Number,
    description: '平均任务耗时(秒)'
  })
  avTaskDuration: number

  @ApiProperty({
    type: Number,
    description: '发布中数'
  })
  penddingCount: number

  @ApiProperty({
    type: Number,
    description: '成功数'
  })
  successCount: number

  @ApiProperty({
    type: Number,
    description: '失败数'
  })
  failedCount: number

  @ApiProperty({
    type: Number,
    description: '是否定时任务',
    default: false
  })
  isTimed: number

  @ApiProperty({
    type: String,
    description: '发布渠道类型local:本机发布, cloud:云端发布'
  })
  publishChannel: PublishChannel
}

export class TaskSetListResponse {
  @ApiResponseProperty({
    type: [TaskSetDetailResponse]
  })
  data: TaskSetDetailResponse[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

// 任务集的详情
export class TaskSetDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TaskSetDetailResponse
  })
  data: TaskSetDetailResponse
}

// 任务集下的任务列表
export class TaskDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TaskDetailResponse]
  })
  data: TaskDetailResponse[]
}

export class TaskSetListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TaskSetListResponse
  })
  data: TaskSetListResponse
}

export class TaskCloudPushResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TaskCloudPushDTO]
  })
  data: TaskCloudPushDTO[]
}

export class TaskSetCreateResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: String
  })
  data: string
}
