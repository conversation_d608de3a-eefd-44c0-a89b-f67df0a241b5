import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { Types } from 'mongoose'
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator'
import { BaseResponseDTO } from 'apps/gateway/src/common/dto/BaseResponseDTO'

export class CategorysDTO {
  @ApiProperty({
    type: String,
    description: '分类ID',
    example: '3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '分类名称',
    example: '分类名称'
  })
  name: string

  @ApiProperty({
    type: Types.ObjectId,
    description: '分组ID',
    example: '3941b7c06fa2'
  })
  groupId: Types.ObjectId

  @ApiProperty({
    type: String,
    description: '平台名称',
    example: '平台名称'
  })
  platformName: string

  @ApiProperty({
    description: '分类参数，前端显示的一级二级分类结构'
  })
  categoryArgs: unknown
}

export class PostCategorysDTO {
  @ApiProperty({
    type: String,
    description: '分类名称',
    example: '分类名称'
  })
  name: string

  @ApiProperty({
    type: String,
    description: '平台名称',
    example: '平台名称'
  })
  platformName: string

  @ApiProperty({
    description: '分类参数，前端显示的一级二级分类结构'
  })
  categoryArgs: unknown
}

export class CategorysGroupDTO {
  @ApiProperty({
    type: String,
    description: '分组ID',
    example: '3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '分组名称',
    example: '分组一'
  })
  name: String
}

export class CategorysListDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [CategorysDTO]
  })
  data: CategorysDTO[]
}

export class PostCategorysRequest {
  @ApiProperty({
    type: [PostCategorysDTO],
    required: true,
    description: '分类列表'
  })
  @IsNotEmpty({ message: '分类列表不能为空' })
  categorys: PostCategorysDTO[]

  @ApiProperty({
    type: String,
    required: true,
    description: '分组ID',
    example: '9413-3941b7c06fa2'
  })
  @IsString()
  @IsNotEmpty({ message: '分组不能为空' })
  groupId: string
}

/**
 * 分组
 */
export class PostCategoryGroupsRequest {
  @ApiProperty({
    type: String,
    example: '分组',
    required: true
  })
  @MaxLength(8, { message: '分组名称长度太长。不得多于 $constraint1 个字符' })
  @IsNotEmpty({ message: '分组名称不能为空' })
  @IsString()
  name: string
}

export class CategorysDetailResponse {
  @ApiResponseProperty({
    type: String,
    example: '9413-3941b7c06fa2'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '我的话题'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '9413-3941b7c06fa2'
  })
  groupId: string
}

export class CategorysDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: CategorysDetailResponse
  })
  data: CategorysDetailResponse
}

export class CategorysListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: CategorysListDTO
  })
  data: CategorysListDTO
}

export class CategorysGroupResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: CategorysGroupDTO
  })
  data: CategorysGroupDTO
}

export class TopicsGroupListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [CategorysGroupDTO]
  })
  data: CategorysGroupDTO[]
}
