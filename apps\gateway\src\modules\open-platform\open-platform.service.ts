import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common'
import { PatchStatusRequest } from '../publish/task.dto'
import { Model, Types, UpdateQuery } from 'mongoose'
import {
  ContentEntity,
  ContentStatisticEntity,
  PlatformAccountEntity,
  PlatformAccountOverviewEntity,
  PlatformAccountSummaryEntity,
  TaskEntity,
  TeamComponentEntity,
  TeamEntity,
  TrafficBillingEntity
} from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import {
  ContentType,
  EventNames,
  PlatformNameEnum,
  PlatformType,
  PublishChannel,
  StageStatus,
  StatisticCommonService,
  TaskAuditStatusEvent,
  TaskStages
} from '@yxr/common'
import { TlsService } from '@yxr/huoshan'
import { EventEmitter2 } from '@nestjs/event-emitter'
import axios from 'axios'
import { PutPlatformAccountOverviewRequest } from '../platform/platform-account.dto'
import { WxPublishService } from '../wx-third-platform/wx-publish.service'
import dayjs from 'dayjs'
import { eventKey } from '../order/order.event'
import { contentStatisticEventEmitter } from '../platform/content.event'
import { ContentStatisticDataDTO, ReportContentRequest } from '../platform/content.dto'

@Injectable()
export class OpenPlatformService {
  constructor(
    private readonly loggerService: TlsService,
    private readonly statisticCommonService: StatisticCommonService,
    private readonly wxPublishService: WxPublishService,
    private eventEmitter: EventEmitter2,
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(TrafficBillingEntity.name)
    private trafficBillingModel: Model<TrafficBillingEntity>,
    @InjectModel(TeamComponentEntity.name)
    private teamComponentModel: Model<TeamComponentEntity>,
    @InjectModel(PlatformAccountOverviewEntity.name)
    private platformAccountOverviewModel: Model<PlatformAccountOverviewEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformAccountSummaryEntity.name)
    private platformAccountSummaryModel: Model<PlatformAccountSummaryEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>
  ) {}

  async patchStatus(currentTeamId: string, taskId: string, request: PatchStatusRequest) {
    const task = await this.taskModel
      .findOne({
        taskId: taskId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('taskSetId contentId publishChannel publishType stages stageStatus')

    if (!task) {
      throw new NotFoundException('任务不存在')
    }
    if (task.stages === TaskStages.Success || task.stageStatus === StageStatus.Fail) {
      console.log('任务已结束,不再更新')
      return
    }
    if (task.stages !== request.stages || task.stageStatus !== request.stageStatus) {
      await this.loggerService.info(null, '任务状态变更日志', {
        taskId: taskId,
        task_stages: task.stages,
        task_stageStatus: task.stageStatus,
        request_stages: request.stages,
        request_stageStatus: request.stageStatus
      })
    }

    const update: UpdateQuery<TaskEntity> = {}
    if (request.documentId) {
      update.documentId = request.documentId
    }
    if (request.publishId) {
      update.publishId = request.publishId
    }
    if (request.openUrl) {
      update.openUrl = request.openUrl
    }
    if (request.mediaType) {
      update.mediaType = request.mediaType
    }
    if (request.stages !== TaskStages.NotFount) {
      //未找到作品只更新信息
      if (request.stages) {
        update.stages = request.stages
      }
      if (request.stageStatus) {
        update.stageStatus = request.stageStatus
      }
    }

    update.errorMessage = request.errorMessage ?? ''
    update.stageTime = new Date()
    await this.taskModel.updateOne(
      {
        taskId: taskId,
        teamId: new Types.ObjectId(currentTeamId)
      },
      update
    )

    const content = await this.contentModel
      .findOne({
        _id: new Types.ObjectId(task.contentId)
      })
      .select('teamId videoSize userId isAppContent superId superLockId')

    //云发布视频流量扣除
    if (
      task.publishType == 'video' &&
      (task.publishChannel == PublishChannel.cloud || content.isAppContent === true) &&
      request.stages !== TaskStages.Upload &&
      request.stageStatus == StageStatus.Success
    ) {
      const charged = await this.trafficBillingModel.findOne({
        taskId: taskId
      })
      if (!charged) {
        await this.teamModel.updateOne(
          {
            _id: new Types.ObjectId(content.teamId)
          },
          {
            $inc: {
              useNetworkTraffic: content.videoSize
            }
          }
        )
        //流量扣费记录
        await this.trafficBillingModel.create({
          teamId: new Types.ObjectId(content.teamId),
          userId: new Types.ObjectId(content.userId),
          taskId: taskId,
          useNetworkTraffic: content.videoSize
        })
        await this.loggerService.info(null, '云发布流量扣费成功', {
          taskId: taskId,
          useNetworkTraffic: content.videoSize
        })
      }
    }

    if (update.stages == TaskStages.Upload || update.stages == TaskStages.Push) {
      // 触发任务状态变更事件
      await this.eventEmitter.emitAsync(
        EventNames.TaskAuditStatusChangedEvent,
        new TaskAuditStatusEvent(currentTeamId, task.taskSetId)
      )

      if (content.superId && content.superLockId) {
        if (request.stageStatus === StageStatus.Fail) {
          const superdir = await this.teamComponentModel
            .findOne({
              teamId: new Types.ObjectId(content.teamId),
              name: 'superdir'
            })
            .lean()

          const res = await axios.post(
            `${process.env.SUPER_API_URL}/v1/openapi/matrix/video/release`,
            {
              lock_source: 'douyin_yixiaoer',
              video_ids: [Number(content.superId)]
            },
            {
              headers: {
                Token: superdir?.componentArgs?.token
              }
            }
          )

          await this.loggerService.info(null, '超级编导-失败 Fail', {
            taskId: taskId,
            task_stages: task.stages,
            task_stageStatus: task.stageStatus,
            request_stages: request.stages,
            request_stageStatus: request.stageStatus,
            res: JSON.stringify(res.data),
            superId: content.superId
          })
        } else if (
          update.stages === TaskStages.Push &&
          request.stageStatus === StageStatus.Success
        ) {
          const superdir = await this.teamComponentModel
            .findOne({
              teamId: new Types.ObjectId(content.teamId),
              name: 'superdir'
            })
            .lean()

          const res = await axios.post(
            `${process.env.SUPER_API_URL}/v1/openapi/matrix/video/publish`,
            {
              lock_source: 'douyin_yixiaoer',
              video_id: Number(content.superId)
            },
            {
              headers: {
                Token: superdir?.componentArgs?.token
              }
            }
          )

          await this.loggerService.info(null, '超级编导-成功 Success', {
            taskId: taskId,
            task_stages: task.stages,
            task_stageStatus: task.stageStatus,
            request_stages: request.stages,
            request_stageStatus: request.stageStatus,
            res: JSON.stringify(res.data),
            superId: content.superId
          })
        }
      }
    }
  }

  /**
   * 账号数据上报
   * @param platformAccountId
   * @param body
   */
  async putPlatformAccountOverviews(
    currentTeamId: string,
    platformAccountId: string,
    body: PutPlatformAccountOverviewRequest
  ) {
    // const { session } = this.request
    const platformAccountOverview = await this.platformAccountOverviewModel.findOne({
      platformAccountId: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId)
    })
    if (!platformAccount) {
      return
    }

    const overview = await this.statisticCommonService.getAccountOverview(
      platformAccount.platformName,
      body.overviewData
    )
    if (
      platformAccount.platformName === PlatformNameEnum.微信公众号 &&
      platformAccount.platformType === PlatformType.开放平台
    ) {
      const wxUserCumulate = await this.wxPublishService.getUserCumulate(
        currentTeamId,
        platformAccountId
      )
      if (wxUserCumulate.errcode === 0) {
        const firstItem = wxUserCumulate.list[0]
        body.overviewData = JSON.stringify({
          value: {
            dynamic: [
              {
                name: '总粉丝数',
                value: firstItem.cumulate_user || 0,
                list: []
              }
            ]
          },
          updateTime: firstItem.ref_date ? dayjs(firstItem.ref_date).valueOf() : dayjs().valueOf()
        })
        overview.fansTotal = firstItem.cumulate_user || 0

        contentStatisticEventEmitter.emit(eventKey, {
          teamId: currentTeamId,
          platformAccountId: platformAccount._id,
          platformName: platformAccount.platformName,
          contentData: null
        })
      } else {
        throw new ForbiddenException('获取微信公众号粉丝数据失败')
      }
    }

    if (platformAccountOverview) {
      //更新
      await this.platformAccountOverviewModel.updateOne(
        {
          _id: platformAccountOverview._id
        },
        {
          overviewData: body.overviewData,
          platformName: platformAccount.platformName
        }
      )
    } else {
      //新增
      await this.platformAccountOverviewModel.create({
        teamId: new Types.ObjectId(currentTeamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        overviewData: body.overviewData,
        platformName: platformAccount.platformName
      })
    }

    const summary = await this.platformAccountSummaryModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      platformAccountId: new Types.ObjectId(platformAccountId)
    })
    if (summary) {
      //更新
      await this.platformAccountSummaryModel.updateOne(
        {
          teamId: new Types.ObjectId(currentTeamId),
          platformAccountId: new Types.ObjectId(platformAccountId)
        },
        {
          $set: {
            platformName: platformAccount.platformName,
            ...overview
          }
        }
      )
    } else {
      //新增
      await this.platformAccountSummaryModel.create({
        teamId: new Types.ObjectId(currentTeamId),
        platformAccountId: new Types.ObjectId(platformAccountId),
        platformName: platformAccount.platformName,
        ...overview
      })
    }
  }

  async postContents(currentTeamId: string, body: ReportContentRequest) {
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(body.platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    if (
      platformAccount.platformName === PlatformNameEnum.微信公众号 &&
      platformAccount.platformType === PlatformType.开放平台
    ) {
      await this.wxContentStatisticUpdate(
        currentTeamId,
        platformAccount._id.toString(),
        platformAccount.platformName
      )
    } else {
      const dataStatistic = body.contentStatisticsData as ContentStatisticDataDTO[]
      if (dataStatistic.length > 0) {
        contentStatisticEventEmitter.emit(eventKey, {
          teamId: currentTeamId,
          platformAccountId: platformAccount._id,
          platformName: platformAccount.platformName,
          contentData: dataStatistic
        })
      }
    }
  }

  /**
   * 统计非爬虫微信公众号内容数据
   * @param contentId
   * @returns
   */
  async wxContentStatisticUpdate(teamId: string, platformAccountId: string, platformName: string) {
    const result = await this.wxPublishService.getArticleTotal(teamId, platformAccountId)
    if (result.list.length <= 0) {
      return
    }
    const contentDatas = result.list
    const dataArr = []
    for (const item of contentDatas) {
      const parts = item.msgid.split('_')
      const mainId = parts[0]
      if (!dataArr[mainId]) {
        dataArr[mainId] = {
          publishId: mainId,
          publishTime: new Date(item.ref_date),
          title: item.title,
          contentType: ContentType.dynamic,
          share: item.share_count ?? 0,
          collect: item.add_to_fav_count ?? 0,
          read: item.int_page_read_count ?? 0
        }
      } else {
        dataArr[mainId].share += item.share_count
        dataArr[mainId].collect += item.add_to_fav_count
        dataArr[mainId].read += item.int_page_read_count
      }
    }
    if (dataArr.length > 0) {
      for (const element of dataArr) {
        const contentStatistic = await this.contentStatisticModel.findOne({
          publishId: element.publishId,
          teamId: new Types.ObjectId(teamId)
        })
        const task = await this.taskModel.findOne({
          teamId: new Types.ObjectId(teamId),
          publishId: element.publishId
        })
        const data: UpdateQuery<ContentStatisticEntity> = {
          platformName: platformName,
          publishId: element.publishId,
          publishTime: element.publishTime,
          contentType: element.contentType,
          title: element.title,
          publishUserId: task?.userId ? new Types.ObjectId(task?.userId) : null,
          read: element.read ? StatisticCommonService.convertToNumber(element.read) : 0,
          share: element.share ? StatisticCommonService.convertToNumber(element.share) : 0,
          collect: element.collect ? StatisticCommonService.convertToNumber(element.collect) : 0
        }
        if (contentStatistic) {
          //存在就只更新统计数据
          await this.contentStatisticModel.updateOne(
            {
              _id: contentStatistic._id
            },
            data
          )
        } else {
          data.teamId = new Types.ObjectId(teamId)
          data.platformAccountId = new Types.ObjectId(platformAccountId)
          await this.contentStatisticModel.create(data)
        }
      }
    }
  }
}
