import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, Length, IsNumber, Min, Matches } from 'class-validator'
import { Transform } from 'class-transformer'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformOrderResourceType, OrderStatus, OrderType } from '@yxr/common'

/**
 * 创建Gateway用户请求DTO（ApplicationAccess）
 */
export class CreateGatewayUserRequestDto {
  @ApiProperty({
    description: '第三方唯一id',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString({ message: 'uniqueId必须是字符串' })
  uniqueId?: string

  @ApiProperty({
    description: '默认团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString()
  teamId?: string
}

/**
 * 创建Gateway用户请求DTO（OpenPlatformAccess）
 */
export class CreateOpenPlatformGatewayUserRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty({ message: '应用ID不能为空' })
  @IsString({ message: '应用ID必须是字符串' })
  applicationId: string

  @ApiProperty({
    description: '账号',
    example: 'user_account_123',
    required: true
  })
  @IsString({ message: '账号格式不正确' })
  @IsNotEmpty({ message: '账号不能为空' })
  @Length(8, 20, { message: '账号长度必须在8-20位之间' })
  username: string

  @ApiProperty({
    description: '密码',
    example: 'password123'
  })
  @IsNotEmpty({ message: '密码不能为空' })
  @IsString({ message: '密码必须是字符串' })
  @Length(6, 32, { message: '密码长度必须在6-32位之间' })
  password: string

  @ApiProperty({
    description: '用户昵称',
    example: '测试用户',
    required: false
  })
  @IsOptional()
  @IsString({ message: '昵称必须是字符串' })
  @Length(1, 64, { message: '昵称长度必须在1-64位之间' })
  nickname?: string

  @ApiProperty({
    description: '默认团队ID',
    example: '507f1f77bcf86cd799439011',
    required: false
  })
  @IsOptional()
  @IsString()
  teamId?: string
}

/**
 * 更新Gateway用户请求DTO
 */
export class UpdateGatewayUserRequestDto {
  @ApiPropertyOptional({
    description: '用户昵称',
    example: '李四'
  })
  @IsOptional()
  @IsString({ message: '昵称必须是字符串' })
  @Length(1, 64, { message: '昵称长度必须在1-64位之间' })
  nickName?: string

  @ApiPropertyOptional({
    description: '用户头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  @IsOptional()
  @IsString({ message: '头像URL必须是字符串' })
  @Length(0, 64, { message: '头像URL长度不能超过64位' })
  avatar?: string

  @ApiPropertyOptional({
    description: '默认团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString()
  latestTeamId?: string

  @ApiPropertyOptional({
    description: '渠道码',
    example: 'CHANNEL002'
  })
  @IsOptional()
  @IsString({ message: '渠道码必须是字符串' })
  channelCode?: string
}

/**
 * 查询Gateway用户列表请求DTO
 */
export class GetGatewayUsersRequestDto {
  @ApiPropertyOptional({
    description: '手机号（模糊搜索）',
    example: '138'
  })
  @IsOptional()
  @IsString({ message: '手机号必须是字符串' })
  phone?: string

  @ApiPropertyOptional({
    description: '昵称（模糊搜索）',
    example: '张'
  })
  @IsOptional()
  @IsString({ message: '昵称必须是字符串' })
  nickName?: string

  @ApiPropertyOptional({
    description: '渠道码',
    example: 'CHANNEL001'
  })
  @IsOptional()
  @IsString({ message: '渠道码必须是字符串' })
  channelCode?: string

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Transform(({ value }) => parseInt(value) || 10)
  size?: number = 10
}

/**
 * Gateway用户响应DTO
 */
export class GatewayUserResponseDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '用户昵称',
    example: '张三'
  })
  nickName: string

  @ApiProperty({
    description: '唯一id',
    example: '507f1f77bcf86cd799439011'
  })
  uniqueId: string

  @ApiProperty({
    description: '默认团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiPropertyOptional({
    description: '用户头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  avatar?: string

  @ApiPropertyOptional({
    description: '渠道码',
    example: 'CHANNEL001'
  })
  channelCode?: string

  @ApiPropertyOptional({
    description: '注册来源',
    example: 'open_platform_app'
  })
  registrationSource?: string

  @ApiProperty({
    description: '用户来源',
    example: 'open_platform_app',
    enum: ['gateway', 'open_platform_app']
  })
  source: string

  @ApiPropertyOptional({
    description: '创建用户的应用appId',
    example: '68467e81b8009ce414a6133a'
  })
  sourceAppId?: string

  @ApiPropertyOptional({
    description: '关联的开放平台用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  openPlatformUserId?: string

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * Gateway用户列表响应DTO
 */
export class GatewayUsersListResponseDto {
  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 10
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 10
  })
  totalPage: number

  @ApiProperty({
    description: '用户列表',
    type: [GatewayUserResponseDto]
  })
  data: GatewayUserResponseDto[]
}

/**
 * 创建用户响应DTO
 */
export class CreateGatewayUserResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: GatewayUserResponseDto,
    description: '创建的用户信息'
  })
  data: GatewayUserResponseDto
}

/**
 * 更新用户响应DTO
 */
export class UpdateGatewayUserResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: GatewayUserResponseDto,
    description: '更新后的用户信息'
  })
  data: GatewayUserResponseDto
}

/**
 * 生成Token请求DTO
 */
export class GenerateTokenRequestDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty({ message: '团队ID不能为空' })
  @IsString()
  teamId: string
}

/**
 * 应用用户列表请求DTO
 */
export class GetAppUsersRequestDto {
  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Transform(({ value }) => parseInt(value) || 10)
  size?: number = 10
}

/**
 * 应用团队列表请求DTO
 */
export class GetAppTeamsRequestDto {
  @ApiPropertyOptional({
    description: '团队名称（模糊搜索）',
    example: '测试团队'
  })
  @IsOptional()
  @IsString({ message: '团队名称必须是字符串' })
  teamName?: string

  @ApiPropertyOptional({
    description: 'VIP状态过滤',
    example: true
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true
    if (value === 'false') return false
    return value
  })
  isVip?: boolean

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Transform(({ value }) => parseInt(value) || 10)
  size?: number = 10
}

/**
 * 应用用户响应DTO
 */
export class AppUserResponseDto {
  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '手机号',
    example: '13800138000'
  })
  phone: string

  @ApiProperty({
    description: '用户昵称',
    example: '张三'
  })
  nickName: string

  @ApiPropertyOptional({
    description: '用户头像URL',
    example: 'https://example.com/avatar.jpg'
  })
  avatar?: string

  @ApiProperty({
    description: '默认团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiPropertyOptional({
    description: '渠道码',
    example: 'CHANNEL001'
  })
  channelCode?: string

  @ApiProperty({
    description: '注册时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '最后更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 应用团队响应DTO
 */
export class AppTeamResponseDto {
  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '团队名称',
    example: '测试团队'
  })
  name: string

  @ApiProperty({
    description: '团队代码',
    example: 'ABC123'
  })
  code: string

  @ApiPropertyOptional({
    description: '团队LOGO',
    example: 'https://example.com/logo.jpg'
  })
  logo?: string

  @ApiProperty({
    description: '当前团队成员数',
    example: 5
  })
  memberCount: number

  @ApiProperty({
    description: '团队总流量数（GB）',
    example: 100
  })
  networkTraffic: number

  @ApiProperty({
    description: '已使用流量数（GB）',
    example: 25
  })
  useNetworkTraffic: number

  @ApiProperty({
    description: 'VIP状态',
    example: true
  })
  isVip: boolean

  @ApiPropertyOptional({
    description: 'VIP过期时间',
    example: *************
  })
  expiredAt?: number

  @ApiProperty({
    description: '账号点数限制',
    example: 100
  })
  accountCapacityLimit: number

  @ApiProperty({
    description: '当前账号点数',
    example: 50
  })
  accountCapacity: number

  @ApiProperty({
    description: '创建时间',
    example: *************
  })
  createdAt: number

  @ApiProperty({
    description: '最后更新时间',
    example: *************
  })
  updatedAt: number
}

/**
 * 应用用户列表响应DTO
 */
export class AppUsersListResponseDto {
  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 10
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 10
  })
  totalPage: number

  @ApiProperty({
    description: '用户列表',
    type: [AppUserResponseDto]
  })
  data: AppUserResponseDto[]
}

/**
 * 应用团队列表响应DTO
 */
export class AppTeamsListResponseDto {
  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 10
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 10
  })
  totalPage: number

  @ApiProperty({
    description: '团队列表',
    type: [AppTeamResponseDto]
  })
  data: AppTeamResponseDto[]
}

/**
 * 订单查询请求DTO
 */
export class GetOrdersRequestDto {
  @ApiPropertyOptional({
    description: '订单号（精确匹配或模糊搜索）',
    example: '******************'
  })
  @IsOptional()
  @IsString({ message: '订单号必须是字符串' })
  orderNo?: string

  @ApiPropertyOptional({
    description: '团队code',
    example: '507f1f77bcf86cd799439011'
  })
  @IsOptional()
  @IsString({ message: '团队code必须是字符串' })
  code?: string

  @ApiPropertyOptional({
    description: '支付开始时间（时间戳）',
    example: ************0
  })
  @IsOptional()
  @IsNumber({}, { message: '支付开始时间必须是数字' })
  @Transform(({ value }) => parseInt(value))
  paymentStartTime?: number

  @ApiPropertyOptional({
    description: '支付结束时间（时间戳）',
    example: *************
  })
  @IsOptional()
  @IsNumber({}, { message: '支付结束时间必须是数字' })
  @Transform(({ value }) => parseInt(value))
  paymentEndTime?: number

  @ApiPropertyOptional({
    description: '订单类型',
    enum: OpenPlatformOrderResourceType,
    example: OpenPlatformOrderResourceType.AccountPoints
  })
  @IsOptional()
  @IsString({ message: '订单类型必须是字符串' })
  resourceType?: OpenPlatformOrderResourceType

  @ApiPropertyOptional({
    description: '页码',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码必须大于0' })
  @Transform(({ value }) => parseInt(value) || 1)
  page?: number = 1

  @ApiPropertyOptional({
    description: '每页数量',
    example: 10,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量必须大于0' })
  @Transform(({ value }) => parseInt(value) || 10)
  size?: number = 10
}

/**
 * 订单响应DTO
 */
export class OrderResponseDto {
  @ApiProperty({
    description: '订单ID',
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiProperty({
    description: '订单编号',
    example: '******************'
  })
  orderNo: string

  @ApiProperty({
    description: '订单状态',
    enum: OrderStatus,
    example: OrderStatus.Paid
  })
  orderStatus: OrderStatus

  @ApiProperty({
    description: '订单类型',
    enum: OrderType,
    example: OrderType.OpenPlatformAccountPoints
  })
  orderType: OrderType

  @ApiPropertyOptional({
    description: '资源类型',
    enum: OpenPlatformOrderResourceType,
    example: OpenPlatformOrderResourceType.AccountPoints
  })
  resourceType?: OpenPlatformOrderResourceType

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  teamId: string

  @ApiProperty({
    description: '团队名称',
    example: '我的团队'
  })
  teamName: string

  @ApiProperty({
    description: '团队code',
    example: '团队code'
  })
  code: string

  @ApiProperty({
    description: 'appId',
    example: '507f1f77bcf86cd799439011'
  })
  appId: string

  @ApiProperty({
    description: '应用名称',
    example: '应用名称'
  })
  applicationName: string

  @ApiProperty({
    description: '用户ID',
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiProperty({
    description: '订单金额',
    example: 100.00
  })
  totalAmount: number

  @ApiProperty({
    description: '应付金额',
    example: 100.00
  })
  payableAmount: number

  @ApiProperty({
    description: '实付金额',
    example: 100.00
  })
  payAmount: number

  @ApiPropertyOptional({
    description: '账号点数数量（仅账号点数订单）',
    example: 10
  })
  accountCapacity?: number

  @ApiPropertyOptional({
    description: '流量数量（GB，仅流量订单）',
    example: 100
  })
  trafficCount?: number

  @ApiPropertyOptional({
    description: '时长（月）',
    example: 3
  })
  duration?: number

  @ApiPropertyOptional({
    description: '订单开始时间（时间戳）',
    example: ************0
  })
  startTime?: number

  @ApiPropertyOptional({
    description: '订单结束时间（时间戳）',
    example: *************
  })
  endTime?: number

  @ApiPropertyOptional({
    description: '流量过期时间（时间戳，仅流量订单）',
    example: *************
  })
  trafficExpiredAt?: number

  @ApiPropertyOptional({
    description: '支付时间（时间戳）',
    example: ************0
  })
  payTime?: number

  @ApiPropertyOptional({
    description: '备注',
    example: '订单备注'
  })
  remark?: string

  @ApiProperty({
    description: '创建时间（时间戳）',
    example: ************0
  })
  createdAt: number

  @ApiProperty({
    description: '更新时间（时间戳）',
    example: ************0
  })
  updatedAt: number
}

/**
 * 订单列表响应DTO
 */
export class OrdersListResponseDto {
  @ApiProperty({
    description: '总数量',
    example: 100
  })
  totalSize: number

  @ApiProperty({
    description: '当前页码',
    example: 1
  })
  page: number

  @ApiProperty({
    description: '每页数量',
    example: 10
  })
  size: number

  @ApiProperty({
    description: '总页数',
    example: 10
  })
  totalPage: number

  @ApiProperty({
    description: '订单列表',
    type: [OrderResponseDto]
  })
  data: OrderResponseDto[]
}

/**
 * 创建OpenPlatform Gateway用户响应DTO
 */
export class CreateOpenPlatformGatewayUserResponseDto extends BaseResponseDTO {
  @ApiProperty({
    type: GatewayUserResponseDto
  })
  data: GatewayUserResponseDto
}