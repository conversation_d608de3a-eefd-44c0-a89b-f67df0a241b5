# 开放平台应用充值实体用户ID字段添加说明

## 概述

为了实现数据隔离，确保每个开放平台用户只能查看和管理自己创建的充值记录，在 `OpenPlatformApplicationRechargeEntity` 中添加了 `openPlatformUserId` 字段。

## 修改内容

### 1. Schema 修改

**文件**: `packages/mongo/lib/schemas/open_platform_application_recharge.schema.ts`

添加了新字段：
```typescript
/**
 * 开放平台用户ID - 用于数据隔离，确保每个开放平台用户只能访问自己的充值记录
 */
@Prop({
  type: Types.ObjectId,
  required: true,
  index: true,
  ref: 'OpenPlatformUserEntity'
})
openPlatformUserId: Types.ObjectId
```

**字段特性**：
- **类型**: `ObjectId`，引用 `OpenPlatformUserEntity`
- **必填**: `required: true`
- **索引**: `index: true` - 优化查询性能
- **引用**: `ref: 'OpenPlatformUserEntity'` - 支持 populate 操作

### 2. Service 层修改

**文件**: `apps/admin/src/modules/open-platform/services/recharge.service.ts`

#### 2.1 依赖注入修改
- 添加了 `@Inject(REQUEST)` 注入请求对象
- 支持获取当前用户会话信息

#### 2.2 创建充值记录逻辑修改
```typescript
// 获取开放平台用户ID
let openPlatformUserId: Types.ObjectId

if (userSession?.userType === UserType.OPEN_PLATFORM) {
  // 开放平台用户：使用当前用户ID
  openPlatformUserId = new Types.ObjectId(userSession.userId)
  
  // 验证用户权限
  if (application.userId.toString() !== userSession.userId) {
    throw new ForbiddenException('无权限操作该应用')
  }
} else {
  // 管理员操作：使用应用所有者ID
  openPlatformUserId = application.userId
}

// 在创建数据中添加用户ID
const rechargeData = {
  // ... 其他字段
  openPlatformUserId, // 新增字段
  // ... 其他字段
}
```

#### 2.3 查询列表数据隔离
```typescript
// 数据隔离：开放平台用户只能查看自己的充值记录
if (userSession?.userType === UserType.OPEN_PLATFORM) {
  filter.openPlatformUserId = new Types.ObjectId(userSession.userId)
}
```

## 数据隔离机制

### 1. 创建时的用户ID设置

- **开放平台用户创建**: 使用当前登录用户的ID
- **管理员代为创建**: 使用应用所有者的ID
- **权限验证**: 开放平台用户只能为自己的应用创建充值记录

### 2. 查询时的数据过滤

- **开放平台用户**: 只能查询 `openPlatformUserId` 等于自己ID的记录
- **管理员用户**: 可以查询所有记录（无额外过滤条件）

### 3. 权限控制层级

1. **Controller层**: 使用 `@AdminAndOpenPlatformAccess()` 装饰器
2. **Service层**: 根据用户类型进行数据隔离
3. **Database层**: 通过索引优化查询性能

## 数据迁移

### 迁移脚本

**文件**: `scripts/migrations/add-open-platform-user-id-to-recharge.js`

**功能**:
- 为现有充值记录添加 `openPlatformUserId` 字段
- 根据 `applicationId` 查找对应应用的 `userId`
- 批量更新所有缺失该字段的记录
- 创建索引优化查询性能

**执行方式**:
```bash
# 方式1：直接执行脚本
node scripts/migrations/add-open-platform-user-id-to-recharge.js

# 方式2：在MongoDB shell中执行
load('scripts/migrations/add-open-platform-user-id-to-recharge.js')
```

### 迁移验证

迁移完成后，可以通过以下查询验证：
```javascript
// 检查是否还有缺失openPlatformUserId的记录
db.open_platform_application_recharges.countDocuments({
  openPlatformUserId: { $exists: false }
})

// 验证索引是否创建成功
db.open_platform_application_recharges.getIndexes()
```

## 向后兼容性

### 1. 现有API兼容
- 所有现有的API接口保持不变
- 响应数据格式保持一致
- 查询参数保持不变

### 2. 数据完整性
- 通过迁移脚本确保所有现有数据都有正确的用户ID
- 新创建的记录自动填充用户ID
- 数据库约束确保字段不能为空

### 3. 性能影响
- 添加了索引，查询性能得到优化
- 数据隔离逻辑在Service层实现，不影响数据库性能

## 测试建议

### 1. 功能测试
- 测试开放平台用户创建充值记录
- 测试管理员代为创建充值记录
- 测试数据隔离是否生效
- 测试权限验证是否正确

### 2. 性能测试
- 测试大量数据下的查询性能
- 验证索引是否生效
- 测试并发创建充值记录的性能

### 3. 数据一致性测试
- 验证迁移后的数据完整性
- 测试新旧数据的查询一致性
- 验证用户ID关联的正确性

## 注意事项

1. **数据迁移**: 在生产环境执行迁移前，请先在测试环境验证
2. **索引创建**: 大量数据情况下，索引创建可能需要较长时间
3. **权限验证**: 确保开放平台用户只能操作自己的应用
4. **错误处理**: 充值记录创建时如果用户ID获取失败，会抛出相应异常
5. **监控**: 建议在生产环境部署后监控相关API的性能和错误率

## 后续优化

1. **缓存优化**: 可以考虑缓存用户-应用关系，减少数据库查询
2. **批量操作**: 支持批量创建充值记录时的用户ID设置
3. **审计日志**: 记录充值记录的创建和修改操作日志
4. **数据统计**: 基于用户ID进行充值数据的统计分析
