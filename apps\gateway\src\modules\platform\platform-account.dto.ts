import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  isString,
  IsString,
  IsBoolean,
  ValidateIf
} from 'class-validator'
import { LoginStatus } from '@yxr/mongo'
import { Transform, Type } from 'class-transformer'
import { BrowserFavoriteDTO } from '../browser/browser.dto'
import { PlatformNameEnum } from '@yxr/common'

export class PlatformAccountDTO {
  @ApiProperty({
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '父级ID',
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  parentId: string

  @ApiProperty({
    description: '媒体账号空间ID',
    type: String,
    example: '********-da27-42cb-9413-3941b7c06fa2'
  })
  spaceId: string

  @ApiProperty({
    type: String,
    example: 'http://xxxxxx.png'
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    example: '葫芦娃七兄弟'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    example: '备注'
  })
  remark: string

  @ApiProperty({
    type: [String],
    example: '分组ID'
  })
  groups: string[]

  @ApiProperty({
    type: String,
    example: '1e222223'
  })
  platformAuthorId: string

  @ApiProperty({
    type: String,
    example: '抖音'
  })
  platformName: string

  @ApiProperty({
    default: LoginStatus.Succesed,
    enum: LoginStatus,
    description: '账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败, 4：取消授权>',
    required: true
  })
  status: LoginStatus

  @ApiProperty({
    type: [BrowserFavoriteDTO],
    example: '收藏列表'
  })
  favorites: BrowserFavoriteDTO[]

  @ApiProperty({
    default: false,
    description: '是否有账号运营权限'
  })
  isOperate: boolean

  @ApiProperty({
    default: false,
    description: '微信号下视频号是否有被锁定'
  })
  isLock: boolean

  @ApiProperty({
    type: Number,
    example: '**********'
  })
  createdAt: number

  @ApiProperty({
    type: Boolean,
    description: '是否冻结',
    required: true,
    example: false
  })
  isFreeze: boolean

  /**
   * 平台类型
   */
  @ApiProperty({
    type: Number,
    description: '0其他平台 1开放平台',
    default: 0
  })
  platformType: number

  // 公众号服务类型ID
  @ApiProperty({
    type: Number,
    description: '0订阅号 1由历史老账号升级后的订阅号 2服务号'
  })
  serviceTypeId: number

  @ApiProperty({
    type: Number,
    description: '1：正常,14：已注销,16：已封禁,18：已告警,19：已冻结'
  })
  accountStatus: number

  @ApiProperty({
    type: String,
    description:
      '公众号认证类型 -1未认证 0微信认证 1新浪微博认证 3已资质认证通过但还未通过名称认证 4已资质认证通过、还未通过名称认证，但通过了新浪微博认证'
  })
  verifyTypeInfo: string

  /**
   * 代理地区
   */
  @ApiProperty({
    type: String,
    description: '代理地区编码号：430100'
  })
  kuaidailiArea?: string

  @ApiProperty({
    type: Boolean,
    description: '是否实名认证'
  })
  isRealNameVerified: boolean
}

export class PlatformAccountListResponse {
  @ApiResponseProperty({
    type: [PlatformAccountDTO]
  })
  data: PlatformAccountDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class PlatformAccountPutOperators {
  @ApiProperty({
    type: String,
    description: '成员id 列表',
    required: true
  })
  @IsArray()
  @IsNotEmpty()
  memberIds: string[]
}

export class PlatformAccountPutPrincipal {
  @ApiProperty({
    type: String,
    description: '成员id',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  memberId: string
}

export class WechatKeysCreateRequest {
  @ApiProperty({
    type: [String],
    description: '账号id数组',
    required: true
  })
  @IsArray({ message: '账号格式不匹配' })
  @IsNotEmpty({ message: '账号id不能为空' })
  platformAccountIds: string[]
}

export class PlatformAccountListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10

  @ApiProperty({
    type: Number,
    description: '查询此时间戳之后是否有新消息 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  time: number = 0

  @ApiProperty({
    type: String,
    description: '账号名称查询',
    example: '糊涂的倒霉蛋',
    required: false
  })
  @IsOptional()
  @IsString()
  name: string

  @ApiProperty({
    type: String,
    description: '分组查询',
    example: '张三的团队',
    required: false
  })
  @IsOptional()
  @IsString()
  group: string

  @ApiProperty({
    type: String,
    description: '平台查询',
    example: '抖音',
    required: false
  })
  @IsOptional()
  @IsString()
  platform: string

  @ApiProperty({
    type: [String],
    description: '平台批量查询',
    example: ['66a8b11e5f8d230bc3140f1c', '66a9e2bf52e68e8300599a4f'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @Transform(({ value }) => (isString(value) ? [value] : value))
  platforms: string[]

  @ApiProperty({
    type: Number,
    enum: LoginStatus,
    description: '账号状态 0未登录 1成功 2过期失效 3失败',
    example: LoginStatus.Succesed,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  loginStatus: LoginStatus

  @ApiProperty({
    type: String,
    description: '是否显示隔离数据 true|false 默认为false, 显示全部账号',
    required: false
  })
  @IsOptional()
  @IsString()
  isolation?: string

  @ApiProperty({
    type: String,
    description: '父级ID,查询子账号列表',
    required: false
  })
  @IsOptional()
  @IsString()
  parentId?: string

  @ApiProperty({
    type: String,
    description: '是否实名认证',
    required: false
  })
  @IsOptional()
  @IsString()
  isRealNameVerified: string
}

/**
 * 更新媒体账号请求体
 */
export class PatchPlatformAccountRequest {
  @ApiProperty({
    type: Number,
    default: LoginStatus.Succesed,
    enum: LoginStatus,
    description: '账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败>',
    required: false
  })
  @IsOptional()
  @IsNumber()
  status: LoginStatus

  @ApiProperty({
    type: String,
    description: '账号登录状态校验码(在状态由有效状态变更为无效状态时必须提供)',
    required: false
  })
  @IsOptional()
  @IsString()
  statusChecksum?: string

  @ApiProperty({
    type: String,
    default: '账号备注名称',
    description: '备注信息',
    required: false
  })
  @IsOptional()
  @IsString()
  remark: string

  @ApiProperty({
    type: Boolean,
    default: true,
    description: '媒体账号实名认证状态',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isRealNameVerified: boolean

  @ApiProperty({
    default: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a'],
    description: '设置分组ID',
    required: false,
    type: [String]
  })
  @IsOptional()
  groups: string[]

  @ApiProperty({
    default: '430100',
    description: '地区编码',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString()
  kuaidailiArea: string

  @ApiProperty({
    description: '浏览器空间颜色',
    default: 'red',
    required: false,
    type: String
  })
  @IsString()
  @IsOptional()
  color: string

  @ApiProperty({
    description: '浏览器访问地址',
    default: 'red',
    required: false,
    type: String
  })
  @IsString({ message: '网站地址不正确' })
  @IsOptional()
  spaceUrl: string

  @ApiProperty({
    description: '浏览器访问地址',
    default: 'red',
    required: false,
    type: String
  })
  @IsString({ message: '网站地址不正确' })
  @IsOptional()
  platformAccountName: string
}

/**
 * 媒体账号请求体
 */
export class PlatformAccountRequest {
  @ApiProperty({
    description: '平台账号昵称',
    example: '葫芦娃七兄弟',
    required: true
  })
  @IsString({ message: '平台昵称格式不正确' })
  @IsNotEmpty({ message: '平台昵称不能为空' })
  platformAccountName: string

  @ApiProperty({
    description: '平台账号头像',
    required: true
  })
  @ValidateIf((o) => o.platformName !== PlatformNameEnum.其他) // 当 platformName 为其他时，必填
  @IsString({ message: '账号头像格式不正确' })
  platformAvatar: string

  @ApiProperty({
    description: '浏览器空间颜色',
    default: 'red',
    required: false,
    type: String
  })
  @ValidateIf((o) => o.platformName === PlatformNameEnum.其他)
  @IsString()
  color: string

  @ApiProperty({
    description: '浏览器访问地址',
    default: 'red',
    required: false,
    type: String
  })
  @ValidateIf((o) => o.platformName === PlatformNameEnum.其他)
  @IsString({ message: '网站地址不正确' })
  spaceUrl: string

  @ApiProperty({
    description: '平台账号登录凭证',
    default: 'Di95dJtl7PMAdJhr_3iHc',
    required: false,
    deprecated: true
  })
  @IsString()
  @IsOptional()
  token: string

  @ApiProperty({
    default: LoginStatus.Succesed,
    enum: LoginStatus,
    description: '账号登录状态 <0：未登录, 1：登录成功, 2：登录过期, 3：登录失败>',
    required: false
  })
  @IsOptional()
  @IsNumber()
  status: LoginStatus

  @ApiProperty({
    default: '抖音号',
    description: '平台账号名称',
    required: true
  })
  @IsString()
  platformName: string

  @ApiProperty({
    default: '1222333',
    description: '平台作者ID',
    required: true
  })
  @ValidateIf((o) => o.platformName !== PlatformNameEnum.其他)
  @IsString()
  platformAuthorId: string

  /**
   * 平台类型
   */
  @ApiProperty({
    type: Number,
    description: '0其他平台 1开放平台',
    required: false,
    default: 0
  })
  @Transform(({ value }) => (value === undefined || value === null ? 0 : value)) // 如果没有传值，默认为 0
  platformType: number

  // 公众号服务类型ID
  @ApiProperty({
    type: Number,
    required: false,
    description: '0订阅号 1由历史老账号升级后的订阅号 2服务号'
  })
  @IsOptional()
  serviceTypeId: number

  // 公众号服务类型说明
  @ApiProperty({
    type: String,
    required: false,
    description: '公众号服务类型说明'
  })
  @IsOptional()
  serviceTypeName: string

  @ApiProperty({
    type: Number,
    required: false,
    description: '1：正常,14：已注销,16：已封禁,18：已告警,19：已冻结'
  })
  @IsOptional()
  accountStatus: number

  // 公众号主体名称
  @ApiProperty({
    type: String,
    required: false,
    description: '公众号主体名称'
  })
  @IsOptional()
  principalName: string

  @ApiProperty({
    type: String,
    required: false,
    description:
      '公众号认证类型 -1未认证 0微信认证 1新浪微博认证 3已资质认证通过但还未通过名称认证 4已资质认证通过、还未通过名称认证，但通过了新浪微博认证'
  })
  @IsOptional()
  verifyTypeInfo: string

  @ApiProperty({
    type: Boolean,
    description: '是否实名认证',
    required: false,
    default: true
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => (value === undefined || value === null ? true : value)) // 如果没有传值，默认为 true
  isRealNameVerified: boolean

  @ApiProperty({
    description: '开放平台账号的授权凭证',
    required: false
  })
  @IsOptional()
  credentials: unknown
}

/**
 * 账号概览上报
 */
export class PutPlatformAccountOverviewRequest {
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsOptional()
  overviewData?: string
}

export class PutPlatformAccountOverviewResponse {
  @ApiProperty({
    type: String,
    example: ''
  })
  platformAccountId: string

  @ApiProperty({
    type: Object,
    example: ''
  })
  overviewData: object
}

/**
 * 媒体账号详情
 */
export class PlatformAccountDetailResponse {
  @ApiResponseProperty({
    type: String,
    example: '********da2742cb413941b7c06fa2'
  })
  id: string

  @ApiProperty({
    description: '媒体账号空间ID',
    deprecated: true,
    type: String,
    example: '********da2742cb413941b7c06fa2'
  })
  spaceId: string

  @ApiProperty({
    description: '媒体账号空间ID',
    type: String,
    example: '********da2742cb413941b7c06fa2'
  })
  parentId: string

  @ApiResponseProperty({
    type: String,
    example: 'http://xxxxxxxx.png'
  })
  platformAvatar: string

  @ApiResponseProperty({
    type: String,
    example: '账号昵称'
  })
  platformAccountName: string

  @ApiResponseProperty({
    type: String,
    example: '备注'
  })
  remark: string

  @ApiResponseProperty({
    type: [String],
    example: ['66c7f6d44b30e7e908d2dec1', '66c7f52851b222cccdddb68a']
  })
  groups: string[]

  @ApiResponseProperty({
    type: String,
    example: '1e222223'
  })
  platformAuthorId: string

  @ApiResponseProperty({
    type: String,
    example: '抖音'
  })
  platformName: string

  @ApiResponseProperty({
    type: Number,
    example: '1'
  })
  status: number

  @ApiProperty({
    type: [BrowserFavoriteDTO],
    example: '收藏列表'
  })
  favorites?: BrowserFavoriteDTO[]

  @ApiResponseProperty({
    type: Number,
    example: '**********'
  })
  createdAt: number

  @ApiProperty({
    description: '浏览器空间颜色',
    default: 'red',
    required: false,
    type: String
  })
  color: string

  @ApiProperty({
    description: '浏览器访问地址',
    default: 'red',
    required: false,
    type: String
  })
  spaceUrl: string

  @ApiProperty({
    type: Boolean,
    description: '是否冻结',
    required: true,
    example: false
  })
  isFreeze: boolean

  /**
   * 平台类型
   */
  @ApiProperty({
    type: Number,
    description: '0其他平台 1开放平台',
    default: 0
  })
  platformType: number

  // 公众号服务类型ID
  @ApiProperty({
    type: Number,
    description: '0订阅号 1由历史老账号升级后的订阅号 2服务号'
  })
  serviceTypeId: number

  @ApiProperty({
    type: Number,
    description: '1：正常,14：已注销,16：已封禁,18：已告警,19：已冻结'
  })
  accountStatus: number

  @ApiProperty({
    type: String,
    description:
      '公众号认证类型 -1未认证 0微信认证 1新浪微博认证 3已资质认证通过但还未通过名称认证 4已资质认证通过、还未通过名称认证，但通过了新浪微博认证'
  })
  verifyTypeInfo: string

  @ApiProperty({
    type: Boolean,
    description: '是否实名认证'
  })
  isRealNameVerified: boolean

  @ApiProperty({
    default: '430100',
    description: '地区编码',
    type: String
  })
  kuaidailiArea: string

  @ApiProperty({
    default: false,
    description: '是否有账号运营权限'
  })
  isOperate: boolean
}

/**
 * 媒体账号详情响应体
 */
export class PlatformAccountDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [PlatformAccountDetailResponse]
  })
  data: PlatformAccountDetailResponse[]
}

/**
 * 媒体账号列表响应体
 */
export class PlatformAccountListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PlatformAccountListResponse
  })
  data: PlatformAccountListResponse
}

/**
 * 更新团队代理区域请求体
 */
export class UpdateAreasInputDto {
  code: string
}

/**
 * 新增ipad微信账号请求
 */
export class CreateWxIpadAccountRequest {
  @ApiProperty({
    description: '微信ID',
    type: String,
    example: 'wxid_1234567890'
  })
  wxid: string

  @ApiProperty({
    description: 'uuid',
    type: String,
    example: 'uuid_1234567890'
  })
  uuid: string

  @ApiProperty({
    description: '账号昵称',
    type: String,
    example: 'device_1234567890'
  })
  nickName: string

  @ApiProperty({
    description: '账号头像',
    type: String,
    example:
      'https://wx.qlogo.cn/mmhead/ver_1/270cu6oGrhJVibZCYejGHlFRJZ454CLKaR1HDgOFicV8x7PWQiaC5qibnPg3Q6VMVZBibXeSKuiaLUz4gojycepzwYSu0qtPhzsZSKV3puZkicMDIibsresUN3ZhwUUqH4EHxxWib/0'
  })
  imgHead: string
}

export class PlatformAccountCookieRequest {
  @ApiProperty({
    type: String,
    required: false
  })
  @IsOptional()
  @IsString()
  localStorage?: string

  @ApiProperty({
    type: String,
    required: false
  })
  @IsOptional()
  @IsString()
  cookie?: string
}

export class AccountOverviewReportRequest {

  @ApiProperty({
    type: String,
    description: '账号名称查询',
    example: '糊涂的倒霉蛋',
    required: false
  })
  @IsOptional()
  @IsString()
  name: string

  @ApiProperty({
    type: String,
    description: '分组查询',
    example: '张三的团队',
    required: false
  })
  @IsOptional()
  @IsString()
  group: string

  @ApiProperty({
    type: String,
    description: '平台查询',
    example: '抖音',
    required: false
  })
  @IsString()
  @IsNotEmpty({message:'平台不能为空'})
  platform: string

  @ApiProperty({
    type: Number,
    enum: LoginStatus,
    description: '账号状态 0未登录 1成功 2过期失效 3失败',
    example: LoginStatus.Succesed,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  loginStatus: LoginStatus
}
