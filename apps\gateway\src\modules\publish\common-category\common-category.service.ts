import { ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { REQUEST } from '@nestjs/core'
import { InjectModel } from '@nestjs/mongoose'
import { FastifyRequest } from 'fastify'
import { Model, Types } from 'mongoose'
import { CommonCategorysEntity, CommonCategorysGroupEntity } from '@yxr/mongo'
import { CategorysDTO, PostCategorysRequest } from './common-category.dto'

@Injectable()
export class CommonCategorysService {
  logger = new Logger('CommonCategorysService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(CommonCategorysGroupEntity.name)
    private categorysGroupModel: Model<CommonCategorysGroupEntity>,
    @InjectModel(CommonCategorysEntity.name)
    private categorysModel: Model<CommonCategorysEntity>
  ) {}

  /**
   * 获取分组下分类列表
   * @param groupId
   * @returns
   */
  async getCategorys(groupId: string): Promise<CategorysDTO[]> {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const query: any = {}

    query.teamId = new Types.ObjectId(currentTeamId)
    query.userId = new Types.ObjectId(currentUserId)
    if (groupId) {
      if (Types.ObjectId.isValid(groupId)) {
        query.groupId = new Types.ObjectId(groupId)
      } else {
        throw new ForbiddenException('分组参数错误')
      }
    }

    const result = await this.categorysModel.find(query).sort({ createdAt: 'desc' })
    return result.map((item) => ({
      id: item._id.toString(),
      name: item.name,
      groupId: item.groupId,
      categoryArgs: item.categoryArgs,
      platformName: item.platformName
    }))
  }

  /**
   * 新增话题
   * @param body
   */
  async postCategorys(body: PostCategorysRequest): Promise<CategorysDTO[]> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const group = await this.categorysGroupModel.findById({
      _id: new Types.ObjectId(body.groupId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!group) {
      throw new NotFoundException('添加分组失败，分组不存在')
    }
    if (body.categorys.length === 0) {
      throw new ForbiddenException('分类列表不能为空')
    }

    // 新增内容
    const categoryEntities: CommonCategorysEntity[] = body.categorys.map((request) => {
      return {
        teamId: new Types.ObjectId(currentTeamId),
        userId: new Types.ObjectId(currentUserId),
        name: request.name,
        groupId: new Types.ObjectId(body.groupId),
        categoryArgs: request.categoryArgs,
        platformName: request.platformName
      }
    })
    const categorysInfo = await this.categorysModel.create(categoryEntities)
    return categorysInfo.map((item) => ({
      id: item._id.toString(),
      name: item.name,
      groupId: item.groupId,
      categoryArgs: item.categoryArgs,
      platformName: item.platformName
    }))
  }

  /**
   * 删除话题
   * @param categoryId
   * @returns
   */
  async deleteCategory(categoryId: string) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const category = await this.categorysModel.findOne({
      _id: new Types.ObjectId(categoryId),
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (!category) {
      throw new NotFoundException('分类不存在')
    }

    await this.categorysModel.deleteOne({
      _id: category._id
    })
  }
}
