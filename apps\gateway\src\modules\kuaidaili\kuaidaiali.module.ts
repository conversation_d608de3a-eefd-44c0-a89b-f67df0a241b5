import { Module } from '@nestjs/common'
import { KuaidailiService } from './kuaidaili.service'
import { KuaidailiController } from './kuaidaili.controller'
import { HuoshanModule } from '@yxr/huoshan'
import { PlatformProxyMongoose } from '@yxr/mongo'

@Module({
  imports: [PlatformProxyMongoose,HuoshanModule],
  controllers: [KuaidailiController],
  providers: [KuaidailiService],
  exports: [KuaidailiService]
})
export class KuaidailiModule {}
