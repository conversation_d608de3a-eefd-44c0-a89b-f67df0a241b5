import { Injectable } from '@nestjs/common'
import * as ALY from 'aliyun-sdk'
import { GenerateSLSDto } from './logger.dto'
import crypto from 'crypto'
import { FastifyRequest } from 'fastify'

@Injectable()
export class LoggerService {

  private aliLogger: ALY.SLS
  private project: string
  private logStore: string

  constructor() {
    this.aliLogger = new ALY.SLS({
      accessKeyId: process.env.SLS_ACCESS_KEY_ID,
      secretAccessKey: process.env.SLS_SECRET_ACCESS_KEY,
      endpoint: process.env.SLS_ENDPOINT,
      apiVersion: '2015-06-01'
    })

    this.project = process.env.SLS_PROJECT
    this.logStore = process.env.SLS_LOGSTORE
  }

  private generateRequestId(): string {
    return crypto.randomBytes(8).toString('hex')
  }

  async info(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'info', message, extra)
  }

  async error(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'error', message, extra)
  }

  async warn(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'warn', message, extra)
  }

  async debug(req: FastifyRequest | null, message: string, extra?: Record<string, any>) {
    await this.log(req, 'debug', message, extra)
  }

  private async log(
    req: FastifyRequest | null,
    level: 'info' | 'error' | 'warn' | 'debug',
    message: string,
    extra: Record<string, any> = {}
  ) {
    const logEntry = {
      logLevel: level,
      requestId: req?.headers['x-request-id'] || this.generateRequestId(),
      host: req?.headers['host'] || 'unknown',
      userAgent: req?.headers['user-agent'] || 'unknown',
      authorization: req?.headers['authorization'] || 'unknown',
      message: message,
      ...extra
    }

    try {
      const param = {
        projectName: this.project,
        logStoreName: this.logStore,
        logGroup: {
          // 必选，写入的日志数据。
          logs: [this.dtoToGenerateSLSDto(logEntry)]
        }
      }

      this.aliLogger.putLogs(param, (err) => {
        if (err) {
          console.log(err)
        }
      })
    } catch (error) {
      console.log(error)
    }
  }

  private dtoToGenerateSLSDto(slsDto: Record<string, any> = {}): GenerateSLSDto {
    return {
      time: Math.floor(Date.now() / 1000),
      contents: Object.entries(slsDto)
        .filter(([_, value]) => value !== undefined && value !== null) // 过滤无效值
        .map(([key, value]) => ({ key, value: String(value) })) // 统一转换为字符串
    }
  }
}
