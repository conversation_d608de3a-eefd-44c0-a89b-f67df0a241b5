import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsBoolean, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { SalesType } from '@yxr/common'

export class TeamListItemDTO {
  @ApiProperty({
    description: '团队Id',
    example: '66b2d7ee1c0ea559bd1994bd',
    required: true
  })
  id: string

  @ApiProperty({
    description: '团队名称',
    example: 'XX的团队',
    required: true
  })
  name: string

  @ApiProperty({
    description: '团队LOGO图片地址',
    example: 'https://xxx.com/xxx.png',
    required: true
  })
  logoUrl: string

  @ApiProperty({
    description: '团队LOGO图片存储KEY',
    example: 'xxx.png',
    required: true
  })
  logoKey: string

  @ApiProperty({
    description: '是否为免费版',
    type: Boolean,
    required: true
  })
  @IsBoolean()
  isVip: boolean

  @ApiProperty({
    description: 'VIP 版到期时间, 免费版无此属性值',
    type: Number,
    required: false
  })
  expiredAt?: number
}

export class componentDTO {
  @ApiProperty({
    description: '组件id'
  })
  id: string

  @ApiProperty({
    description: '组件名称'
  })
  name: string

  @ApiProperty({
    description: '组件开启状态true 开启 false未开启',
    required: true
  })
  enabled: boolean

  @ApiProperty({
    description: '组件使用信息，由客户端传入结构自定义'
  })
  componentArgs: unknown
}

export class TeamDetailsDto extends TeamListItemDTO {
  @ApiProperty({
    description: '团队邀请码',
    example: '76T4HX',
    required: true
  })
  code: string

  /** @deprecated 请使用 accountCapacityLimit */
  @ApiProperty({
    description: '账号数量上限',
    deprecated: true,
    required: true
  })
  accountCountLimit: number  // 变更为账号点数

  @ApiProperty({
    description: '账号点数上限',
    required: true
  })
  accountCapacityLimit: number

  /** @deprecated 已废弃, 请使用 accountCapacity */
  @ApiProperty({
    description: '账号数量',
    required: true,
    deprecated: true
  })
  accountCount: number  // 变更为账号点数

  @ApiResponseProperty({
    type: Number,
    example: '账号点数'
  })
  accountCapacity: number

  @ApiProperty({
    description: '成员数量上限',
    required: true
  })
  memberCountLimit: number

  @ApiProperty({
    description: '成员数量',
    required: true
  })
  memberCount: number

  @ApiProperty({
    description: '素材容量',
    required: true
  })
  capacity?: number

  @ApiProperty({
    description: '已使用素材容量',
    required: true
  })
  usedCapacity?: number

  @ApiProperty({
    description: '团队流量',
    required: true
  })
  networkTraffic?: number

  @ApiProperty({
    description: '已使用流量',
    required: true
  })
  useNetworkTraffic?: number

  @ApiProperty({
    description: '团队创建时间',
    required: true
  })
  createdAt: number

  @ApiProperty({
    description: '权益包数量',
    type: Number
  })
  interestCount: number

  @ApiProperty({
    description: '是否可以app发布',
    type: Boolean
  })
  appPublish: boolean

  @ApiProperty({
    description: '权益包剩余时长（天）',
    type: Number
  })
  remainingDay: number

  @ApiProperty({
    example: 10,
    description: '可以发布账号数',
    type: Number
  })
  publishAccountLimit?: number

  @ApiProperty({
    description: '客服二维码',
    type: String
  })
  corporateTransfer?: string

  @ApiProperty({
    type: Number,
    description: '0 未购买 1新购 2复购',
    enum: SalesType
  })
  salesType: SalesType

  @ApiProperty({
    type: componentDTO,
    description: '团队插件信息'
  })
  components?: componentDTO[]
}

export class TeamResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamDetailsDto
  })
  data: TeamDetailsDto
}

export class TeamPagedListResponse {
  @ApiResponseProperty({
    type: [TeamListItemDTO]
  })
  data: TeamListItemDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class getTeamListOkResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamPagedListResponse
  })
  data: TeamPagedListResponse
}

export class createTeamRequestBodyDTO {
  @ApiProperty({
    description: '团队名称',
    example: 'XX的团队',
    required: true
  })
  @IsString({ message: '团队名称格式不正确' })
  @IsNotEmpty({ message: '团队名称不能为空' })
  @MaxLength(64, { message: '团队名称长度太长。不得多于 $constraint1 个字符' })
  name: string

  @ApiProperty({
    description: '团队LOGO, 以图片存储key形式提供, 由获取资源上传接口返回',
    example: 'foo/bar/xxx.png',
    required: true
  })
  @IsString()
  logoKey: string
}

export class patchTeamRequestBodyDTO {
  @ApiProperty({
    type: String,
    description: '团队名称',
    example: 'XX的团队',
    required: false
  })
  @IsString()
  @IsOptional()
  name: string

  @ApiProperty({
    type: String,
    description: '团队LOGO, 以图片存储key形式提供, 由获取资源上传接口返回',
    example: 'foo/bar/xxx.png',
    required: false
  })
  @IsString()
  @IsOptional()
  logoKey: string
}

export class putTeamComponentRequestBodyDTO {
  @ApiProperty({
    type: String,
    description: '组件名称',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '插件名称不能为空' })
  name: string

  @ApiProperty({
    type: Object,
    description: '组件数据信息',
    required: false
  })
  @IsOptional()
  componentArgs: unknown

  @ApiProperty({
    type: Boolean,
    description: '组件开启状态',
    required: false
  })
  @IsBoolean()
  @IsOptional()
  enable: boolean
}
