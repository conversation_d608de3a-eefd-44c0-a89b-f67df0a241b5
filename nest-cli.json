{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/gateway/src", "root": "apps/gateway", "compilerOptions": {"assets": ["**/*.proto"], "deleteOutDir": true, "webpack": true, "builder": "swc"}, "monorepo": true, "projects": {"@yxr/gateway": {"type": "application", "root": "apps/gateway", "entryFile": "main", "sourceRoot": "apps/gateway/src", "compilerOptions": {"tsConfigPath": "apps/gateway/tsconfig.json"}}, "@yxr/socket": {"type": "application", "root": "apps/socket", "entryFile": "main", "sourceRoot": "apps/socket/src", "compilerOptions": {"tsConfigPath": "apps/socket/tsconfig.json"}}, "@yxr/admin": {"type": "application", "root": "apps/admin", "entryFile": "main", "sourceRoot": "apps/admin/src", "compilerOptions": {"tsConfigPath": "apps/admin/tsconfig.json"}}, "@yxr/overseavice": {"type": "application", "root": "apps/overseavice", "entryFile": "main", "sourceRoot": "apps/overseavice/src", "compilerOptions": {"tsConfigPath": "apps/overseavice/tsconfig.json"}}}}