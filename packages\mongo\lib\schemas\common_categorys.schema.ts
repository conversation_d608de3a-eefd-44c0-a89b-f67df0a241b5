import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { MaterialTypeEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class CommonCategorysEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: false,
    index: true
  })
  groupId?: Types.ObjectId

  @Prop({
    type: String,
    required: true
  })
  platformName: string

  @Prop({
    type: String,
    required: true
  })
  name: string

  /**
   * 分类参数包 前端需要的分类数据
   */
  @Prop({
    type: Types.Map,
    required: true
  })
  categoryArgs: unknown

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const CommonCategorysSchema: ModelDefinition = {
  name: CommonCategorysEntity.name,
  schema: SchemaFactory.createForClass(CommonCategorysEntity)
}

export const CommonCategorysMongoose = MongooseModule.forFeature([CommonCategorysSchema])
