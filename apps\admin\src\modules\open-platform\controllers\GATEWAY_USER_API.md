# Gateway User API 新增接口文档

## 概述

为 gateway-user 控制器新增了两个API接口，用于支持 openPlatform 和 admin 角色查询应用下的用户和团队信息。

## 新增接口

### 1. 获取应用下的用户列表

**接口路径**: `GET /gateway/users/app/:appId/users`

**权限控制**: `@AdminAndOpenPlatformAccess()` - 管理员和开放平台用户可访问

**请求参数**:
- `appId` (路径参数): 应用ID
- `phone` (查询参数, 可选): 手机号模糊搜索
- `nickName` (查询参数, 可选): 昵称模糊搜索
- `page` (查询参数, 可选): 页码，默认1
- `size` (查询参数, 可选): 每页数量，默认10

**响应数据**:
```json
{
  "totalSize": 100,
  "page": 1,
  "size": 10,
  "totalPage": 10,
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "phone": "13800138000",
      "nickName": "张三",
      "avatar": "https://example.com/avatar.jpg",
      "teamId": "507f1f77bcf86cd799439012",
      "channelCode": "CHANNEL001",
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

### 2. 获取应用下的团队列表

**接口路径**: `GET /gateway/users/app/:appId/teams`

**权限控制**: `@AdminAndOpenPlatformAccess()` - 管理员和开放平台用户可访问

**请求参数**:
- `appId` (路径参数): 应用ID
- `teamName` (查询参数, 可选): 团队名称模糊搜索
- `isVip` (查询参数, 可选): VIP状态过滤
- `page` (查询参数, 可选): 页码，默认1
- `size` (查询参数, 可选): 每页数量，默认10

**响应数据**:
```json
{
  "totalSize": 50,
  "page": 1,
  "size": 10,
  "totalPage": 5,
  "data": [
    {
      "id": "507f1f77bcf86cd799439013",
      "name": "测试团队",
      "code": "ABC123",
      "logo": "https://example.com/logo.jpg",
      "memberCount": 8,
      "totalTraffic": 150.75,
      "usedTraffic": 45.32,
      "isVip": true,
      "expiredAt": *************,
      "accountCountLimit": 100,
      "accountCount": 50,
      "createdAt": *************,
      "updatedAt": *************
    }
  ]
}
```

## 数据隔离机制

- **应用级隔离**: 通过 `sourceAppId` 字段确保只返回指定应用创建的数据
- **用户类型隔离**: 通过 `source: 'open_platform_app'` 过滤条件确保只返回开放平台应用创建的用户/团队
- **权限控制**: 使用 `@AdminAndOpenPlatformAccess()` 装饰器确保只有管理员和开放平台用户可以访问

## 实现特点

1. **遵循现有架构**: 采用 Controller + Service + DTO 架构模式
2. **统一权限控制**: 使用现有的访问控制装饰器系统
3. **完整的数据验证**: 包含请求参数验证和响应数据格式化
4. **分页支持**: 支持标准的分页查询
5. **模糊搜索**: 支持手机号、昵称、团队名称的模糊搜索
6. **错误处理**: 包含完整的API文档和错误响应定义

## 使用示例

### 查询应用用户
```bash
GET /gateway/users/app/507f1f77bcf86cd799439011/users?phone=138&page=1&size=10
Authorization: Bearer <token>
```

### 查询应用团队
```bash
GET /gateway/users/app/507f1f77bcf86cd799439011/teams?teamName=测试&isVip=true&page=1&size=10
Authorization: Bearer <token>
```
