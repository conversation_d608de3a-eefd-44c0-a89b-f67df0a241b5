import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  Logger,
  UnauthorizedException
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { AdminEntity, OpenPlatformUserEntity } from '@yxr/mongo'
import type { FastifyRequest } from 'fastify'
import process from 'node:process'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { UserType } from '@yxr/common'

/**
 * 统一认证守卫 - 基于现有AuthGuard.ts实现，保持向后兼容性
 *
 * 兼容性策略：
 * 1. 保持原有的 `await this.cacheManager.get<AdminEntity>(authorization)` 认证方式
 * 2. 扩展支持开放平台用户认证
 * 3. 确保现有admin接口无需修改即可正常工作
 * 4. 在request对象中正确设置用户类型和会话信息
 */
@Injectable()
export class UnifiedTokenGuard implements CanActivate {
  private readonly logger = new Logger(UnifiedTokenGuard.name)

  constructor(
    private reflector: Reflector,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(OpenPlatformUserEntity.name) private openPlatformUserModel: Model<OpenPlatformUserEntity>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { authorization } = request.headers

    const anonymous = this.reflector.getAllAndOverride('anonymous', [
      context.getHandler(),
      context.getClass()
    ])

    if (anonymous) {
      return true
    } else {
      try {
        // 第一步：尝试原有的admin用户认证方式（保持完全兼容）
        let user = await this.cacheManager.get<AdminEntity>(authorization)

        if (user) {
          // 原有admin用户认证成功 - 保持原有的request设置方式
          request.user = user
          request.authorization = authorization
          // 扩展：添加统一的session信息
          request.session = {
            userId: (user as any)._id?.toString() || (user as any).id?.toString(),
            userType: UserType.ADMIN,
            teamId: undefined,
            applicationId: undefined
          }
          return true
        }

        // 第二步：尝试开放平台用户认证（新增功能）
        const openPlatformUser = await this.tryOpenPlatformAuth(authorization)
        if (openPlatformUser) {
          request.user = openPlatformUser
          request.authorization = authorization
          request.session = {
            userId: (openPlatformUser as any)._id.toString(),
            userType: UserType.OPEN_PLATFORM,
            teamId: undefined,
            applicationId: undefined
          }
          return true
        }

        // 第三步：开发环境模拟认证（保持原有逻辑）
        if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev') {
          const mockUser = await this.handleDevMockAuth(authorization)
          if (mockUser) {
            request.user = mockUser.user
            request.authorization = authorization
            request.session = {
              userId: mockUser.userId,
              userType: mockUser.userType,
              teamId: mockUser.teamId,
              applicationId: mockUser.applicationId
            }
            return true
          }
        }

      } catch {
        // ignore - 保持原有的错误处理方式
      }

      throw new UnauthorizedException('登录失效, 请重新登录')
    }
  }

  /**
   * 尝试开放平台用户认证
   * 使用特定的缓存key格式来区分开放平台用户
   */
  private async tryOpenPlatformAuth(authorization: string): Promise<OpenPlatformUserEntity | null> {
    try {
      // 尝试开放平台用户的缓存格式：open_platform:${authorization}
      const openPlatformUser = await this.cacheManager.get<OpenPlatformUserEntity>(`open_platform:${authorization}`)
      return openPlatformUser || null
    } catch {
      return null
    }
  }

  /**
   * 开发环境模拟认证 - 基于原有逻辑扩展
   */
  private async handleDevMockAuth(authorization: string): Promise<{
    user: AdminEntity | OpenPlatformUserEntity,
    userId: string,
    userType: UserType,
    teamId?: string,
    applicationId?: string
  } | null> {
    // 首先尝试admin用户模拟（保持原有逻辑）
    try {
      const adminUser = await this.adminModel.findById<AdminEntity>(new Types.ObjectId(authorization))
      if (adminUser) {
        this.logger.debug(`模拟的会话: userId: ${authorization}`) // 保持原有日志格式
        return {
          user: adminUser,
          userId: (adminUser as any)._id.toString(),
          userType: UserType.ADMIN,
          teamId: undefined,
          applicationId: undefined
        }
      }
    } catch {
      // ignore
    }

    // 然后尝试开放平台用户模拟（新增功能）
    try {
      const openPlatformUser = await this.openPlatformUserModel.findById<OpenPlatformUserEntity>(
        new Types.ObjectId(authorization)
      )
      if (openPlatformUser) {
        this.logger.debug(`模拟的开放平台会话: userId: ${authorization}`)
        return {
          user: openPlatformUser,
          userId: (openPlatformUser as any)._id.toString(),
          userType: UserType.OPEN_PLATFORM,
          teamId: undefined,
          applicationId: undefined
        }
      }
    } catch {
      // ignore
    }

    return null
  }
}
