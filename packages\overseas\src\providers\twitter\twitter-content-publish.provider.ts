import { Injectable, Logger } from '@nestjs/common'
import { ContentPublishProvider } from '../content-publish.provider'
import { 
  OverseasContext, 
  PublishTaskData, 
  PublishResult, 
  PublishContentData, 
  PublishContentType,
  PublishTaskStatus 
} from '../types'
import { TwitterApi } from './twitter-api'

@Injectable()
export class TwitterContentPublishProvider extends ContentPublishProvider {
  private readonly logger = new Logger(TwitterContentPublishProvider.name)

  constructor(private readonly twitterApi: TwitterApi) {
    super()
  }

  /**
   * 验证发布内容是否符合Twitter要求
   */
  async validateContent(
    context: OverseasContext,
    content: PublishContentData
  ): Promise<{ valid: boolean; errors?: string[] }> {
    const errors: string[] = []

    this.logger.log(`验证Twitter发布内容: 类型=${content.type}`)

    // 检查内容类型支持
    const supportedTypes = this.getSupportedContentTypes()
    if (!supportedTypes.includes(content.type)) {
      errors.push(`不支持的内容类型: ${content.type}`)
    }

    // 检查文本长度
    const limits = this.getContentLimits()
    if (content.text && content.text.length > limits.maxTextLength) {
      errors.push(`文本内容超过最大长度限制 ${limits.maxTextLength} 字符`)
    }

    // 检查图片数量
    if (content.images && content.images.length > limits.maxImageCount) {
      errors.push(`图片数量超过最大限制 ${limits.maxImageCount} 张`)
    }

    // 检查视频大小（如果有视频）
    if (content.videoUrl && content.platformSpecific?.videoSize) {
      if (content.platformSpecific.videoSize > limits.maxVideoSize) {
        errors.push(`视频文件大小超过最大限制 ${Math.round(limits.maxVideoSize / 1024 / 1024)} MB`)
      }
    }

    // 检查图片格式
    if (content.images && content.images.length > 0) {
      for (const imageUrl of content.images) {
        const extension = imageUrl.split('.').pop()?.toLowerCase()
        if (extension && !limits.supportedImageFormats.includes(extension)) {
          errors.push(`不支持的图片格式: ${extension}`)
        }
      }
    }

    // Twitter必须有文本内容
    if (!content.text || content.text.trim().length === 0) {
      errors.push('Twitter发布必须包含文本内容')
    }

    // 检查是否同时包含视频和图片（Twitter不支持）
    if (content.videoUrl && content.images && content.images.length > 0) {
      errors.push('Twitter不支持同时发布视频和图片，请选择其中一种媒体类型')
    }

    this.logger.log(`内容验证完成: 错误数量=${errors.length}`)

    return {
      valid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * 发布内容到Twitter
   */
  async publishContent(
    context: OverseasContext,
    taskData: PublishTaskData
  ): Promise<PublishResult> {
    this.logger.log(`开始发布到Twitter: 任务=${taskData.taskId}, 账号=${context.accountOpenId}`)

    try {
      let result: any

      switch (taskData.content.type) {
        case PublishContentType.Text:
          result = await this.publishTextContent(context, taskData)
          break
        case PublishContentType.Image:
          result = await this.publishImageContent(context, taskData)
          break
        case PublishContentType.Video:
          result = await this.publishVideoContent(context, taskData)
          break
        case PublishContentType.Mixed:
          result = await this.publishMixedContent(context, taskData)
          break
        default:
          throw new Error(`不支持的内容类型: ${taskData.content.type}`)
      }

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Success,
        platformContentId: result.data.id,
        platformContentUrl: `https://twitter.com/i/status/${result.data.id}`,
        rawResponse: result,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`Twitter发布失败: 任务=${taskData.taskId}`, error)

      return {
        taskId: taskData.taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        errorCode: error.code || 'TWITTER_PUBLISH_ERROR',
        rawResponse: error.response?.data,
        completedAt: new Date()
      }
    }
  }

  /**
   * 查询发布状态
   */
  async getPublishStatus(
    context: OverseasContext,
    taskId: string,
    platformContentId: string
  ): Promise<PublishResult> {
    try {
      const tweet = await this.twitterApi.getTweet(context, platformContentId)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Success,
        platformContentId: platformContentId,
        platformContentUrl: `https://twitter.com/i/status/${platformContentId}`,
        rawResponse: tweet,
        completedAt: new Date()
      }
    } catch (error) {
      this.logger.error(`查询Twitter发布状态失败: 任务=${taskId}`, error)
      
      return {
        taskId: taskId,
        status: PublishTaskStatus.Failed,
        errorMessage: error.message,
        completedAt: new Date()
      }
    }
  }

  /**
   * 删除已发布的内容
   */
  async deleteContent(
    context: OverseasContext,
    platformContentId: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      await this.twitterApi.deleteTweet(context, platformContentId)
      return { success: true }
    } catch (error) {
      this.logger.error(`删除Twitter内容失败: ${platformContentId}`, error)
      return { 
        success: false, 
        errorMessage: error.message 
      }
    }
  }

  /**
   * 获取支持的内容类型
   */
  getSupportedContentTypes(): string[] {
    return [
      PublishContentType.Text,
      PublishContentType.Image,
      PublishContentType.Video,
      PublishContentType.Mixed
    ]
  }

  /**
   * 获取内容限制
   */
  getContentLimits() {
    return {
      maxTextLength: 280, // Twitter文本限制
      maxImageCount: 4,   // Twitter最大图片数
      maxVideoSize: 512 * 1024 * 1024, // 512MB
      maxVideoDuration: 140, // 140秒
      supportedImageFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
      supportedVideoFormats: ['mp4', 'mov']
    }
  }

  /**
   * 发布纯文本内容
   */
  private async publishTextContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const text = taskData.content.text
    return await this.twitterApi.createTweet(context, { text })
  }

  /**
   * 发布图片内容
   */
  private async publishImageContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { images, text } = taskData.content

    if (!images || images.length === 0) {
      throw new Error('图片内容发布必须包含至少一张图片')
    }

    this.logger.log(`开始上传 ${images.length} 张图片`)

    // 上传图片并获取media_ids
    const mediaIds = []
    for (let i = 0; i < images.length; i++) {
      const imageUrl = images[i]
      try {
        this.logger.log(`上传第 ${i + 1} 张图片: ${imageUrl}`)
        const media = await this.twitterApi.uploadMedia(context, imageUrl, 'image')
        mediaIds.push(media.media_id_string)
        this.logger.log(`图片上传成功，media_id: ${media.media_id_string}`)
      } catch (error) {
        this.logger.error(`图片上传失败: ${imageUrl}`, error)
        throw new Error(`第 ${i + 1} 张图片上传失败: ${error.message}`)
      }
    }

    this.logger.log(`所有图片上传完成，开始创建推文`)

    return await this.twitterApi.createTweet(context, {
      text: text,
      media: { media_ids: mediaIds }
    })
  }

  /**
   * 发布视频内容
   */
  private async publishVideoContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { videoUrl, text } = taskData.content

    if (!videoUrl) {
      throw new Error('视频内容发布必须包含视频URL')
    }

    this.logger.log(`开始上传视频: ${videoUrl}`)

    try {
      // 上传视频并获取media_id
      const media = await this.twitterApi.uploadMedia(context, videoUrl, 'video')
      this.logger.log(`视频上传成功，media_id: ${media.media_id_string}`)

      this.logger.log(`开始创建包含视频的推文`)

      return await this.twitterApi.createTweet(context, {
        text: text,
        media: { media_ids: [media.media_id_string] }
      })
    } catch (error) {
      this.logger.error(`视频上传失败: ${videoUrl}`, error)
      throw new Error(`视频上传失败: ${error.message}`)
    }
  }

  /**
   * 发布混合内容
   */
  private async publishMixedContent(context: OverseasContext, taskData: PublishTaskData): Promise<any> {
    const { text, images, videoUrl } = taskData.content

    this.logger.log(`发布混合内容: 文本=${!!text}, 图片=${images?.length || 0}张, 视频=${!!videoUrl}`)

    if (videoUrl) {
      // 如果有视频，发布视频（Twitter不支持视频+图片混合）
      this.logger.log('检测到视频内容，将发布视频推文')
      return await this.publishVideoContent(context, taskData)
    } else if (images && images.length > 0) {
      // 否则发布图片
      this.logger.log(`检测到 ${images.length} 张图片，将发布图片推文`)
      return await this.publishImageContent(context, taskData)
    } else {
      // 最后发布文本
      this.logger.log('未检测到媒体内容，将发布纯文本推文')
      return await this.publishTextContent(context, taskData)
    }
  }
}
