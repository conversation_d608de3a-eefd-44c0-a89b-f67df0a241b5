import { Module } from '@nestjs/common'
import {
  OpenPlatformUserMongoose,
  OpenPlatformApplicationMongoose,
  OpenPlatformUserRoleMongoose,
  OpenPlatformAppAuthorizationMongoose,
  OpenPlatformInvitationMongoose,
  OpenPlatformApplicationRechargeMongoose,
  OpenPlatformApplicationBalanceMongoose,
  OpenPlatformChannelUserRelationMongoose,
  OrderMongoose,
  UserMongoose,
  TeamMongoose,
  AdminMongoose,
  MemberMongoose,
  PlatformAccountMongoose,
  TrafficBillingMongoose
} from '@yxr/mongo'
import { UnifiedAuthService } from '../../common/services/unified-auth.service'
import { OpenPlatformAuthController } from './controllers/auth.controller'
import { ApplicationController } from './controllers/application.controller'
import { InvitationController } from './controllers/invitation.controller'
import { AuthorizationController } from './controllers/authorization.controller'
import { ChannelUserRelationController } from './controllers/channel-user-relation.controller'
import { OpenPlatformOrderController } from './controllers/open-platform-order.controller'
import { OpenPlatformOrderAdminController } from './controllers/open-platform-order-admin.controller'
import { RechargeController } from './controllers/recharge.controller'
import { ApplicationTokenController } from './controllers/application-token.controller'
import { GatewayUserController } from './controllers/gateway-user.controller'
import { OpenPlatformAuthService } from './services/auth.service'
import { ApplicationService } from './services/application.service'
import { InvitationService } from './services/invitation.service'
import { AuthorizationService } from './services/authorization.service'
import { ChannelUserRelationService } from './services/channel-user-relation.service'
import { OpenPlatformPermissionService } from './services/open-platform-permission.service'
import { RechargeService } from './services/recharge.service'
import { GatewayUserService } from './services/gateway-user.service'
import { TeamManagementService } from './services/team-management.service'
import { TeamManagementController } from './controllers/team-management.controller'
import { UserAdminService } from './services/user-admin.service'
import { UserAdminController } from './controllers/user-admin.controller'
import { OpenPlatformOrderManagerModule } from '@yxr/open-platform-order'
import { AuthModule } from '../../common/auth/auth.module'
import { OpenPlatformOrderService } from './services/open-platform-order.service'
import { OrderManagerModule } from '@yxr/order'

// 注意：ApplicationTokenService 和 DataIsolationService 现在由 AuthModule 提供

@Module({
  imports: [
    // 移除 JwtModule 和 AdminMongoose，这些现在由 AuthModule 提供
    OpenPlatformUserMongoose,
    OpenPlatformApplicationMongoose,
    OpenPlatformUserRoleMongoose,
    OpenPlatformAppAuthorizationMongoose,
    OpenPlatformInvitationMongoose,
    OpenPlatformApplicationRechargeMongoose,
    OpenPlatformApplicationBalanceMongoose,
    OpenPlatformChannelUserRelationMongoose,
    OrderMongoose,
    UserMongoose,
    OpenPlatformOrderManagerModule,
    TeamMongoose,
    AdminMongoose,
    MemberMongoose,
    PlatformAccountMongoose,
    TrafficBillingMongoose,
    AuthModule,
    OrderManagerModule
  ],
  controllers: [
    OpenPlatformAuthController,
    ApplicationController,
    InvitationController,
    AuthorizationController,
    ChannelUserRelationController,
    OpenPlatformOrderController,
    OpenPlatformOrderAdminController,
    RechargeController,
    ApplicationTokenController,
    GatewayUserController,
    TeamManagementController,
    UserAdminController
  ],
  providers: [
    UnifiedAuthService,
    OpenPlatformAuthService,
    ApplicationService,
    InvitationService,
    AuthorizationService,
    ChannelUserRelationService,
    OpenPlatformPermissionService,
    RechargeService,
    GatewayUserService,
    TeamManagementService,
    UserAdminService,
    OpenPlatformOrderService
    // ApplicationTokenService 和 DataIsolationService 现在由 AuthModule 全局提供
  ],
  exports: [
    UnifiedAuthService,
    OpenPlatformAuthService,
    ApplicationService,
    InvitationService,
    AuthorizationService,
    ChannelUserRelationService,
    OpenPlatformPermissionService,
    RechargeService,
    GatewayUserService,
    TeamManagementService,
    UserAdminService,
    OpenPlatformOrderService
    // ApplicationTokenService 由 AuthModule 导出，无需在这里重复导出
  ]
})
export class OpenPlatformModule {}
