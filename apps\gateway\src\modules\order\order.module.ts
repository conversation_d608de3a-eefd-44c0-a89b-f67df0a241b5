import { Module } from '@nestjs/common'
import {
  InterestMongoose,
  OrderMongoose,
  UserMongoose,
  TeamMongoose,
  ChannelMongoose,
  MemberMongoose
} from '@yxr/mongo'
import { OrderService } from './order.service'
import { OrderController } from './order.controller'
import { OrderManagerModule } from '@yxr/order'
import { OrderEventService } from './order.event'
import { WebhookService } from '../webhook/webhook.service'

@Module({
  imports: [
    OrderMongoose,
    TeamMongoose,
    UserMongoose,
    InterestMongoose,
    ChannelMongoose,
    MemberMongoose,
    OrderManagerModule
  ],
  controllers: [OrderController],
  providers: [OrderService, OrderEventService, WebhookService]
})
export class OrderModule {}
