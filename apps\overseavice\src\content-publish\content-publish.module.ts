import { Module } from '@nestjs/common'
import { OverseasModule } from '@yxr/overseas'
import { ContentPublishController } from './content-publish.controller'
import { ContentPublishService } from './content-publish.service'

@Module({
  imports: [
    OverseasModule
  ],
  controllers: [ContentPublishController],
  providers: [ContentPublishService],
  exports: [ContentPublishService]
})
export class ContentPublishModule {}
