import { Module } from '@nestjs/common'
import { AccountAuthModule } from './account-auth/account-auth.module'
import { ContentPublishModule } from './content-publish/content-publish.module'
import { OverseasModule } from '@yxr/overseas'
import { CacheModule } from '@nestjs/cache-manager'
import { redisStore } from 'cache-manager-ioredis-yet'
import { ConfigModule } from '@yxr/config'

const redis = new Map<string, { host: string, port: number, db: number, password: string }>([
  ['local', {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT!, 10),
    db: parseInt(process.env.REDIS_NORMAL_DB!, 10),
    password: process.env.REDIS_PASSWORD
  }],
  ['dev', {
    host: 'redis',
    port: 6379,
    db: 0,
    password: 'LFFSH47vXGNZq4NG@kbcFLyK'
  }],
  ['test', {
    host: 'redis',
    port: 6379,
    db: 1,
    password: 'LFFSH47vXGNZq4NG@kbcFLyK'
  }],
  ['prod', {
    host: 'redis',
    port: 6379,
    db: 2,
    password: 'LFFSH47vXGNZq4NG@kbcFLyK'
  }]
])

@Module({
  imports: [
    ConfigModule,
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: async () => {
        return {
          ...redis.get(process.env.NODE_ENV),
          store: redisStore
        }
      }
    }),
    AccountAuthModule,
    ContentPublishModule
  ],
  providers: []
})
export class OverseaviceModule {
}
