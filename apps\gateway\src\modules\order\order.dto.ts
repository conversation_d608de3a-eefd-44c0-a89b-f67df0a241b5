import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { OrderSource, OrderStatus, OrderType, PayType } from '@yxr/common'
import { Type } from 'class-transformer'
import { OrderPriceResponseDto } from '@yxr/order'

export class orderResponseCreateOrder {
  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112'
  })
  orderNo: string
}

export class OrderResponseCreateOrderDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: orderResponseCreateOrder
  })
  data: orderResponseCreateOrder
}

export class OrderRequestCreateOrderDTO {
  @ApiProperty({
    description: '权益包id',
    required: true,
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '权益包数量',
    required: true,
    example: 1
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: false
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class orderPriceRequestDTO {
  @ApiProperty({
    enum: OrderType,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)',
    required: true
  })
  @IsNotEmpty()
  @IsEnum(OrderType)
  orderType: OrderType

  @ApiProperty({
    description: '权益包id',
    required: true,
    example: 1
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '权益包数量',
    required: false,
    example: 1
  })
  @IsNumber()
  @IsOptional()
  interestCount: number

  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  month: number

  @ApiProperty({
    description: '天数数量',
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  days: number
}

export class UpgradeOrderRequest {
  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    type: Number
  })
  @IsNumber()
  @IsNotEmpty()
  interestCount: number

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: true
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class RenewOrderRequest {
  @ApiProperty({
    description: '月份数量',
    example: 1,
    required: true
  })
  @IsNumber()
  @IsNotEmpty()
  month: number

  @ApiProperty({
    type: String
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    type: Boolean,
    description: '是否为对公转账',
    required: true
  })
  @IsBoolean()
  isCorporateTransfer: boolean
}

export class OrderStatusRequestDTO {
  @ApiProperty({
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    example: OrderStatus.Cancelled,
    enum: OrderStatus,
    required: true
  })
  @IsString()
  orderStatus: OrderStatus
}

export class OrderStatusResponse {
  @ApiResponseProperty({
    enum: Object.values(OrderStatus),
    type: OrderStatus,
    example: OrderStatus.Pending
  })
  orderStatus: OrderStatus
}

export class OrderResponseQueryOrderDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderStatusResponse
  })
  data: OrderStatusResponse
}

export class VipInfo {
  /**
   * 权益包数量
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  interestCount: number

  /**
   * 账号数
   * @deprecated 已废弃, 使用 platformAccountCapacity
   */
  @ApiResponseProperty({
    type: Number,
    example: 20,
    deprecated: true
  })
  platformAccountCount: number  // 变更为账号点数

  /**
   * 账号点数
   */
  @ApiResponseProperty({
    type: Number,
    example: 20
  })
  platformAccountCapacity: number

  /**
   * 成员数
   */
  @ApiResponseProperty({
    type: Number,
    example: 5
  })
  teamMemberCount: number

  /**
   * 素材库额度
   */
  @ApiResponseProperty({
    type: Number,
    example: 1000
  })
  capacityLimit: number

  /**
   * VIP时长
   */
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  month: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  freeMonth: number

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  expirationTime: number
}

export class OrderInfo {
  @ApiProperty({
    type: String,
    example: '订单编号'
  })
  orderNo: string

  @ApiProperty({
    type: String,
    example: '团队名称'
  })
  teamName: string

  @ApiProperty({
    type: String,
    example: '团队编号(唯一ID)'
  })
  code: string

  @ApiProperty({
    type: Number,
    example: '创建时间'
  })
  createdAt: number

  @ApiProperty({
    type: String,
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    enum: OrderStatus,
    example: OrderStatus.Cancelled
  })
  orderStatus: OrderStatus

  @ApiProperty({
    type: String,
    enum: OrderType,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)',
    example: OrderType.Create
  })
  orderType: OrderType

  @ApiProperty({
    type: String,
    enum: OrderSource,
    description: '订单类型 online:线上, system:系统',
    example: OrderSource.Online
  })
  type: OrderSource

  @ApiResponseProperty({
    type: Number,
    example: '订单金额'
  })
  totalAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '待支付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '已支付金额'
  })
  payAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: String,
    example: '支付方式'
  })
  payType: string
}

export class OrderInfoResponse {
  @ApiResponseProperty({
    type: VipInfo,
    example: 1
  })
  vipInfo: VipInfo

  @ApiResponseProperty({
    type: OrderInfo,
    example: 1
  })
  orderInfo: OrderInfo
}

export class OrderInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInfoResponse,
    example: 1
  })
  data: OrderInfoResponse
}

export class PayInfoResponse {
  @ApiProperty({
    type: String,
    description: '客服二维码地址',
    example: 'https://example.com/pay'
  })
  corporateTransfer: string

  @ApiProperty({
    type: String,
    description: '支付宝支付二维码地址',
    example: 'https://example.com/pay'
  })
  alipayUrl: string

  @ApiProperty({
    type: String,
    description: '微信支付二维码地址',
    example: 'https://example.com/pay'
  })
  weixinUrl: string

  @ApiProperty({
    type: String,
    enum: OrderType,
    description: '订单类型(create:开通,upgrade:升级,renew:续费,gift赠送)',
    example: OrderType.Create
  })
  orderType: OrderType

  @ApiProperty({
    type: Number,
    description: '产品金额',
    example: 100
  })
  price: number

  @ApiProperty({
    type: Number,
    description: '应付金额',
    example: 50
  })
  dueAmount: number

  @ApiProperty({
    type: String,
    description: '订单号'
  })
  orderNo: string

  @ApiProperty({
    type: Number,
    description: '订单有效时间'
  })
  remainingTimeInSeconds: number
}

export class PayInfoResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PayInfoResponse
  })
  data: PayInfoResponse
}

export class PeddingOrderResponse {
  @ApiResponseProperty({
    type: Boolean,
    example: true
  })
  hasPendingOrder: boolean

  @ApiResponseProperty({
    type: Number,
    example: 0
  })
  count: number
}

export class PeddingOrderResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PeddingOrderResponse
  })
  data: PeddingOrderResponse
}

class OrderInterestVipOftenDTO {
  @ApiProperty({
    type: Number,
    description: '月份数',
    example: 100
  })
  mount: number

  @ApiProperty({
    type: Number,
    description: '赠送月份数',
    example: 100
  })
  present: number
}

export class OrderInterestData {
  @ApiProperty({
    type: String,
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  id: string

  /** @deprecated 已废弃, 请使用 platformAccountCapacity 代替 */
  @ApiProperty({
    type: Number,
    example: 100,
    deprecated: true
  })
  platformAccountCount: number  // 变更为账号点数

  @ApiProperty({
    type: Number,
    example: 100
  })
  platformAccountCapacity: number

  @ApiProperty({
    type: Number,
    description: '素材库容量大小',
    example: 10
  })
  capacityLimit: number

  @ApiProperty({
    type: Number,
    description: '网络流量限制',
    example: 10
  })
  networkTrafficLimit: number

  @ApiProperty({
    type: Boolean,
    description: '是否app发布',
    example: false
  })
  appPublish: boolean

  @ApiProperty({
    type: Number,
    example: 10
  })
  memberCount: number

  @ApiProperty({
    type: Number,
    example: 100
  })
  price: number

  @ApiProperty({
    type: [OrderInterestVipOftenDTO]
  })
  vipOften: OrderInterestVipOftenDTO[]
}

export class OrderInterestResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderInterestData
  })
  data: OrderInterestData
}

export class Orders {
  @ApiResponseProperty({
    type: String,
    example: '订单id'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: 'EX123KIO112 订单编号'
  })
  orderNo: string

  @ApiResponseProperty({
    type: String,
    example: '所属团队'
  })
  teamName: string

  @ApiResponseProperty({
    type: String,
    example: '订单创建人'
  })
  creatorName: string

  @ApiProperty({
    type: String,
    enum: OrderStatus,
    description: '订单状态 (pending:待支付,paid:已支付,cancelled:已取消,refunded:已退费)',
    example: OrderStatus.Paid
  })
  orderStatus: OrderStatus

  @ApiResponseProperty({
    type: String,
    enum: OrderSource,
    example: '订单类型 online:线上, system:系统'
  })
  type: OrderSource

  @ApiResponseProperty({
    type: Number,
    example: '创建时间'
  })
  createTime: number

  @ApiResponseProperty({
    type: Number,
    example: '支付时间'
  })
  payTime: number

  @ApiResponseProperty({
    type: Number,
    example: '过期时间'
  })
  expireTime: number

  @ApiResponseProperty({
    type: Number,
    example: '剩余时间（单位秒）'
  })
  remainingTimeInSeconds: number

  @ApiResponseProperty({
    type: Number,
    example: '订单金额'
  })
  price: number

  @ApiResponseProperty({
    type: Number,
    example: '应付金额'
  })
  dueAmount: number

  @ApiResponseProperty({
    type: Number,
    example: '实付金额'
  })
  payAmount: number

  @ApiProperty({
    type: String,
    enum: PayType,
    description:
      '支付方式(wechatPay:微信支付,alipay:支付宝支付,corporateTransfer:对公转账,other:其他)',
    example: PayType.Other
  })
  payType: PayType

  @ApiProperty({
    type: String,
    enum: OrderType,
    description: '订单类型(create:开通,renew:续费,upgrade:升级,gift:赠送)'
  })
  orderType: OrderType
}

export class OrdersListResponse {
  @ApiResponseProperty({
    type: [Orders]
  })
  data: Orders[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class OrdersListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrdersListResponse
  })
  data: OrdersListResponse
}

export class OrderListRequest {
  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10
}

export class OrderPriceDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: OrderPriceResponseDto
  })
  data: OrderPriceResponseDto
}

export class ChannelGiftRequestDTO {
  @ApiProperty({
    description: '渠道码',
    example: '10001',
    required: true
  })
  @IsString()
  @IsNotEmpty({ message: '渠道码不能为空' })
  channelCode: string
}

export class ChannelGiftResponseDto {
  @ApiProperty({
    type: String,
    description: '渠道码',
    example: '10001'
  })
  channelCode: string

  @ApiProperty({
    type: Number,
    description: '赠送天数',
    example: 1
  })
  giftDays: number
}

export class ChannelGiftResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ChannelGiftResponseDto
  })
  data: ChannelGiftResponseDto
}
