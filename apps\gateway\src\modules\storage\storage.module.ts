import { Modu<PERSON> } from '@nestjs/common'
import { StorageController } from './storage.controller'
import { StorageService } from './storage.service'
import { AuthorizationService } from '../../common/security/authorization.service'
import { MemberMongoose } from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { StorageCallbackController } from './storage-callback.controller'
import { TianyiyunOssService } from '@yxr/common'
import { HuoshanModule } from '@yxr/huoshan'

@Module({
  imports: [MemberMongoose, HuoshanModule,CommonModule],
  controllers: [StorageController, StorageCallbackController],
  providers: [StorageService, AuthorizationService, TianyiyunOssService]
})
export class StorageModule {}
