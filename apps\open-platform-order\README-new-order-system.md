# 新订单系统设计文档

## 概述

新订单系统是对开放平台订单逻辑的重大重构，简化了订单类型，优化了权益计算逻辑，提供了更灵活的订单管理方式。

## 核心变更

### 1. 订单类型简化

**原系统：**
- 主订单（Main）：开通/续费订单，时间不能重叠
- 增购订单（Addon）：必须在主订单时间范围内，有父子关系

**新系统：**
- 账号点数订单（AccountPoints）：购买账号使用权益
- 流量订单（Traffic）：购买流量权益，直接累积

### 2. 时间规则调整

**原规则：**
- 主订单时间不能重叠
- 增购订单必须在主订单时间范围内
- 订单开始时间可以是过去

**新规则：**
- 允许所有订单时间重叠
- 订单开始时间必须 >= 当前日期
- 重叠期间权益叠加计算

### 3. 权益计算逻辑

**账号点数订单：**
- 重叠期间账号点数叠加
- 例：两个5账号订单重叠时，该时间段账号权益为10

**流量订单：**
- 直接累积到团队流量总量
- 每次购买的流量有效期1年
- 过期流量自动清零

## 数据库设计

### 新增枚举类型

```typescript
// 订单类型
export enum OrderType {
  OpenPlatformAccountPoints = 'open_platform_account_points', // 账号点数订单
  OpenPlatformTraffic = 'open_platform_traffic' // 流量订单
}

// 资源类型
export enum OpenPlatformOrderResourceType {
  AccountPoints = 'account_points', // 账号点数
  Traffic = 'traffic' // 流量
}
```

### 订单表新增字段

```typescript
// OrderEntity 新增字段
resourceType?: OpenPlatformOrderResourceType // 资源类型
trafficExpiredAt?: Date // 流量过期时间（仅流量订单）
```

### 团队表新增字段

```typescript
// TeamEntity 新增字段
accumulatedTraffic?: number // 累积流量总量（KB）
trafficRecords?: Array<{   // 流量过期记录
  amount: number           // 流量数量（KB）
  expiredAt: Date         // 过期时间
  createdAt: Date         // 创建时间
}>
```

## API接口

### 1. 创建账号点数订单

```http
POST /new-orders/account-points
Authorization: Bearer {app_token}
Content-Type: application/json

{
  "teamId": "507f1f77bcf86cd799439011",
  "userId": "507f1f77bcf86cd799439011",
  "accountCount": 10,
  "duration": 3,
  "startTime": "2024-01-01T00:00:00.000Z",
  "sourceAppId": "app_123456",
  "remark": "账号点数订单"
}
```

**特点：**
- 支持时间重叠
- 重叠期间账号点数叠加
- 自动延长团队VIP过期时间

### 2. 创建流量订单

```http
POST /new-orders/traffic
Authorization: Bearer {app_token}
Content-Type: application/json

{
  "teamId": "507f1f77bcf86cd799439011",
  "userId": "507f1f77bcf86cd799439011",
  "trafficCount": 100,
  "sourceAppId": "app_123456",
  "remark": "流量订单"
}
```

**特点：**
- 流量直接累积到团队
- 有效期固定1年
- 立即生效

### 3. 查询团队账号点数

```http
GET /new-orders/teams/{teamId}/account-points
Authorization: Bearer {app_token}
```

**返回：**
- 当前有效账号点数总和
- 有效订单列表
- VIP过期时间

## 权益计算逻辑

### 账号点数计算

```typescript
// 示例：团队有3个账号点数订单
订单A: 5账号, 2024-01-01 到 2024-04-01
订单B: 3账号, 2024-02-01 到 2024-03-01  
订单C: 2账号, 2024-02-15 到 2024-05-01

// 在2024-02-20查询时：
// 订单A: 有效 (5账号)
// 订单B: 有效 (3账号) 
// 订单C: 有效 (2账号)
// 总计: 10账号
```

### 流量累积计算

```typescript
// 示例：团队购买流量
第1次: 100GB, 2024-01-01购买, 2025-01-01过期
第2次: 50GB,  2024-06-01购买, 2025-06-01过期
第3次: 200GB, 2024-12-01购买, 2025-12-01过期

// 团队累积流量: 350GB
// 流量记录: 3条记录，各自独立过期
```

## 服务架构

### 核心服务

1. **NewOrderService** - 新订单创建服务
   - 创建账号点数订单
   - 创建流量订单
   - 权益计算

2. **TrafficCleanupService** - 流量清理服务
   - 定时清理过期流量
   - 手动清理接口
   - 流量统计查询

3. **BenefitCalculationService** - 权益计算服务
   - 团队权益计算
   - 权益历史查询
   - 批量权益计算

### 定时任务

```typescript
// 每天凌晨2点清理过期流量
@Cron('0 2 * * *')
async handleTrafficCleanup(): Promise<void> {
  // 清理所有团队的过期流量记录
  // 更新累积流量总量
}
```

## 业务优势

### 1. 简化的订单管理
- 只有两种订单类型，易于理解
- 移除复杂的父子关系
- 支持灵活的时间安排

### 2. 灵活的权益叠加
- 账号点数可以叠加使用
- 流量累积制，使用更灵活
- VIP时间自动延长

### 3. 清晰的过期机制
- 账号点数按订单时间过期
- 流量统一1年有效期
- 自动清理过期数据

## 迁移策略

### 1. 数据兼容性
- 保留原有订单数据
- 新增字段使用可选类型
- 渐进式迁移

### 2. API兼容性
- 新API使用独立路由
- 原API继续可用
- 逐步迁移客户端

### 3. 权益计算兼容
- 新旧系统权益分别计算
- 提供统一查询接口
- 确保数据一致性

## 监控和维护

### 1. 关键指标
- 订单创建成功率
- 权益计算准确性
- 流量清理执行状态
- 系统性能指标

### 2. 日志记录
- 详细的订单创建日志
- 权益计算过程日志
- 流量清理执行日志
- 错误和异常日志

### 3. 数据一致性检查
- 定期验证权益计算
- 检查流量记录完整性
- 监控VIP状态准确性

## 性能优化

### 1. 查询优化
- 合理的数据库索引
- 缓存热点数据
- 批量查询优化

### 2. 计算优化
- 权益计算结果缓存
- 异步处理大批量操作
- 分页查询大数据集

### 3. 存储优化
- 定期清理历史数据
- 压缩过期记录
- 优化数据结构

## 测试策略

### 1. 单元测试
- 订单创建逻辑测试
- 权益计算算法测试
- 流量清理逻辑测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 定时任务测试

### 3. 性能测试
- 大量订单创建测试
- 权益计算性能测试
- 并发访问测试

## 未来扩展

### 1. 功能扩展
- 支持更多资源类型
- 添加订单修改功能
- 实现订单退款机制

### 2. 性能扩展
- 分布式权益计算
- 实时权益更新
- 大数据分析支持

### 3. 业务扩展
- 多应用权益共享
- 权益转移功能
- 高级权益策略
