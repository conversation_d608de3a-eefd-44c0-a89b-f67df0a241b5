<!doctype html>
<html>
  <head>
    <title>Socket.IO chat</title>
  </head>
  <body style="display: flex">
    <div style="flex: 1">
      <div id="id">未连接</div>
      <div>
        <span>url：</span>
        <input type="text" id="url" value="ws://localhost:3002" />
        <button id="connect">连接socketIO</button>
      </div>
      <div style="margin-top: 8px">
        <span>事件名</span>
        <input type="text" id="event" value="event" />
        <button id="send" style="margin: 4px; padding: 2px 8px">发送</button>
      </div>
      <div style="margin-top: 8px">
        <span>消息体</span>
        <button id="pretty">格式化</button>
        <div style="margin-top: 8px">
          <textarea id="msgBody" cols="60" rows="20">{a:1}</textarea>
        </div>
      </div>
      <div style="margin-top: 8px">
        <span>消息</span><input type="text" id="msg" /><button id="sendmsg">发送</button>
      </div>
    </div>
    <div style="width: 80px"></div>
    <div style="flex: 1">
      <div style="display: flex; margin-top: 8px">
        <div style="flex: 1">
          <div>收到事件：</div>
          <ul id="events"></ul>
        </div>
      </div>
    </div>
  </body>
  <script src="https://cdn.jsdelivr.net/npm/socket.io-client/dist/socket.io.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/JSON/dist/JSON.min.js"></script>
  <script>
    function q(selector) {
      return document.querySelector(selector)
    }
    var socket = initSocket()
    function initSocket() {
      if (socket) {
        socket.disconnect()
      }
      const _socket = io(`${q('#url').value}`, {
          path: '/socket/socket.io',
          query: {
            authorization: 'taQlWX94rKTx3o1UhQ4xs'
          },
          transports: ['websocket']
      })
      _socket.on('connect', (res) => {
        console.log(res)
        q('#id').innerHTML = `连接成功：${_socket.id}`
      })
      _socket.on('connect_error', (error) => {
        console.log('connect_error:', error)
        q('#id').innerHTML = '连接失败' + error
      })
      _socket.on('disconnect', (reason) => {
        console.log('disconnect:', reason)
        q('#id').innerHTML = '断开连接' + reason
      })
      _socket.onAny((name, data) => {
        console.log(name)
        console.log(data)
        const ul = q('#events')
        const li = document.createElement('li')
        li.innerText = `${name}：${JSON.stringify(data)}`
        ul.appendChild(li)
      })
      return _socket
    }
    q('#connect').onclick = () => {
      socket = initSocket()
    }
    q('#pretty').onclick = () => {
      const value = parseJson(q('#msgBody').value, null, 2)
      q('#msgBody').value = value
    }
    q('#send').onclick = () => {
      const value = JSON.parse(q('#msgBody').value)
      socket.emit(q('#event').value, value)
    }
    q('#sendmsg').onclick = () => {
      socket.emit('events', {
        msgType: 1,
        msg: q('#msg').value,
        timestamp: Date.now(),
        receiver: 'de503db827063a17eba15a9e10e17440',
        receiverType: 1,
        localMsgId: Date.now(),
        sendUserName: '郭伟',
        sendAvatar: 'http://dummyimage.com/100x100'
      })
    }
    function parseJson(value, placeholder, blank) {
      try {
        return JSON.stringify(JSON.parse(value), placeholder, blank)
      } catch {
        return value
      }
    }
    function generateClientId(data) {
      return `${data.userId}|${data.deviceType}|${data.deviceId}`
    }
  </script>
</html>
