import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { ObjectId, Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class OnlineScriptEntity {
  //类型 rpa/spider/windows/macos/ios/android
  @Prop({
    type: String,
    required: true
  })
  type: string

  @Prop({
    type: String,
    required: true
  })
  version: string

  //数字版本号
  @Prop({
    type: Number,
    required: true
  })
  numberVersion: number

  @Prop({
    type: String,
    required: false
  })
  storage?: string

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Types.ObjectId,
    required: false
  })
  userId?: Types.ObjectId

  //版本公告
  @Prop({
    type: String,
    required: false,
    default: ''
  })
  notice?: string

  //请求地址
  @Prop({
    type: String,
    required: false,
    default: ''
  })
  requestUrl?: string

  @Prop({
    type: Boolean,
    required: false,
    default: false
  })
  isForce?: boolean
}

export const OnlineScriptSchema: ModelDefinition = {
  name: OnlineScriptEntity.name,
  schema: SchemaFactory.createForClass(OnlineScriptEntity)
}

OnlineScriptSchema.schema.index({ type: 1, version: 1 }, { unique: true })

export const OnlineScriptMongoose = MongooseModule.forFeature([OnlineScriptSchema])
