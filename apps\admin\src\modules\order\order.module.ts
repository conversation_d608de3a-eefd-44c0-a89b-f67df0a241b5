import { Module } from '@nestjs/common'
import {
  AdminMongoose,
  ContractMongoose,
  InterestMongoose,
  MemberMongoose,
  OrderMongoose,
  TeamMongoose} from '@yxr/mongo'
import { OrderService } from './order.service'
import { OrderController } from './order.controller'
import { OrderManagerModule } from '@yxr/order'
import { OrderEventService } from './order.event'
import { WebhookModule } from '../webhook/webhook.module'

@Module({
  imports: [
    OrderMongoose,
    TeamMongoose,
    MemberMongoose,
    InterestMongoose,
    ContractMongoose,
    AdminMongoose,
    OrderManagerModule,
    WebhookModule
  ],
  controllers: [OrderController],
  providers: [OrderService, OrderEventService]
})
export class OrderModule {}
