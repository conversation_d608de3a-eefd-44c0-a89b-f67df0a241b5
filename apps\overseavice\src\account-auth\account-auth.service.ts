import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { AuthorizationStatus } from './account-auth.dto'
import { OverseasProviderFactory } from '@yxr/overseas'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { nanoid } from 'nanoid'
import axios from 'axios'

type AuthorizationStateCache = {
  platform: string
  teamId: string
  userId: string
  status: AuthorizationStatus
  errorMessage: string
}

@Injectable()
export class AccountAuthService {
  logger = new Logger(AccountAuthService.name)

  constructor(
    private readonly overseasProviderFactory: OverseasProviderFactory,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>) {
  }

  private authorizationStateCacheExpire = 1000 * 60 * 5 // 5 分钟

  private _calculateAuthorizationStateCacheKey(state: string): string {
    return `account-auth:${state}`
  }

  /**
   * 获取平台的授权地址和授权状态码
   * @param platform
   * @param teamId
   * @param userId
   */
  async getAuthorizationUrl(platform: string, teamId: string, userId: string): Promise<{
    authorizationUrl: string,
    state: string
  }> {

    const state = nanoid()
    const provider = await this.overseasProviderFactory.accountAuthProvider(platform)
    const authorizationUrl = await provider.generateAuthorizationUrl(state)

    await this.cacheManager.set(
      this._calculateAuthorizationStateCacheKey(state),
      {
        platform: platform,
        teamId: teamId,
        userId: userId,
        status: AuthorizationStatus.Waiting
      },
      this.authorizationStateCacheExpire
    )

    return {
      authorizationUrl: `${process.env.OVERSEAS_BASE_ADDRESS}auth/redirect?to=${Buffer.from(authorizationUrl).toString('base64')}`,
      state: state
    }
  }

  async authorization(code: string, state: string) {

    const cacheKey = this._calculateAuthorizationStateCacheKey(state)
    const cache = await this.cacheManager.get<AuthorizationStateCache>(cacheKey)
    if (!cache) {
      throw new ForbiddenException('无效状态码')
    }

    // 用户授权成功, 平台已经 callback 到海外服务站点, 下一步需要将授权的账号信息发送到国内服务

    const accountAuthProvider = await this.overseasProviderFactory.accountAuthProvider(cache.platform)

    try {

      const context = { teamId: cache.teamId, userId: cache.userId }

      // 获取授权的账号信息
      const accounts = await accountAuthProvider.exchangeAuthCodeForAccounts(context, code, state)
      this.logger.log(`[account-auth] accounts: ${JSON.stringify(accounts)}`)

      // 组装成账号信息, 返回给Lite服务API
      const result = await axios.post(
        `${process.env.LITE_BASE_ADDRESS}overseas-integration/auth-callback`,
        { state: state, accounts: accounts.map(x => ({
            openId: x.openId,
            credentials: x.credentials,
            nick_name: x.name,
            avatar: x.avatar,
          }))},
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 30 * 1000 // 30秒超时
        }
      )

      this.logger.log(`[account-auth] result: ${JSON.stringify(result.data)}`)

      // 更新缓存状态
      await this.cacheManager.del(cacheKey)
      return [] // TODO: 返回给callback页面, 以便于可以显示授权结果
    } catch (error) {
      // TODO: 如果授权失败, 需要将授权失败的原因发送到国内服务
      console.error(error)
    }
  }
}
