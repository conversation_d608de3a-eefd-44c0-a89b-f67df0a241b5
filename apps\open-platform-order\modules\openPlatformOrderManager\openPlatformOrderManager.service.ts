import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common'
import { Cron } from '@nestjs/schedule'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, Model, Types } from 'mongoose'
import { OrderEntity, TeamEntity } from '@yxr/mongo'
import { OrderStatus, OrderType, OrderSource, OpenPlatformOrderResourceType } from '@yxr/common'
import dayjs from 'dayjs'

/**
 * 新订单系统服务
 * 实现简化的账号点数订单和流量订单逻辑
 */
@Injectable()
export class OpenPlatformOrderManagerService {
  private readonly logger = new Logger(OpenPlatformOrderManagerService.name)

  constructor(
    @InjectModel(OrderEntity.name)
    private orderModel: Model<OrderEntity>,
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectConnection() private connection: Connection
  ) {}

  /**
   * 创建账号点数订单
   */
  async createAccountPointsOrder(createDto: {
    userId: string
    teamId: string
    accountCapacityLimit: number
    duration: number // 月数
    startTime: Date
    payAmount: number
    sourceAppId?: string
    remark?: string
  }): Promise<any> {
    this.logger.log(
      `创建账号点数订单: teamId=${createDto.teamId}, accountCapacityLimit=${createDto.accountCapacityLimit}, duration=${createDto.duration}`
    )

    // 验证开始时间不能是过去
    const now = dayjs().startOf('day').toDate()

    if (createDto.startTime < now) {
      throw new BadRequestException('订单开始时间不能小于当前日期')
    }

    const endTime = dayjs(createDto.startTime).add(createDto.duration, 'month').toDate()

    const session = await this.connection.startSession()
    session.startTransaction()

    try {
      // 生成订单号
      const orderNo = this.generateOrderNo()

      // 创建订单
      const [order] = await this.orderModel.create(
        [
          {
            orderNo,
            userId: new Types.ObjectId(createDto.userId),
            teamId: new Types.ObjectId(createDto.teamId),
            orderSource: OrderSource.OpenPlatform,
            orderType: OrderType.OpenPlatformAccountPoints,
            orderStatus: OrderStatus.Paid,
            resourceType: OpenPlatformOrderResourceType.AccountPoints,
            accountCapacity: createDto.accountCapacityLimit,
            duration: createDto.duration,
            startTime: createDto.startTime,
            endTime,
            sourceAppId: createDto.sourceAppId,
            openPlatformUserId: new Types.ObjectId(createDto.userId),
            remark: createDto.remark || '',
            totalAmount: createDto.payAmount, // 暂不涉及金额
            payableAmount: createDto.payAmount,
            interestId: new Types.ObjectId('000000000000000000000000'), // 占位符
            interestCount: 1
          }
        ],
        { session }
      )

      // 更新团队VIP过期时间和账号点数
      await this.updateTeamVipExpiration(
        createDto.teamId,
        createDto.startTime,
        endTime,
        createDto.accountCapacityLimit,
        session
      )

      await session.commitTransaction()

      this.logger.log(
        `账号点数订单创建成功: ${orderNo}, 团队: ${createDto.teamId}, ` +
          `生效时间: ${createDto.startTime.toISOString()} - ${endTime.toISOString()}`
      )

      return await this.formatOrderResponse(order)
    } catch (error) {
      await session.abortTransaction()
      this.logger.error(`账号点数订单创建失败: ${error.message}`, error.stack)
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * 创建流量订单
   */
  async createTrafficOrder(createDto: {
    userId: string
    teamId: string
    trafficCount: number // GB
    payAmount: number
    sourceAppId?: string
    remark?: string
  }): Promise<any> {
    this.logger.log(
      `创建流量订单: teamId=${createDto.teamId}, trafficCount=${createDto.trafficCount}GB`
    )

    const now = new Date()

    const trafficExpiredAt = dayjs().add(1, 'year').toDate()

    const session = await this.connection.startSession()
    session.startTransaction()

    try {
      // 生成订单号
      const orderNo = this.generateOrderNo()

      // 创建订单
      const [order] = await this.orderModel.create(
        [
          {
            orderNo,
            userId: new Types.ObjectId(createDto.userId),
            teamId: new Types.ObjectId(createDto.teamId),
            orderSource: OrderSource.OpenPlatform,
            orderType: OrderType.OpenPlatformTraffic,
            orderStatus: OrderStatus.Paid,
            resourceType: OpenPlatformOrderResourceType.Traffic,
            trafficCount: createDto.trafficCount,
            startTime: now,
            endTime: trafficExpiredAt,
            trafficExpiredAt,
            sourceAppId: createDto.sourceAppId,
            openPlatformUserId: new Types.ObjectId(createDto.userId),
            remark: createDto.remark || '',
            totalAmount: createDto.payAmount, // 暂不涉及金额
            payableAmount: createDto.payAmount,
            interestId: new Types.ObjectId('000000000000000000000000'), // 占位符
            interestCount: 1,
            duration: 12 // 流量订单固定12个月有效期
          }
        ],
        { session }
      )

      // 直接增加团队累积流量
      await this.addTeamAccumulatedTraffic(
        createDto.teamId,
        createDto.trafficCount * 1024 * 1024, // 转换为KB
        trafficExpiredAt,
        session
      )

      await session.commitTransaction()

      this.logger.log(
        `流量订单创建成功: ${orderNo}, 团队: ${createDto.teamId}, ` +
          `流量: ${createDto.trafficCount}GB, 有效期至: ${trafficExpiredAt.toISOString()}`
      )

      return await this.formatOrderResponse(order)
    } catch (error) {
      await session.abortTransaction()
      this.logger.error(`流量订单创建失败: ${error.message}`, error.stack)
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * 计算团队当前有效的账号点数
   */
  async calculateTeamAccountPoints(teamId: string): Promise<number> {
    const now = new Date()

    // 查找所有有效的账号点数订单
    const accountOrders = await this.orderModel.find({
      teamId: new Types.ObjectId(teamId),
      orderStatus: OrderStatus.Paid,
      resourceType: OpenPlatformOrderResourceType.AccountPoints,
      startTime: { $lte: now },
      endTime: { $gt: now }
    })

    // 累加重叠期间的账号点数
    const totalAccountPoints = accountOrders.reduce((sum, order) => {
      return sum + (order.accountCapacity || 0)
    }, 0)

    this.logger.debug(
      `团队账号点数计算: teamId=${teamId}, 有效订单=${accountOrders.length}个, 总点数=${totalAccountPoints}`
    )

    return totalAccountPoints
  }

  /**
   * 计算团队账号点数（包含新订单）
   * 用于VIP团队时间重叠时重新计算总账号点数
   */
  private async calculateTeamAccountPointsWithNewOrder(
    teamId: string,
    newOrderStartTime: Date,
    newOrderEndTime: Date,
    newOrderAccountCapacity: number,
    session: any
  ): Promise<number> {
    const now = new Date()

    // 查找所有现有的有效账号点数订单
    const existingAccountOrders = await this.orderModel
      .find({
        teamId: new Types.ObjectId(teamId),
        orderStatus: OrderStatus.Paid,
        resourceType: OpenPlatformOrderResourceType.AccountPoints,
        startTime: { $lte: now },
        endTime: { $gt: now }
      })
      .session(session)

    // 累加现有订单的账号点数
    let totalAccountPoints = existingAccountOrders.reduce((sum, order) => {
      return sum + (order.accountCapacity || 0)
    }, 0)

    // 检查新订单是否在当前时间有效
    if (newOrderStartTime <= now && newOrderEndTime > now) {
      totalAccountPoints += newOrderAccountCapacity
    }

    this.logger.debug(
      `团队账号点数计算（含新订单）: teamId=${teamId}, ` +
        `现有有效订单=${existingAccountOrders.length}个, ` +
        `新订单账号点数=${newOrderAccountCapacity}, ` +
        `总点数=${totalAccountPoints}`
    )

    return totalAccountPoints
  }

  /**
   * 更新团队VIP过期时间和账号点数
   * 根据业务规则处理VIP和非VIP团队的过期时间更新逻辑，同时更新账号点数限制
   */
  private async updateTeamVipExpiration(
    teamId: string,
    orderStartTime: Date,
    orderEndTime: Date,
    accountCapacityLimit: number,
    session: any
  ): Promise<void> {
    const team = await this.teamModel.findById(teamId).session(session)
    if (!team) {
      this.logger.warn(`团队不存在: teamId=${teamId}`)
      return
    }

    const now = new Date()
    now.setHours(0, 0, 0, 0) // 设置为当天0点，便于日期比较

    const orderStartDay = new Date(orderStartTime)
    orderStartDay.setHours(0, 0, 0, 0)

    // 判断团队当前是否为VIP状态
    const isCurrentlyVip = team.isVip && team.expiredAt && team.expiredAt > now

    this.logger.debug(
      `团队VIP状态检查: teamId=${teamId}, ` +
        `当前VIP状态=${isCurrentlyVip}, ` +
        `当前过期时间=${team.expiredAt?.toISOString() || 'null'}, ` +
        `当前账号点数=${team.accountCapacityLimit || 0}, ` +
        `订单时间段=${orderStartTime.toISOString()} - ${orderEndTime.toISOString()}, ` +
        `订单账号点数=${accountCapacityLimit}`
    )

    if (!isCurrentlyVip) {
      // 非VIP团队处理逻辑
      if (orderStartDay <= now) {
        // 订单生效日期是当天或之前，设置为VIP并更新过期时间和账号点数
        await this.teamModel.findByIdAndUpdate(
          teamId,
          {
            isVip: true,
            expiredAt: orderEndTime,
            accountCapacityLimit: accountCapacityLimit,
            updatedAt: new Date()
          },
          { session }
        )

        this.logger.log(
          `非VIP团队设置为VIP: teamId=${teamId}, ` +
            `订单立即生效, 新过期时间=${orderEndTime.toISOString()}, ` +
            `账号点数=${accountCapacityLimit}`
        )
      } else {
        // 订单生效日期在未来，暂不更新VIP状态
        this.logger.debug(
          `非VIP团队订单未来生效: teamId=${teamId}, ` +
            `订单生效日期=${orderStartTime.toISOString()}, 暂不更新VIP状态`
        )
      }
    } else {
      // VIP团队处理逻辑 - 判断时间重叠
      const currentExpiredAt = team.expiredAt!

      // 检查是否有时间重叠
      // 重叠条件：订单开始时间 < 团队过期时间 AND 订单结束时间 > 当前时间
      const hasOverlap = orderStartTime < currentExpiredAt && orderEndTime > now

      if (hasOverlap) {
        // 有重叠，取较晚的过期时间，并重新计算账号点数
        const newExpiredAt = orderEndTime > currentExpiredAt ? orderEndTime : currentExpiredAt

        // 重新计算团队的总账号点数（包括新订单）
        const totalAccountPoints = await this.calculateTeamAccountPointsWithNewOrder(
          teamId,
          orderStartTime,
          orderEndTime,
          accountCapacityLimit,
          session
        )

        await this.teamModel.findByIdAndUpdate(
          teamId,
          {
            expiredAt: newExpiredAt,
            accountCapacityLimit: totalAccountPoints,
            updatedAt: new Date()
          },
          { session }
        )

        this.logger.log(
          `VIP团队时间重叠处理: teamId=${teamId}, ` +
            `原过期时间=${currentExpiredAt.toISOString()}, ` +
            `订单结束时间=${orderEndTime.toISOString()}, ` +
            `新过期时间=${newExpiredAt.toISOString()}, ` +
            `原账号点数=${team.accountCapacityLimit || 0}, ` +
            `新账号点数=${totalAccountPoints}`
        )
      } else {
        // 无重叠，不更新过期时间和账号点数
        this.logger.debug(
          `VIP团队无时间重叠: teamId=${teamId}, ` +
            `团队过期时间=${currentExpiredAt.toISOString()}, ` +
            `订单时间段=${orderStartTime.toISOString()} - ${orderEndTime.toISOString()}, ` +
            `不更新过期时间和账号点数`
        )
      }
    }
  }

  /**
   * 增加团队累积流量
   */
  private async addTeamAccumulatedTraffic(
    teamId: string,
    trafficAmountKB: number,
    expiredAt: Date,
    session: any
  ): Promise<void> {
    // 增加累积流量总量
    await this.teamModel.findByIdAndUpdate(
      teamId,
      {
        $inc: { networkTraffic: trafficAmountKB, accumulatedTraffic: trafficAmountKB },
        $push: {
          trafficRecords: {
            amount: trafficAmountKB,
            expiredAt,
            createdAt: new Date()
          }
        },
        updatedAt: new Date()
      },
      { session }
    )

    this.logger.debug(
      `团队累积流量增加: teamId=${teamId}, 增加=${trafficAmountKB}KB, 过期时间=${expiredAt.toISOString()}`
    )
  }

  /**
   * 生成订单号
   */
  private generateOrderNo(): string {
    const timestamp = Date.now().toString()
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')
    return `OP${timestamp}${random}`
  }

  /**
   * 格式化订单响应数据
   */
  private async formatOrderResponse(order: any): Promise<any> {
    const team = await this.teamModel.findById(order.teamId)

    return {
      id: order._id.toString(),
      orderNo: order.orderNo,
      teamId: order.teamId.toString(),
      teamName: team?.name || '',
      userId: order.userId.toString(),
      orderStatus: order.orderStatus,
      resourceType: order.resourceType,
      accountCapacity: order.accountCapacity || 0,
      trafficCount: order.trafficCount || 0,
      startTime: order.startTime?.getTime() || 0,
      endTime: order.endTime?.getTime() || 0,
      trafficExpiredAt: order.trafficExpiredAt?.getTime() || 0,
      remark: order.remark || '',
      createdAt: order.createdAt?.getTime() || 0,
      updatedAt: order.updatedAt?.getTime() || 0
    }
  }

  /**
   * 获取团队账号点数数据
   * 使用聚合查询优化性能
   */
  async getTeamAccountCapacityData(): Promise<
    Array<{
      teamId: string
      currentCapacity: number
      calculatedCapacity: number
    }>
  > {
    const beijingStart = dayjs().tz('Asia/Shanghai').startOf('day')
    // 转换为 UTC 时间
    const now = beijingStart.utc().toDate()

    // 使用聚合查询计算每个团队的有效账号点数
    const aggregationResult = await this.orderModel.aggregate([
      {
        $match: {
          orderStatus: OrderStatus.Paid,
          resourceType: OpenPlatformOrderResourceType.AccountPoints,
          startTime: { $lte: now },
          endTime: { $gt: now }
        }
      },
      {
        $group: {
          _id: '$teamId',
          totalAccountCapacity: { $sum: '$accountCapacity' },
          orderCount: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'teams',
          localField: '_id',
          foreignField: '_id',
          as: 'team'
        }
      },
      {
        $unwind: '$team'
      },
      {
        $project: {
          teamId: { $toString: '$_id' },
          currentCapacity: { $ifNull: ['$team.accountCapacityLimit', 0] },
          calculatedCapacity: { $ifNull: ['$totalAccountCapacity', 0] },
          orderCount: 1
        }
      }
    ])

    this.logger.debug(
      null,
      `聚合查询完成: 找到${aggregationResult.length}个团队有有效账号点数订单`,
      { aggregationResultLength: aggregationResult.length }
    )

    return aggregationResult
  }

  async processBatchTeamAccountCapacity(
    teamData: Array<{
      teamId: string
      currentCapacity: number
      calculatedCapacity: number
    }>
  ): Promise<{ processedCount: number; totalCapacity: number }> {
    let processedCount = 0
    let totalCapacity = 0

    // 准备批量更新操作
    const bulkOps = []

    for (const data of teamData) {
      try {
        // 只有当计算出的账号点数与当前不同时才更新
        if (data.calculatedCapacity !== data.currentCapacity) {
          bulkOps.push({
            updateOne: {
              filter: { _id: new Types.ObjectId(data.teamId) },
              update: {
                $set: {
                  accountCapacityLimit: data.calculatedCapacity,
                  updatedAt: new Date()
                }
              }
            }
          })

          this.logger.debug(
            `团队账号点数需要更新: teamId=${data.teamId}, ` +
              `当前=${data.currentCapacity}, 计算=${data.calculatedCapacity}`
          )
        }

        processedCount++
        totalCapacity += data.calculatedCapacity
      } catch (error) {
        this.logger.error(`处理团队账号点数失败: teamId=${data.teamId}, error=${error.message}`)
      }
    }

    // 执行批量更新
    if (bulkOps.length > 0) {
      try {
        const result = await this.teamModel.bulkWrite(bulkOps)
        this.logger.debug(`批量更新完成: 匹配=${result.matchedCount}, 修改=${result.modifiedCount}`)
      } catch (error) {
        this.logger.error(`批量更新失败: ${error.message}`, error.stack)
        throw error
      }
    }

    return { processedCount, totalCapacity }
  }
}
