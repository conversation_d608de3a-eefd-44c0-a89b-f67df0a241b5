import { ForbiddenException, Injectable, Logger, OnModuleInit } from '@nestjs/common'

import OSS from 'ali-oss'

@Injectable()
export class OssService {
  logger = new Logger('OssService')

  private client: OSS

  private bucket = 'yixiaoer-lite-asserts'
  private region = 'oss-cn-shanghai'

  constructor() {
    this.logger.log('OssService init')

    this.client = new OSS({
      secure: true,
      region: this.region,
      // @ts-ignore @types/ali-oss 内没有包含 authorizationV4 定义
      authorizationV4: true,
      accessKeyId: process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
      bucket: this.bucket
    })

    // this.client.putBucketCORS(this.bucket, [
    //   {
    //     allowedOrigin: '*',
    //     allowedMethod: ['GET', 'POST', 'PUT'],
    //     allowedHeader: '*'
    //   }
    // ])
  }

  /**
   * 获取资源直传地址
   */
  getUploadSignatureUrl(key: string, checksum: string | undefined): Promise<string> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
    const additionalHeaders = ['Content-Type']

    if (checksum) {
      headers['X-Oss-Meta-Checksum'] = checksum
      additionalHeaders.push('X-Oss-Meta-Checksum')
    }

    // @ts-ignore @types/ali-oss 内没有包含 signatureUrlV4 定义
    return this.client.signatureUrlV4(
      'PUT',
      60 * 30, // 默认 1800s
      {
        headers: headers
      },
      key,
      additionalHeaders
    )
  }

  /**
   * 获取资源访问地址
   * @param key
   * @param queries 资源处理指令
   * @returns
   */
  getAccessSignatureUrl(key: string, expires = 1800, queries?: {}): Promise<string> {
    // @ts-ignore @types/ali-oss 内没有包含 signatureUrlV4 定义
    return this.client.signatureUrlV4(
      'GET',
      expires, // 默认 1800s
      {
        headers: {},
        queries: queries
      },
      key
    )
  }

  /**
   * 获取不签名的资源访问地址
   * @param key
   * @param queries 资源处理指令
   * @returns
   */
  getUrl(key: string): Promise<string> {
    // @ts-ignore @types/ali-oss 内没有包含 signatureUrlV4 定义
    return this.client.get('GET', key)
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await this.client.delete(key)
    } catch (error) {
      Logger.error('文件删除失败 info:', error)
    }
  }

  /**
   * 批量删除OSS指定文件
   * @param key
   */
  async deleteMultiOssObject(key: string[]) {
    try {
      await this.client.deleteMulti(key)
    } catch (error) {
      Logger.error('文件删除失败 info:', error)
    }
  }

  /**
   * 获取资源的元数据
   * @param key
   */
  async headFileInfo(key: string) {
    try {
      const result = await this.client.head(key)
      if (result.res.status === 200) {
        return result.res.headers
      }
      return null
    } catch (error) {
      Logger.error('Error getting file info:', error)
      throw new ForbiddenException('文件信息获取错误')
    }
  }

  async getDesktopDownloadUrl(key: string, expires = 10) {
    try {
      const downloadOss = new OSS({
        secure: true,
        region: this.region,
        // @ts-ignore @types/ali-oss 内没有包含 authorizationV4 定义
        authorizationV4: true,
        accessKeyId: 'LTAI5tFhZmRcthmbdWEu9PGc',
        accessKeySecret: '******************************',
        endpoint: 'yixiaoer.cn',
        bucket: 'lite-desktop'
      })

      // @ts-ignore @types/ali-oss 内没有包含 authorizationV4 定义
      return downloadOss.signatureUrlV4(
        'GET',
        expires, // 默认 10s
        {
          headers: {}
        },
        key
      )
    } catch (error) {
      throw new ForbiddenException(error)
    }
  }
}
