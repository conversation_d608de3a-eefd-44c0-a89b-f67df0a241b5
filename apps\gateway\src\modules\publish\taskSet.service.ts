import { v4 as uuid } from 'uuid'
import {
  ForbiddenException,
  HttpException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { ContentStatisticDTO, TaskDetailResponse } from './task.dto'
import { REQUEST } from '@nestjs/core'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, FilterQuery, Model, Types, UpdateQuery } from 'mongoose'
import { FastifyRequest } from 'fastify'
import {
  TaskEntity,
  ContentEntity,
  PlatformAccountEntity,
  UserEntity,
  TaskSetEntity,
  MemberEntity,
  ContentStatisticEntity,
  TeamEntity,
  PlatformAccountCookieEntity
} from '@yxr/mongo'
import {
  ContentType,
  EventNames,
  FirstLevelPlatform,
  LiteSystemErrorCode,
  PlatformNameEnum,
  PublishChannel,
  StageStatus,
  TaskCloudPushEvent,
  TaskSetStatusEnum,
  TaskStages,
  TeamRoleNames,
  TianyiyunOssService,
  UploadStatusEnum
} from '@yxr/common'
import {
  TaskSetCreateRequest,
  TaskSetDetailResponse,
  TaskSetListRequest,
  TaskSetListResponse,
  TaskSetUpdateRequest
} from './taskSet.dto'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { WebhookEventEmitterService } from '../webhook-event/webhook-event-emitter.service'
import { getTeamOnlineUsersRedisKey } from '@yxr/utils'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { Cache } from 'cache-manager'
import { TlsService } from '@yxr/huoshan'
import { TeamService } from '../team/team.service'
import { OverseasPublishIntegrationService } from '../overseas-platform/overseas-publish-integration.service'
import { TaskCloudPushDTO, TaskDTO, YxrOpenPlatformService } from '@yxr/yxr-open-platform'

@Injectable()
export class TaskSetService {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(TaskEntity.name) private taskModel: Model<TaskEntity>,
    @InjectModel(TaskSetEntity.name) private taskSetModel: Model<TaskSetEntity>,
    @InjectModel(ContentEntity.name) private contentModel: Model<ContentEntity>,
    @InjectModel(ContentStatisticEntity.name)
    private contentStatisticModel: Model<ContentStatisticEntity>,
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectConnection() private readonly connection: Connection,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    private readonly webhookService: WebhookService,
    private eventEmitter: EventEmitter2,
    private readonly teamService: TeamService,
    private readonly loggerService: TlsService,
    private readonly tianyiyunOssService: TianyiyunOssService,
    private readonly overseasPublishIntegrationService: OverseasPublishIntegrationService,
    private readonly webhookEventEmitterService: WebhookEventEmitterService,
    private readonly yxrOpenPlatformService: YxrOpenPlatformService
  ) {}

  /**
   * 任务集合下的任务列表
   * @param taskSetId
   * @returns
   */
  async getSetTaskList(taskSetId: string): Promise<TaskDetailResponse[]> {
    const teamId = this.request.session.teamId

    const result = await this.taskModel.aggregate([
      {
        $match: {
          teamId: new Types.ObjectId(teamId),
          taskSetId: taskSetId
        }
      },
      {
        $lookup: {
          from: 'contententities',
          localField: 'contentId',
          foreignField: '_id',
          as: 'contents',
          pipeline: [
            {
              $project: {
                _id: 0,
                platformName: 1,
                platformAvatar: 1,
                platformAccountId: 1,
                platformAccountName: 1,
                phone: 1,
                nickName: 1,
                cover: 1,
                desc: 1,
                createdAt: 1
              }
            }
          ]
        }
      },
      {
        $unwind: { path: '$contents' }
      },
      {
        $project: {
          stages: 1,
          documentId: 1,
          publishId: 1,
          publishType: 1,
          mediaType: 1,
          errorMessage: 1,
          taskSetId: 1,
          isTimed: 1,
          taskId: 1,
          teamId: 1,
          stageStatus: 1,
          openUrl: 1,
          taskStatus: 1,
          contents: '$contents'
        }
      },
      {
        $facet: {
          items: [{ $sort: { createdAt: -1 } }]
        }
      }
    ])

    const data = Promise.all(
      result[0]
        ? result[0].items.map(async (item) => {
            let statistic: ContentStatisticDTO = null
            if (
              item.stages === TaskStages.Success &&
              FirstLevelPlatform.includes(item.contents.platformName)
            ) {
              const contentStatistic = await this.contentStatisticModel
                .findOne({
                  publishId: item.documentId,
                  teamId: new Types.ObjectId(item.teamId)
                })
                .select('contentType play read great comment share collect')

              statistic = {}
              if (contentStatistic) {
                statistic.viewCount =
                  contentStatistic.contentType === ContentType.video ||
                  contentStatistic.contentType === ContentType.miniVideo
                    ? contentStatistic.play
                    : contentStatistic.read
                statistic.greatCount = contentStatistic.great
                statistic.commentCount = contentStatistic.comment
                statistic.shareCount = contentStatistic.share
                statistic.collectCount = contentStatistic.collect
              }
            }

            return {
              id: item._id.toString(),
              platformAvatar: item.contents.platformAvatar,
              platformAccountId: item.contents.platformAccountId,
              platformAccountName: item.contents.platformAccountName,
              platformName: item.contents.platformName,
              phone: item.contents.phone,
              nickName: item.contents.nickName,
              documentId: item.documentId,
              publishId: item.publishId,
              publishType: item.publishType,
              mediaType: item.mediaType,
              errorMessage: item.errorMessage,
              cover: `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(item.contents?.cover)}`,
              desc: item.contents.desc,
              taskId: item.taskId,
              taskSetId: item.taskSetId,
              isTimed: item.isTimed,
              openUrl: item.openUrl,
              taskStatus: item.taskStatus,
              createdAt: item.contents.createdAt.getTime(),
              stages: item.stages,
              stageStatus: item.stageStatus,
              statistic: statistic
            }
          })
        : {}
    )

    return data
  }

  /**
   * 任务集详情
   * @param taskId
   * @returns
   */
  async getTaskSetDetailAsync(taskSetId: string): Promise<TaskSetDetailResponse> {
    const teamId = this.request.session.teamId

    const taskSet = await this.taskSetModel.findOne({
      teamId: new Types.ObjectId(teamId),
      taskIdentityId: taskSetId
    })

    if (!taskSet) {
      throw new NotFoundException('任务集不存在')
    }
    const tasks = await this.taskModel.find({
      teamId: new Types.ObjectId(teamId),
      taskSetId: taskSetId
    })

    let penddingCount = tasks.length
    let successCount = 0
    let failedCount = 0

    for (const task of tasks) {
      if (task.stageStatus === StageStatus.Success) {
        successCount++
      } else if (task.stageStatus === StageStatus.Fail) {
        failedCount++
      }
    }

    const totalTaskDuration = taskSet.updatedAt.getTime() - taskSet.createdAt.getTime()

    return {
      id: taskSet.taskIdentityId,
      userId: taskSet.userId.toString(),
      platforms: taskSet.platforms,
      nickName: taskSet.nickName,
      coverKey: taskSet.cover,
      publishType: taskSet.publishType,
      isAppContent: taskSet.isAppContent,
      createdAt: taskSet.createdAt.getTime(),
      coverUrl: `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(taskSet.cover)}`,
      desc: taskSet.desc,
      descRich: taskSet.descRich,
      taskSetStatus: taskSet.taskSetStatus,
      taskCount: tasks.length,
      isTimed: taskSet.isTimed,
      totalTaskDuration: totalTaskDuration,
      avTaskDuration: Math.ceil(totalTaskDuration / tasks.length),
      penddingCount: penddingCount - successCount - failedCount,
      successCount: successCount,
      failedCount: failedCount,
      isDraft: taskSet.isDraft,
      publishChannel: taskSet.publishChannel
    }
  }

  /**
   * 任务集列表
   * @returns
   * @param query
   * @param userIds
   */
  async getTaskSetsAsync(
    query: TaskSetListRequest,
    userIds: string[]
  ): Promise<TaskSetListResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const filter: FilterQuery<TaskSetEntity> = {}
    filter.teamId = new Types.ObjectId(currentTeamId)
    filter.$or = [
      { isDeleted: false }, // 查找 isDeleted 为 false 的文档
      { isDeleted: { $exists: false } } // 查找没有 isDeleted 字段的文档
    ]
    if (query.publishEndTime && query.publishStartTime) {
      filter.createdAt = {
        $lt: new Date(query.publishEndTime),
        $gt: new Date(query.publishStartTime)
      }
    }
    if (query.publishType) {
      filter.publishType = query.publishType
    }
    if (query.taskSetStatus) {
      filter.taskSetStatus = query.taskSetStatus
    }
    //通过时间游标分页
    if (query.time > 0) {
      filter.createdAt = { $lt: new Date(query.time) }
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })
    if (member.roles.some((role) => role === TeamRoleNames.MEMBER)) {
      filter.userId = new Types.ObjectId(currentUserId)
    } else {
      if (userIds) {
        const ids = userIds.map((value) => new Types.ObjectId(value))
        filter.userId = { $in: ids }
      }
    }

    const result = await this.taskSetModel
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)
    const totalSize = await this.taskSetModel.find(filter).countDocuments()
    const data = await Promise.all(
      result.map(
        async (item) =>
          ({
            id: item.taskIdentityId,
            userId: item.userId.toString(),
            platforms: item.platforms,
            nickName: item.nickName,
            coverKey: item.cover,
            publishType: item.publishType,
            isAppContent: item.isAppContent,
            createdAt: item.createdAt.getTime(),
            coverUrl: `${process.env.OSS_TIANYIYUN_DOWNLOAD_URL}/${encodeURIComponent(item?.cover)}`,
            desc: item.desc,
            descRich: item.descRich,
            taskSetStatus: item.taskSetStatus,
            isDraft: item.isDraft,
            publishChannel: item.publishChannel
          }) as TaskSetDetailResponse
      )
    )

    return {
      totalSize,
      page: query.page,
      size: query.size,
      totalPage: Math.ceil(totalSize / query.size),
      data
    }
  }

  /**
   * 创建任务集
   * @param body
   */
  async postTaskSetAsync(body: TaskSetCreateRequest): Promise<string> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    if (body) {
      const checkPush = await this.teamService.checkPush(currentTeamId)
      if (!checkPush) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.IsNotVip,
          message: '今日发布次数已达上限'
        })
      }
      let videoArr = {}
      if (body.publishChannel === PublishChannel.cloud) {
        //云端发布推送消息给云端爬虫
        const platformAccontIds = body.platformAccounts.map(
          (item) => new Types.ObjectId(item.platformAccountId)
        )
        const platformAccounts = await this.platformAccountModel
          .find({
            _id: { $in: platformAccontIds }
          })
          .select('_id kuaidailiIp')

        //检查是否所有账户都有 kuaidailiIp 值
        const allHaveKuaidailiArea = platformAccounts.every((account) => account.kuaidailiIp)
        if (!allHaveKuaidailiArea) {
          throw new ForbiddenException('账号未设置代理，请先设置代理')
        }
      }

      if (
        (body.publishChannel === PublishChannel.cloud || body.isAppContent === true) &&
        body.publishType == 'video'
      ) {
        //App发布和云发布计算流量消耗
        const team = await this.teamModel
          .findOne({
            _id: new Types.ObjectId(currentTeamId)
          })
          .select('networkTraffic useNetworkTraffic')
        let size = 0
        for (const accountInfo of body.platformAccounts) {
          const videoSize = await this.tianyiyunOssService.headFileInfo(accountInfo.videoKey)
          videoArr[accountInfo.videoKey] = videoSize
          size += videoSize
        }
        if (team.networkTraffic < team.useNetworkTraffic + size) {
          throw new ForbiddenException('流量不足以本次发布')
        }
      }

      let taskIdentityId = null
      //开启数据事务
      const dbTransaction = await this.connection.startSession()
      dbTransaction.startTransaction()

      const userInfo = await this.userModel.findById(new Types.ObjectId(currentUserId))
      const memberRemark = await this.memberModel.findOne({
        userId: new Types.ObjectId(currentUserId),
        teamId: new Types.ObjectId(currentTeamId)
      })
      try {
        taskIdentityId = body.taskSetId ?? uuid()
        // 创建任务集
        await this.taskSetModel.create(
          [
            {
              teamId: new Types.ObjectId(currentTeamId),
              userId: new Types.ObjectId(currentUserId),
              platforms: body.platforms ? [...new Set(body.platforms)] : [], //去重处理
              phone: userInfo.phone,
              nickName: memberRemark.remark ?? userInfo.nickName,
              cover: body.coverKey,
              desc: body.desc,
              descRich: body.descRich,
              taskIdentityId: taskIdentityId,
              publishType: body.publishType,
              isTimed: body.isTimed,
              taskSetStatus: TaskSetStatusEnum.Publishing,
              isDraft: body.isDraft,
              publishChannel: body.publishChannel,
              publishArgs: body.publishArgs,
              isAppContent: body.isAppContent
            }
          ],
          { session: dbTransaction }
        )

        if (body.platformAccounts) {
          for (const accountInfo of body.platformAccounts) {
            const platformAccount = await this.platformAccountModel.findOne({
              _id: new Types.ObjectId(accountInfo.platformAccountId),
              teamId: new Types.ObjectId(currentTeamId)
            })

            if (!platformAccount) {
              throw new NotFoundException('媒体账号未找到')
            }

            //新增内容
            const [content] = await this.contentModel.create(
              [
                {
                  teamId: new Types.ObjectId(currentTeamId),
                  userId: new Types.ObjectId(currentUserId),
                  platformAccountId: new Types.ObjectId(accountInfo.platformAccountId),
                  cover: accountInfo.coverKey,
                  video: accountInfo.videoKey,
                  superId: accountInfo.superId,
                  superLockId: accountInfo.superLockId,
                  desc: body.desc,
                  publishType: body.publishType,
                  platformAvatar: platformAccount.platformAvatar,
                  platformName: platformAccount.platformName,
                  platformAccountName: platformAccount.platformAccountName,
                  phone: userInfo.phone,
                  nickName: memberRemark.remark ?? userInfo.nickName,
                  isAppContent: body.isAppContent,
                  isDraft: body.isDraft,
                  taskSetId: taskIdentityId,
                  publishChannel: body.publishChannel,
                  videoSize: Object.keys(videoArr).length === 0 ? 0 : videoArr[accountInfo.videoKey]
                }
              ],
              { session: dbTransaction }
            )

            //新增任务
            await this.taskModel.create(
              [
                {
                  contentId: new Types.ObjectId(content._id),
                  taskId: content.id,
                  userId: new Types.ObjectId(currentUserId),
                  teamId: new Types.ObjectId(currentTeamId),
                  documentId: '',
                  publishId: '',
                  publishType: body.publishType,
                  mediaType: body.mediaType,
                  platformAccountId: new Types.ObjectId(accountInfo.platformAccountId),
                  errorMessage: '',
                  isTimed: body.isTimed,
                  taskSetId: taskIdentityId,
                  publishChannel: body.publishChannel
                }
              ],
              { session: dbTransaction }
            )
          }
        }

        await dbTransaction.commitTransaction()
        await this.teamService.setPush(currentTeamId)

        // 触发开放平台Webhook事件
        await this.webhookEventEmitterService.emitTaskSetCreated({
          teamId: currentTeamId,
          taskSetId: taskIdentityId,
          taskSetName: body.desc || '未命名任务集',
          userId: currentUserId,
          taskCount: body.platformAccounts ? body.platformAccounts.length : 0
        })
      } catch (error) {
        taskIdentityId = null
        await this.loggerService.error(this.request, '任务上报失败', { error: error })
        await dbTransaction.abortTransaction()
        throw new HttpException('任务上报失败, 请稍后再试', -1)
      } finally {
        await dbTransaction.endSession()
      }

      if (body.publishChannel === PublishChannel.cloud) {
        const authorization = this.request.authorization
        //云端发布推送消息给云端爬虫
        await this.eventEmitter.emitAsync(
          EventNames.TaskCloudPushEvent,
          new TaskCloudPushEvent(currentTeamId.toString(), taskIdentityId, authorization)
        )
      }
      if (body.publishChannel === PublishChannel.local && body.isAppContent === true) {
        // App发布如果有在线人数可以直接随机一个节点推送任务集
        const onlineUsers = await this.cacheManager.store.client.zrange(
          getTeamOnlineUsersRedisKey(currentTeamId),
          0,
          -1
        )
        if (onlineUsers.length > 0) {
          await this.webhookService.grpchook([onlineUsers[0]], null, {
            event: WebhookEvents.AppTaskPushing,
            body: [
              {
                taskIdentityId: taskIdentityId,
                version: 0
              }
            ]
          })
        }
      }
      return taskIdentityId
    }
  }

  /**
   * 创建任务集v1
   * @param body
   */
  async postTaskSetAsync1(body: TaskSetCreateRequest): Promise<string> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    if (body) {
      const checkPush = await this.teamService.checkPush(currentTeamId)
      if (!checkPush) {
        throw new ForbiddenException({
          code: LiteSystemErrorCode.IsNotVip,
          message: '今日发布次数已达上限'
        })
      }
      if (body.publishChannel === PublishChannel.cloud) {
        //云端发布推送消息给云端爬虫
        const platformAccontIds = body.platformAccounts.map(
          (item) => new Types.ObjectId(item.platformAccountId)
        )
        const platformAccounts = await this.platformAccountModel
          .find({
            _id: { $in: platformAccontIds }
          })
          .select('_id kuaidailiIp')

        //检查是否所有账户都有 kuaidailiIp 值
        const allHaveKuaidailiArea = platformAccounts.every((account) => account.kuaidailiIp)
        if (!allHaveKuaidailiArea) {
          throw new ForbiddenException('账号未设置代理，请先设置代理')
        }
      }

      //开启数据事务
      const dbTransaction = await this.connection.startSession()
      dbTransaction.startTransaction()

      const userInfo = await this.userModel.findById(new Types.ObjectId(currentUserId))
      const memberRemark = await this.memberModel.findOne({
        userId: new Types.ObjectId(currentUserId),
        teamId: new Types.ObjectId(currentTeamId)
      })
      try {
        const taskIdentityId = body.taskSetId ?? uuid()
        // 创建任务集
        await this.taskSetModel.create(
          [
            {
              teamId: new Types.ObjectId(currentTeamId),
              userId: new Types.ObjectId(currentUserId),
              platforms: body.platforms ? [...new Set(body.platforms)] : [], //去重处理
              phone: userInfo.phone,
              nickName: memberRemark.remark ?? userInfo.nickName,
              cover: body.coverKey,
              desc: body.desc,
              descRich: body.descRich,
              taskIdentityId: taskIdentityId,
              publishType: body.publishType,
              isTimed: body.isTimed,
              taskSetStatus: TaskSetStatusEnum.Publishing,
              isDraft: body.isDraft,
              publishChannel: body.publishChannel,
              publishArgs: body.publishArgs,
              isAppContent: body.isAppContent
            }
          ],
          { session: dbTransaction }
        )

        await dbTransaction.commitTransaction()
        return taskIdentityId
      } catch (error) {
        await this.loggerService.error(this.request, '任务集创建失败', { error: error })
        await dbTransaction.abortTransaction()
        throw new HttpException('任务集失败, 请稍后再试', -1)
      } finally {
        await dbTransaction.endSession()
      }
    }
  }

  /**
   * 更新任务集信息
   * @param taskSetId
   * @param body
   */
  async patchPublishTaskSets(taskSetId: string, body: TaskSetUpdateRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    const taskSet = await this.taskSetModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      taskIdentityId: taskSetId
    })
    if (!taskSet) {
      throw new NotFoundException('任务集不存在')
    }
    if (
      taskSet.uploadStatus === UploadStatusEnum.Successful ||
      taskSet.uploadStatus === UploadStatusEnum.Failed
    ) {
      //终结状态不执行
      return
    }
    let updateData: UpdateQuery<TaskSetEntity> = {}
    if (body.publishArgs) {
      updateData.publishArgs = body.publishArgs
    }
    if (body.coverKey) {
      updateData.coverKey = body.coverKey
    }
    if (body.progress) {
      updateData.progress = body.progress
    }
    if (body.uploadStatus) {
      updateData.uploadStatus = body.uploadStatus
    }

    let videoArr = {}
    if (
      (taskSet.publishChannel === PublishChannel.cloud || taskSet.isAppContent === true) &&
      taskSet.publishType == 'video'
    ) {
      //App发布和云发布计算流量消耗
      const team = await this.teamModel
        .findOne({
          _id: new Types.ObjectId(currentTeamId)
        })
        .select('networkTraffic useNetworkTraffic')
      let size = 0
      for (const accountInfo of body.platformAccounts) {
        const videoSize = await this.tianyiyunOssService.headFileInfo(accountInfo.videoKey)
        videoArr[accountInfo.videoKey] = videoSize
        size += videoSize
      }
      if (team.networkTraffic < team.useNetworkTraffic + size) {
        updateData.uploadStatus = UploadStatusEnum.Failed
        updateData.progress = '流量不足以本次发布'
      }
    }

    await this.taskSetModel.updateOne(
      {
        _id: new Types.ObjectId(taskSet.id)
      },
      updateData
    )
    if (updateData.uploadStatus === UploadStatusEnum.Successful) {
      //开启数据事务
      const dbTransaction = await this.connection.startSession()
      dbTransaction.startTransaction()

      const userInfo = await this.userModel.findById(new Types.ObjectId(currentUserId))
      const memberRemark = await this.memberModel.findOne({
        userId: new Types.ObjectId(currentUserId),
        teamId: new Types.ObjectId(currentTeamId)
      })
      try {
        if (body.platformAccounts) {
          for (const accountInfo of body.platformAccounts) {
            const platformAccount = await this.platformAccountModel.findOne({
              _id: new Types.ObjectId(accountInfo.platformAccountId),
              teamId: new Types.ObjectId(currentTeamId)
            })

            if (!platformAccount) {
              throw new NotFoundException('媒体账号未找到')
            }

            //新增内容
            const [content] = await this.contentModel.create(
              [
                {
                  teamId: new Types.ObjectId(currentTeamId),
                  userId: new Types.ObjectId(currentUserId),
                  platformAccountId: new Types.ObjectId(accountInfo.platformAccountId),
                  cover: accountInfo.coverKey,
                  video: accountInfo.videoKey,
                  superId: accountInfo.superId,
                  superLockId: accountInfo.superLockId,
                  desc: taskSet.desc,
                  publishType: taskSet.publishType,
                  platformAvatar: platformAccount.platformAvatar,
                  platformName: platformAccount.platformName,
                  platformAccountName: platformAccount.platformAccountName,
                  phone: userInfo.phone,
                  nickName: memberRemark.remark ?? userInfo.nickName,
                  isAppContent: taskSet.isAppContent,
                  isDraft: taskSet.isDraft,
                  taskSetId: taskSet.taskIdentityId,
                  publishChannel: taskSet.publishChannel,
                  videoSize: Object.keys(videoArr).length === 0 ? 0 : videoArr[accountInfo.videoKey]
                }
              ],
              { session: dbTransaction }
            )

            //新增任务
            await this.taskModel.create(
              [
                {
                  contentId: new Types.ObjectId(content._id),
                  taskId: content.id,
                  userId: new Types.ObjectId(currentUserId),
                  teamId: new Types.ObjectId(currentTeamId),
                  documentId: '',
                  publishId: '',
                  publishType: taskSet.publishType,
                  mediaType: null,
                  platformAccountId: new Types.ObjectId(accountInfo.platformAccountId),
                  errorMessage: '',
                  isTimed: taskSet.isTimed,
                  taskSetId: taskSet.taskIdentityId,
                  publishChannel: taskSet.publishChannel
                }
              ],
              { session: dbTransaction }
            )
          }
        }

        await dbTransaction.commitTransaction()
        await this.teamService.setPush(currentTeamId)
      } catch (error) {
        await this.loggerService.error(this.request, '任务上报失败', { error: error })
        await dbTransaction.abortTransaction()
        throw new HttpException('任务上报失败, 请稍后再试', -1)
      } finally {
        await dbTransaction.endSession()
      }

      // 检查是否有海外平台账号，如果有则处理海外发布
      if (body.platformAccounts) {
        const platformAccountIds = body.platformAccounts.map((item) => item.platformAccountId)
        const hasOverseasAccounts =
          await this.overseasPublishIntegrationService.hasOverseasAccounts(platformAccountIds)

        if (taskSet.publishChannel === PublishChannel.local && hasOverseasAccounts) {
          // 异步处理海外平台发布任务
          setImmediate(async () => {
            try {
              await this.overseasPublishIntegrationService.handleOverseasPublishTasks(
                taskSet.taskIdentityId
              )
            } catch (error) {
              await this.loggerService.error(this.request, '海外平台发布任务处理失败', {
                taskSetId: taskSet.taskIdentityId,
                error: error
              })
            }
          })
        }
      }

      if (taskSet.publishChannel === PublishChannel.cloud) {
        const authorization = this.request.authorization
        //云端发布推送消息给云端爬虫
        await this.eventEmitter.emitAsync(
          EventNames.TaskCloudPushEvent,
          new TaskCloudPushEvent(currentTeamId.toString(), taskSet.taskIdentityId, authorization)
        )
      }
      if (taskSet.publishChannel === PublishChannel.local && taskSet.isAppContent === true) {
        // App发布如果有在线人数可以直接随机一个节点推送任务集
        const onlineUsers = await this.cacheManager.store.client.zrange(
          getTeamOnlineUsersRedisKey(currentTeamId),
          0,
          -1
        )
        if (onlineUsers.length > 0) {
          await this.webhookService.grpchook([onlineUsers[0]], null, {
            event: WebhookEvents.AppTaskPushing,
            body: [
              {
                taskIdentityId: taskSet.taskIdentityId,
                version: 0
              }
            ]
          })
        }
      }
    }
  }

  /**
   * 删除任务集
   * @param taskSetId
   */
  async deleteTaskSetAsync(taskSetIds: string[]) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const taskSets = await this.taskSetModel.find({
      teamId: new Types.ObjectId(currentTeamId),
      taskIdentityId: { $in: taskSetIds }
    })

    if (!taskSets || taskSets.length === 0) {
      throw new NotFoundException('任务集不存在')
    }

    const member = await this.memberModel.findOne({
      teamId: new Types.ObjectId(currentTeamId),
      userId: new Types.ObjectId(currentUserId)
    })

    const taskSetArr = []
    for (let index = 0; index < taskSets.length; index++) {
      const taskSet = taskSets[index]
      if (
        member.roles.includes(TeamRoleNames.MEMBER) &&
        taskSet.userId.toString() !== currentUserId
      ) {
        throw new ForbiddenException('无法删除不是我创建的作品')
      }
      if (taskSet.createdAt.getTime() > Date.now() - 3600 * 1000) {
        throw new ForbiddenException('无法删除创建时间小于1小时的作品')
      }
      taskSetArr.push(taskSet.id)
    }

    if (taskSetArr.length > 0) {
      await this.taskSetModel.updateMany(
        {
          _id: { $in: taskSetArr }
        },
        { $set: { isDeleted: true } }
      )
    }
  }

  /**
   * 获取可发布的任务集对象
   * @param taskSetId
   */
  async getPublishForm(taskSetId: string): Promise<TaskCloudPushDTO> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const taskSet = await this.taskSetModel
      .findOne({
        teamId: new Types.ObjectId(currentTeamId),
        taskIdentityId: taskSetId
      })
      .select(
        'taskIdentityId publishArgs publishType isAppContent teamId userId taskSetStatus progressToken'
      )
    if (!taskSet) {
      throw new NotFoundException('任务集不存在')
    }

    if (taskSet.isAppContent && !taskSet.progressToken) {
      //app本机发布第一次分配到用户
      await this.taskSetModel.updateOne(
        {
          _id: taskSet.id
        },
        {
          $set: {
            progressToken: currentUserId
          }
        }
      )
    }
    const contents = await this.contentModel
      .find({
        taskSetId: taskSetId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('platformAccountId cover video')
    const tasks = await this.taskModel
      .find({
        taskSetId: taskSetId,
        teamId: new Types.ObjectId(currentTeamId)
      })
      .select('_id platformAccountId taskId contentId')
    const resTaskMap = tasks.reduce((ts, task) => {
      ts[task.contentId.toString()] = task
      return ts
    }, {})

    const grouped: TaskDTO[] = [] //数据组装
    for (const item of contents) {
      const platformAccount = await this.platformAccountModel
        .findById(item.platformAccountId)
        .select(
          '_id platformAuthorId platformAccountName platformName spaceId kuaidailiArea parentId token'
        )
      if (!platformAccount) {
        //账号不存在或已经删除则跳过
        continue
      }
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } = await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }
      const platformAccountObject: TaskDTO = {
        taskId: resTaskMap[item._id.toString()].taskId,
        platform: platformAccount.platformName,
        videoPath: item.video,
        cover: item.cover,
        platformAccount: {
          platformAccountId: platformAccount._id.toString(),
          platformAuthorId: platformAccount.platformAuthorId,
          cookie: cookie
        }
      }

      grouped.push(platformAccountObject)
    }

    return {
      callBackData: {
        teamId: taskSet.teamId.toString(),
        userId: taskSet.userId.toString()
      },
      publishType: taskSet.publishType,
      formData: taskSet.publishArgs,
      data: Object.values(grouped)
    }
  }
}
