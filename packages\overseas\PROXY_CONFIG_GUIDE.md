# 海外平台连接器代理配置指南

## 📋 目录

- [概述](#概述)
- [环境变量配置](#环境变量配置)
- [代理配置说明](#代理配置说明)
- [使用示例](#使用示例)
- [故障排除](#故障排除)
- [安全注意事项](#安全注意事项)
- [性能优化](#性能优化)

## 概述

海外平台连接器支持通过HTTP代理访问各个海外平台的API，以解决网络环境限制问题。系统会根据环境变量自动判断是否使用代理，并提供灵活的配置选项。

### 🎯 主要特性

- **自动代理检测**: 根据环境变量自动启用/禁用代理
- **协议自适应**: 自动根据目标URL协议选择HTTP/HTTPS代理
- **环境隔离**: 支持不同环境使用不同的代理配置
- **故障恢复**: 内置重试机制和错误处理

## 环境变量配置

### 🔧 基础配置

```bash
# 代理控制
OVERSEAS_USE_PROXY=true          # 启用代理（true启用，false或不设置禁用）

# 代理服务器地址（默认：http://127.0.0.1:7897）
OVERSEAS_PROXY_URL=http://127.0.0.1:7897

# 请求超时时间（默认：30000毫秒）
OVERSEAS_REQUEST_TIMEOUT=30000
```

### 📊 代理启用逻辑

| 环境变量值 | 代理状态 | 说明 |
|-----------|---------|------|
| `OVERSEAS_USE_PROXY=true` | ✅ 启用 | 使用指定的代理服务器 |
| `OVERSEAS_USE_PROXY=false` | ❌ 禁用 | 直连访问 |
| 未设置 | ❌ 禁用 | 默认直连访问 |

### 🌍 不同环境的配置示例

#### 本地开发环境 (.env.local)
```bash
# 代理配置
OVERSEAS_USE_PROXY=true
OVERSEAS_PROXY_URL=http://127.0.0.1:7897
OVERSEAS_REQUEST_TIMEOUT=30000

# TikTok平台配置
TIKTOK_CLIENT_KEY=your_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret

# 其他平台配置（计划中）
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
INSTAGRAM_CLIENT_ID=your_instagram_app_id
INSTAGRAM_CLIENT_SECRET=your_instagram_app_secret
TWITTER_CLIENT_ID=your_twitter_app_id
TWITTER_CLIENT_SECRET=your_twitter_app_secret
YOUTUBE_CLIENT_ID=your_youtube_app_id
YOUTUBE_CLIENT_SECRET=your_youtube_app_secret
```

#### 香港生产环境 (.env.production)
```bash
# 不设置OVERSEAS_USE_PROXY，默认直连访问

# TikTok平台配置
TIKTOK_CLIENT_KEY=your_production_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_production_tiktok_client_secret

# 其他平台配置
FACEBOOK_CLIENT_ID=your_production_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_production_facebook_app_secret
# ... 其他平台配置
```

#### 测试环境 (.env.test)
```bash
OVERSEAS_USE_PROXY=true
OVERSEAS_PROXY_URL=http://test-proxy.example.com:8080
OVERSEAS_REQUEST_TIMEOUT=60000

# 测试环境API配置
TIKTOK_CLIENT_KEY=your_test_tiktok_client_key
TIKTOK_CLIENT_SECRET=your_test_tiktok_client_secret
```

## 代理配置说明

### 🔌 支持的代理类型

- **HTTP代理**: 用于HTTP请求
- **HTTPS代理**: 用于HTTPS请求（自动根据目标URL协议选择）

### 🛠️ 代理服务器要求

1. **支持HTTP CONNECT方法**
2. **支持HTTPS隧道**
3. **稳定的网络连接**
4. **足够的带宽**

### 📱 常用代理软件

| 软件 | 类型 | 适用场景 | 推荐度 |
|------|------|----------|--------|
| **Clash** | 图形界面 | 本地开发 | ⭐⭐⭐⭐⭐ |
| **V2Ray** | 命令行 | 企业级部署 | ⭐⭐⭐⭐ |
| **Shadowsocks** | 轻量级 | 简单场景 | ⭐⭐⭐ |
| **Squid** | HTTP代理 | 企业网关 | ⭐⭐⭐ |

## 使用示例

### 💻 在海外平台连接器中的使用

```typescript
import { createOverseasAxiosInstance } from '../../utils/axios-config'
import { createTikTokBusinessErrorChecker } from './tiktok-error-handler'

// 创建TikTok API实例（自动根据环境变量配置代理）
const createTikTokAxiosInstance = (context: OverseasContext) => {
  const interceptorConfig: InterceptorConfig = {
    context,
    businessErrorChecker: createTikTokBusinessErrorChecker(),
    enableRetry: true,
    enableBusinessErrorCheck: true
  }

  return createOverseasAxiosInstance(
    'https://business-api.tiktok.com',
    interceptorConfig
  )
}

// 使用示例
const tiktokApi = createTikTokAxiosInstance(context)
const response = await tiktokApi.post('/open_api/v1.3/tt_user/oauth2/token/', data)
```

### 📝 日志输出示例

#### 使用代理时
```
[OverseasAxios] 使用代理: http://127.0.0.1:7897 for https://business-api.tiktok.com
[OverseasAxios] 请求: POST https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/
[OverseasAxios] 响应: 200 https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/
```

#### 直连访问时
```
[OverseasAxios] 直连访问: https://business-api.tiktok.com
[OverseasAxios] 请求: POST https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/
[OverseasAxios] 响应: 200 https://business-api.tiktok.com/open_api/v1.3/tt_user/oauth2/token/
```

### 🔍 代理配置检查

```typescript
import { getProxyConfig } from '../../utils/axios-config'

// 检查当前代理配置
const proxyConfig = getProxyConfig()
console.log('代理配置:', {
  enabled: proxyConfig.enabled,
  url: proxyConfig.url,
  timeout: proxyConfig.timeout
})
```

## 故障排除

### 🚨 常见问题

#### 1. 代理连接失败
**症状**: 请求超时或连接被拒绝
**解决方案**:
```bash
# 检查代理服务器状态
curl --proxy http://127.0.0.1:7897 https://www.google.com

# 验证代理配置
echo $OVERSEAS_USE_PROXY
echo $OVERSEAS_PROXY_URL
```

#### 2. 请求超时
**症状**: 请求耗时过长
**解决方案**:
```bash
# 增加超时时间
OVERSEAS_REQUEST_TIMEOUT=60000

# 检查代理服务器性能
ping proxy-server-ip
```

#### 3. SSL证书错误
**症状**: HTTPS请求失败
**解决方案**:
- 确保代理服务器支持HTTPS隧道
- 检查目标API的SSL证书有效性
- 验证代理服务器的SSL配置

#### 4. TikTok API特定问题
**症状**: TikTok API返回错误
**解决方案**:
```typescript
// 检查TikTok API响应
try {
  const response = await tiktokApi.post('/api/endpoint', data)
} catch (error) {
  if (error instanceof RemoteApiError) {
    console.error('TikTok API错误:', error.localizedMessage)
    console.error('错误代码:', error.errorCode)
  }
}
```

### 🔧 调试方法

#### 1. 启用详细日志
```bash
DEBUG=axios* npm start
```

#### 2. 测试代理连接
```bash
# 测试HTTP代理
curl --proxy http://127.0.0.1:7897 http://httpbin.org/ip

# 测试HTTPS代理
curl --proxy http://127.0.0.1:7897 https://httpbin.org/ip

# 测试TikTok API连接
curl --proxy http://127.0.0.1:7897 https://business-api.tiktok.com
```

#### 3. 检查环境变量
```typescript
// 在代码中检查配置
console.log('环境变量检查:', {
  useProxy: process.env.OVERSEAS_USE_PROXY,
  proxyUrl: process.env.OVERSEAS_PROXY_URL,
  timeout: process.env.OVERSEAS_REQUEST_TIMEOUT
})

// 检查代理配置
import { getProxyConfig } from '../../utils/axios-config'
console.log('代理配置:', getProxyConfig())
```

## 安全注意事项

### 🔒 代理服务器安全

1. **使用可信的代理服务器**
   - 选择知名的代理服务提供商
   - 避免使用免费的公共代理
   - 定期审查代理服务器日志

2. **代理软件安全**
   - 定期更新代理软件版本
   - 使用官方渠道下载软件
   - 配置适当的访问控制

3. **监控和审计**
   - 监控代理服务器日志
   - 设置异常访问告警
   - 定期审查网络流量

### 🛡️ API密钥保护

1. **环境变量管理**
   ```bash
   # ✅ 正确：使用环境变量
   TIKTOK_CLIENT_SECRET=your_secret_key
   
   # ❌ 错误：硬编码在代码中
   const clientSecret = 'your_secret_key'
   ```

2. **日志安全**
   - 不要在代理日志中记录敏感信息
   - 过滤API密钥和访问令牌
   - 使用脱敏处理

3. **密钥轮换**
   - 定期轮换API密钥
   - 使用密钥管理服务
   - 实施最小权限原则

### 🌐 网络安全

1. **加密传输**
   - 优先使用HTTPS协议
   - 验证SSL证书
   - 使用加密的代理协议

2. **访问控制**
   - 限制代理服务器访问权限
   - 使用白名单机制
   - 实施网络隔离

## 性能优化

### ⚡ 代理服务器优化

1. **地理位置选择**
   - 选择地理位置接近的代理服务器
   - 考虑目标API服务器的位置
   - 使用CDN加速

2. **带宽和性能**
   - 使用高带宽的代理服务器
   - 监控代理服务器性能
   - 考虑使用多个代理服务器负载均衡

3. **连接优化**
   ```typescript
   // 优化连接配置
   const axiosConfig = {
     timeout: 30000,
     maxRedirects: 3,
     keepAlive: true,
     maxSockets: 100
   }
   ```

### 🔄 重试和缓存策略

1. **智能重试**
   ```typescript
   const retryConfig = {
     maxRetries: 3,
     baseDelay: 1000,
     maxDelay: 30000,
     backoffMultiplier: 2
   }
   ```

2. **缓存策略**
   - 缓存API响应结果
   - 使用条件请求减少数据传输
   - 实现本地缓存机制

3. **连接池管理**
   - 合理设置连接超时时间
   - 使用连接复用
   - 监控连接池状态

---

## 📞 技术支持

如果在代理配置过程中遇到问题，请：

1. 查阅本文档的故障排除部分
2. 检查相关的错误日志和调试信息
3. 联系海外平台连接器开发团队

**文档版本**: v1.0.0  
**最后更新**: 2025年1月  
**维护团队**: 海外平台连接器开发组
