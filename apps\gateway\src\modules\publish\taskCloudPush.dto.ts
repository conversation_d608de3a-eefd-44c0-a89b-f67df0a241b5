export class AccountDTO {
  platformAccountId: string
  platformAuthorId: string
  platformAccountName: string
  platformAccountSpaceId: string
  platformAuthorData?: string
  cookie?: string
  parentId?: string
  localStorage?: string
}

export class ProxyDTO {
  serverAdd: string
  serverPort: string
  type: string
  userName: string
  password: string
  area: string
  province: string
  city: string
  sid: string
}

export class TaskDTO {
  videoPath: string
  cover: string
  taskId: string
  platform: string
  wxkey: string
  proxy?: ProxyDTO
  platformAccount: AccountDTO
}

export class TaskCloudPushDTO {
  publishType: string
  authToken?: string
  formData: unknown
  data: TaskDTO[]
}

export class CloudBaseDTO {
  authToken: string
  platform: string
  platformAccountId: string
  platformAccountSpaceId: string
  parentId?: string
  wxKey?: string
  token?: string
  docId?: string
  platformContentType?: string
  isDraft?: boolean
}
