import { BadRequestException, ForbiddenException, Injectable, Logger } from '@nestjs/common'
import { TosClient, TosClientError, TosServerError } from '@volcengine/tos-sdk'
import axios from 'axios'
import { PassThrough } from 'stream'

@Injectable()
export class TosService {
  private client: TosClient
  private bucketName = 'yixiaoer-lite-asserts'
  private host = 'https://yixiaoer-lite-asserts.tos-cn-shanghai.volces.com'
  private region = 'cn-shanghai' // 填写 Bucket 所在地域。以华北2（北京)为例，"Provide your region" 填写为 cn-beijing
  private endpoint = 'tos-cn-shanghai.volces.com' //填写域名地址

  logger = new Logger('TosService')

  constructor() {
    this.client = new TosClient({
      accessKeyId: process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
      region: this.region, 
      endpoint: this.endpoint 
    })
  }

  /**
   * 上传地址
   * @param buffer
   * @param name
   * @returns
   */
  async uploadFile(buffer: Buffer, name: string, bucketName?: string) {
    try {
      await this.client.putObject({
        bucket: bucketName ?? this.bucketName,
        key: name,
        body: buffer
      })
      return name
    } catch (error) {
      throw new BadRequestException(error)
    }
  }

  /**
   * 获取资源访问地址
   * @param key
   * @param queries 资源处理指令
   * @returns
   */
  async getAccessSignatureUrl(
    objectName: string,
    expires = 1800,
    queries?: {},
    bucketName?: string
  ) {
    try {
      const url = this.client.getPreSignedUrl({
        method: 'GET',
        expires: expires, // 过期时间，单位
        bucket: bucketName ?? this.bucketName,
        key: objectName,
        query: queries
      })

      return url
    } catch (error) {
      throw new Error(`unexpected exception, message: ${error.message}`)
    }
  }

  /**
   * 获取资源元数据地址
   * @param key
   * @param bucketName
   * @returns
   */
  async getHeadSignatureUrl(key: string, bucketName?: string): Promise<string> {
    try {
      const url = this.client.getPreSignedUrl({
        // @ts-ignore 没有包含HEAD的定义
        method: 'HEAD',
        expires: 300, // 过期时间，单位
        bucket: bucketName ?? this.bucketName,
        key: key
      })

      return url
    } catch (error) {
      if (error instanceof TosClientError) {
        throw new Error(`Client Err Msg:${error.message}, Client Err Stack:${error.stack}`)
      } else if (error instanceof TosServerError) {
        throw new Error(
          `Request ID:${error.requestId}, Response Status Code:${error.statusCode}, Response Err Code:${error.code}, Response Err Msg:${error.message}`
        )
      } else {
        this.logger.log('unexpected exception, message: ', error)
      }
    }
  }

  /**
   * 获取资源直传地址
   * @param objectName
   * @param checksum
   * @returns
   */
  async getUploadSignatureUrl(
    objectName: string,
    checksum?: string | undefined,
    bucketName?: string
  ) {
    try {
      let query = {}
      if (checksum) {
        query = {
          'x-tos-meta-checksum': checksum
        }
      }
      const res = await this.client.getPreSignedUrl({
        bucket: bucketName ?? this.bucketName,
        key: objectName,
        method: 'PUT',
        expires: 60 * 30,
        query: query
      })
      return res
    } catch (error) {
      throw new Error(`unexpected exception, message: ${error.message}`)
    }
  }

  async putObjectByStream(url: string, referer: string, teamCode: string) {
    try {
      // 使用 axios 请求远程视频文件并获取流
      const response = await axios.get(url, {
        responseType: 'stream',
        headers: {
          Referer: referer
        }
      })

      // 创建一个 PassThrough 流，将远程视频流传递到 OSS 上传
      const passThrough = new PassThrough()
      response.data.pipe(passThrough) // 将下载的数据流输送到 PassThrough 流

      const fileName = this.getFileNameFromUrl(url)

      // 调用 tos 服务上传文件流
      const result = await this.client.putObject({
        bucket: this.bucketName,
        key: `${teamCode}/${fileName}`,
        body: passThrough
      })

      if (result.statusCode === 200) {
        return `${this.host}/${teamCode}/${fileName}`
      }

      return ''
    } catch (error) {
      this.logger.log(error.message)
    }
  }

  /**
   * 获取文件列表
   * @param prefix
   * @returns
   */
  async getFileList(prefix: string, bucketName?: string) {
    try {
      const result = await this.client.listObjectsType2({
        bucket: bucketName ?? this.bucketName,
        prefix: prefix,
        delimiter: '/',
        maxKeys: 10
      })
      return result
    } catch (error) {
      this.logger.error('获取文件列表失败', error)
    }
  }

  /**
   * 删除OSS指定文件
   * @param key
   */
  async deleteOssObject(key: string) {
    try {
      await this.client.deleteObject({ bucket: this.bucketName, key: key })
    } catch (error) {
      Logger.error('Oss文件删除失败 info:', error)
    }
  }

  /**
   * 批量删除OSS指定文件
   * @param key
   */
  async deleteMultiOssObject(key: string[]) {
    try {
      const objects = key.map((item) => {
        return {
          key: item
        }
      })
      await this.client.deleteMultiObjects({ bucket: this.bucketName, objects: objects })
    } catch (error) {
      Logger.error('文件删除失败 info:', error)
    }
  }

  /**
   * 获取资源的元数据
   * @param key
   */
  async headFileInfo(key: string) {
    try {
      const result = await this.client.headObject({ bucket: this.bucketName, key: key })
      if (result.statusCode === 200) {
        return result.headers['content-length']
      }
      return null
    } catch (error) {
      Logger.error('Error getting file info:', error)
      throw new ForbiddenException('文件信息获取错误')
    }
  }

  /**
   * app的apk包下载必须使用自定义域名
   * @param bucketName
   * @param key
   * @param expires
   * @returns
   */
  async getDesktopDownloadUrl(bucketName: string, key: string, expires = 10) {
    try {
      const desktopClient = new TosClient({
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        region: this.region,
        endpoint: 'yixiaoer.cn' // 填写域名地址
      })

      const url = desktopClient.getPreSignedUrl({
        method: 'GET',
        expires: expires, // 过期时间，单位
        bucket: bucketName ?? this.bucketName,
        key: key
      })

      return url
    } catch (error) {
      this.logger.log('unexpected exception, message: ', error)
      throw new ForbiddenException('下载地址获取失败')
    }
  }

  getFileNameFromUrl(url: string): string {
    const parsedUrl = new URL(url)
    const { pathname } = parsedUrl
    if (!pathname) {
      return ''
    }
    // 去除问号及后面的部分
    const pathWithoutQuery = pathname.split('?')[0]
    const segments = pathWithoutQuery.split('/')
    const fileName = segments[segments.length - 1]
    return fileName
  }
}
