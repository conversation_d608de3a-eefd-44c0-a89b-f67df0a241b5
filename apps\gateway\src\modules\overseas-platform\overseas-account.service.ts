import { Injectable, Logger } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { LoginStatus, PlatformAccountEntity } from '@yxr/mongo'
import { PlatformType } from '@yxr/common'
import { PlatformAccountService } from '../platform/platform-account.service'
import { PlatformAccountDetailResponse, PlatformAccountRequest } from '../platform/platform-account.dto'

export interface OverseasAccountInfo {
  openId: string
  nick_name: string
  avatar: string
  credentials: unknown
}

export interface CreateOverseasAccountsInput {
  accounts: OverseasAccountInfo[]
  teamId: string
  platform: string
}

@Injectable()
export class OverseasAccountService {
  private readonly logger = new Logger(OverseasAccountService.name)
  
  // 海外平台每个账号占用4个点数
  private readonly OVERSEAS_ACCOUNT_CAPACITY = 4

  constructor(
    @InjectModel(PlatformAccountEntity.name) private platformAccountModel: Model<PlatformAccountEntity>,
    private readonly platformAccountService: PlatformAccountService
  ) {}

  /**
   * 创建海外平台账号
   * @param input 创建账号的输入参数
   * @returns 创建成功的账号列表
   */
  async createOverseasAccounts(input: CreateOverseasAccountsInput): Promise<PlatformAccountDetailResponse[]> {
    const result: Array<PlatformAccountDetailResponse> = []

    for (const account of input.accounts) {
      try {
        // 检查账号是否已存在
        const exist = await this.platformAccountModel.findOne({
          platformAuthorId: account.openId,
          platformType: PlatformType.海外平台
        })

        if (exist) {
          if (input.teamId !== exist.teamId.toString()) {
            // 账号已绑定在其他团队，更新授权凭据
            await this.platformAccountModel.updateOne(
              { _id: new Types.ObjectId(exist._id) },
              { credentials: account.credentials }
            )
            this.logger.warn(`账号 ${account.openId} 已绑定在其他团队`)
            continue
          } else {
            // 账号已存在于当前团队，更新授权凭据
            await this.platformAccountModel.updateOne(
              { _id: new Types.ObjectId(exist._id) },
              { credentials: account.credentials }
            )
            this.logger.debug(`更新现有账号 ${account.openId} 的授权凭据`)
            continue
          }
        }

        // 创建新账号
        const body = {
          credentials: account.credentials,
          status: LoginStatus.Succesed,
          platformAccountName: account.nick_name ?? '未知',
          platformAvatar: account.avatar ?? '',
          platformType: PlatformType.海外平台,
          platformName: input.platform,
          platformAuthorId: account.openId,
          accountStatus: 1
        } as PlatformAccountRequest

        // 创建账号
        const platformAccount = await this.platformAccountService.platformAccountCreateAsync(body)
        
        // 更新账号的点数为海外平台标准（4个点数）
        await this.platformAccountModel.updateOne(
          { _id: new Types.ObjectId(platformAccount.id) },
          { capacity: this.OVERSEAS_ACCOUNT_CAPACITY }
        )

        result.push(platformAccount)
        this.logger.debug(`成功创建海外账号: ${account.openId}`)

      } catch (error) {
        this.logger.error(`创建账号 ${account.openId} 失败: ${error.message}`, error.stack)
        // 继续处理其他账号，不中断整个流程
      }
    }

    this.logger.log(`成功创建 ${result.length} 个海外平台账号`)
    return result
  }

  /**
   * 格式化账号信息用于WebSocket事件
   * @param accounts 原始账号信息
   * @param platform 平台名称
   * @returns 格式化后的账号信息
   */
  formatAccountsForWebSocket(accounts: OverseasAccountInfo[], platform: string) {
    return accounts.map(account => ({
      id: account.openId,
      avatar: account.avatar,
      nickName: account.nick_name,
      platform: platform
    }))
  }

  /**
   * 获取海外账号占用的点数
   * @returns 海外账号点数
   */
  getOverseasAccountCapacity(): number {
    return this.OVERSEAS_ACCOUNT_CAPACITY
  }
}
