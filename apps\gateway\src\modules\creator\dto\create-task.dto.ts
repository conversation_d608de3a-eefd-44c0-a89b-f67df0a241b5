import { IsArray, IsBoolean, IsN<PERSON><PERSON><PERSON><PERSON>, IsOptional, IsString } from 'class-validator';

class PlatformInfo {
  @IsOptional()
  @IsString()
  category?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  music?: string;

  @IsOptional()
  @IsString()
  topic?: string;
}

export class CreateTaskInputDto {
  @IsArray()
  @IsNotEmpty()
  accountIds: string[];

  @IsString()
  @IsNotEmpty()
  videoKey: string;

  @IsString()
  @IsNotEmpty()
  coverKey: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsBoolean()
  @IsNotEmpty()
  isOriginal: boolean;

  @IsString()
  @IsOptional()
  scheduledTime?: string;

  @IsOptional()
  platformInfo?: PlatformInfo;
}
