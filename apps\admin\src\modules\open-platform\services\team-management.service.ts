import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ForbiddenException
} from '@nestjs/common'
import { InjectConnection, InjectModel } from '@nestjs/mongoose'
import { Connection, Model, Types } from 'mongoose'
import {
  TeamEntity,
  PlatformAccountEntity,
  OpenPlatformApplicationEntity
} from '@yxr/mongo'
import { LoginStatus } from '@yxr/mongo'
import {
  UpdateTeamExpirationRequestDto,
  SetTeamQuotaRequestDto,
  UpdateTeamExpirationResponseDto,
  SetTeamQuotaResponseDto,
  FrozenAccountInfoDto
} from '../dto/team-management.dto'

/**
 * 开放平台团队管理服务
 * 提供团队过期时间修改和资源配额设置功能
 */
@Injectable()
export class TeamManagementService {
  private readonly logger = new Logger(TeamManagementService.name)

  constructor(
    @InjectModel(TeamEntity.name)
    private teamModel: Model<TeamEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectConnection() private connection: Connection
  ) {}

  /**
   * 修改团队过期时间
   */
  async updateTeamExpiration(
    sourceAppId: string,
    updateDto: UpdateTeamExpirationRequestDto
  ): Promise<UpdateTeamExpirationResponseDto> {
    this.logger.log(`开始修改团队过期时间: teamId=${updateDto.teamId}, sourceAppId=${sourceAppId}`)

    // 验证应用权限
    await this.validateApplicationAccess(sourceAppId, updateDto.teamId)

    // 查找团队
    const team = await this.teamModel.findById(updateDto.teamId)
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    // 验证新的过期时间
    const newExpiredAt = new Date(updateDto.expiredAt)
    const now = new Date()
    
    if (newExpiredAt <= now) {
      throw new BadRequestException('新的过期时间必须大于当前时间')
    }

    // 记录原过期时间
    const oldExpiredAt = team.expiredAt

    const session = await this.connection.startSession()
    session.startTransaction()

    try {
      // 更新团队过期时间
      const updatedTeam = await this.teamModel.findByIdAndUpdate(
        updateDto.teamId,
        {
          expiredAt: newExpiredAt,
          updatedAt: new Date()
        },
        { session, new: true }
      )

      if (!updatedTeam) {
        throw new NotFoundException('团队更新失败')
      }

      await session.commitTransaction()

      const result: UpdateTeamExpirationResponseDto = {
        teamId: updatedTeam._id.toString(),
        teamName: updatedTeam.name,
        oldExpiredAt: oldExpiredAt?.getTime() || 0,
        newExpiredAt: newExpiredAt.getTime(),
        updatedAt: updatedTeam.updatedAt?.getTime() || Date.now()
      }

      this.logger.log(
        `团队过期时间修改成功: teamId=${updateDto.teamId}, ` +
        `原过期时间=${oldExpiredAt?.toISOString() || 'null'}, ` +
        `新过期时间=${newExpiredAt.toISOString()}`
      )

      return result
    } catch (error) {
      await session.abortTransaction()
      this.logger.error(`团队过期时间修改失败: ${error.message}`, error.stack)
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * 设置团队资源配额
   */
  async setTeamQuota(
    sourceAppId: string,
    quotaDto: SetTeamQuotaRequestDto
  ): Promise<SetTeamQuotaResponseDto> {
    this.logger.log(
      `开始设置团队资源配额: teamId=${quotaDto.teamId}, ` +
      `accountCapacityLimit=${quotaDto.accountCapacityLimit}, ` +
      `networkTraffic=${quotaDto.networkTraffic}, ` +
      `sourceAppId=${sourceAppId}`
    )

    // 验证应用权限
    await this.validateApplicationAccess(sourceAppId, quotaDto.teamId)

    // 查找团队
    const team = await this.teamModel.findById(quotaDto.teamId)
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    // 记录原配额
    const oldNetworkTraffic = team.networkTraffic || 0

    const session = await this.connection.startSession()
    session.startTransaction()

    try {
      let frozenAccounts: FrozenAccountInfoDto[] = []

      // 更新团队配额
      const updatedTeam = await this.teamModel.findByIdAndUpdate(
        quotaDto.teamId,
        {
          accountCapacityLimit: team.accountCapacity + quotaDto.accountCapacityLimit,
          networkTraffic: team.networkTraffic + quotaDto.networkTraffic,
          updatedAt: new Date()
        },
        { session, new: true }
      )

      if (!updatedTeam) {
        throw new NotFoundException('团队更新失败')
      }

      await session.commitTransaction()

      const result: SetTeamQuotaResponseDto = {
        teamId: updatedTeam._id.toString(),
        teamName: updatedTeam.name,
        accountCapacityLimit: updatedTeam.accountCapacityLimit,
        newNetworkTraffic: updatedTeam.networkTraffic,
        updatedAt: updatedTeam.updatedAt?.getTime() || Date.now()
      }

      this.logger.log(
        `团队资源配额设置成功: teamId=${quotaDto.teamId}, ` +
        `账号限制: ${updatedTeam.accountCapacityLimit} -> ${quotaDto.accountCapacityLimit}, ` +
        `流量配额: ${oldNetworkTraffic} -> ${quotaDto.networkTraffic}, ` +
        `冻结账号数量: ${frozenAccounts.length}`
      )

      return result
    } catch (error) {
      await session.abortTransaction()
      this.logger.error(`团队资源配额设置失败: ${error.message}`, error.stack)
      throw error
    } finally {
      await session.endSession()
    }
  }

  /**
   * 冻结多余的账号
   */
  private async freezeExcessAccounts(
    teamId: string,
    newLimit: number,
    currentCount: number,
    session: any
  ): Promise<FrozenAccountInfoDto[]> {
    const excessCount = currentCount - newLimit

    this.logger.log(
      `需要冻结 ${excessCount} 个账号: teamId=${teamId}, ` +
      `当前在线=${currentCount}, 新限制=${newLimit}`
    )

    // 查找需要冻结的账号（按最后登录时间排序，优先冻结最久未登录的账号）
    const accountsToFreeze = await this.platformAccountModel
      .find({
        teamId: new Types.ObjectId(teamId),
        loginStatus: LoginStatus.Succesed
      })
      .sort({ updatedAt: 1 }) // 按更新时间升序，最久未活动的在前
      .limit(excessCount)
      .session(session)

    if (accountsToFreeze.length === 0) {
      return []
    }

    // 冻结账号
    const accountIds = accountsToFreeze.map(account => account._id)
    await this.platformAccountModel.updateMany(
      { _id: { $in: accountIds } },
      { 
        isFreeze: true,
        updatedAt: new Date()
      },
      { session }
    )

    // 构建冻结账号信息
    const frozenAccounts: FrozenAccountInfoDto[] = accountsToFreeze.map(account => ({
      accountId: account._id.toString(),
      platformAccountName: account.platformAccountName,
      platformName: account.platformName,
      lastLoginTime: account.updatedAt?.getTime() || 0
    }))

    this.logger.log(
      `账号冻结完成: teamId=${teamId}, 冻结数量=${frozenAccounts.length}, ` +
      `账号列表=[${frozenAccounts.map(a => a.platformAccountName).join(', ')}]`
    )

    return frozenAccounts
  }

  /**
   * 验证应用访问权限
   */
  private async validateApplicationAccess(sourceAppId: string, teamId: string): Promise<void> {
    // 查找团队，验证是否属于该应用
    const team = await this.teamModel.findById(teamId)
    if (!team) {
      throw new NotFoundException('团队不存在')
    }

    // 验证团队是否属于该应用
    if (team.sourceAppId?.toString() !== sourceAppId) {
      throw new ForbiddenException('无权限操作该团队')
    }

    // 验证应用是否存在且有效
    const application = await this.applicationModel.findById(sourceAppId)
    if (!application) {
      throw new ForbiddenException('应用不存在或无效')
    }

    this.logger.debug(
      `应用权限验证通过: sourceAppId=${sourceAppId}, teamId=${teamId}, ` +
      `teamName=${team.name}, appName=${application.name}`
    )
  }
}
