import { Controller, Get, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiQuery,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { PlatformAccountListRequest, PlatformAccountListResponse } from './platform-account.dto'
import { PlatformAccountService } from './platform-account.service'

@Controller('platform-account')
@ApiTags('媒体账号列表')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class PlatformAccountController {
  constructor(private readonly platformAccountService: PlatformAccountService) {}

  /**
   * 账号列表
   * @returns
   * @param query
   */
  @Get()
  @ApiOperation({ summary: '媒体账号列表' })
  @ApiOkResponse({ type: PlatformAccountListResponse })
  @ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
  @ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
  @ApiQuery({ type: PlatformAccountListRequest })
  @ApiHeader({ name: 'authorization', required: true })
  async getPlatformAccounts(@Query() query: PlatformAccountListRequest) {
    return await this.platformAccountService.getPlatformAccountListAsync(query)
  }
}
