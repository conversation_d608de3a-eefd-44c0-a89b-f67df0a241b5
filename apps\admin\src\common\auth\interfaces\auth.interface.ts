import { UserType } from '@yxr/common'

/**
 * 认证用户信息接口
 */
export interface AuthenticatedUser {
  id: string
  userType: UserType
  [key: string]: any
}

/**
 * 会话信息接口
 */
export interface SessionInfo {
  userId: string
  userType: UserType
  teamId?: string
  applicationId?: string
  appId?: string
}

/**
 * Token验证结果接口
 */
export interface TokenValidationResult {
  isValid: boolean
  user?: AuthenticatedUser
  session?: SessionInfo
  error?: string
}

/**
 * 应用Token载荷接口
 */
export interface ApplicationTokenPayload {
  userId: string
  appId: string
  applicationId: string
  userType: UserType.APPLICATION
  expiresAt: number
}

/**
 * 认证服务抽象接口
 */
export interface IAuthService {
  /**
   * 验证管理员Token
   */
  validateAdminToken(token: string): Promise<TokenValidationResult>
  
  /**
   * 验证开放平台用户Token
   */
  validateOpenPlatformToken(token: string): Promise<TokenValidationResult>
  
  /**
   * 验证应用Token
   */
  validateApplicationToken(token: string): Promise<TokenValidationResult>
  
  /**
   * 生成管理员Token
   */
  generateAdminToken(user: any): Promise<string>
  
  /**
   * 生成开放平台用户Token
   */
  generateOpenPlatformToken(user: any): Promise<string>
}

/**
 * 权限验证服务抽象接口
 */
export interface IAccessControlService {
  /**
   * 验证用户是否有权限访问指定资源
   */
  hasPermission(session: SessionInfo, requiredPermissions: string[]): boolean
  
  /**
   * 获取用户的数据隔离信息
   */
  getDataIsolationInfo(session: SessionInfo): {
    sourceAppId?: string
    applicationId?: string
  } | null
}
