import { Injectable, Logger, OnModuleInit } from '@nestjs/common'
import { EventEmitter } from 'events'
import { Queue, Worker } from 'bullmq'
import { OrderEntity } from '@yxr/mongo'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { OrderStatus, SalesType } from '@yxr/common'

export const eventKey = 'update-order'

export const orderEventEmitter = new EventEmitter()

@Injectable()
export class OrderEventService implements OnModuleInit {
  logger = new Logger('OrderEventService')

  constructor(@InjectModel(OrderEntity.name) private orderModel: Model<OrderEntity>) {}

  taskQueue: Queue

  taskWorker: Worker

  onModuleInit() {
    orderEventEmitter.on(eventKey, this.createUpdateOrderTask.bind(this))

    this.taskQueue = new Queue('order-event', {
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT!, 10),
        db: parseInt(process.env.REDIS_SYNC_DB!, 10),
        password: process.env.REDIS_PASSWORD
      }
    })
    this.taskWorker = new Worker(
      'order-event',
      async (job) => {
        const { orderNo } = job.data
        await this.updateOrder(orderNo)
      },
      {
        connection: {
          host: process.env.REDIS_HOST,
          port: parseInt(process.env.REDIS_PORT!, 10),
          db: parseInt(process.env.REDIS_SYNC_DB!, 10),
          password: process.env.REDIS_PASSWORD
        }
      }
    )

    // this.migrate()
  }

  async createUpdateOrderTask({ orderNo, type }: { orderNo: string; type: 'create' | 'close' }) {
    switch (type) {
      case 'create':
        await this.taskQueue.add(
          'update-order',
          { orderNo },
          {
            delay: 60 * 60 * 1000,
            removeOnComplete: true,
            removeOnFail: true,
            jobId: `${orderNo}-order-task`
          }
        )
        break
      case 'close':
        const job = await this.taskQueue.getJob(`${orderNo}-order-task`)
        if (job) {
          await job.remove()
        }
        break
      default:
        break
    }
  }

  async updateOrder(orderNo: string) {
    try {
      const oldOrder = await this.orderModel.findOne({
        orderNo
      })

      if (oldOrder && oldOrder.orderStatus === OrderStatus.Pending) {
        await this.orderModel.findByIdAndUpdate(new Types.ObjectId(oldOrder.id), {
          orderStatus: OrderStatus.Cancelled,
          salesType: SalesType.NotBuy
        })
      }
    } catch (error) {
      this.logger.error(error)
    }
  }
}
