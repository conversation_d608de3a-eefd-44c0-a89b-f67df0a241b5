import { Test, TestingModule } from '@nestjs/testing'
import { ExecutionContext, ForbiddenException } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { AccessControlGuard } from '../guards/access-control.guard'
import { UserType } from '@yxr/common'
import { ACCESS_CONTROL_KEY } from '../decorators/access-control.decorator'

describe('AccessControlGuard', () => {
  let guard: AccessControlGuard
  let reflector: Reflector

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AccessControlGuard,
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn()
          }
        }
      ]
    }).compile()

    guard = module.get<AccessControlGuard>(AccessControlGuard)
    reflector = module.get<Reflector>(Reflector)
  })

  const createMockContext = (session: any, accessConfig: any = null) => {
    const mockRequest = {
      session,
      dataIsolation: undefined
    }

    const mockContext = {
      switchToHttp: () => ({
        getRequest: () => mockRequest
      }),
      getHandler: jest.fn(),
      getClass: jest.fn()
    } as unknown as ExecutionContext

    jest.spyOn(reflector, 'getAllAndOverride').mockReturnValue(accessConfig)

    return { mockContext, mockRequest }
  }

  describe('AdminOnly access control', () => {
    it('should allow ADMIN user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.ADMIN, userId: 'admin123' },
        { allowedUserTypes: [UserType.ADMIN], requireDataIsolation: false }
      )

      expect(guard.canActivate(mockContext)).toBe(true)
    })

    it('should deny OPEN_PLATFORM user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.OPEN_PLATFORM, userId: 'op123' },
        { allowedUserTypes: [UserType.ADMIN], requireDataIsolation: false }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })

    it('should deny APPLICATION user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.APPLICATION, userId: 'app123' },
        { allowedUserTypes: [UserType.ADMIN], requireDataIsolation: false }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })
  })

  describe('OpenPlatformAccess access control', () => {
    it('should allow OPEN_PLATFORM user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.OPEN_PLATFORM, userId: 'op123' },
        { allowedUserTypes: [UserType.OPEN_PLATFORM], requireDataIsolation: false }
      )

      expect(guard.canActivate(mockContext)).toBe(true)
    })

    it('should deny ADMIN user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.ADMIN, userId: 'admin123' },
        { allowedUserTypes: [UserType.OPEN_PLATFORM], requireDataIsolation: false }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })
  })

  describe('ApplicationAccess access control', () => {
    it('should allow APPLICATION user access with data isolation', () => {
      const { mockContext, mockRequest } = createMockContext(
        { 
          userType: UserType.APPLICATION, 
          userId: 'app123',
          applicationId: 'app123',
          appId: 'test-app'
        },
        { allowedUserTypes: [UserType.APPLICATION], requireDataIsolation: true }
      )

      expect(guard.canActivate(mockContext)).toBe(true)
      expect(mockRequest.dataIsolation).toEqual({
        sourceAppId: 'test-app',
        applicationId: 'app123'
      })
    })

    it('should deny APPLICATION user without complete token info', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.APPLICATION, userId: 'app123' }, // 缺少 appId 和 applicationId
        { allowedUserTypes: [UserType.APPLICATION], requireDataIsolation: true }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })
  })

  describe('AdminAndOpenPlatformAccess access control', () => {
    it('should allow ADMIN user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.ADMIN, userId: 'admin123' },
        { allowedUserTypes: [UserType.ADMIN, UserType.OPEN_PLATFORM], requireDataIsolation: false }
      )

      expect(guard.canActivate(mockContext)).toBe(true)
    })

    it('should allow OPEN_PLATFORM user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.OPEN_PLATFORM, userId: 'op123' },
        { allowedUserTypes: [UserType.ADMIN, UserType.OPEN_PLATFORM], requireDataIsolation: false }
      )

      expect(guard.canActivate(mockContext)).toBe(true)
    })

    it('should deny APPLICATION user access', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.APPLICATION, userId: 'app123' },
        { allowedUserTypes: [UserType.ADMIN, UserType.OPEN_PLATFORM], requireDataIsolation: false }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })
  })

  describe('No access control configuration', () => {
    it('should allow access when no access control is configured', () => {
      const { mockContext } = createMockContext(
        { userType: UserType.ADMIN, userId: 'admin123' },
        null // 没有访问控制配置
      )

      expect(guard.canActivate(mockContext)).toBe(true)
    })
  })

  describe('Unauthenticated access', () => {
    it('should deny access when no session', () => {
      const { mockContext } = createMockContext(
        null, // 没有session
        { allowedUserTypes: [UserType.ADMIN], requireDataIsolation: false }
      )

      expect(() => guard.canActivate(mockContext)).toThrow(ForbiddenException)
    })
  })
})
