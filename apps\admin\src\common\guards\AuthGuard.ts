import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable, Logger,
  UnauthorizedException
} from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { Cache } from 'cache-manager'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { AdminEntity, UserEntity } from '@yxr/mongo'
import type { FastifyRequest } from 'fastify'
import process from 'node:process'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'

@Injectable()
export class TokenGuard implements CanActivate {
  private readonly logger = new Logger(TokenGuard.name)

  constructor(
    private reflector: Reflector,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>
  ) {
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<FastifyRequest>()
    const { authorization } = request.headers

    const anonymous = this.reflector.getAllAndOverride('anonymous', [
      context.getHandler(),
      context.getClass()
    ])

    if (anonymous) {
      return true
    } else {
      try {
        let user = await this.cacheManager.get<AdminEntity>(authorization)

        // 允许在本地/开发环境下使用模拟的 token 验证
        if (process.env.NODE_ENV === 'local' || process.env.NODE_ENV === 'dev') {
          if (user === null || user === undefined) {
            user = await this.adminModel.findById<AdminEntity>(new Types.ObjectId(authorization))
            if (user) {
              this.logger.debug(`模拟的会话: userId: ${authorization}`)
            }
          }
        }

        if (user) {
          request.user = user
          request.authorization = authorization
          return true
        }
      } catch {
        // ignore
      }

      throw new UnauthorizedException('登录失效, 请重新登录')
    }
  }
}
