import { Module } from '@nestjs/common'
import { UserController } from './user.controller'
import { UserService } from './user.service'
import {
  ChannelMongoose,
  MemberMongoose,
  TeamMongoose,
  UserDeviceLogsMongoose,
  UserDevicesMongoose,
  UserMongoose
} from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { UserDevicesController } from './user-devices.controller'
import { UserDevicesService } from './user-devices.service'
import { UserIdentifierService } from './user-identifier.service'
import { OrderManagerModule } from '@yxr/order'
import { WebhookModule } from '../webhook/webhook.module'

@Module({
  imports: [
    UserMongoose,
    TeamMongoose,
    MemberMongoose,
    ChannelMongoose,
    UserDevicesMongoose,
    UserDeviceLogsMongoose,
    OrderManagerModule,
    CommonModule,
    WebhookModule
  ],
  controllers: [UserController, UserDevicesController],
  providers: [UserService, UserDevicesService, UserIdentifierService],
  exports: [UserService, UserDevicesService, UserIdentifierService]
})
export class UserModule {}
