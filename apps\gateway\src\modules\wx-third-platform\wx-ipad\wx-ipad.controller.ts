import { Body, Controller, Get, Inject, Param, Post, Req, Res } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from 'apps/gateway/src/common/dto/BaseResponseDTO'
import { WxIpadService } from './wx-ipad.service'
import { FastifyReply, FastifyRequest } from 'fastify'
import { REQUEST } from '@nestjs/core'
import {
  postIpadQrcodeRequest,
  WxAccountIsLoginResponseDTO,
  WxQrCodeResponseDTO
} from './wx-ipad.dto'

@Controller('wx/ipad')
@ApiTags('媒体账号管理/微信Ipad协议管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
export class WxIpadController {
  constructor(
    private readonly wxIpadService: WxIpadService,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  @Post('callback')
  @ApiOperation({ summary: 'ipad微信回调信息' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postIpadCallback(@Req() req: FastifyRequest, @Res() res: FastifyReply) {
    const body = req.body
    await this.wxIpadService.postIpadCallback(body)

    res.status(200).send('success')
  }

  /**
   * 获取微信二维码
   * @returns
   */
  @Post('qrCodes')
  @ApiOperation({ summary: '生成ipad微信二维码' })
  @ApiOkResponse({ type: WxQrCodeResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async postIpadQrcode(@Body() body: postIpadQrcodeRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    return await this.wxIpadService.postIpadQrcode(currentTeamId, currentUserId, body)
  }

  @Get('qrCodes/:uuid/info')
  @ApiOperation({ summary: '获取微信二维码扫码信息' })
  @ApiOkResponse({ type: WxAccountIsLoginResponseDTO })
  @ApiHeader({ name: 'authorization', required: true })
  async getQrCodeInfo(@Param('uuid') uuid: string) {
    return await this.wxIpadService.getCheckLogin(uuid)
  }

}
