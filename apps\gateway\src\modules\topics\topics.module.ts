import { Module } from '@nestjs/common'
import { AuthorizationService } from '../../common/security/authorization.service'
import { CommonTopicsGroupMongoose, CommonTopicsMongoose, MemberMongoose } from '@yxr/mongo'
import { CommonModule } from '@yxr/common'
import { TopicsController } from './topics.controller'
import { TopicsService } from './topics.service'
import { TopicsGroupService } from './topics-group.service'

@Module({
  imports: [MemberMongoose, CommonTopicsGroupMongoose, CommonTopicsMongoose, CommonModule],
  controllers: [TopicsController],
  providers: [TopicsService, TopicsGroupService, AuthorizationService]
})
export class TopicsModule {}
