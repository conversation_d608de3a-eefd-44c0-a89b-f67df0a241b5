import { BadRequestException, Injectable, Logger } from '@nestjs/common'
import { TeamBucketNamesEnum, TianyiyunOssService } from '@yxr/common'
import { getOssSignatureDto } from './storage.dto'
import { nanoid } from 'nanoid'
import { TosService } from '@yxr/huoshan'

@Injectable()
export class StorageService {
  logger = new Logger('StorageService')

  private teamBucketDirs = {
    [TeamBucketNamesEnum.Assets]: 'as',
    [TeamBucketNamesEnum.Attachments]: 'at',
    [TeamBucketNamesEnum.SiteSpaces]: 'sp',
    [TeamBucketNamesEnum.MaterialLibrary]: 'ml',
    [TeamBucketNamesEnum.WechatPublish]: 'wxp',
    [TeamBucketNamesEnum.Logs]: 'log'
  }

  constructor(private readonly ossService: TosService) {}

  private calculateDir(teamId: string, bucket: TeamBucketNamesEnum) {
    const bucketDir = this.teamBucketDirs[bucket]
    if (!bucketDir) throw new BadRequestException(`bucket \'${bucket}\' is not valid`)

    // 根据不同环境添加不同的前缀, 生产环境不添加前缀
    const prefix = process.env.NODE_ENV === 'prod' ? '' : `${process.env.NODE_ENV}/`
    return `${prefix}t-${teamId}/${bucketDir}/`
  }

  /**
   * 获取资源直传地址
   */
  async getOssUploadUrl({
    teamId,
    bucket,
    fileKey
  }: {
    teamId: string
    bucket: TeamBucketNamesEnum
    fileKey: string | undefined
  }): Promise<getOssSignatureDto> {
    const startsWith = this.calculateDir(teamId, bucket)
    fileKey = fileKey ?? nanoid().toLocaleLowerCase()
    const key = `${startsWith}${fileKey}`

    const signatureUrl = await this.ossService.getUploadSignatureUrl(key)

    return {
      serviceUrl: signatureUrl,
      key: key
    }
  }

  async getOssAccessUrl({
    teamId,
    bucket,
    fileKey
  }: {
    bucket: TeamBucketNamesEnum
    teamId: string
    fileKey: string
  }): Promise<string> {
    const startsWith = this.calculateDir(teamId, bucket)
    fileKey = fileKey ?? nanoid().toLocaleLowerCase()
    const key = `${startsWith}${fileKey}`
    // const key = `${fileKey}`

    return await this.ossService.getAccessSignatureUrl(key)
  }

  getOssHeadUrl({
    teamId,
    bucket,
    fileKey
  }: {
    bucket: TeamBucketNamesEnum
    teamId: string
    fileKey: string
  }): Promise<string> {
    const startsWith = this.calculateDir(teamId, bucket)
    fileKey = fileKey ?? nanoid().toLocaleLowerCase()
    const key = `${startsWith}${fileKey}`

    return this.ossService.getHeadSignatureUrl(key)
  }
}
