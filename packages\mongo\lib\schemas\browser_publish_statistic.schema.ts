import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})

/**
 * 浏览器发布统计
 */
export class BrowserPublishStatisticEntity {
  @Prop({
    type: Types.ObjectId,
    required: true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: Number,
    required: true
  })
  publishCount: number

  @Prop({
    type: String,
    index: true,
    default: null
  })
  publishType: string

  @Prop({
    type: Date,
    index: true
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const BrowserPublishStatisticSchema: ModelDefinition = {
  name: BrowserPublishStatisticEntity.name,
  schema: SchemaFactory.createForClass(BrowserPublishStatisticEntity)
}

export const BrowserPublishStatisticMongoose = MongooseModule.forFeature([
  BrowserPublishStatisticSchema
])
