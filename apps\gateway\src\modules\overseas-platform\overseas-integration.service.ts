import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { AuthorizationStateCache } from './types'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { WebhookEvents } from '../webhook/constant'
import { AuthorizationStatus } from './overseas-platform-auth.dto'
import { WebhookService } from '../webhook/webhook.service'
import { PlatformAccountService } from '../platform/platform-account.service'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { PlatformAccountDetailResponse } from '../platform/platform-account.dto'
import { IntegrationAuthorizationCallbackInput } from './overseas-integration.dto'
import { calculateAuthorizationStateCacheKey } from './utils'
import { AccountCapacityService } from './account-capacity.service'
import { OverseasAccountService } from './overseas-account.service'

@Injectable()
export class OverseasIntegrationService {
  private readonly logger = new Logger(OverseasIntegrationService.name)

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly webhookService: WebhookService,
    private readonly accountCapacityService: AccountCapacityService,
    private readonly overseasAccountService: OverseasAccountService
  ) {
  }

  async authorizationCallback(input: IntegrationAuthorizationCallbackInput): Promise<PlatformAccountDetailResponse[]> {
    console.log('authorizationCallback', JSON.stringify(input))
    const stateCacheKey = calculateAuthorizationStateCacheKey(input.state)
    const cache = await this.cacheManager.get<AuthorizationStateCache>(stateCacheKey)

    if (!cache) {
      throw new ForbiddenException('无效状态码')
    }

    this.request.session = {
      ...this.request.session,
      userId: cache.userId,
      teamId: cache.teamId
    }

    // 检查账号点数限制
    const overseasAccountCapacity = this.overseasAccountService.getOverseasAccountCapacity()
    const capacityCheckResult = await this.accountCapacityService.checkAccountCapacityLimit(
      cache.teamId,
      input.accounts.length,
      overseasAccountCapacity
    )

    // 如果超出点数限制，暂停写入数据库并发送超限通知
    if (capacityCheckResult.isExceeded) {
      // 将授权成功的账号临时存储到缓存中，等待用户选择
      const tempAccountsCacheKey = `temp_accounts_${input.state}`
      await this.cacheManager.set(tempAccountsCacheKey, {
        accounts: input.accounts,
        cache,
        timestamp: Date.now()
      }, 300000) // 5分钟过期

      await this.webhookService.grpchook([cache.userId], cache.teamId, {
        event: WebhookEvents.OverseasAccountAuthorized,
        body: {
          state: input.state,
          status: 'capacity_exceed',
          accounts: this.overseasAccountService.formatAccountsForWebSocket(input.accounts, cache.platform),
          allowable: capacityCheckResult.allowableCount,
          message: 'OK'
        }
      })

      return []
    }

    // 点数未超限，正常处理账号添加
    const result = await this.overseasAccountService.createOverseasAccounts({
      accounts: input.accounts,
      teamId: cache.teamId,
      platform: cache.platform
    })

    // 发送授权成功通知
    await this.webhookService.grpchook([cache.userId], cache.teamId, {
      event: WebhookEvents.OverseasAccountAuthorized,
      body: {
        state: input.state,
        status: 'authorized',
        message: 'OK'
      }
    })

    return result
  }

}
