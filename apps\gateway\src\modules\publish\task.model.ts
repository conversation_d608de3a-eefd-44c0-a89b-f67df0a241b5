import { Modu<PERSON> } from '@nestjs/common'
import { TaskController } from './task.controller'
import { TaskService } from './task.service'
import {
  TaskMongoose,
  UserMongoose,
  ContentMongoose,
  PlatformAccountMongoose,
  TeamMongoose,
  MemberMongoose,
  BrowserPublishStatisticMongoose,
  TaskSetMongoose,
  CommonCategorysGroupMongoose,
  CommonCategorysMongoose,
  ContentStatisticMongoose,
  TrafficBillingMongoose,
  TeamComponentMongoose
} from '@yxr/mongo'
import { AppTaskController } from './appTask.controller'
import { AppTaskService } from './appTask.service'
import { WebhookModule } from '../webhook/webhook.module'
import { PushService } from './push.service'
import { PushController } from './push.controller'
import { CommonModule } from '@yxr/common'
import { TaskSetController } from './taskSet.controller'
import { TaskSetService } from './taskSet.service'
import { TaskAuditStatusChangedListener } from './task-audit-status.changed.listeners'
import { CommonCategorysController } from './common-category/common-category.controller'
import { CommonCategorysService } from './common-category/common-category.service'
import { CommonCategorysGroupService } from './common-category/common-category-group.service'
import { TaskCloudPushListener } from './task-cloud-push.listeners'
import { HuoshanModule } from '@yxr/huoshan'
import { TaskCloudService } from './task-cloud.service'
import { WxThirdPlatformModule } from '../wx-third-platform/wx-third-platform.module'
import { TeamModule } from '../team/team.module'
import { OverseasPublishIntegrationService } from '../overseas-platform/overseas-publish-integration.service'

@Module({
  imports: [
    UserMongoose,
    TaskMongoose,
    ContentMongoose,
    ContentStatisticMongoose,
    PlatformAccountMongoose,
    TeamMongoose,
    MemberMongoose,
    BrowserPublishStatisticMongoose,
    TaskSetMongoose,
    CommonCategorysMongoose,
    CommonCategorysGroupMongoose,
    TrafficBillingMongoose,
    TeamComponentMongoose,
    WebhookModule,
    CommonModule,
    HuoshanModule,
    TeamModule,
    WxThirdPlatformModule
  ],
  controllers: [
    TaskController,
    AppTaskController,
    PushController,
    TaskSetController,
    CommonCategorysController
  ],
  providers: [
    TaskService,
    AppTaskService,
    PushService,
    TaskSetService,
    CommonCategorysService,
    CommonCategorysGroupService,
    TaskAuditStatusChangedListener,
    TaskCloudPushListener,
    TaskCloudService,
    TaskCloudPushListener,
    OverseasPublishIntegrationService
  ],
  exports: [PushService]
})
export class TaskModule {}
