import { Injectable, Inject, ForbiddenException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types, FilterQuery } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  OpenPlatformUserRoleEntity,
  OpenPlatformChannelUserRelationEntity,
  UserEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  UserType
} from '@yxr/common'

/**
 * 开放平台权限控制服务
 * 提供基于角色的权限验证和数据隔离功能
 */
@Injectable()
export class OpenPlatformPermissionService {
  constructor(
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformChannelUserRelationEntity.name)
    private relationModel: Model<OpenPlatformChannelUserRelationEntity>,
    @InjectModel(UserEntity.name)
    private userModel: Model<UserEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 检查用户在应用中的角色
   */
  async getUserRoleInApplication(userId: string, applicationId: string): Promise<string | null> {
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(userId),
      applicationId: new Types.ObjectId(applicationId),
      status: OpenPlatformStatus.ACTIVE
    })

    return userRole?.role || null
  }

  /**
   * 检查用户是否为应用管理员
   */
  async isApplicationAdmin(userId: string, applicationId: string): Promise<boolean> {
    const role = await this.getUserRoleInApplication(userId, applicationId)
    return role === OpenPlatformRoleNames.ADMIN
  }

  /**
   * 检查用户是否为应用渠道商
   */
  async isApplicationChannel(userId: string, applicationId: string): Promise<boolean> {
    const role = await this.getUserRoleInApplication(userId, applicationId)
    return role === OpenPlatformRoleNames.CHANNEL
  }

  /**
   * 获取渠道商关联的用户ID列表
   */
  async getChannelRelatedUserIds(channelUserId: string, applicationId: string): Promise<string[]> {
    const relations = await this.relationModel.find({
      channelUserId: new Types.ObjectId(channelUserId),
      applicationId: new Types.ObjectId(applicationId),
      status: OpenPlatformStatus.ACTIVE
    })

    return relations.map(relation => relation.relatedUserId.toString())
  }

  /**
   * 为用户查询添加基于角色的数据隔离
   */
  async applyUserDataIsolation<T>(
    filter: FilterQuery<T>,
    applicationId: string,
    options: {
      userIdField?: string
      allowAdminFullAccess?: boolean
    } = {}
  ): Promise<FilterQuery<T>> {
    const { session } = this.request
    const { userIdField = 'userId', allowAdminFullAccess = true } = options

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问')
    }

    const userRole = await this.getUserRoleInApplication(session.userId, applicationId)

    if (!userRole) {
      throw new ForbiddenException('您没有权限访问此应用的数据')
    }

    // 管理员可以查看所有数据
    if (allowAdminFullAccess && userRole === OpenPlatformRoleNames.ADMIN) {
      return filter
    }

    // 渠道商只能查看自己关联的用户数据
    if (userRole === OpenPlatformRoleNames.CHANNEL) {
      const relatedUserIds = await this.getChannelRelatedUserIds(session.userId, applicationId)
      
      if (relatedUserIds.length === 0) {
        // 如果没有关联任何用户，返回空结果
        return {
          ...filter,
          [userIdField]: { $in: [] }
        }
      }

      return {
        ...filter,
        [userIdField]: { $in: relatedUserIds.map(id => new Types.ObjectId(id)) }
      }
    }

    throw new ForbiddenException('未知的用户角色')
  }

  /**
   * 验证用户是否有权限访问指定用户的数据
   */
  async validateUserDataAccess(
    targetUserId: string,
    applicationId: string,
    options: {
      allowAdminFullAccess?: boolean
    } = {}
  ): Promise<boolean> {
    const { session } = this.request
    const { allowAdminFullAccess = true } = options

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      return false
    }

    const userRole = await this.getUserRoleInApplication(session.userId, applicationId)

    if (!userRole) {
      return false
    }

    // 管理员可以访问所有用户数据
    if (allowAdminFullAccess && userRole === OpenPlatformRoleNames.ADMIN) {
      return true
    }

    // 渠道商只能访问自己关联的用户数据
    if (userRole === OpenPlatformRoleNames.CHANNEL) {
      const relatedUserIds = await this.getChannelRelatedUserIds(session.userId, applicationId)
      return relatedUserIds.includes(targetUserId)
    }

    return false
  }

  /**
   * 获取用户在应用中可访问的用户列表
   */
  async getAccessibleUsers(applicationId: string): Promise<UserEntity[]> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以访问')
    }

    const userRole = await this.getUserRoleInApplication(session.userId, applicationId)

    if (!userRole) {
      throw new ForbiddenException('您没有权限访问此应用的数据')
    }

    // 管理员可以查看所有用户
    if (userRole === OpenPlatformRoleNames.ADMIN) {
      return await this.userModel.find({
        status: OpenPlatformStatus.ACTIVE
      })
    }

    // 渠道商只能查看自己关联的用户
    if (userRole === OpenPlatformRoleNames.CHANNEL) {
      const relatedUserIds = await this.getChannelRelatedUserIds(session.userId, applicationId)
      
      if (relatedUserIds.length === 0) {
        return []
      }

      return await this.userModel.find({
        _id: { $in: relatedUserIds.map(id => new Types.ObjectId(id)) },
        status: OpenPlatformStatus.ACTIVE
      })
    }

    throw new ForbiddenException('未知的用户角色')
  }

  /**
   * 检查当前用户是否有权限执行指定操作
   */
  async checkPermission(
    applicationId: string,
    requiredRole: string,
    options: {
      allowHigherRole?: boolean
    } = {}
  ): Promise<boolean> {
    const { session } = this.request
    const { allowHigherRole = true } = options

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      return false
    }

    const userRole = await this.getUserRoleInApplication(session.userId, applicationId)

    if (!userRole) {
      return false
    }

    // 精确匹配
    if (userRole === requiredRole) {
      return true
    }

    // 允许更高级别的角色
    if (allowHigherRole) {
      if (requiredRole === OpenPlatformRoleNames.CHANNEL && userRole === OpenPlatformRoleNames.ADMIN) {
        return true
      }
    }

    return false
  }

  /**
   * 获取当前用户的权限信息
   */
  async getCurrentUserPermissions(applicationId: string): Promise<{
    role: string | null
    isAdmin: boolean
    isChannel: boolean
    canManageUsers: boolean
    canViewAllUsers: boolean
    relatedUserIds: string[]
  }> {
    const { session } = this.request

    if (session?.userType !== UserType.OPEN_PLATFORM) {
      return {
        role: null,
        isAdmin: false,
        isChannel: false,
        canManageUsers: false,
        canViewAllUsers: false,
        relatedUserIds: []
      }
    }

    const role = await this.getUserRoleInApplication(session.userId, applicationId)
    const isAdmin = role === OpenPlatformRoleNames.ADMIN
    const isChannel = role === OpenPlatformRoleNames.CHANNEL

    let relatedUserIds: string[] = []
    if (isChannel) {
      relatedUserIds = await this.getChannelRelatedUserIds(session.userId, applicationId)
    }

    return {
      role,
      isAdmin,
      isChannel,
      canManageUsers: isAdmin,
      canViewAllUsers: isAdmin,
      relatedUserIds
    }
  }
}
