const webpack = require('webpack')
const swcDefaultConfig =
  require('@nestjs/cli/lib/compiler/defaults/swc-defaults').swcDefaultsFactory().swcOptions

let GRPC_URL = '0.0.0.0'
let WEBHOOK_URL = 'webhook'
let ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
let WECHATPAY_WEBHOOK_URL = 'cDQJ8ckw5P5HP1zZ8h-wechatpay-webhook'

if (process.env.NODE_ENV === 'dev') {
  GRPC_URL = '*************'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
  WECHATPAY_WEBHOOK_URL = 'cDQJ8ckw5P5HP1zZ8h-wechatpay-webhook'
} else if (process.env.NODE_ENV === 'test') {
  GRPC_URL = '*************'
  WEBHOOK_URL = 'ttac99ddetest-webhook'
  WECHATPAY_WEBHOOK_URL = 'k9u7r3gspm05d-wechat-webhook'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
} else if (process.env.NODE_ENV === 'prod') {
  GRPC_URL = '************'
  WEBHOOK_URL = 'ttac99ddeprod-webhook' 
  WECHATPAY_WEBHOOK_URL = 'k9u7r3gspm05d-wechat-webhook'
  ALIPAY_WEBHOOK_URL = '3MCrp9Bt0zsdsNpiJd-alipay-webhook'
}

module.exports = {
  target: 'node',
  node: {
    __dirname: false
  },
  plugins: [
    new webpack.DefinePlugin({
      // 初始化需要的环境变量, 不能卸载.env文件中
      'process.env.GRPC_URL': JSON.stringify(GRPC_URL),
      // 'process.env.OSS_ACCESS_KEY_ID': JSON.stringify('LTAI5tD3QdxUQZbJAeHeX7Ma'),
      // 'process.env.OSS_ACCESS_KEY_SECRET': JSON.stringify('******************************'),
      'process.env.WEBHOOK_URL': JSON.stringify(WEBHOOK_URL),
      'process.env.WECHATPAY_WEBHOOK_URL': JSON.stringify(WECHATPAY_WEBHOOK_URL),
      'process.env.ALIPAY_WEBHOOK_URL': JSON.stringify(ALIPAY_WEBHOOK_URL)
    })
  ],
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: {
          loader: 'swc-loader',
          options: swcDefaultConfig
        }
      },
      {
        test: /\.node$/,
        loader: 'node-loader'
      }
    ]
  }
}
