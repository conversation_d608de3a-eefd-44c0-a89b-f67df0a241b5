# 重构后的开放平台订单服务

## 概述

对 `apps/admin/src/modules/open-platform/services/open-platform-order.service.ts` 中的订单创建逻辑进行了重构，将原来的单一 `createOpenPlatformOrder` 方法拆分为三个独立的方法，每个方法专门处理一种订单类型，提高了代码的可维护性和业务逻辑的清晰度。

## 重构目标

### ✅ **方法拆分**
- **createOpenOrder** - 处理开通订单（新团队首次开通VIP权益）
- **createUpgradeOrder** - 处理升级订单（增加现有团队的权益包数量）
- **createRenewOrder** - 处理续费订单（延长现有团队的VIP有效期）

### ✅ **独立DTO设计**
- 每个方法有独立的DTO参数定义，只包含该订单类型所需的字段
- 清晰的业务逻辑区分和验证规则

### ✅ **公共逻辑抽取**
- 三个方法调用共同的私有辅助方法处理公共逻辑
- 保持现有的事务处理、错误处理和日志记录机制

## 技术实现

### 1. DTO设计

#### 1.1 开通订单DTO
```typescript
export class CreateOpenOrderRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string

  @ApiProperty({
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '权益包数量',
    example: 1,
    minimum: 1
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  interestCount: number

  @ApiPropertyOptional({
    description: '月份数量',
    example: 1,
    minimum: 1
  })
  @ValidateIf((o) => !o.days)
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  month?: number

  @ApiPropertyOptional({
    description: '自定义天数',
    example: 30,
    minimum: 1
  })
  @ValidateIf((o) => !o.month)
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  days?: number

  @ApiProperty({
    description: '实付金额（元）',
    example: 100.00,
    minimum: 0
  })
  @ValidateIf((o) => o.isPay === true)
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01, { message: '实付金额必须大于0' })
  payAmount: number

  @ApiProperty({
    description: '是否需要支付',
    example: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiPropertyOptional({
    description: '备注',
    example: '开通权益包订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}
```

#### 1.2 升级订单DTO
```typescript
export class CreateUpgradeOrderRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string

  @ApiProperty({
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  @ApiProperty({
    description: '升级后的权益包数量',
    example: 2,
    minimum: 1
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  interestCount: number

  // 注意：升级订单不需要month和days参数
  // 升级是基于当前VIP剩余时间计算费用

  @ApiProperty({
    description: '实付金额（元）',
    example: 100.00,
    minimum: 0
  })
  @ValidateIf((o) => o.isPay === true)
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01, { message: '实付金额必须大于0' })
  payAmount: number

  @ApiProperty({
    description: '是否需要支付',
    example: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiPropertyOptional({
    description: '备注',
    example: '升级权益包订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}
```

#### 1.3 续费订单DTO
```typescript
export class CreateRenewOrderRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  applicationId: string

  @ApiProperty({
    description: '权益包ID',
    example: '6763bf166d5c258e55ac9657'
  })
  @IsString()
  @IsNotEmpty()
  interestId: string

  @ApiProperty({
    description: '团队ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsString()
  @IsNotEmpty()
  teamId: string

  // 注意：续费订单不需要interestCount参数
  // 续费保持当前权益包数量不变，只延长时间

  @ApiPropertyOptional({
    description: '月份数量',
    example: 1,
    minimum: 1
  })
  @ValidateIf((o) => !o.days)
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  month?: number

  @ApiPropertyOptional({
    description: '自定义天数',
    example: 30,
    minimum: 1
  })
  @ValidateIf((o) => !o.month)
  @IsNumber()
  @IsNotEmpty()
  @Min(1)
  days?: number

  @ApiProperty({
    description: '实付金额（元）',
    example: 100.00,
    minimum: 0
  })
  @ValidateIf((o) => o.isPay === true)
  @IsNumber()
  @IsNotEmpty()
  @Min(0.01, { message: '实付金额必须大于0' })
  payAmount: number

  @ApiProperty({
    description: '是否需要支付',
    example: true
  })
  @IsBoolean()
  @IsNotEmpty()
  isPay: boolean

  @ApiPropertyOptional({
    description: '备注',
    example: '续费权益包订单'
  })
  @IsOptional()
  @IsString()
  remark?: string
}
```

### 2. Service层重构

#### 2.1 主要方法

**开通订单方法**
```typescript
async createOpenOrder(createOrderDto: CreateOpenOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
  const { applicationId, interestId, interestCount, month, days, teamId, isPay, payAmount, remark } = createOrderDto

  // 验证基础权限和参数
  await this.validateBasicPermissions(applicationId, teamId)
  await this.validatePaymentParams(isPay, payAmount)

  // 检查应用余额
  if (isPay && payAmount > 0) {
    await this.validateApplicationBalance(applicationId, payAmount)
  }

  // 获取团队创建者
  const member = await this.getTeamMaster(teamId)

  // 创建虚拟管理员
  const virtualAdmin = this.createVirtualAdmin()

  const dbSession = await this.connection.startSession()

  try {
    let orderNo: string

    await dbSession.withTransaction(async () => {
      // 创建开通订单
      orderNo = await this.legacyOrderManagerService.createOrder({
        teamId: teamId,
        interestCount: interestCount,
        interestId: interestId,
        isCorporateTransfer: false,
        month: month,
        days: days,
        userId: member.userId.toString(),
        remark: remark,
        creatorId: virtualAdmin.id,
        creatorName: virtualAdmin.username,
        payAmount: payAmount,
        isPay: isPay
      })

      // 处理支付和订单完成
      await this.handleOrderPaymentAndCompletion(orderNo, isPay, payAmount, applicationId, teamId, dbSession, '开通')
    })

    return { orderNo }
  } catch (error) {
    this.logger.error(`创建开通订单失败: ${error.message}`, error.stack)
    throw this.handleOrderError(error)
  } finally {
    await dbSession.endSession()
  }
}
```

**升级订单方法**
```typescript
async createUpgradeOrder(upgradeOrderDto: CreateUpgradeOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
  // 类似的结构，但调用 legacyOrderManagerService.upgradeOrder
  // 升级订单不需要month和days参数
}
```

**续费订单方法**
```typescript
async createRenewOrder(renewOrderDto: CreateRenewOrderRequestDto): Promise<OpenPlatformOrderResponseDto> {
  // 类似的结构，但调用 legacyOrderManagerService.renewOrder
  // 续费订单不需要interestCount参数
}
```

#### 2.2 公共辅助方法

**基础权限验证**
```typescript
private async validateBasicPermissions(applicationId: string, teamId: string): Promise<void> {
  const { session } = this.request

  // 验证用户权限
  if (session?.userType !== UserType.OPEN_PLATFORM) {
    throw new ForbiddenException('只有开放平台用户可以创建订单')
  }

  // 验证应用权限
  const application = await this.applicationModel.findOne({
    _id: new Types.ObjectId(applicationId),
    userId: new Types.ObjectId(session.userId)
  }).lean()

  if (!application) {
    throw new ForbiddenException('无权限访问该应用')
  }

  // 验证团队是否属于该应用
  const team = await this.teamModel.findOne({
    _id: new Types.ObjectId(teamId),
    source: 'open_platform_app',
    sourceAppId: applicationId,
    isDeleted: false
  }).lean()

  if (!team) {
    throw new NotFoundException('团队不存在或无权限访问')
  }

  // 检查是否有未支付的订单
  const unexpiredOrderCount = await this.orderModel.countDocuments({
    teamId: new Types.ObjectId(teamId),
    orderStatus: OrderStatus.Pending,
    expiredAt: {
      $gt: new Date()
    }
  })

  if (unexpiredOrderCount > 0) {
    throw new ForbiddenException('还有未支付的订单')
  }
}
```

**支付参数验证**
```typescript
private async validatePaymentParams(isPay: boolean, payAmount: number): Promise<void> {
  if (isPay && payAmount <= 0) {
    throw new ForbiddenException('需要支付时折扣价必须大于0')
  }
}
```

**应用余额验证**
```typescript
private async validateApplicationBalance(applicationId: string, payAmount: number): Promise<void> {
  const balance = await this.getApplicationBalance(applicationId)
  if (balance.availableBalance < payAmount * 100) { // 转换为分
    throw new BadRequestException(
      `应用余额不足，当前可用余额：${(balance.availableBalance / 100).toFixed(2)}元，需要支付：${payAmount.toFixed(2)}元`
    )
  }
}
```

**订单支付和完成处理**
```typescript
private async handleOrderPaymentAndCompletion(
  orderNo: string,
  isPay: boolean,
  payAmount: number,
  applicationId: string,
  teamId: string,
  dbSession: any,
  orderType: string
): Promise<void> {
  if (isPay && payAmount > 0) {
    // 更新订单状态为已支付
    await this.orderModel.updateOne(
      { orderNo },
      {
        orderStatus: OrderStatus.Paid,
        payAmount: payAmount,
        payTime: new Date(),
        payType: PayType.Other
      },
      { session: dbSession }
    )

    // 扣除应用余额
    await this.deductApplicationBalance(applicationId, payAmount * 100, dbSession)

    // 处理订单完成逻辑
    await this.legacyOrderManagerService.handleCompletedOrder(orderNo)

    this.logger.log(
      `开放平台${orderType}订单支付成功: ${orderNo}, 应用ID: ${applicationId}, ` +
        `团队ID: ${teamId}, 支付金额: ${payAmount}元`
    )
  } else {
    // 免费订单处理逻辑
    // ...
  }
}
```

## 重构优势

### 1. **业务逻辑清晰**
- ✅ 每个方法专注于一种订单类型
- ✅ 参数验证更加精确和针对性
- ✅ 错误处理更加具体

### 2. **代码可维护性**
- ✅ 公共逻辑抽取为私有方法，避免重复代码
- ✅ 单一职责原则，每个方法职责明确
- ✅ 易于扩展和修改

### 3. **类型安全**
- ✅ 独立的DTO确保类型安全
- ✅ 编译时检查参数正确性
- ✅ 更好的IDE支持和代码提示

### 4. **测试友好**
- ✅ 每个方法可以独立测试
- ✅ 公共方法可以单独测试
- ✅ 更容易模拟和验证

## 使用示例

### 开通订单
```typescript
const openOrderResult = await openPlatformOrderService.createOpenOrder({
  applicationId: '507f1f77bcf86cd799439011',
  interestId: '6763bf166d5c258e55ac9657',
  teamId: '507f1f77bcf86cd799439012',
  interestCount: 1,
  month: 3,
  isPay: true,
  payAmount: 299.00,
  remark: '开通VIP权益'
})
```

### 升级订单
```typescript
const upgradeOrderResult = await openPlatformOrderService.createUpgradeOrder({
  applicationId: '507f1f77bcf86cd799439011',
  interestId: '6763bf166d5c258e55ac9657',
  teamId: '507f1f77bcf86cd799439012',
  interestCount: 2, // 升级到2个权益包
  isPay: true,
  payAmount: 150.00,
  remark: '升级权益包数量'
})
```

### 续费订单
```typescript
const renewOrderResult = await openPlatformOrderService.createRenewOrder({
  applicationId: '507f1f77bcf86cd799439011',
  interestId: '6763bf166d5c258e55ac9657',
  teamId: '507f1f77bcf86cd799439012',
  month: 6, // 续费6个月
  isPay: true,
  payAmount: 500.00,
  remark: '续费VIP权益'
})
```

## 兼容性保证

### ✅ **现有功能保持**
- 保持所有现有的事务处理机制
- 保持所有现有的错误处理和日志记录
- 保持所有现有的权限控制要求

### ✅ **数据一致性**
- 使用相同的余额管理逻辑
- 使用相同的订单处理流程
- 使用相同的数据隔离机制

### ✅ **性能优化**
- 减少了不必要的参数传递
- 更精确的验证逻辑
- 更好的错误处理性能

这个重构提高了代码的可维护性、可读性和可扩展性，同时保持了所有现有功能的完整性和一致性。
