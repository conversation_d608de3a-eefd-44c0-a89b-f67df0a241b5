import { ForbiddenException, Inject, Injectable, Logger } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import {
  AuthorizationStatus,
  OverseasGetAuthorizationUrlOutputDto,
  SelectAccountsInput
} from './overseas-platform-auth.dto'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import axios from 'axios'
import { authorizationStateCacheExpire, calculateAuthorizationStateCacheKey } from './utils'
import { PlatformAccountDetailResponse } from '../platform/platform-account.dto'
import { WebhookService } from '../webhook/webhook.service'
import { WebhookEvents } from '../webhook/constant'
import { AuthorizationStateCache } from './types'
import { AccountCapacityService } from './account-capacity.service'
import { OverseasAccountService } from './overseas-account.service'


@Injectable()
export class OverseasPlatformAuthService {
  logger = new Logger(OverseasPlatformAuthService.name)
  private readonly overseas_base_address = process.env.OVERSEAS_BASE_ADDRESS || ''

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly webhookService: WebhookService,
    private readonly accountCapacityService: AccountCapacityService,
    private readonly overseasAccountService: OverseasAccountService
  ) {
  }

  async getAuthorizationUrl(platform: string): Promise<OverseasGetAuthorizationUrlOutputDto> {
    // this.logger.debug(`getAuthorizationUrl input: ${JSON.stringify(input)}`)

    const { userId, teamId } = this.request.session

    // 调用远端的海外服务生成授权连接, 因为授权回调需要验证 state

    try {
      const result = await axios.get<{
        authorizationUrl: string,
        state: string
      }>(`${this.overseas_base_address}auth/url`, { params: { platform, userId, teamId } })

      await this.cacheManager.set(
        calculateAuthorizationStateCacheKey(result.data.state),
        {
          platform: platform,
          teamId,
          userId,
          status: AuthorizationStatus.Waiting
        },
        authorizationStateCacheExpire
      )

      return {
        authorizationUrl: result.data.authorizationUrl,
        state: result.data.state
      }
    } catch (error) {
      console.error(error)
    }
  }

  /**
   * 处理用户选择的账号
   * @param input 用户选择的账号信息
   * @returns 创建的账号列表
   */
  async selectAccounts(input: SelectAccountsInput): Promise<PlatformAccountDetailResponse[]> {
    const tempAccountsCacheKey = `temp_accounts_${input.state}`
    const tempData = await this.cacheManager.get<{
      accounts: any[],
      cache: AuthorizationStateCache,
      timestamp: number
    }>(tempAccountsCacheKey)

    if (!tempData) {
      throw new ForbiddenException('授权数据已过期，请重新授权')
    }

    // 设置会话信息
    this.request.session = {
      ...this.request.session,
      userId: tempData.cache.userId,
      teamId: tempData.cache.teamId
    }

    // 筛选用户选择的账号
    const selectedAccounts = tempData.accounts.filter(account =>
      input.selectedAccountIds.includes(account.openId)
    )

    if (selectedAccounts.length === 0) {
      throw new ForbiddenException('未选择任何账号')
    }

    // 再次检查点数限制（防止并发问题）
    const overseasAccountCapacity = this.overseasAccountService.getOverseasAccountCapacity()
    const capacityCheckResult = await this.accountCapacityService.checkAccountCapacityLimit(
      tempData.cache.teamId,
      selectedAccounts.length,
      overseasAccountCapacity
    )

    if (capacityCheckResult.isExceeded) {
      throw new ForbiddenException(`选择的账号数量超出点数限制，最多可选择 ${capacityCheckResult.allowableCount} 个账号`)
    }

    // 创建选择的账号
    const result = await this.overseasAccountService.createOverseasAccounts({
      accounts: selectedAccounts,
      teamId: tempData.cache.teamId,
      platform: tempData.cache.platform
    })

    // 删除临时缓存数据
    await this.cacheManager.del(tempAccountsCacheKey)

    // 发送授权成功通知
    await this.webhookService.grpchook([tempData.cache.userId], tempData.cache.teamId, {
      event: WebhookEvents.OverseasAccountAuthorized,
      body: {
        state: input.state,
        status: 'authorized',
        message: 'OK'
      }
    })

    this.logger.log(`用户 ${tempData.cache.userId} 成功添加 ${result.length} 个海外平台账号`)

    return result
  }

}
