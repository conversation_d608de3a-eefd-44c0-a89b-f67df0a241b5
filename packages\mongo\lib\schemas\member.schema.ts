import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Types } from 'mongoose'
import { MemberStatusEnum } from '@yxr/common'

@Schema({
  timestamps: true,
  versionKey: false
})
export class MemberEntity {
  @Prop({
    type: Types.ObjectId,
    required: true,
    index:true
  })
  userId: Types.ObjectId

  @Prop({
    type: Types.ObjectId,
    required: true,
    index:true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    required: false,
    maxlength: [15, '昵称长度不能大于15']
  })
  remark?: string

  @Prop({
    type: [String],
    required: true
  })
  roles: string[]

  @Prop({
    type: [String],
    required: true
  })
  accounts: string[]

  /**
   * @deprecated
   * 账号空间
   */
  @Prop({
    type: [String],
    required: false,
    default: []
  })
  browsers?: string[]

  @Prop({
    type: String,
    enum: {
      values: [MemberStatusEnum.NotJoined, MemberStatusEnum.Joined, MemberStatusEnum.Pending],
      message: '{VALUE} is not a valid state'
    },
    required: true,
    default: MemberStatusEnum.Pending
  })
  status: MemberStatusEnum

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: Boolean,
    default: false,
    required: false
  })
  isFreeze?: boolean
}

export const MemberSchema: ModelDefinition = {
  name: MemberEntity.name,
  schema: SchemaFactory.createForClass(MemberEntity)
}

MemberSchema.schema.index({ teamId: 1, userId: 1 }, { unique: true })

export const MemberMongoose = MongooseModule.forFeature([MemberSchema])
