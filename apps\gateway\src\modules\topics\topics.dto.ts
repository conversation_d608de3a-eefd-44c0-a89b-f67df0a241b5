import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Types } from 'mongoose'
import { IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator'

export class TopicsDTO {
  @ApiProperty({
    type: String,
    description: '话题ID',
    example: '3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '话题',
    example: '我的话题'
  })
  name: string

  @ApiProperty({
    type: Types.ObjectId,
    description: '话题分组ID',
    example: '3941b7c06fa2'
  })
  groupId: Types.ObjectId
}

export class TopicsGroupDTO {
  @ApiProperty({
    type: String,
    description: '分组ID',
    example: '3941b7c06fa2'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '分组名称',
    example: '分组一'
  })
  name: String
}

export class TopicsListDTO {
  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    type: Number,
    example: 50
  })
  maxTopicCountLimit: number

  @ApiResponseProperty({
    type: [TopicsDTO]
  })
  data: TopicsDTO[]
}

export class PostTopicsRequest {
  @ApiProperty({
    type: String,
    required: true,
    description: '话题名称',
    example: '我的话题'
  })
  @IsString()
  @MaxLength(15, { message: '话题名称长度太长。不得多于 $constraint1 个字符' })
  @IsNotEmpty({ message: '话题名称不能为空' })
  name: string

  @ApiProperty({
    type: String,
    required: true,
    description: '分组ID',
    example: '9413-3941b7c06fa2'
  })
  @IsString()
  @IsNotEmpty({ message: '分组不能为空' })
  groupId: string
}

/**
 * 分组
 */
export class PostTopicsGroupsRequest {
  @ApiProperty({
    type: String,
    example: '分组',
    required: true
  })
  @MaxLength(8, { message: '分组名称长度太长。不得多于 $constraint1 个字符' })
  @IsNotEmpty({ message: '分组名称不能为空' })
  @IsString()
  name: string
}

export class TopicsDetailResponse {
  @ApiResponseProperty({
    type: String,
    example: '9413-3941b7c06fa2'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '我的话题'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '9413-3941b7c06fa2'
  })
  groupId: string
}

export class TopicsDetailResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TopicsDetailResponse
  })
  data: TopicsDetailResponse
}

export class TopicsListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TopicsListDTO
  })
  data: TopicsListDTO
}

export class TopicsGroupResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TopicsGroupDTO
  })
  data: TopicsGroupDTO
}

export class TopicsGroupListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TopicsGroupDTO]
  })
  data: TopicsGroupDTO[]
}
