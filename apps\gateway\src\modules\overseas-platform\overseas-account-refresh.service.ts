import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import axios from 'axios';
import { PlatformAccountEntity } from '@yxr/mongo';
import { Cron, CronExpression } from '@nestjs/schedule';

function getCronExpression() {
  return process.env.NODE_ENV === 'local' ? CronExpression.EVERY_MINUTE : CronExpression.EVERY_5_MINUTES;
}

/**
 * 定时自动刷新海外平台账号 access_token 的服务
 */
@Injectable()
export class OverseasAccountRefreshService {
  private readonly logger = new Logger(OverseasAccountRefreshService.name);
  // 提前多少秒刷新（如提前5分钟）
  private readonly REFRESH_BEFORE_EXPIRE = 300;
  // 海外服务端刷新接口
  private readonly OVERSEAS_REFRESH_URL = `${process.env.OVERSEAS_BASE_ADDRESS}auth/refresh-token`;
  private readonly OVERSEAS_REFRESH_TOKEN = process.env.OVERSEAS_REFRESH_AUTH_TOKEN;

  constructor(
    @InjectModel(PlatformAccountEntity.name) private platformAccountModel: Model<PlatformAccountEntity>,
  ) {}

  /**
   * 定时自动刷新所有即将过期的海外账号 access_token
   */
  @Cron(getCronExpression())
  async refreshAllAccounts() {
    const now = Date.now();
    // 查询10分钟内即将过期的海外账号
    const tenMinutesLater = new Date(now + 10 * 60 * 1000);
    const accounts = await this.platformAccountModel.find({
      'credentials.access_token': { $exists: true },
      'credentials.expire_at': { $exists: true, $lte: tenMinutesLater },
      platformType: 2 // 假设2为海外平台类型
    });
    for (const account of accounts) {
      try {
        const credentials = account.credentials as any;
        if (!credentials.refresh_token || !credentials.expire_at) continue;
        
        this.logger.log(`账号${account.platformAccountName}(${account.platformName}) token 即将过期，自动刷新...`);
        const newToken = await this.refreshToken(account.platformName, String(account.teamId), credentials.refresh_token);
        if (newToken && newToken.access_token) {
            // 更新 credentials
            await this.platformAccountModel.updateOne(
                { _id: account._id },
                { $set: { 'credentials': { ...credentials, ...newToken } } }
            );
            
            this.logger.log(`账号${account.platformAccountName}刷新成功`);
        } else {
            this.logger.warn(`账号${account.platformAccountName}刷新失败，未获得新token`);
        }
      } catch (err) {
        this.logger.error(`账号${account.platformAccountName}刷新异常: ${err.message}`);
      }
    }
  }

  /**
   * 调用海外服务端刷新接口
   */
  async refreshToken(platform: string, teamId: string, refresh_token: string): Promise<any> {
    console.log(this.OVERSEAS_REFRESH_URL)
    try {
      const res = await axios.get(this.OVERSEAS_REFRESH_URL, {
        params: {
          teamId: teamId,
          userId: '123',
          platform,
          refresh_token,
          token: this.OVERSEAS_REFRESH_TOKEN,
        },
        timeout: 10000,
      });
      if (res.data && res.data.code === 0) {
        return res.data.data;
      }
      this.logger.warn(`刷新接口返回异常: ${JSON.stringify(res.data)}`);
      return null;
    } catch (err) {
      this.logger.error(`调用海外刷新接口异常: ${err.message}`);
      return null;
    }
  }
} 