import { Inject, Injectable, Logger } from '@nestjs/common'
import {
  AccountDetailsOutputDto,
  FindAllAccountsInputDto,
  FindAllAccountsOutputDto
} from './webapp-accounts-query.dto'
import { FilterQuery, Model, PipelineStage, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { InjectModel } from '@nestjs/mongoose'
import { PlatformAccountEntity } from '@yxr/mongo'

// 各个表需要查询出来的字段类型定义(不需要 fields 参数控制的字段)
type PlatformAccountProjectionFields = Pick<
  PlatformAccountEntity,
  'platformName' | 'platformAccountName' | 'remark' | 'platformAvatar' | 'status' | 'createdAt' | 'updatedAt'
> & {
  _id: Types.ObjectId
}

// Projection 阶段的类型约束
type PlatformAccountProjection = { [K in keyof Required<PlatformAccountProjectionFields>]: 1 }

// Aggregate 查询结果类型定义
type AggregateItemResult = PlatformAccountProjectionFields & {}

type AggregateListResult = {
  totalSize?: number
  items: Array<AggregateItemResult>
}

@Injectable()
export class WebappAccountsQueryService {
  logger = new Logger(WebappAccountsQueryService.name)

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(PlatformAccountEntity.name) private platformAccountModel: Model<PlatformAccountEntity>
  ) {
  }

  async findAll(input: FindAllAccountsInputDto): Promise<FindAllAccountsOutputDto> {
    this.logger.debug(`findAll input: ${JSON.stringify(input)}`)

    const { userId, teamId } = this.request.session

    const pipeline: PipelineStage[] = [
      {
        // 1. 基础过滤: 应用在账号主表的筛选条件
        $match: {
          teamId: new Types.ObjectId(teamId),
          deleted: false,
          isFreeze: false,
          ...(input.scope === 'operational' && { members: { $in: [userId] } } as FilterQuery<PlatformAccountEntity>),  // scope: 我运营的
          ...(input.platformName && { platformName: input.platformName } as FilterQuery<PlatformAccountEntity>),  // 平台过滤
          ...(input.keyword && {
            $or: [
              { platformAccountName: { $regex: input.keyword, $options: 'i' } } as FilterQuery<PlatformAccountEntity>,
              { remark: { $regex: input.keyword, $options: 'i' } } as FilterQuery<PlatformAccountEntity>
            ]
          }) // 根据查询条件中的关键字模糊匹配
        } as FilterQuery<PlatformAccountEntity>
      },

      ...this.makeFieldStages(input.fields),

      // 计算总数和排序、分页
      {
        $facet: {
          counts: [{ $count: 'totalSize' }], // 计算总数
          items: [
            { $sort: { createdAt: -1 } }, // 根据条件中的排序参数进行排序, 这里是默认的排序

            // 分页, 如果 featchAll=true 则不需要
            ...(input.fetchAll
              ? []
              : [{ $skip: (input.page - 1) * input.size }, { $limit: input.size }])
          ]
        }
      },
      {
        $project: {
          totalSize: { $arrayElemAt: ['$counts.totalSize', 0] }, // 将 counts 数组中的第一个元素的 totalSize 字段提取出来
          items: 1
        }
      }
    ]

    // 如果有必要可以打印出 pipeline 看看
    // this.logger.debug(`findAll pipeline: ${JSON.stringify(pipeline, null, 2)}`)

    const [{ totalSize, items }] = await this.platformAccountModel.aggregate<AggregateListResult>(pipeline)

    // 如果有必要可以打印出 结果 看看结构是否和定义的类型一致
    // this.logger.debug(`findAll result: ${JSON.stringify({ totalSize, items }, null, 2)}`)

    return {
      totalSize: totalSize ?? 0,

      // 切忌将查询结果直接返回给前端, 需要进行转换, 因为保不齐什么时候数据库中会增加/删除/修改字段, 容易引发未知的问题
      items: await Promise.all(items?.map((item) => this.mapToAccountDto(item)))
    }
  }

  private makeFieldStages(fields: string[]): PipelineStage[] {
    return [
      {
        $project: {
          _id: 1,
          platformName: 1,
          platformAccountName: 1,
          remark: 1,
          platformAvatar: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1
          // fields 参数中指明的字段
          // ...includeField(fields, 'creator', { creatorId: 1 }),
          // ...includeField(fields, 'accounts'),
          // ...includeField(fields, 'tasks')
        } as PlatformAccountProjection
      }
    ]
  }

  private async mapToAccountDto(item: AggregateItemResult): Promise<AccountDetailsOutputDto> {
    return {
      id: item._id.toString(),
      status: item.status,
      createdAt: item.createdAt?.getTime(),
      updatedAt: item.updatedAt?.getTime(),
      platformAccountName: item.platformAccountName,
      platformName: item.platformName,
      remark: item.remark,
      platformAvatar: item.platformAvatar,
    } as AccountDetailsOutputDto
  }
}
