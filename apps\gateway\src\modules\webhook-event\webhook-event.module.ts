import { Module } from '@nestjs/common'
import { HttpModule } from '@nestjs/axios'
import { EventEmitterModule } from '@nestjs/event-emitter'
import {
  TeamMongoose,
  OpenPlatformApplicationMongoose,
  PlatformAccountMongoose,
  TaskSetMongoose,
  TaskMongoose  
} from '@yxr/mongo'
import { WebhookEventService } from './webhook-event.service'
import { WebhookEventEmitterService } from './webhook-event-emitter.service'

@Module({
  imports: [
    HttpModule,
    EventEmitterModule.forRoot(),
    TeamMongoose,
    OpenPlatformApplicationMongoose,
    PlatformAccountMongoose,
    TaskSetMongoose,
    TaskMongoose
  ],
  providers: [WebhookEventService, WebhookEventEmitterService],
  exports: [WebhookEventService, WebhookEventEmitterService]
})
export class WebhookEventModule {}
