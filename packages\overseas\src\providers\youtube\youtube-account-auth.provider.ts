import { Injectable, Logger, Scope } from '@nestjs/common'
import { AccountAuthProvider } from '../account-auth.provider'
import { createGoogleOAuth2Client } from './utils'
import { OverseasContext, AuthorizationAccessToken, PlatformAccountInfo } from '../types'
import { Auth, google } from 'googleapis'
import { nanoid } from 'nanoid'

@Injectable({ scope: Scope.TRANSIENT })
export class YoutubeAccountAuthProvider implements AccountAuthProvider {

  logger = new Logger(YoutubeAccountAuthProvider.name)

  private readonly scopes = [
    'openid',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
    'https://www.googleapis.com/auth/youtube',
    'https://www.googleapis.com/auth/youtube.download',
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/youtube.upload',
    'https://www.googleapis.com/auth/youtube.force-ssl' // 管理评论
  ]

  async generateAuthorizationUrl(state: string): Promise<string> {
    // Access scopes for two non-Sign-In scopes: Read-only Drive activity and Google Calendar.

    const oauth2Client = createGoogleOAuth2Client()

    // Generate a url that asks permissions for the Drive activity and Google Calendar scope
    const url = oauth2Client.generateAuthUrl({
      // 'online' (default) or 'offline' (gets refresh_token)
      access_type: 'offline',
      /** Pass in the scopes array defined above.
       * Alternatively, if only one scope is needed, you can pass a scope URL as a string */
      scope: this.scopes,
      // Enable incremental authorization. Recommended as a best practice.
      include_granted_scopes: true,
      prompt: 'consent',
      // Include the state parameter to reduce the risk of CSRF attacks.
      state
    })

    return url
  }

  async exchangeAuthCodeForAccounts(context: OverseasContext, code: string, state: string): Promise<PlatformAccountInfo[]> {
    const oauth2Client = createGoogleOAuth2Client()

    const { tokens: credentials } = await oauth2Client.getToken(code)
    // this.logger.debug(`credentials: ${JSON.stringify(credentials, null, 2)}`)

    // 刷新令牌过期策略: https://developers.google.com/identity/protocols/oauth2?hl=zh-cn#expiration

    const youtube = google.youtube({
      version: 'v3',
      auth: createGoogleOAuth2Client(credentials)
    })

    const response = await youtube.channels.list({
      part: ['snippet', 'id'],
      mine: true
    })

    this.logger.debug(`[youtube.channels.list] response.status: ${JSON.stringify(response.status, null, 2)}`)
    this.logger.debug(`[youtube.channels.list] response.data: ${JSON.stringify(response.data, null, 2)}`)

    const { items } = response.data
    if (items && items.length > 0) {
      const { snippet, id } = items[0]
      if (snippet && id) {
        if (!snippet.title) {
          this.logger.warn('获取youtube的频道时, 无法获取频道名称, 将会导致名称随机生成')
        }
        return [
          {
            openId: id,  // YouTube 用于唯一标识频道的 ID。
            credentials: credentials,  // youtube 的授权凭证保持和平台一致即可
            // default 是 88x88, medium 是 240x240, high 是 800x800
            avatar: snippet.thumbnails?.default?.url ?? snippet.thumbnails?.medium?.url ?? snippet.thumbnails?.high?.url,
            name: snippet.title ?? snippet.customUrl ?? `Youtube-${nanoid()}`,
            username: snippet.customUrl // 通过添加字母和数字来选择你的唯一标识名。 你可以在 14 天内将标识名改回去。 每 14 天可以更改标识名两次, 也是频道主页地址
          } as PlatformAccountInfo

          // {
          //   openId: id, // YouTube 用于唯一标识频道的 ID。
          //   // default 是 88x88, medium 是 240x240, high 是 800x800
          //   avatar: snippet.thumbnails?.default?.url ?? snippet.thumbnails?.medium?.url ?? snippet.thumbnails?.high?.url,
          //   name: snippet.title ?? snippet.customUrl ?? `Youtube-${nanoid()}`,
          //   username: snippet.customUrl // 通过添加字母和数字来选择你的唯一标识名。 你可以在 14 天内将标识名改回去。 每 14 天可以更改标识名两次, 也是频道主页地址
          // }
        ]
      }

      throw new Error('未获取到 Youtube 用户信息')
    } else {
      // 用户可能未开通该 Youtube
      this.logger.warn(`用户未开通 Youtube 频道。`)
      throw new Error('用户未开通 Youtube 频道, 请在 Youtube 开通频道后重新授权。')
    }
  }
}
