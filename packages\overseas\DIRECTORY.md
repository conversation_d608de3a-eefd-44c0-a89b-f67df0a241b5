# 海外平台连接器项目目录结构

## 📁 项目结构

```
packages/overseas/
├── README.md                        # 📖 主文档 - 海外平台连接器完整指南
├── DIRECTORY.md                     # 📋 本文件 - 项目目录结构说明
├── PROXY_CONFIG_GUIDE.md            # 🌐 代理配置指南 - 详细的代理配置和故障排除
├── ERROR_HANDLING_GUIDE.md          # 🛡️ 异常处理指南 - 详细的异常处理机制说明
├── src/
│   ├── utils/                       # 🔧 核心工具
│   │   ├── axios-config.ts          # 统一axios配置和拦截器
│   │   └── error-handler.ts         # 异常处理核心逻辑
│   ├── providers/                   # 🌐 平台连接器
│   │   ├── types.ts                 # 通用类型定义
│   │   ├── utils.ts                 # 通用工具函数
│   │   ├── account-auth.provider.ts # 账号授权提供者基类
│   │   ├── content-publish.provider.ts # 内容发布提供者基类
│   │   ├── error-handling-example.ts # 异常处理示例
│   │   ├── tiktok/                  # 🎵 TikTok平台连接器
│   │   │   ├── tiktok-api.ts        # TikTok API调用封装
│   │   │   ├── tiktok-error-handler.ts # TikTok错误处理器
│   │   │   ├── tiktok-api-types.ts  # TikTok API类型定义
│   │   │   ├── tiktok-account-auth.provider.ts # TikTok账号授权
│   │   │   ├── tiktok-content-publish.provider.ts # TikTok内容发布
│   │   │   └── providers.ts         # TikTok提供者导出
│   │   ├── facebook/                # 📘 Facebook平台连接器
│   │   ├── instagram/               # 📷 Instagram平台连接器
│   │   ├── twitter/                 # 🐦 Twitter平台连接器
│   │   └── youtube/                 # 📺 YouTube平台连接器
│   ├── models/                      # 📊 数据模型
│   ├── constants.ts                 # 常量定义
│   ├── index.ts                     # 主入口文件
│   ├── overseas-provider.factory.ts # 平台连接器工厂
│   └── overseas.module.ts           # NestJS模块
├── tests/                           # 🧪 测试文件
│   ├── utils/                       # 🔧 核心工具测试
│   │   ├── error-handler.spec.ts    # 异常处理测试
│   │   └── axios-config.spec.ts     # axios配置测试
│   └── providers/                   # 🌐 平台连接器测试
│       ├── utils.spec.ts            # 工具函数测试
│       └── tiktok/                  # 🎵 TikTok平台测试
│           ├── tiktok-api.spec.ts   # TikTok API测试
│           └── tiktok-error-handler.spec.ts # TikTok错误处理器测试
└── package.json                     # 包配置
```

## 📖 文档导航

### 主要文档
- **[README.md](./README.md)** - 📖 **主文档**，海外平台连接器完整指南
- **[DIRECTORY.md](./DIRECTORY.md)** - 📋 本文件，项目目录结构说明
- **[PROXY_CONFIG_GUIDE.md](./PROXY_CONFIG_GUIDE.md)** - 🌐 **代理配置指南**，详细的代理配置和故障排除
- **[ERROR_HANDLING_GUIDE.md](./ERROR_HANDLING_GUIDE.md)** - 🛡️ **异常处理指南**，详细的异常处理机制说明

### 技术文档
- **[providers/error-handling-example.ts](./src/providers/error-handling-example.ts)** - 💡 异常处理使用示例

### 测试文件
- **[tests/utils/error-handler.spec.ts](./tests/utils/error-handler.spec.ts)** - 🧪 异常处理测试
- **[tests/utils/axios-config.spec.ts](./tests/utils/axios-config.spec.ts)** - 🧪 axios配置测试
- **[tests/providers/utils.spec.ts](./tests/providers/utils.spec.ts)** - 🧪 工具函数测试
- **[tests/providers/tiktok/tiktok-api.spec.ts](./tests/providers/tiktok/tiktok-api.spec.ts)** - 🧪 TikTok API测试
- **[tests/providers/tiktok/tiktok-error-handler.spec.ts](./tests/providers/tiktok/tiktok-error-handler.spec.ts)** - 🧪 TikTok错误处理器测试

## 🚀 快速开始

1. **阅读主文档**: 从 [README.md](./README.md) 开始了解项目概述和快速开始指南
2. **查看示例**: 参考 [error-handling-example.ts](./src/providers/error-handling-example.ts) 了解具体用法
3. **深入技术**: 阅读 [ERROR_HANDLING_README.md](./src/utils/ERROR_HANDLING_README.md) 了解技术细节

## 🎯 核心文件说明

### 连接器核心架构
- **`utils/axios-config.ts`** - 统一的axios配置，包含拦截器、代理配置、重试机制
- **`utils/error-handler.ts`** - 核心异常处理逻辑，RemoteApiError类定义
- **`providers/utils.ts`** - 通用工具函数，包含上下文创建、API调用包装等
- **`overseas-provider.factory.ts`** - 平台连接器工厂，负责创建不同平台的提供者实例

### TikTok平台连接器
- **`providers/tiktok/tiktok-api.ts`** - TikTok API调用封装，统一接口抹平平台差异
- **`providers/tiktok/tiktok-error-handler.ts`** - TikTok特定的业务错误检查器
- **`providers/tiktok/tiktok-types.ts`** - TikTok API的TypeScript类型定义

### 业务提供者实现
- **`providers/tiktok/tiktok-content-publish.provider.ts`** - TikTok内容发布提供者
- **`providers/tiktok/tiktok-account-auth.provider.ts`** - TikTok账号授权提供者
- **`providers/account-auth.provider.ts`** - 账号授权提供者基类
- **`providers/content-publish.provider.ts`** - 内容发布提供者基类

## 🔧 开发指南

### 添加新平台连接器
1. 在 `providers/` 下创建新平台目录
2. 实现核心组件：
   - `*-api.ts` - API调用封装
   - `*-api-types.ts` - 类型定义
   - `*-error-handler.ts` - 错误处理器
   - `*-account-auth.provider.ts` - 账号授权提供者
   - `*-content-publish.provider.ts` - 内容发布提供者
3. 在工厂中注册新平台
4. 参考TikTok实现进行开发

### 修改异常处理逻辑
1. 核心逻辑在 `utils/error-handler.ts`
2. 平台特定逻辑在各平台的 `*-error-handler.ts`
3. 拦截器配置在 `utils/axios-config.ts`

### 扩展业务功能
1. 账号授权：继承 `AccountAuthProvider` 基类
2. 内容发布：继承 `ContentPublishProvider` 基类
3. Webhook处理：实现 `WebhookParser` 接口

### 运行测试
```bash
# 运行所有测试
pnpm test

# 运行特定模块测试
pnpm test tests/utils/                    # 核心工具测试
pnpm test tests/providers/               # 平台连接器测试

# 运行特定文件测试
pnpm test tests/utils/error-handler.spec.ts        # 异常处理测试
pnpm test tests/providers/tiktok/tiktok-api.spec.ts # TikTok API测试

# 运行特定平台的所有测试
pnpm test tests/providers/tiktok/        # TikTok平台所有测试
```

## 📞 获取帮助

- **使用问题**: 查看 [README.md](./README.md) 的故障排除部分
- **技术细节**: 参考 [ERROR_HANDLING_README.md](./src/utils/ERROR_HANDLING_README.md)
- **代码示例**: 查看 [error-handling-example.ts](./src/providers/error-handling-example.ts)
- **开发支持**: 联系海外平台连接器开发团队

---

**提示**: 建议从主文档 [README.md](./README.md) 开始阅读，它包含了海外平台连接器的完整介绍和使用指南。
