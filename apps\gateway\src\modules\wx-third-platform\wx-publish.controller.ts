import {
  BadRequestException,
  Body,
  Controller,
  ForbiddenException,
  Get,
  Inject,
  Param,
  Post,
  Query,
  Req
} from '@nestjs/common'
import { FastifyRequest } from 'fastify'
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConsumes,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { WxPublishService } from './wx-publish.service'
import {
  WxMaterialRequest,
  WxMaterialResponseDTO,
  WxMediaType,
  WxPublishCreateRequest,
  WxPublishPreviewRequest,
  WxUploadimgResponseDTO
} from './wx-publish.dto'
import { REQUEST } from '@nestjs/core'

@Controller('wx/publish')
@ApiTags('媒体账号管理/微信第三方平台管理/发布管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class WxPublishController {
  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly wxPublishService: WxPublishService
  ) {}

  @Post()
  @ApiOperation({ summary: '公众号发布' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postWxDraft(@Body() body: WxPublishCreateRequest) {
    const { teamId: currentTeamId, userId: currentUserId } = this.request.session
    return await this.wxPublishService.postWxDraft(currentTeamId, currentUserId, body)
  }

  @Post(':platformAccountId/preview')
  @ApiOperation({ summary: '公众号发布预览' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async getPublishPreview(
    @Param('platformAccountId') platformAccountId: string,
    @Body() body: WxPublishPreviewRequest
  ) {
    const { teamId: currentTeamId } = this.request.session
    return await this.wxPublishService.publishPreview(currentTeamId, platformAccountId, body)
  }

  @Get(':taskId/status')
  @ApiOperation({ summary: '公众号发布状态' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async getPublishStatus(@Param('taskId') taskId: string) {
    return await this.wxPublishService.getPublishStatus(taskId)
  }

  @Post(':platformAccountId/uploadimg')
  @ApiOperation({ summary: '上传图文消息内的图片获取URL' })
  @ApiOkResponse({ type: WxUploadimgResponseDTO })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  async postUploadimg(
    @Param('platformAccountId') platformAccountId: string,
    @Req() req: FastifyRequest
  ) {
    const { teamId: currentTeamId } = this.request.session
    const data = await req.file()
    const fileBuffer = await data.toBuffer()
    const reg = /\.(jpeg|png)$/i
    if (!data.filename || !reg.test(data.filename)) {
      throw new BadRequestException('图片仅支持jpg/png格式')
    }

    if (fileBuffer.byteLength > 1024 * 1024 * 1) {
      throw new BadRequestException('图片大小不能超过1MB')
    }

    return await this.wxPublishService.postUploadimg(
      currentTeamId,
      platformAccountId,
      fileBuffer,
      data.filename,
      fileBuffer.byteLength,
      data.mimetype
    )
  }

  @Post(':platformAccountId/add_material')
  @ApiOperation({ summary: '新增其他类型永久素材' })
  @ApiOkResponse({ type: WxMaterialResponseDTO })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        image: {
          type: 'string',
          format: 'binary'
        }
      }
    }
  })
  async postAddmaterial(
    @Param('platformAccountId') platformAccountId: string,
    @Query() body: WxMaterialRequest,
    @Req() req: FastifyRequest
  ) {
    const { teamId: currentTeamId } = this.request.session
    const data = await req.file()
    const fileBuffer = await data.toBuffer()
    switch (body.type) {
      case WxMediaType.image:
        const imageReg = /\.(bmp|png|jpeg|jpg|gif)$/i
        if (!data.filename || !imageReg.test(data.filename)) {
          throw new BadRequestException('图片仅支持bmp/png/jpeg/jpg/gif格式')
        }
        if (fileBuffer.byteLength > 1024 * 1024 * 10) {
          throw new BadRequestException('图片大小不能超过10MB')
        }
        break
      case WxMediaType.voice:
        const voiceReg = /\.(mp3|wma|wav|amr)$/i
        if (!data.filename || !voiceReg.test(data.filename)) {
          throw new BadRequestException('语音仅支持bmp/png/jpeg/jpg/gif格式')
        }
        if (fileBuffer.byteLength > 1024 * 1024 * 2) {
          throw new BadRequestException('语音大小不能超过2MB')
        }
        break
      case WxMediaType.video:
        const videoReg = /\.(mp4)$/i
        if (!data.filename || !videoReg.test(data.filename)) {
          throw new BadRequestException('视频仅支持mp4格式')
        }
        if (fileBuffer.byteLength > 1024 * 1024 * 10) {
          throw new BadRequestException('视频大小不能超过10MB')
        }
        break
      case WxMediaType.thumb:
        const thumbReg = /\.(jpg)$/i
        if (!data.filename || !thumbReg.test(data.filename)) {
          throw new BadRequestException('缩略图仅支持jpg格式')
        }
        if (fileBuffer.byteLength > 1024 * 64) {
          throw new BadRequestException('缩略图大小不能超过64KB')
        }
        break
      default:
        throw new ForbiddenException('媒体文件类型错误')
        break
    }

    return await this.wxPublishService.postAddmaterial(
      currentTeamId,
      platformAccountId,
      fileBuffer,
      data.filename,
      data.mimetype,
      body
    )
  }
}
