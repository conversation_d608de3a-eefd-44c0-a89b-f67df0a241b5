import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsMobilePhone, IsOptional, Length, IsBoolean } from 'class-validator'
import { Transform } from 'class-transformer'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

export class SendCodeRequestDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000'
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string

  @ApiProperty({
    description: '蚁小二验证码验证参数',
    example: 'captcha_verify_param_string',
    required: true
  })
  @IsNotEmpty({ message: '验证码验证参数不能为空' })
  @IsString()
  captchaVerifyParam: string
}

export class RegisterRequestDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000'
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string

  @ApiProperty({
    description: '验证码',
    example: '123456'
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string

  @ApiProperty({
    description: '密码（可选）',
    example: 'password123',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  password?: string

  @ApiProperty({
    description: '昵称（可选）',
    example: '张三',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: '昵称长度必须在1-50位之间' })
  nickname?: string
}

export class LoginRequestDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000'
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string

  @ApiProperty({
    description: '验证码（验证码登录时必填）',
    example: '123456',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code?: string

  @ApiProperty({
    description: '密码（密码登录时必填）',
    example: 'password123',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  password?: string
}

/**
 * 统一认证请求DTO（合并登录/注册）
 */
export class UnifiedAuthRequestDto {
  @ApiProperty({
    description: '手机号',
    example: '13800138000'
  })
  @IsNotEmpty()
  @IsMobilePhone('zh-CN', {}, { message: '手机号格式不正确' })
  phone: string

  @ApiProperty({
    description: '验证码',
    example: '123456'
  })
  @IsNotEmpty()
  @IsString()
  @Length(6, 6, { message: '验证码必须是6位数字' })
  code: string

  @ApiProperty({
    description: '密码（可选，用于设置账号密码）',
    example: 'password123',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(6, 20, { message: '密码长度必须在6-20位之间' })
  password?: string

  @ApiProperty({
    description: '昵称（可选，新用户注册时使用）',
    example: '张三',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(1, 50, { message: '昵称长度必须在1-50位之间' })
  nickname?: string
}

export class UserInfoDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '13800138000'
  })
  phone: string

  @ApiResponseProperty({
    example: '张三'
  })
  nickname: string

  @ApiResponseProperty({
    example: 0
  })
  status: number
}

export class LoginResponseDto {
  @ApiResponseProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  authorization: string

  @ApiResponseProperty({
    type: UserInfoDto
  })
  userInfo: UserInfoDto
}

export class SendCodeResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    example: '123456',
  })
  data?: string
}

export class RegisterResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: LoginResponseDto
  })
  data: LoginResponseDto
}

export class LoginOkResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: LoginResponseDto
  })
  data: LoginResponseDto
}

/**
 * 统一认证响应DTO
 */
export class UnifiedAuthResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: LoginResponseDto
  })
  data: LoginResponseDto
}

/**
 * 退出登录请求DTO
 */
export class LogoutRequestDto {
  @ApiProperty({
    description: '是否退出所有设备',
    example: false,
    required: false
  })
  @IsOptional()
  @IsBoolean({ message: '退出所有设备必须是布尔值' })
  @Transform(({ value }) => {
    if (value === 'true') return true
    if (value === 'false') return false
    return value
  })
  logoutAllDevices?: boolean = false
}

/**
 * 退出登录响应DTO
 */
export class LogoutResponseDto {
  @ApiResponseProperty({
    example: true
  })
  success: boolean

  @ApiResponseProperty({
    example: '退出登录成功'
  })
  message: string

  @ApiResponseProperty({
    example: 1
  })
  clearedTokensCount: number
}

/**
 * 退出登录成功响应DTO
 */
export class LogoutOkResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: LogoutResponseDto
  })
  data: LogoutResponseDto
}
