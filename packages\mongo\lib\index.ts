import { MongooseModule } from '@nestjs/mongoose'
import { MongoService } from './mongo.service'
import { MongoModule as _MongoModule } from './mongo.module'

// 这里列出所有需要导出的 Schema
export { UserMongoose, UserEntity } from './schemas/user.schema'
export { TeamMongoose, TeamEntity } from './schemas/team.schema'
export { ProposalMongoose, ProposalEntity } from './schemas/proposal.schema'
export { InvitationMongoose, InvitationEntity } from './schemas/invitation.schema'
export { MemberMongoose, MemberEntity } from './schemas/member.schema'
export { AdminEntity, AdminMongoose } from './schemas/admin.schema'
export {
  PlatformAccountMongoose,
  PlatformAccountEntity,
  LoginStatus
} from './schemas/platform_accounts.schema'
export {
  PlatformAccountOverviewMongoose,
  PlatformAccountOverviewEntity
} from './schemas/platform_accounts_overview.schema'
export { NoticeMongoose, NoticeEntity } from './schemas/notice.schema'
export { TaskMongoose, TaskEntity } from './schemas/task.schema'
export { ContentMongoose, ContentEntity, MediaType } from './schemas/content.schema'
export { GroupMongoose, GroupEntity } from './schemas/group.schema'
export { BrowserMongoose, BrowserEntity } from './schemas/browser.schema'
export { OnlineScriptMongoose, OnlineScriptEntity } from './schemas/online-script.schema'
export {
  BrowserFavoritesMongoose,
  BrowserFavoritesEntity
} from './schemas/browser_favorites.schema'
export { BrowserGroupMongoose, BrowserGroupEntity } from './schemas/browser_group.schema'
export { MaterialLibraryMongoose, MaterialLibraryEntity } from './schemas/material_library.schema'
export {
  MaterialLibraryGroupMongoose,
  MaterialLibraryGroupEntity
} from './schemas/material_library_group.schema'
export {
  CommonTopicsGroupMongoose,
  CommonTopicsGroupEntity
} from './schemas/common_topics_group.schema'
export { CommonTopicsMongoose, CommonTopicsEntity } from './schemas/common_topics.schema'
export {
  CommonCategorysGroupMongoose,
  CommonCategorysGroupEntity
} from './schemas/common_categorys_group.schema'
export { CommonCategorysMongoose, CommonCategorysEntity } from './schemas/common_categorys.schema'
export { FavoritesGroupMongoose, FavoritesGroupEntity } from './schemas/favorites_group.schema'
export {
  FavoritesGroupItemMongoose,
  FavoritesGroupItemEntity
} from './schemas/favorites_group_item.schema'
export { DataStatisticsMongoose, DataStatisticEntity } from './schemas/data_statistic.schema'
export {
  BrowserPublishStatisticMongoose,
  BrowserPublishStatisticEntity
} from './schemas/browser_publish_statistic.schema'
export { TaskSetMongoose, TaskSetEntity } from './schemas/taskset.schema'
export { ContractMongoose, ContractEntity } from './schemas/contract.schema'
export { OrderMongoose, OrderEntity } from './schemas/order.schema'
export { InterestMongoose, InterestEntity } from './schemas/interest.schema'
export { RefundMongoose, RefundEntity } from './schemas/refund.schema'
export {
  ContentStatisticMongoose,
  ContentStatisticEntity
} from './schemas/content_statistic.schema'
export { QuickEntranceMongoose, QuickEntranceEntity } from './schemas/quick_entrance.schema'
export { AdMongoose, AdEntity } from './schemas/ad.schema'
export {
  CustomerAssignRecordMongoose,
  CustomerAssignRecordEntity
} from './schemas/customer_assign_record.schema'
export { FollowRecordMongoose, FollowRecordEntity } from './schemas/follow_record.schema'
export { ChannelMongoose, ChannelEntity } from './schemas/channel.schema'
export { TeamStatisticMongoose, TeamStatisticEntity } from './schemas/team_statistic.schema'
export { TeamExpiredLogMongoose, TeamExpiredLogEntity } from './schemas/team_expired_log.schema'
export { TeamComponentMongoose, TeamComponentEntity } from './schemas/team_component.schema'
export {
  PlatformAccountStatusLogMongoose,
  PlatformAccountStatusLogEntity
} from './schemas/platform_accounts_status_log.schema'
export {
  PlatformAccountSummaryMongoose,
  PlatformAccountSummaryEntity
} from './schemas/platform_accounts_summary.schema'
export { MessageMongoose, MessageEntity } from './schemas/message.schema'
export { TrafficBillingMongoose, TrafficBillingEntity } from './schemas/traffic_billing.schema'
export { ContentTrendMongoose, ContentTrendEntity } from './schemas/content_trend.schema'
export {
  PlatformAccountTrendMongoose,
  PlatformAccountTrendEntity
} from './schemas/platform_accounts_trend.schema'
export {
  PlatformDataStatisticMongoose,
  PlatformDataStatisticEntity
} from './schemas/platform_data_statistic.schema'
export { UserDevicesMongoose, UserDevicesEntity } from './schemas/user_devices.schema'
export { UserDeviceLogsMongoose, UserDeviceLogsEntity } from './schemas/user_devices_logs.schema'
export {
  HistoryStatisticMongoose,
  HistoryStatisticEntity
} from './schemas/history_statistic.schema'
export { RobotMongoose, RobotEntity } from './schemas/robot.schema'
export { PlatformProxyMongoose, PlatformProxyEntity } from './schemas/platform_proxy.schema'
export {
  PlatformAccountCookieMongoose,
  PlatformAccountCookieEntity
} from './schemas/platform_account_cookie.schema'
export {
  OpenPlatformUserMongoose,
  OpenPlatformUserEntity
} from './schemas/open_platform_user.schema'
export {
  OpenPlatformApplicationMongoose,
  OpenPlatformApplicationEntity
} from './schemas/open_platform_application.schema'
export {
  OpenPlatformUserRoleMongoose,
  OpenPlatformUserRoleEntity
} from './schemas/open_platform_user_role.schema'
export {
  OpenPlatformAppAuthorizationMongoose,
  OpenPlatformAppAuthorizationEntity
} from './schemas/open_platform_app_authorization.schema'
export {
  OpenPlatformInvitationMongoose,
  OpenPlatformInvitationEntity
} from './schemas/open_platform_invitation.schema'
export {
  OpenPlatformApplicationRechargeMongoose,
  OpenPlatformApplicationRechargeEntity
} from './schemas/open_platform_application_recharge.schema'
export {
  OpenPlatformApplicationBalanceMongoose,
  OpenPlatformApplicationBalanceEntity
} from './schemas/open_platform_application_balance.schema'
export {
  OpenPlatformChannelUserRelationMongoose,
  OpenPlatformChannelUserRelationEntity
} from './schemas/open_platform_channel_user_relation.schema'
export {
  TeamDailyStatsMongoose,
  TeamDailyStatsEntity
} from './schemas/team-daily-stats.schema'
export {
  OpenPlatformAppDailyStatsMongoose,
  OpenPlatformAppDailyStatsEntity
} from './schemas/open-platform-app-daily-stats.schema'

export const MongoModule = MongooseModule.forRootAsync({
  useFactory: (mongoService: MongoService) => mongoService.createOptions(),
  imports: [_MongoModule],
  inject: [MongoService]
})
