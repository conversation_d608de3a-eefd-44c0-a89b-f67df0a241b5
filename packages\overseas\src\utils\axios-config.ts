import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { HttpsProxyAgent } from 'https-proxy-agent'
import { HttpProxyAgent } from 'http-proxy-agent'
import {
  RemoteApiError,
  RemoteApiErrorCodes,
  DefaultOverseasApiErrorHandler,
  RetryConfig,
  DEFAULT_RETRY_CONFIG,
  extractRequestInfo,
  extractResponseInfo,
  ErrorSeverity
} from './error-handler'

export interface ProxyConfig {
  enabled: boolean
  url: string
  timeout: number
}

/**
 * 平台特定业务错误检查器接口
 */
export interface BusinessErrorChecker {
  /**
   * 检查响应是否包含业务错误
   * @param response Axios响应对象
   * @returns 如果包含业务错误返回true
   */
  hasBusinessError: (response: AxiosResponse) => boolean

  /**
   * 处理业务错误，将其转换为RemoteApiError
   * @param response Axios响应对象
   * @param context 平台上下文
   * @returns 永远不返回，总是抛出RemoteApiError
   */
  handleBusinessError: (response: AxiosResponse, context: OverseasContext) => Promise<never>
}

/**
 * 海外平台上下文接口
 */
export interface OverseasContext {
  platform: string
  accountOpenId?: string
  teamId?: string
  userId?: string
  options?: Record<string, any>
}

/**
 * 拦截器配置选项
 */
export interface InterceptorConfig {
  context: OverseasContext
  retryConfig?: Partial<RetryConfig>
  businessErrorChecker?: BusinessErrorChecker
  enableRetry?: boolean
  enableBusinessErrorCheck?: boolean
}

/**
 * 获取代理配置
 */
export function getProxyConfig(): ProxyConfig {
  // 通过环境变量控制是否启用代理
  const enabled = process.env.OVERSEAS_USE_PROXY === 'true'

  return {
    enabled,
    url: process.env.OVERSEAS_PROXY_URL || 'http://127.0.0.1:7897',
    timeout: parseInt(process.env.OVERSEAS_REQUEST_TIMEOUT || '30000', 10)
  }
}

/**
 * 睡眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 创建统一的响应拦截器
 */
function createResponseInterceptor(config: InterceptorConfig) {
  const { context, retryConfig = {}, businessErrorChecker, enableRetry = true, enableBusinessErrorCheck = true } = config
  const finalRetryConfig = { ...DEFAULT_RETRY_CONFIG, ...retryConfig }
  const errorHandler = new DefaultOverseasApiErrorHandler()

  return {
    onResponseSuccess: async (response: AxiosResponse): Promise<AxiosResponse> => {
      console.log(`[OverseasAxios] 响应: ${response.config.url}`, response.data)

      // 检查业务错误
      if (enableBusinessErrorCheck && businessErrorChecker?.hasBusinessError(response)) {
        await businessErrorChecker.handleBusinessError(response, context)
      }

      return response
    },

    onResponseError: async (error: AxiosError): Promise<never> => {
      const requestConfig = error.config
      if (!requestConfig) {
        throw error
      }

      // 获取重试次数
      const retryCount = (requestConfig as any).__retryCount || 0

      try {
        // 处理错误并转换为RemoteApiError
        if (error.response) {
          await errorHandler.handleHttpError(error, context)
        } else {
          await errorHandler.handleNetworkError(error, context)
        }
      } catch (remoteApiError) {
        // 检查是否需要重试
        if (enableRetry &&
            remoteApiError instanceof RemoteApiError &&
            remoteApiError.isRetryable &&
            retryCount < finalRetryConfig.maxRetries) {

          // 计算延迟时间（指数退避）
          const delay = Math.min(
            finalRetryConfig.baseDelay * Math.pow(finalRetryConfig.backoffMultiplier, retryCount),
            finalRetryConfig.maxDelay
          )

          console.warn(`[OverseasAxios] 第${retryCount + 1}次重试失败，${delay}ms后重试: ${remoteApiError.localizedMessage}`)

          // 等待延迟
          await sleep(delay)

          // 增加重试计数
          ;(requestConfig as any).__retryCount = retryCount + 1

          // 重新发起请求
          const axiosInstance = axios.create()
          return axiosInstance.request(requestConfig)
        }

        // 不重试或达到最大重试次数，抛出错误
        throw remoteApiError
      }

      // 这里永远不会执行到，因为errorHandler总是会抛出异常
      throw error
    }
  }
}

/**
 * 创建统一的海外平台axios实例
 * @param baseURL 基础URL
 * @param interceptorConfig 拦截器配置
 * @param options 额外的axios配置选项
 * @returns 配置好的axios实例
 */
export function createOverseasAxiosInstance(
  baseURL: string,
  interceptorConfig: InterceptorConfig,
  options: AxiosRequestConfig = {}
): AxiosInstance {
  const proxyConfig = getProxyConfig()

  // 基础配置
  const axiosConfig: AxiosRequestConfig = {
    baseURL,
    timeout: proxyConfig.timeout,
    headers: {
      'User-Agent': 'YiXiaoEr-Overseas-Service/1.0',
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }

  // 代理配置
  if (proxyConfig.enabled) {
    // 根据目标URL协议选择代理类型
    if (baseURL.startsWith('https://')) {
      axiosConfig.httpsAgent = new HttpsProxyAgent(proxyConfig.url)
    } else {
      axiosConfig.httpAgent = new HttpProxyAgent(proxyConfig.url)
    }

    console.log(`[OverseasAxios] 使用代理: ${proxyConfig.url} for ${baseURL}`)
  } else {
    console.log(`[OverseasAxios] 直连访问: ${baseURL}`)
  }

  // 创建axios实例
  const instance = axios.create(axiosConfig)

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      console.log(`[OverseasAxios] 请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
      return config
    },
    (error) => {
      console.error('[OverseasAxios] 请求错误:', error.message)
      return Promise.reject(error)
    }
  )

  // 创建响应拦截器
  const responseInterceptor = createResponseInterceptor(interceptorConfig)

  // 响应拦截器
  instance.interceptors.response.use(
    responseInterceptor.onResponseSuccess,
    responseInterceptor.onResponseError
  )

  return instance
}

/**
 * 创建简单的axios实例（向后兼容）
 * @deprecated 请使用 createOverseasAxiosInstance
 */
export function createAxiosInstance(
  baseURL: string,
  options: AxiosRequestConfig = {}
): AxiosInstance {
  const proxyConfig = getProxyConfig()

  // 基础配置
  const axiosConfig: AxiosRequestConfig = {
    baseURL,
    timeout: proxyConfig.timeout,
    headers: {
      'User-Agent': 'YiXiaoEr-Overseas-Service/1.0',
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }

  // 代理配置
  if (proxyConfig.enabled) {
    if (baseURL.startsWith('https://')) {
      axiosConfig.httpsAgent = new HttpsProxyAgent(proxyConfig.url)
    } else {
      axiosConfig.httpAgent = new HttpProxyAgent(proxyConfig.url)
    }
  }

  return axios.create(axiosConfig)
}
