import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, Length, IsEnum, IsUrl } from 'class-validator'
import { OpenPlatformStatus, ApplicationType, WebhookStatus } from '@yxr/common'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

export class CreateApplicationRequestDto {
  @ApiProperty({
    description: '应用名称',
    example: '我的应用'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 100, { message: '应用名称长度必须在1-100位之间' })
  name: string

  @ApiProperty({
    description: '应用描述',
    example: '这是一个测试应用',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '应用描述长度不能超过500位' })
  description?: string
}

export class UpdateApplicationRequestDto {
  @ApiProperty({
    description: '应用名称',
    example: '我的应用'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 100, { message: '应用名称长度必须在1-100位之间' })
  name: string

  @ApiProperty({
    description: '应用描述',
    example: '这是一个测试应用',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '应用描述长度不能超过500位' })
  description?: string

  @ApiProperty({
    description: '应用状态',
    example: 0
  })
  status: string
}

export class ApplicationDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '我的应用'
  })
  name: string

  @ApiResponseProperty({
    example: 'app_1234567890abcdef'
  })
  appId: string

  @ApiResponseProperty({
    example: 'sk_1234567890abcdef1234567890abcdef'
  })
  secretKey?: string

  @ApiResponseProperty({
    example: '这是一个测试应用'
  })
  description: string

  @ApiResponseProperty({
    example: 0
  })
  status: number

  @ApiResponseProperty({
    enum: ApplicationType,
    example: ApplicationType.TECHNICAL_SERVICE
  })
  applicationType: ApplicationType

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiResponseProperty({
    example: *************
  })
  createdAt: number

  @ApiResponseProperty({
    example: *************
  })
  updatedAt: number

  @ApiResponseProperty({
    example: 'admin'
  })
  userRole: string

  @ApiResponseProperty({
    example: true
  })
  canManage: boolean

  @ApiResponseProperty({
    example: 'owner'
  })
  userIdentity: 'owner' | 'invited'

  @ApiResponseProperty({
    example: '拥有者'
  })
  userIdentityLabel: string

  @ApiResponseProperty({
    example: 10000
  })
  availableBalance: number

  @ApiResponseProperty({
    example: 10000
  })
  totalAccountPoints: number

  @ApiResponseProperty({
    example: 5000
  })
  usedAccountPoints: number

  @ApiResponseProperty({
    example: 1024.5
  })
  totalTraffic: number

  @ApiResponseProperty({
    example: 512.3
  })
  usedTraffic: number
}

export class ApplicationListRequestDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  size?: number = 10

  @ApiProperty({
    description: '搜索关键词（应用名称）',
    example: '我的应用',
    required: false
  })
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiProperty({
    enum: OpenPlatformStatus,
    description: '应用状态筛选',
    example: 0,
    required: false
  })
  @IsEnum(OpenPlatformStatus)
  @IsOptional()
  status?: OpenPlatformStatus

  @ApiProperty({
    enum: ApplicationType,
    description: '应用类型筛选',
    example: ApplicationType.TECHNICAL_SERVICE,
    required: false
  })
  @IsEnum(ApplicationType)
  @IsOptional()
  applicationType?: ApplicationType
}

export class ApplicationListResponseDto {
  @ApiResponseProperty({
    type: [ApplicationDto]
  })
  data: ApplicationDto[]

  @ApiResponseProperty({
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  size: number
}

export class CreateApplicationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationDto
  })
  data: ApplicationDto
}

export class GetApplicationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationDto
  })
  data: ApplicationDto
}

export class GetApplicationListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationListResponseDto
  })
  data: ApplicationListResponseDto
}

export class RegenerateSecretResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Object
  })
  data: {
    secretKey: string
    message: string
  }
}

/**
 * 应用概览统计请求DTO
 */
export class ApplicationOverviewRequestDto {
  @ApiProperty({
    description: '应用ID',
    example: '507f1f77bcf86cd799439011'
  })
  @IsNotEmpty()
  @IsString()
  applicationId: string
}

/**
 * 日统计数据DTO
 */
export class DailyStatsDto {
  @ApiResponseProperty({
    example: '2023-12-01'
  })
  date: string

  @ApiResponseProperty({
    example: 10.5
  })
  newTraffic: number

  @ApiResponseProperty({
    example: 8.2
  })
  usedTraffic: number

  @ApiResponseProperty({
    example: 1000
  })
  newAccountPoints: number

  @ApiResponseProperty({
    example: 750
  })
  usedAccountPoints: number
}

/**
 * 应用概览统计响应DTO
 */
export class ApplicationOverviewDto {
  @ApiResponseProperty({
    example: 50000
  })
  availableBalance: number

  @ApiResponseProperty({
    example: 50000
  })
  totalAccountPoints: number

  @ApiResponseProperty({
    example: 35000
  })
  usedAccountPoints: number

  @ApiResponseProperty({
    example: 1024.5
  })
  totalTraffic: number

  @ApiResponseProperty({
    example: 512.3
  })
  usedTraffic: number

  @ApiResponseProperty({
    example: 150
  })
  totalUsers: number

  @ApiResponseProperty({
    example: 25
  })
  totalTeams: number

  @ApiResponseProperty({
    type: [DailyStatsDto]
  })
  dailyStats: DailyStatsDto[]
}

/**
 * 应用概览统计成功响应DTO
 */
export class ApplicationOverviewOkResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationOverviewDto
  })
  data: ApplicationOverviewDto
}

/**
 * 设置Webhook请求DTO
 */
export class SetWebhookRequestDto {
  @ApiProperty({
    description: 'Webhook URL地址',
    example: 'https://example.com/webhook'
  })
  @IsString()
  @IsNotEmpty()
  @IsUrl({}, { message: 'webhookUrl必须是有效的URL地址' })
  @Length(1, 500, { message: 'webhookUrl长度必须在1-500字符之间' })
  webhookUrl: string
}

/**
 * Webhook验证请求DTO
 */
export class WebhookVerificationRequestDto {
  @ApiProperty({
    description: '验证类型',
    example: 'webhook_verification'
  })
  type: string

  @ApiProperty({
    description: '时间戳',
    example: 1672531200000
  })
  timestamp: number

  @ApiProperty({
    description: '验证挑战值',
    example: 'abc123def456'
  })
  challenge: string
}

/**
 * Webhook验证响应DTO
 */
export class WebhookVerificationResponseDto {
  @ApiProperty({
    description: '验证是否成功',
    example: true
  })
  success: boolean

  @ApiProperty({
    description: '返回的挑战值',
    example: 'abc123def456'
  })
  challenge: string
}

/**
 * Webhook状态DTO
 */
export class WebhookStatusDto {
  @ApiResponseProperty({
    example: 'https://example.com/webhook'
  })
  webhookUrl?: string

  @ApiResponseProperty({
    enum: WebhookStatus,
    example: WebhookStatus.VERIFIED
  })
  webhookStatus: WebhookStatus

  @ApiResponseProperty({
    example: 1672531200000
  })
  webhookVerifiedAt?: number
}

/**
 * 设置Webhook响应DTO
 */
export class SetWebhookResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WebhookStatusDto
  })
  data: WebhookStatusDto
}

/**
 * 获取Webhook状态响应DTO
 */
export class GetWebhookStatusResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: WebhookStatusDto
  })
  data: WebhookStatusDto
}
