import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsString, IsNotEmpty, IsOptional, Length, IsEnum } from 'class-validator'
import { OpenPlatformStatus } from '@yxr/common'
import { BaseResponseDTO } from '../../../common/dto/BaseResponseDTO'

export class CreateApplicationRequestDto {
  @ApiProperty({
    description: '应用名称',
    example: '我的应用'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 100, { message: '应用名称长度必须在1-100位之间' })
  name: string

  @ApiProperty({
    description: '应用描述',
    example: '这是一个测试应用',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '应用描述长度不能超过500位' })
  description?: string
}

export class UpdateApplicationRequestDto {
  @ApiProperty({
    description: '应用名称',
    example: '我的应用'
  })
  @IsNotEmpty()
  @IsString()
  @Length(1, 100, { message: '应用名称长度必须在1-100位之间' })
  name: string

  @ApiProperty({
    description: '应用描述',
    example: '这是一个测试应用',
    required: false
  })
  @IsOptional()
  @IsString()
  @Length(0, 500, { message: '应用描述长度不能超过500位' })
  description?: string

  @ApiProperty({
    description: '应用状态',
    example: 0
  })
  status: string
}

export class ApplicationDto {
  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  id: string

  @ApiResponseProperty({
    example: '我的应用'
  })
  name: string

  @ApiResponseProperty({
    example: 'app_1234567890abcdef'
  })
  appId: string

  @ApiResponseProperty({
    example: 'sk_1234567890abcdef1234567890abcdef'
  })
  secretKey?: string

  @ApiResponseProperty({
    example: '这是一个测试应用'
  })
  description: string

  @ApiResponseProperty({
    example: 0
  })
  status: number

  @ApiResponseProperty({
    example: '507f1f77bcf86cd799439011'
  })
  userId: string

  @ApiResponseProperty({
    example: 1640995200000
  })
  createdAt: number

  @ApiResponseProperty({
    example: 1640995200000
  })
  updatedAt: number

  @ApiResponseProperty({
    example: 'admin'
  })
  userRole: string

  @ApiResponseProperty({
    example: true
  })
  canManage: boolean

  @ApiResponseProperty({
    example: 'owner'
  })
  userIdentity: 'owner' | 'invited'

  @ApiResponseProperty({
    example: '拥有者'
  })
  userIdentityLabel: string
}

export class ApplicationListRequestDto {
  @ApiProperty({
    description: '页码',
    example: 1,
    required: false,
    default: 1
  })
  @IsOptional()
  page?: number = 1

  @ApiProperty({
    description: '每页数量',
    example: 10,
    required: false,
    default: 10
  })
  @IsOptional()
  size?: number = 10

  @ApiProperty({
    description: '搜索关键词（应用名称）',
    example: '我的应用',
    required: false
  })
  @IsOptional()
  @IsString()
  keyword?: string

  @ApiProperty({
    enum: OpenPlatformStatus,
    description: '应用状态筛选',
    example: 0,
    required: false
  })
  @IsEnum(OpenPlatformStatus)
  @IsOptional()
  status?: OpenPlatformStatus
}

export class ApplicationListResponseDto {
  @ApiResponseProperty({
    type: [ApplicationDto]
  })
  data: ApplicationDto[]

  @ApiResponseProperty({
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    example: 100
  })
  totalPage: number

  @ApiResponseProperty({
    example: 1
  })
  page: number

  @ApiResponseProperty({
    example: 10
  })
  size: number
}

export class CreateApplicationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationDto
  })
  data: ApplicationDto
}

export class GetApplicationResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationDto
  })
  data: ApplicationDto
}

export class GetApplicationListResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ApplicationListResponseDto
  })
  data: ApplicationListResponseDto
}

export class RegenerateSecretResponseDto extends BaseResponseDTO {
  @ApiResponseProperty({
    type: Object
  })
  data: {
    secretKey: string
    message: string
  }
}
