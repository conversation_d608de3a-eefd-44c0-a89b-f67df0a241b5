import { ForbiddenException, Injectable, Logger } from '@nestjs/common'
import { KUAIDIALI_AREAS } from '@yxr/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { PlatformProxyEntity } from '@yxr/mongo'

@Injectable()
export class KuaidailiService {
  logger = new Logger(KuaidailiService.name)

  constructor(
    @InjectModel(PlatformProxyEntity.name)
    private platformProxyModel: Model<PlatformProxyEntity>
  ) {}

  /**
   * 获取可分配代理ip
   * @param platformName
   * @param areaCode
   * @returns
   */
  async getProxyAllocation(platformName: string, areaCode: string): Promise<string> {
    const count = await this.platformProxyModel.countDocuments({
      platformName: platformName,
      regionId: areaCode,
      $expr: {
        $lt: ['$currLogin', '$maxLogin']
      }
    })
    if (count <= 0) {
      throw new ForbiddenException('没有可分配的代理,请联系客服')
    }
    const randomSkip = Math.floor(Math.random() * count)
    const proxy = await this.platformProxyModel
      .findOne({
        platformName: platformName,
        regionId: areaCode,
        $expr: {
          $lt: ['$currLogin', '$maxLogin']
        }
      })
      .skip(randomSkip)
      .exec()

    if (!proxy) {
      throw new ForbiddenException('没有可分配的代理,请联系客服')
    }

    return proxy.proxyIp
  }

  /**
   * 更新代理ip使用次数
   * @param platformName
   * @param kuaidailiIp
   */
  async updateProxyAllocation(
    platformName: string,
    kuaidailiIp: string,
    operation: 'increment' | 'decrement' = 'increment'
  ) {
    const amount = operation === 'increment' ? 1 : -1

    // 构建查询条件，包含边界检查
    const query: any = {
      platformName,
      proxyIp: kuaidailiIp
    }
    // 根据操作类型添加边界检查
    if (operation === 'increment') {
      // 确保不会超过 maxLogin
      query.$expr = {
        $lt: ['$currLogin', '$maxLogin']
      }
    } else if (operation === 'decrement') {
      // 确保不会小于 0
      query.currLogin = { $gte: 1 }
    }

    await this.platformProxyModel.findOneAndUpdate(
      query,
      { $inc: { currLogin: amount } },
      { new: true }
    )
  }

  /**
   * 获取代理地区编码
   * @returns
   */
  async getAreas() {
    return KUAIDIALI_AREAS
  }
}
