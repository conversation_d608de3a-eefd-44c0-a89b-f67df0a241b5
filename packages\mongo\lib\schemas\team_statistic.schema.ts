import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false
})
export class TeamStatisticEntity {
  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  /**
   * 注册团队数
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  registerTeamCount: number

  /**
   * 付费团队数
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  paidTeamCount: number

  /**
   * 转化率
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  conversionRate: number

  /**
   * 过期团队数
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  expiredTeamCount: number

  /**
   * 续费团队数
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  renewTeamCount: number

  /**
   * 续费率
   */
  @Prop({
    type: Number,
    min: 0,
    max: Number.MAX_SAFE_INTEGER,
    default: 0
  })
  renewRate: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date

  @Prop({
    type: Number,
    default: 0
  })
  type: number

  /**
   * 分配客服ID
   */
  @Prop({
    type: Types.ObjectId,
    required: false
  })
  customerId?: Types.ObjectId
}

export const TeamStatisticSchema: ModelDefinition = {
  name: TeamStatisticEntity.name,
  schema: SchemaFactory.createForClass(TeamStatisticEntity)
}

export const TeamStatisticMongoose = MongooseModule.forFeature([TeamStatisticSchema])
