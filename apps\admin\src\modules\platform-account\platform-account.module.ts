import { Module } from '@nestjs/common'
import { PlatformAccountController } from './platform-account.controller'
import { PlatformAccountService } from './platform-account.service'
import {
  ContentStatisticMongoose,
  PlatformAccountMongoose,
  PlatformAccountOverviewMongoose,
  TeamMongoose
} from '@yxr/mongo'

@Module({
  imports: [
    PlatformAccountMongoose,
    PlatformAccountOverviewMongoose,
    ContentStatisticMongoose,
    TeamMongoose
  ],
  controllers: [PlatformAccountController],
  providers: [PlatformAccountService],
  exports: [PlatformAccountService]
})
export class PlatformAccountModule {}
