/**
 * TikTok错误处理器测试
 */

import { AxiosResponse } from 'axios'
import { 
  TikTokBusinessErrorChecker,
  createTikTokBusinessErrorChecker,
  TIKTOK_ERROR_CODE_MAPPING,
  getTikTokErrorDescription
} from '../../../src/providers/tiktok/tiktok-error-handler'
import { TikTokApiResponse } from '../../../src/providers/tiktok/tiktok-api-types'
import { 
  RemoteApiError, 
  RemoteApiErrorCodes,
  ErrorCategory,
  ErrorSeverity
} from '../../../src/utils/error-handler'
import { createOverseasContext } from '../../../src/providers/utils'

describe('TikTok错误处理器', () => {
  let errorChecker: TikTokBusinessErrorChecker
  const mockContext = createOverseasContext('tiktok', {
    accountOpenId: 'test-account',
    teamId: 'test-team',
    userId: 'test-user'
  })

  beforeEach(() => {
    errorChecker = new TikTokBusinessErrorChecker()
    // Mock console methods
    jest.spyOn(console, 'warn').mockImplementation()
    jest.spyOn(console, 'error').mockImplementation()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('TikTokBusinessErrorChecker', () => {
    describe('hasBusinessError', () => {
      it('应该正确检测TikTok业务错误', () => {
        const responseWithError = {
          data: {
            code: 40001,
            message: 'Scope not authorized',
            request_id: 'test-request-id'
          }
        } as AxiosResponse<TikTokApiResponse>

        const responseWithoutError = {
          data: {
            code: 0,
            message: 'Success',
            data: { result: 'ok' }
          }
        } as AxiosResponse<TikTokApiResponse>

        expect(errorChecker.hasBusinessError(responseWithError)).toBe(true)
        expect(errorChecker.hasBusinessError(responseWithoutError)).toBe(false)
      })

      it('应该处理缺失数据的响应', () => {
        const responseWithoutData = {} as AxiosResponse<TikTokApiResponse>
        const responseWithNullData = {
          data: null
        } as AxiosResponse<TikTokApiResponse>

        expect(errorChecker.hasBusinessError(responseWithoutData)).toBe(false)
        expect(errorChecker.hasBusinessError(responseWithNullData)).toBe(false)
      })

      it('应该处理未定义code的响应', () => {
        const responseWithoutCode = {
          data: {
            message: 'Some message'
          }
        } as AxiosResponse<TikTokApiResponse>

        expect(errorChecker.hasBusinessError(responseWithoutCode)).toBe(false)
      })
    })

    describe('handleBusinessError', () => {
      it('应该正确处理权限错误', async () => {
        const response = {
          data: {
            code: 40001,
            message: 'Scope not authorized',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        await expect(
          errorChecker.handleBusinessError(response, mockContext)
        ).rejects.toThrow(RemoteApiError)

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.ScopeNotAuthorized)
          expect((error as RemoteApiError).platform).toBe('tiktok')
          expect((error as RemoteApiError).details.tiktokErrorCode).toBe(40001)
          expect((error as RemoteApiError).details.logId).toBe('test-request-id')
          expect((error as RemoteApiError).category).toBe(ErrorCategory.Authorization)
          expect((error as RemoteApiError).severity).toBe(ErrorSeverity.High)
        }
      })

      it('应该正确处理访问令牌错误', async () => {
        const testCases = [
          { code: 40102, expectedError: RemoteApiErrorCodes.AccessTokenInvalid },
          { code: 40104, expectedError: RemoteApiErrorCodes.AccessTokenInvalid },
          { code: 40105, expectedError: RemoteApiErrorCodes.AccessTokenInvalid },
          { code: 40103, expectedError: RemoteApiErrorCodes.AccessTokenExpired }
        ]

        for (const testCase of testCases) {
          const response = {
            data: {
              code: testCase.code,
              message: `Error ${testCase.code}`,
              request_id: 'test-request-id'
            },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: { url: '/test' }
          } as AxiosResponse<TikTokApiResponse>

          try {
            await errorChecker.handleBusinessError(response, mockContext)
            fail(`Expected error for code ${testCase.code}`)
          } catch (error) {
            expect(error).toBeInstanceOf(RemoteApiError)
            expect((error as RemoteApiError).errorCode).toBe(testCase.expectedError)
            expect((error as RemoteApiError).details.tiktokErrorCode).toBe(testCase.code)
          }
        }
      })

      it('应该正确处理请求参数错误', async () => {
        const parameterErrorCodes = [40000, 40002, 40006, 40010, 40011, 40051, 40053]

        for (const code of parameterErrorCodes) {
          const response = {
            data: {
              code,
              message: `Parameter error ${code}`,
              request_id: 'test-request-id'
            },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: { url: '/test' }
          } as AxiosResponse<TikTokApiResponse>

          try {
            await errorChecker.handleBusinessError(response, mockContext)
            fail(`Expected error for code ${code}`)
          } catch (error) {
            expect(error).toBeInstanceOf(RemoteApiError)
            expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.RequestParametersIncorrect)
            expect((error as RemoteApiError).details.tiktokErrorCode).toBe(code)
            expect((error as RemoteApiError).category).toBe(ErrorCategory.Request)
            expect((error as RemoteApiError).isRetryable).toBe(false)
          }
        }
      })

      it('应该正确处理服务器错误', async () => {
        const serverErrorCodes = [50001, 50002]

        for (const code of serverErrorCodes) {
          const response = {
            data: {
              code,
              message: `Server error ${code}`,
              request_id: 'test-request-id'
            },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: { url: '/test' }
          } as AxiosResponse<TikTokApiResponse>

          try {
            await errorChecker.handleBusinessError(response, mockContext)
            fail(`Expected error for code ${code}`)
          } catch (error) {
            expect(error).toBeInstanceOf(RemoteApiError)
            expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.ServerError)
            expect((error as RemoteApiError).details.tiktokErrorCode).toBe(code)
            expect((error as RemoteApiError).category).toBe(ErrorCategory.Server)
            expect((error as RemoteApiError).isRetryable).toBe(true)
          }
        }
      })

      it('应该正确处理限流错误', async () => {
        const response = {
          data: {
            code: 60001,
            message: 'Rate limit exceeded',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.RateLimitExceeded)
          expect((error as RemoteApiError).category).toBe(ErrorCategory.RateLimit)
          expect((error as RemoteApiError).severity).toBe(ErrorSeverity.Medium)
          expect((error as RemoteApiError).isRetryable).toBe(true)
        }
      })

      it('应该正确处理配额超限错误', async () => {
        const response = {
          data: {
            code: 60002,
            message: 'Quota exceeded',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.QuotaExceeded)
          expect((error as RemoteApiError).category).toBe(ErrorCategory.RateLimit)
          expect((error as RemoteApiError).isRetryable).toBe(false)
        }
      })

      it('应该正确处理内容相关错误', async () => {
        const testCases = [
          { code: 70001, expectedError: RemoteApiErrorCodes.ContentViolation },
          { code: 70002, expectedError: RemoteApiErrorCodes.MediaUploadFailed }
        ]

        for (const testCase of testCases) {
          const response = {
            data: {
              code: testCase.code,
              message: `Content error ${testCase.code}`,
              request_id: 'test-request-id'
            },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: { url: '/test' }
          } as AxiosResponse<TikTokApiResponse>

          try {
            await errorChecker.handleBusinessError(response, mockContext)
            fail(`Expected error for code ${testCase.code}`)
          } catch (error) {
            expect(error).toBeInstanceOf(RemoteApiError)
            expect((error as RemoteApiError).errorCode).toBe(testCase.expectedError)
            expect((error as RemoteApiError).details.tiktokErrorCode).toBe(testCase.code)
          }
        }
      })

      it('应该正确处理账号相关错误', async () => {
        const response = {
          data: {
            code: 80001,
            message: 'Account suspended',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.AccountSuspended)
          expect((error as RemoteApiError).category).toBe(ErrorCategory.Account)
          expect((error as RemoteApiError).isRetryable).toBe(false)
        }
      })

      it('应该正确处理功能不可用错误', async () => {
        const response = {
          data: {
            code: 90001,
            message: 'Feature not available',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.FeatureNotAvailable)
          expect((error as RemoteApiError).category).toBe(ErrorCategory.Business)
          expect((error as RemoteApiError).isRetryable).toBe(false)
        }
      })

      it('应该正确处理未知错误码', async () => {
        const response = {
          data: {
            code: 99999,
            message: 'Unknown error',
            request_id: 'test-request-id'
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.UnknownRemoteApiError)
          expect((error as RemoteApiError).details.tiktokErrorCode).toBe(99999)
        }
      })

      it('应该在没有错误时抛出异常', async () => {
        const response = {
          data: {
            code: 0,
            message: 'Success',
            data: { result: 'ok' }
          },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { url: '/test' }
        } as AxiosResponse<TikTokApiResponse>

        try {
          await errorChecker.handleBusinessError(response, mockContext)
        } catch (error) {
          expect(error).toBeInstanceOf(RemoteApiError)
          expect((error as RemoteApiError).errorCode).toBe(RemoteApiErrorCodes.UnknownRemoteApiError)
          expect((error as RemoteApiError).details.message).toContain('意外调用了错误处理方法')
        }
      })
    })
  })

  describe('createTikTokBusinessErrorChecker', () => {
    it('应该创建TikTokBusinessErrorChecker实例', () => {
      const checker = createTikTokBusinessErrorChecker()
      expect(checker).toBeInstanceOf(TikTokBusinessErrorChecker)
    })
  })

  describe('TIKTOK_ERROR_CODE_MAPPING', () => {
    it('应该包含所有主要的错误码映射', () => {
      expect(TIKTOK_ERROR_CODE_MAPPING[40001]).toBe('SCOPE_NOT_AUTHORIZED')
      expect(TIKTOK_ERROR_CODE_MAPPING[40102]).toBe('ACCESS_TOKEN_INVALID')
      expect(TIKTOK_ERROR_CODE_MAPPING[40103]).toBe('ACCESS_TOKEN_EXPIRED')
      expect(TIKTOK_ERROR_CODE_MAPPING[60001]).toBe('RATE_LIMIT_EXCEEDED')
      expect(TIKTOK_ERROR_CODE_MAPPING[60002]).toBe('QUOTA_EXCEEDED')
      expect(TIKTOK_ERROR_CODE_MAPPING[70001]).toBe('CONTENT_VIOLATION')
      expect(TIKTOK_ERROR_CODE_MAPPING[80001]).toBe('ACCOUNT_SUSPENDED')
      expect(TIKTOK_ERROR_CODE_MAPPING[90001]).toBe('FEATURE_NOT_AVAILABLE')
    })
  })

  describe('getTikTokErrorDescription', () => {
    it('应该返回正确的错误描述', () => {
      expect(getTikTokErrorDescription(40001)).toBe('SCOPE_NOT_AUTHORIZED')
      expect(getTikTokErrorDescription(40102)).toBe('ACCESS_TOKEN_INVALID')
      expect(getTikTokErrorDescription(60001)).toBe('RATE_LIMIT_EXCEEDED')
      expect(getTikTokErrorDescription(99999)).toBe('UNKNOWN_ERROR')
    })

    it('应该处理未知错误码', () => {
      expect(getTikTokErrorDescription(12345)).toBe('UNKNOWN_ERROR')
      expect(getTikTokErrorDescription(-1)).toBe('UNKNOWN_ERROR')
    })
  })
})
