import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformUserEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformChannelUserRelationEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  UserType
} from '@yxr/common'
import {
  CreateChannelUserRelationRequestDto,
  UpdateChannelUserRelationRequestDto,
  ChannelUserRelationDto,
  ChannelUserRelationListRequestDto,
  ChannelUserRelationListResponseDto,
  RelatedUserDto
} from '../dto/channel-user-relation.dto'

@Injectable()
export class ChannelUserRelationService {
  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserEntity.name)
    private userModel: Model<OpenPlatformUserEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformChannelUserRelationEntity.name)
    private relationModel: Model<OpenPlatformChannelUserRelationEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建用户关联关系
   */
  async createRelations(createDto: CreateChannelUserRelationRequestDto): Promise<ChannelUserRelationDto[]> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建用户关联')
    }

    // 检查应用是否存在
    const application = await this.applicationModel.findById(createDto.applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    // 检查当前用户是否为该应用的渠道商
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: new Types.ObjectId(createDto.applicationId),
      role: OpenPlatformRoleNames.CHANNEL,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用的渠道商可以创建用户关联')
    }

    // 验证关联用户是否存在
    const relatedUsers = await this.userModel.find({
      _id: { $in: createDto.relatedUserIds.map(id => new Types.ObjectId(id)) }
    })

    if (relatedUsers.length !== createDto.relatedUserIds.length) {
      throw new BadRequestException('部分关联用户不存在')
    }

    // 检查是否已存在关联关系
    const existingRelations = await this.relationModel.find({
      applicationId: new Types.ObjectId(createDto.applicationId),
      channelUserId: new Types.ObjectId(session.userId),
      relatedUserId: { $in: createDto.relatedUserIds.map(id => new Types.ObjectId(id)) },
      status: OpenPlatformStatus.ACTIVE
    })

    if (existingRelations.length > 0) {
      throw new BadRequestException('部分用户已存在关联关系')
    }

    // 批量创建关联关系
    const relations = await this.relationModel.insertMany(
      createDto.relatedUserIds.map(userId => ({
        applicationId: new Types.ObjectId(createDto.applicationId),
        channelUserId: new Types.ObjectId(session.userId),
        relatedUserId: new Types.ObjectId(userId),
        createdBy: new Types.ObjectId(session.userId),
        status: OpenPlatformStatus.ACTIVE,
        remark: createDto.remark || ''
      }))
    )

    // 格式化返回数据
    return Promise.all(relations.map(relation => this.formatRelationDto(relation)))
  }

  /**
   * 获取用户关联列表
   */
  async getRelationList(queryDto: ChannelUserRelationListRequestDto): Promise<ChannelUserRelationListResponseDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看用户关联列表')
    }

    const { page = 1, size = 10, applicationId, keyword } = queryDto
    const skip = (page - 1) * size

    // 构建查询条件
    const query: any = {
      channelUserId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    }

    if (applicationId) {
      query.applicationId = new Types.ObjectId(applicationId)
    }

    // 如果有关键词搜索，需要先查找匹配的用户
    if (keyword) {
      const matchedUsers = await this.userModel.find({
        $or: [
          { phone: { $regex: keyword, $options: 'i' } },
          { nickname: { $regex: keyword, $options: 'i' } }
        ]
      })
      
      if (matchedUsers.length > 0) {
        query.relatedUserId = { $in: matchedUsers.map(user => user._id) }
      } else {
        // 没有匹配的用户，返回空结果
        return {
          data: [],
          totalSize: 0,
          totalPage: 0,
          page,
          size
        }
      }
    }

    const [relations, total] = await Promise.all([
      this.relationModel.find(query).skip(skip).limit(size).sort({ createdAt: -1 }),
      this.relationModel.countDocuments(query)
    ])

    const relationDtos = await Promise.all(
      relations.map(relation => this.formatRelationDto(relation))
    )

    return {
      data: relationDtos,
      totalSize: total,
      totalPage: Math.ceil(total / size),
      page,
      size
    }
  }

  /**
   * 更新用户关联关系
   */
  async updateRelations(
    applicationId: string,
    updateDto: UpdateChannelUserRelationRequestDto
  ): Promise<ChannelUserRelationDto[]> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以更新用户关联')
    }

    // 检查权限
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: new Types.ObjectId(applicationId),
      role: OpenPlatformRoleNames.CHANNEL,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用的渠道商可以更新用户关联')
    }

    // 删除现有关联关系
    await this.relationModel.updateMany(
      {
        applicationId: new Types.ObjectId(applicationId),
        channelUserId: new Types.ObjectId(session.userId),
        status: OpenPlatformStatus.ACTIVE
      },
      { status: OpenPlatformStatus.DISABLED }
    )

    // 创建新的关联关系
    const relations = await this.relationModel.insertMany(
      updateDto.relatedUserIds.map(userId => ({
        applicationId: new Types.ObjectId(applicationId),
        channelUserId: new Types.ObjectId(session.userId),
        relatedUserId: new Types.ObjectId(userId),
        createdBy: new Types.ObjectId(session.userId),
        status: OpenPlatformStatus.ACTIVE,
        remark: updateDto.remark || ''
      }))
    )

    return Promise.all(relations.map(relation => this.formatRelationDto(relation)))
  }

  /**
   * 删除用户关联关系
   */
  async deleteRelation(relationId: string): Promise<{ message: string }> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以删除用户关联')
    }

    const relation = await this.relationModel.findById(relationId)
    if (!relation) {
      throw new NotFoundException('关联关系不存在')
    }

    // 检查权限
    if (!relation.channelUserId.equals(new Types.ObjectId(session.userId))) {
      throw new ForbiddenException('只能删除自己的用户关联')
    }

    await this.relationModel.findByIdAndUpdate(relationId, {
      status: OpenPlatformStatus.DISABLED
    })

    return { message: '删除成功' }
  }

  /**
   * 获取渠道商关联的用户列表
   */
  async getRelatedUsers(applicationId: string): Promise<RelatedUserDto[]> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看关联用户')
    }

    const relations = await this.relationModel.find({
      applicationId: new Types.ObjectId(applicationId),
      channelUserId: new Types.ObjectId(session.userId),
      status: OpenPlatformStatus.ACTIVE
    })

    const userIds = relations.map(relation => relation.relatedUserId)
    const users = await this.userModel.find({ _id: { $in: userIds } })

    return users.map(user => ({
      id: (user as any)._id.toString(),
      phone: user.phone,
      nickname: user.nickname || '',
      status: user.status
    }))
  }

  /**
   * 格式化关联关系DTO
   */
  private async formatRelationDto(relation: OpenPlatformChannelUserRelationEntity): Promise<ChannelUserRelationDto> {
    const [application, channelUser, relatedUser] = await Promise.all([
      this.applicationModel.findById(relation.applicationId),
      this.userModel.findById(relation.channelUserId),
      this.userModel.findById(relation.relatedUserId)
    ])

    return {
      id: (relation as any)._id.toString(),
      applicationId: relation.applicationId.toString(),
      applicationName: application?.name || '',
      channelUserId: relation.channelUserId.toString(),
      channelUserPhone: channelUser?.phone || '',
      channelUserNickname: channelUser?.nickname || '',
      relatedUserId: relation.relatedUserId.toString(),
      relatedUserPhone: relatedUser?.phone || '',
      relatedUserNickname: relatedUser?.nickname || '',
      status: relation.status,
      remark: relation.remark || '',
      createdAt: relation.createdAt?.getTime() || 0,
      updatedAt: relation.updatedAt?.getTime() || 0
    }
  }
}
