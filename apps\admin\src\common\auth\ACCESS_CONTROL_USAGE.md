# 访问控制装饰器使用指南

## 概述

本文档介绍如何在新的控制器中使用访问控制装饰器来实现基于用户类型的权限控制。

## 导入装饰器

```typescript
import { 
  AdminOnly, 
  OpenPlatformAccess, 
  ApplicationAccess,
  AdminAndOpenPlatformAccess,
  AllAuthenticatedAccess 
} from '../../../common/decorators/access-control.decorator'
```

## 使用示例

### 1. 管理员专用控制器

```typescript
@Controller('admin/settings')
@AdminOnly()  // 整个控制器只允许管理员访问
@ApiTags('管理员设置')
export class AdminSettingsController {
  
  @Get()
  async getSettings() {
    // 只有管理员可以访问
    return this.settingsService.getSettings()
  }
  
  @Post()
  async updateSettings(@Body() data: any) {
    // 只有管理员可以访问
    return this.settingsService.updateSettings(data)
  }
}
```

### 2. 开放平台专用控制器

```typescript
@Controller('open-platform/dashboard')
@OpenPlatformAccess()  // 只允许开放平台用户访问
@ApiTags('开放平台仪表板')
export class DashboardController {
  
  @Get('stats')
  async getStats() {
    // 只有开放平台用户可以访问
    return this.dashboardService.getStats()
  }
}
```

### 3. 应用Token专用控制器（带数据隔离）

```typescript
@Controller('api/v1/users')
@ApplicationAccess(true)  // 只允许应用Token访问，启用数据隔离
@ApiTags('API用户管理')
export class ApiUserController {
  
  @Get()
  async getUsers(@Request() request: FastifyRequest) {
    // 只有应用Token可以访问
    // request.dataIsolation 包含数据隔离信息
    const { sourceAppId } = request.dataIsolation
    return this.userService.getUsersByApp(sourceAppId)
  }
  
  @Post()
  @ApplicationAccess(true)
  async createUser(@Body() userData: any, @Request() request: FastifyRequest) {
    // 创建的用户会自动关联到当前应用
    const { sourceAppId } = request.dataIsolation
    return this.userService.createUser({ ...userData, sourceAppId })
  }
}
```

### 4. 混合访问控制器

```typescript
@Controller('shared/resources')
@AdminAndOpenPlatformAccess()  // 管理员和开放平台用户都可以访问
@ApiTags('共享资源')
export class SharedResourceController {
  
  @Get()
  async getResources() {
    // 管理员和开放平台用户都可以访问
    return this.resourceService.getResources()
  }
  
  @Post()
  @AdminOnly()  // 方法级别覆盖：只有管理员可以创建
  async createResource(@Body() data: any) {
    // 只有管理员可以创建资源
    return this.resourceService.createResource(data)
  }
  
  @Delete(':id')
  @AdminOnly()  // 方法级别覆盖：只有管理员可以删除
  async deleteResource(@Param('id') id: string) {
    // 只有管理员可以删除资源
    return this.resourceService.deleteResource(id)
  }
}
```

### 5. 方法级别的细粒度控制

```typescript
@Controller('flexible/api')
@ApiTags('灵活API')
export class FlexibleController {
  
  @Get('public')
  @Anonymous()  // 匿名访问
  async getPublicData() {
    return { message: '公开数据' }
  }
  
  @Get('admin-only')
  @AdminOnly()
  async getAdminData() {
    return { message: '管理员数据' }
  }
  
  @Get('platform-only')
  @OpenPlatformAccess()
  async getPlatformData() {
    return { message: '开放平台数据' }
  }
  
  @Get('app-only')
  @ApplicationAccess(true)
  async getAppData(@Request() request: FastifyRequest) {
    const { sourceAppId } = request.dataIsolation
    return { message: `应用 ${sourceAppId} 的数据` }
  }
  
  @Get('all-authenticated')
  @AllAuthenticatedAccess()
  async getAllUserData(@Request() request: FastifyRequest) {
    const { userType, userId } = request.session
    return { message: `用户 ${userId} (${userType}) 的数据` }
  }
}
```

## 数据隔离使用

当使用 `@ApplicationAccess(true)` 时，系统会自动设置数据隔离信息：

```typescript
@Post('teams')
@ApplicationAccess(true)
async createTeam(@Body() teamData: any, @Request() request: FastifyRequest) {
  // 获取数据隔离信息
  const { sourceAppId, applicationId } = request.dataIsolation
  
  // 创建团队时自动关联到当前应用
  const team = await this.teamService.createTeam({
    ...teamData,
    source: 'open_platform_app',
    sourceAppId: sourceAppId,
    openPlatformUserId: applicationId
  })
  
  return team
}
```

## 错误处理

当用户没有权限访问时，系统会自动抛出 `ForbiddenException`：

```typescript
// 用户类型不匹配时的错误响应
{
  "statusCode": 403,
  "message": "权限不足",
  "error": "Forbidden"
}

// 应用Token信息不完整时的错误响应
{
  "statusCode": 403,
  "message": "应用Token信息不完整",
  "error": "Forbidden"
}
```

## 最佳实践

### 1. 控制器级别 vs 方法级别

- **控制器级别**：当整个控制器都需要相同权限时使用
- **方法级别**：当需要细粒度控制时使用，会覆盖控制器级别的设置

### 2. 数据隔离

- 对于需要数据隔离的应用Token接口，始终使用 `@ApplicationAccess(true)`
- 在服务层使用 `request.dataIsolation` 信息进行数据过滤

### 3. 组合使用

```typescript
@Controller('complex/api')
@AdminAndOpenPlatformAccess()  // 默认允许管理员和开放平台用户
export class ComplexController {
  
  @Get('list')
  // 继承控制器级别权限：管理员和开放平台用户
  async getList() { }
  
  @Post('create')
  @AdminOnly()  // 覆盖：只允许管理员
  async create() { }
  
  @Get('app-data')
  @ApplicationAccess(true)  // 覆盖：只允许应用Token，启用数据隔离
  async getAppData() { }
}
```

### 4. 认证信息获取

```typescript
async someMethod(@Request() request: FastifyRequest) {
  // 获取用户信息
  const user = request.user  // AdminEntity | OpenPlatformUserEntity
  
  // 获取会话信息
  const { userType, userId, applicationId, appId } = request.session
  
  // 获取数据隔离信息（仅当使用 @ApplicationAccess(true) 时）
  const dataIsolation = request.dataIsolation  // { sourceAppId, applicationId }
}
```

## 注意事项

1. **装饰器顺序**：方法级别的装饰器会覆盖控制器级别的设置
2. **匿名接口**：使用 `@Anonymous()` 的接口会跳过所有认证和权限检查
3. **数据隔离**：只有 `@ApplicationAccess(true)` 会设置数据隔离信息
4. **向后兼容**：现有的admin接口无需修改即可正常工作
