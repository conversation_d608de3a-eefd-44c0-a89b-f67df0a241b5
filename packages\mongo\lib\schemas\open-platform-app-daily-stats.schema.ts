import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import { Document, Types } from 'mongoose'
import { MongooseModule } from '@nestjs/mongoose'

export type OpenPlatformAppDailyStatsDocument = OpenPlatformAppDailyStatsEntity & Document

@Schema({
  collection: 'open_platform_app_daily_stats',
  timestamps: true,
  versionKey: false
})
export class OpenPlatformAppDailyStatsEntity {
  @Prop({
    type: Types.ObjectId,
    ref: 'OpenPlatformApplicationEntity',
    required: true,
    index: true
  })
  applicationId: Types.ObjectId

  @Prop({
    type: Date,
    required: true,
    index: true
  })
  date: Date

  @Prop({
    type: Number,
    default: 0,
    min: 0
  })
  totalTrafficUsage: number // 该应用下所有团队的流量使用总和（字节）

  @Prop({
    type: Number,
    default: 0,
    min: 0
  })
  totalAccountsAdded: number // 该应用下所有团队的新增账号总数

  @Prop({
    type: Number,
    default: 0,
    min: 0
  })
  teamCount: number // 该应用下的团队数量

  @Prop({
    type: Date,
    default: Date.now
  })
  createdAt: Date

  @Prop({
    type: Date,
    default: Date.now
  })
  updatedAt: Date
}

export const OpenPlatformAppDailyStatsSchema = SchemaFactory.createForClass(OpenPlatformAppDailyStatsEntity)

// 创建复合索引，确保每个应用每天只有一条记录
OpenPlatformAppDailyStatsSchema.index({ applicationId: 1, date: 1 }, { unique: true })

// 创建日期索引，便于按时间范围查询
OpenPlatformAppDailyStatsSchema.index({ date: -1 })

// 创建应用索引，便于查询特定应用的统计数据
OpenPlatformAppDailyStatsSchema.index({ applicationId: 1, date: -1 })

// 更新时间戳
OpenPlatformAppDailyStatsSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

OpenPlatformAppDailyStatsSchema.pre(['updateOne', 'findOneAndUpdate'], function(next) {
  this.set({ updatedAt: new Date() })
  next()
})

// 导出Mongoose模块
export const OpenPlatformAppDailyStatsMongoose = MongooseModule.forFeature([
  { name: OpenPlatformAppDailyStatsEntity.name, schema: OpenPlatformAppDailyStatsSchema }
])
