import { FavoritesGroupItemEntity, FavoritesGroupEntity, PlatformAccountEntity } from '@yxr/mongo'
import { Model, Types } from 'mongoose'
import { InjectModel } from '@nestjs/mongoose'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import {
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from '@nestjs/common'
import {
  GroupsDetailResponse,
  GroupsListResponse,
  PostGroupsRequest,
  PutCollectGroupItemsRequest
} from './collect-group.dto'

@Injectable()
export class CollectGroupService {
  logger = new Logger('CollectGroupService')

  constructor(
    @Inject(REQUEST) private request: FastifyRequest,
    @InjectModel(FavoritesGroupEntity.name)
    private favoritesGroupModel: Model<FavoritesGroupEntity>,
    @InjectModel(FavoritesGroupItemEntity.name)
    private favoritesGroupItemsModel: Model<FavoritesGroupItemEntity>,
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>
  ) {}
  /**
   * 添加分组
   * @param body
   * @returns
   */
  async postGroupsAsync(body: PostGroupsRequest): Promise<GroupsDetailResponse> {
    const { session } = this.request
    let group = await this.favoritesGroupModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      name: body.name
    })

    if (group) {
      throw new ConflictException('分组已存在')
    }

    const data = await this.favoritesGroupModel.create({
      name: body.name,
      teamId: new Types.ObjectId(session.teamId),
      userId: new Types.ObjectId(session.userId)
    })

    return {
      id: data._id.toString(),
      name: data.name,
      originals: [],
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 分组列表
   * @param name
   * @param page
   * @param size
   * @returns
   */
  async getGroupsAsync(name: string, page: number, size: number): Promise<GroupsListResponse> {
    const skip = (page - 1) * size
    const { session } = this.request
    const query: any = {
      $match: {}
    }

    query.$match.teamId = new Types.ObjectId(session.teamId)
    if (name) {
      query.$match.name = { $regex: name, $options: 'i' }
    }

    const data = await this.favoritesGroupModel
      .aggregate([
        query,
        {
          $lookup: {
            from: 'favoritesgroupitementities',
            localField: '_id',
            foreignField: 'groupId',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  originalId: 1,
                  accountId: 1,
                  groupId: 1,
                  browserId: 1,
                  websiteUrl: 1
                }
              }
            ],
            as: 'originals'
          }
        },
        {
          $facet: {
            counts: [{ $count: 'total' }],
            items: [{ $sort: { createdAt: -1 } }, { $skip: (page - 1) * size }, { $limit: size }]
          }
        }
      ])
      .exec()

    const totalSize = data[0]?.counts[0]?.total ?? 0
    const result = data[0]?.items ?? []

    const accountIds = [
      ...new Set(
        result.flatMap((group: { originals: any[] }) =>
          group.originals
            .map((original: { accountId: { toString: () => any } }) => {
              return original.accountId ? original.accountId.toString() : null
            })
            .filter((accountId: null) => accountId != null)
        )
      )
    ]
    const platformAccounts = await this.platformAccountModel
      .find({
        _id: { $in: accountIds.map((x) => new Types.ObjectId(x.toString())) }
      })
      .select('_id platformName platformAccountName color spaceUrl')

    const platformMap = platformAccounts.reduce((acc, platform) => {
      acc[platform._id.toString()] = platform
      return acc
    }, {})

    return {
      page,
      size,
      totalSize,
      totalPage: Math.ceil(totalSize / size),
      data: data[0].items.map((item) => ({
        id: item._id.toString(),
        name: item.name,
        originals: item.originals.map((v) => ({
          ...v,
          platformName: platformMap[v.accountId?.toString()]?.platformName ?? null,
          color: platformMap[v.accountId?.toString()]?.color ?? null,
          spaceName: platformMap[v.accountId?.toString()]?.platformAccountName ?? null
        })),
        createdAt: item.createdAt.getTime()
      }))
    }
  }

  async getGroupDetailAsync(groupId: string) {
    const { teamId: currentTeamId } = this.request.session

    const data = await this.favoritesGroupModel
      .aggregate([
        {
          $match: {
            teamId: new Types.ObjectId(currentTeamId),
            _id: new Types.ObjectId(groupId)
          }
        },
        {
          $lookup: {
            from: 'favoritesgroupitementities',
            localField: '_id',
            foreignField: 'groupId',
            pipeline: [
              {
                $project: {
                  _id: 0,
                  originalId: 1,
                  accountId: 1,
                  groupId: 1,
                  browserId: 1,
                  websiteUrl: 1
                }
              }
            ],
            as: 'originals'
          }
        }
      ])
      .exec()

    const result = data[0]
    if (!result) {
      throw new NotFoundException('收藏分组未找到')
    }
    if (!result?.originals || result?.originals.length === 0) {
      return {
        id: result?._id?.toString(),
        name: result.name,
        originals: [],
        createdAt: result.createdAt?.getTime()
      }
    }

    const accountIds = [
      ...new Set(
        result.originals
          ?.map((original: { accountId: { toString: () => any } }) => {
            return original.accountId ? original.accountId.toString() : null
          })
          .filter((accountId: null) => accountId != null)
      )
    ]
    const platformAccounts = await this.platformAccountModel
      .find({
        _id: { $in: accountIds.map((x) => new Types.ObjectId(x.toString())) }
      })
      .select('_id platformName platformAccountName color spaceUrl')

    const platformMap = platformAccounts.reduce((acc, platform) => {
      acc[platform._id.toString()] = platform
      return acc
    }, {})

    return {
      id: result._id?.toString(),
      name: result.name,
      originals: result.originals.map((v) => ({
        ...v,
        platformName: platformMap[v.accountId?.toString()]?.platformName ?? null,
        color: platformMap[v.accountId?.toString()]?.color ?? null,
        spaceName: platformMap[v.accountId?.toString()]?.platformAccountName ?? null
      })),
      createdAt: result.createdAt?.getTime()
    }
  }

  /**
   * 修改分组
   * @param groupsId
   * @param body
   */
  async patchGroupsAsync(groupsId: string, body: PostGroupsRequest): Promise<GroupsDetailResponse> {
    const { session } = this.request
    let group = await this.favoritesGroupModel.findOne({
      teamId: new Types.ObjectId(session.teamId),
      _id: new Types.ObjectId(groupsId)
    })

    if (!group) {
      throw new NotFoundException('分组不存在')
    }
    const updateData: any = {}

    if (body.name) {
      updateData.name = body.name
    }

    const data = await this.favoritesGroupModel.findOneAndUpdate(
      { _id: group._id },
      { $set: updateData }, // 使用 $set 只更新传入的字段
      { returnDocument: 'after', new: true } // 返回更新后的文档
    )
    const originals = await this.favoritesGroupItemsModel.find({
      groupId: group._id
    })
    return {
      id: data._id.toString(),
      name: data.name,
      originals: originals.map((item) => ({
        websiteUrl: item.websiteUrl,
        browserId: item.browserId.toString(),
        originalId: item.originalId.toString(),
        groupId: item.groupId.toString(),
        accountId: item.accountId?.toString()
      })),
      createdAt: data.createdAt.getTime()
    }
  }

  /**
   * 删除分组
   * @param groupsId
   */
  async deleteGroupsAsync(groupsId: string) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const group = await this.favoritesGroupModel.findOne({
      _id: new Types.ObjectId(groupsId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    const session = await this.favoritesGroupItemsModel.db.startSession()
    session.startTransaction()

    try {
      await this.favoritesGroupModel.deleteOne({
        _id: new Types.ObjectId(group._id)
      })

      await this.favoritesGroupItemsModel.deleteMany({
        groupId: new Types.ObjectId(group._id)
      })

      await session.commitTransaction()
    } catch (error) {
      await session.abortTransaction()
      this.logger.error('收藏分组删除失败', error)
      throw new ForbiddenException('收藏分组删除失败, 请稍后再试')
    } finally {
      await session.endSession()
    }
  }

  /**
   * 设置收藏分组网站
   * @param groupsId
   * @param body
   */
  async putCollectGroupItems(groupsId: string, body: PutCollectGroupItemsRequest[]) {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session
    const group = await this.favoritesGroupModel.findOne({
      _id: new Types.ObjectId(groupsId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!group) {
      throw new NotFoundException('分组未找到')
    }

    if (body.length >= 0) {
      const favoriteItems = await this.favoritesGroupItemsModel.find({
        groupId: new Types.ObjectId(groupsId)
      })

      // 差异化处理 转换为 Map，以组合键作为唯一标识
      const existingMap = new Map(
        favoriteItems.map((item) => [`${item.originalId}_${item.browserId}`, item])
      )
      const newMap = new Map(body.map((item) => [`${item.originalId}_${item.browserId}`, item]))

      // 新增数据
      const toInsert = body.filter(
        (item) => !existingMap.has(`${item.originalId}_${item.browserId}`)
      )
      // 删除数据
      const toDelete = favoriteItems.filter(
        (item) => !newMap.has(`${item.originalId}_${item.browserId}`)
      )

      const session = await this.favoritesGroupItemsModel.db.startSession()
      session.startTransaction()

      try {
        if (toInsert.length > 0 || toDelete.length > 0) {
          const bulkOps = []
          if (toInsert.length > 0) {
            const toInsertData = toInsert.map((item) => ({
              ...item,
              groupId: new Types.ObjectId(groupsId),
              browserId: new Types.ObjectId(item.browserId),
              originalId: new Types.ObjectId(item.originalId),
              accountId: item.accountId ? new Types.ObjectId(item.accountId) : null
            }))
            bulkOps.push(...toInsertData.map((doc) => ({ insertOne: { document: doc } })))
          }
          if (toDelete.length > 0) {
            const idsToDelete = toDelete.map((item) => item._id)
            bulkOps.push({ deleteMany: { filter: { _id: { $in: idsToDelete } } } })
          }

          await this.favoritesGroupItemsModel.bulkWrite(bulkOps, { session })
        }

        await session.commitTransaction()
      } catch (error) {
        await session.abortTransaction()
        this.logger.error('收藏分组设置失败', error)
        throw new ForbiddenException('收藏分组设置失败, 请稍后再试')
      } finally {
        await session.endSession() // 保证资源清理
      }
    }
  }
}
