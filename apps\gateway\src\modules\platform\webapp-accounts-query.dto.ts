import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { ApiProperty } from '@nestjs/swagger'
import { FetchAllListInputDto, PagedListOutputDto } from '../../common/dto/BaseQueryDTO'
import { IsOptional } from 'class-validator'
import { LoginStatus } from '@yxr/mongo/lib/schemas/platform_accounts.schema'

export class AccountDetailsOutputDto {
  @ApiProperty({
    type: String,
    required: true
  })
  id: string

  @ApiProperty({
    type: String,
    title: '平台名称',
    required: true
  })
  platformName: string

  @ApiProperty({
    type: String,
    title: '账号头像',
    required: true
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    title: '账号名',
    required: false
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    title: '账号备注',
    required: false
  })
  remark: string

  @ApiProperty({
    title: '发布状态',
    description:
      '发布状态: 0(未曾登录), 1(登录成功), 2(登录过期), 3(登录失败), 4(取消授权)',
    type: Number,
    enum: LoginStatus,
    example: LoginStatus.Succesed,
    required: true
  })
  status: LoginStatus

  @ApiProperty({
    type: Number,
    title: '创建时间',
    required: true
  })
  createdAt: number

  @ApiProperty({
    type: Number,
    title: '最后更新时间',
    required: true
  })
  updatedAt: number
}

export class FindAllAccountsOutputDto extends PagedListOutputDto {
  @ApiProperty({
    type: [AccountDetailsOutputDto]
  })
  items: AccountDetailsOutputDto[]
}

export class FindAllAccountsOutputResponse extends BaseResponseDTO {
  @ApiProperty({
    type: [FindAllAccountsOutputDto]
  })
  data: FindAllAccountsOutputDto
}

export class FindAllAccountsInputDto extends FetchAllListInputDto {
  @ApiProperty({
    type: String,
    title: '概念范围查询',
    description: `可以根据一些约定概念范围进行查询
     查询范围和查询条件参数同时生效(如: operational && platform=tiktok && keyword=abc);
     只允许输入一个, 多个概念则会报错;
     支持的范围: 暂不支持任何范围定义`,
    required: false
  })
  @IsOptional()
  scope?: string

  @ApiProperty({
    type: String,
    title: '查询关键字',
    description: '模糊查询关键字, 支持字段: title',
    example: 'Anter',
    required: false
  })
  @IsOptional()
  keyword?: string

  @ApiProperty({
    type: String,
    title: '平台查询条件',
    description: '根据平台过滤',
    example: 'tiktok',
    required: false
  })
  @IsOptional()
  platformName?: string
}
