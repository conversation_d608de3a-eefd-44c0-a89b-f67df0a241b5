import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsString } from 'class-validator'

export class PreAuthCodeDTO {
  @ApiResponseProperty({
    type: String,
    example: '第三方平台appId'
  })
  component_appid: string

  @ApiResponseProperty({
    type: String,
    example: '预授权码'
  })
  pre_auth_code: string

  @ApiResponseProperty({
    type: String,
    example:
      '授权的账号类型 1 则商户点击链接后，手机端仅展示公众号、2 表示仅展示小程序，3 表示公众号和小程序都展示'
  })
  authType: number
}

export class PreAuthCodeResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: PreAuthCodeDTO
  })
  data: PreAuthCodeDTO
}

export class WxPlatformAccountRequest {
  @ApiProperty({
    description: '微信账号授权码',
    type: String
  })
  @IsString()
  auth_code: string
}
