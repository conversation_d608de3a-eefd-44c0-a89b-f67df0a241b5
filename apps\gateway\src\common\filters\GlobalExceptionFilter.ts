import { Catch, ArgumentsHost, HttpException } from '@nestjs/common'
import { FastifyReply } from 'fastify'

@Catch(HttpException)
export class GlobalExceptionFilter {
  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp()

    const reply = ctx.getResponse<FastifyReply>()

    const status = exception.getStatus()
    try {
      const response = exception.getResponse()
      const message = typeof response === 'object' ? (response as any).message : response
      const code = typeof response === 'object' ? (response as any).code : null
      reply.code(status < 0 ? 403 : status).send({
        statusCode: status,
        message: (Array.isArray(message) ? message[0] : message) || '服务错误',
        code: code
      })
    } catch {
      reply.code(status < 0 ? 403 : status).send({
        statusCode: status,
        message: exception.message || '服务错误'
      })
    }
  }
}
