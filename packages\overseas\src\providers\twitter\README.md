# Twitter 内容发布功能

本模块实现了 Twitter 平台的内容发布功能，支持文本、图片、视频和混合内容的发布。

## 功能特性

### 支持的内容类型
- **文本内容** (`PublishContentType.Text`): 纯文本推文
- **图片内容** (`PublishContentType.Image`): 带图片的推文（最多4张）
- **视频内容** (`PublishContentType.Video`): 带视频的推文
- **混合内容** (`PublishContentType.Mixed`): 根据内容自动选择发布类型

### 内容限制
- **文本长度**: 最多 280 字符
- **图片数量**: 最多 4 张
- **视频大小**: 最大 512MB
- **视频时长**: 最大 140 秒
- **支持的图片格式**: jpg, jpeg, png, gif, webp
- **支持的视频格式**: mp4, mov

## 核心组件

### TwitterContentPublishProvider
主要的内容发布提供者，实现了 `ContentPublishProvider` 接口。

#### 主要方法
- `validateContent()`: 验证发布内容是否符合 Twitter 要求
- `publishContent()`: 发布内容到 Twitter
- `getPublishStatus()`: 查询发布状态
- `deleteContent()`: 删除已发布的内容
- `getSupportedContentTypes()`: 获取支持的内容类型
- `getContentLimits()`: 获取内容限制

### TwitterApi
Twitter API 客户端，封装了与 Twitter API 的交互。

#### 主要方法
- `oauth2_token()`: OAuth2 令牌交换
- `get_users_me()`: 获取用户信息
- `createTweet()`: 创建推文
- `uploadMedia()`: 上传媒体文件
- `getTweet()`: 获取推文信息
- `deleteTweet()`: 删除推文

### TwitterBusinessErrorChecker
Twitter 特定的错误处理器，将 Twitter API 错误映射到标准错误类型。

## 使用示例

### 发布文本推文
```typescript
const context: OverseasContext = {
  platform: 'twitter',
  accountOpenId: 'user-account-id',
  options: {
    credentials: {
      access_token: 'twitter-access-token'
    }
  }
}

const taskData: PublishTaskData = {
  taskId: 'task-123',
  content: {
    type: PublishContentType.Text,
    text: '这是一条测试推文 #测试'
  },
  // ... 其他必需字段
}

const result = await twitterPublishProvider.publishContent(context, taskData)
```

### 发布图片推文
```typescript
const taskData: PublishTaskData = {
  taskId: 'task-123',
  content: {
    type: PublishContentType.Image,
    text: '分享一些美丽的图片',
    images: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.png'
    ]
  },
  // ... 其他必需字段
}

const result = await twitterPublishProvider.publishContent(context, taskData)
```

### 发布视频推文
```typescript
const taskData: PublishTaskData = {
  taskId: 'task-123',
  content: {
    type: PublishContentType.Video,
    text: '分享一个有趣的视频',
    videoUrl: 'https://example.com/video.mp4'
  },
  // ... 其他必需字段
}

const result = await twitterPublishProvider.publishContent(context, taskData)
```

## 错误处理

### 常见错误类型
- `ACCESS_TOKEN_INVALID`: 访问令牌无效或过期
- `RATE_LIMIT_EXCEEDED`: 超过 API 调用频率限制
- `REQUEST_PARAMETERS_INCORRECT`: 请求参数错误
- `CONTENT_VIOLATION`: 内容违反 Twitter 政策
- `MEDIA_UPLOAD_FAILED`: 媒体文件上传失败
- `ACCOUNT_SUSPENDED`: 账号被暂停

### 错误处理示例
```typescript
try {
  const result = await twitterPublishProvider.publishContent(context, taskData)
  if (result.status === PublishTaskStatus.Failed) {
    console.error('发布失败:', result.errorMessage)
    console.error('错误代码:', result.errorCode)
  }
} catch (error) {
  console.error('发布异常:', error.message)
}
```

## 配置要求

### 环境变量
```bash
# Twitter API 凭证
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret

# 海外服务基础地址
OVERSEAS_BASE_ADDRESS=https://your-overseas-service.com/
```

### 依赖包
- `twitter-api-sdk`: Twitter 官方 SDK
- `form-data`: 用于媒体文件上传
- `axios`: HTTP 客户端

## 最佳实践

### 1. 内容验证
在发布前始终调用 `validateContent()` 方法验证内容

### 2. 错误重试
对于临时性错误（如网络错误、限流），实现重试机制

### 3. 媒体文件优化
- 确保图片文件大小合理（建议小于 5MB）
- 视频文件应压缩到合适的大小和质量
- 使用支持的文件格式

### 4. 文本内容优化
- 保持推文简洁明了
- 合理使用话题标签（#hashtag）
- 避免重复发布相同内容

## 测试

运行测试：
```bash
npm test twitter-content-publish.test.ts
```

## 注意事项

1. **API 限制**: Twitter API 有严格的调用频率限制，请合理控制发布频率
2. **内容政策**: 确保发布的内容符合 Twitter 的社区准则
3. **媒体处理**: 大文件上传可能需要较长时间，建议实现进度提示
4. **错误监控**: 建议实现完善的错误监控和告警机制
5. **令牌管理**: 定期检查和刷新访问令牌，避免令牌过期导致的发布失败
