import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { FilterQuery, Model, Types } from 'mongoose'
import {
  AdminEntity,
  CustomerAssignRecordEntity,
  FollowRecordEntity,
  MemberEntity,
  TeamEntity,
  UserEntity
} from '@yxr/mongo'
import { MemberQueryDTO, MemberResponse, PatchCustomerRequest } from './member.dto'
import { ConfigService } from '@nestjs/config'
import { RootConfigMap } from '@yxr/config'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { AdminRole, TeamRoleNames } from '@yxr/common'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'

@Injectable()
export class MemberService {
  logger = new Logger('MemberService')

  constructor(
    @InjectModel(UserEntity.name) private userModel: Model<UserEntity>,
    @InjectModel(MemberEntity.name) private memberModel: Model<MemberEntity>,
    @InjectModel(FollowRecordEntity.name) private followRecordModel: Model<FollowRecordEntity>,
    @InjectModel(AdminEntity.name) private adminModel: Model<AdminEntity>,
    @InjectModel(TeamEntity.name) private teamModel: Model<TeamEntity>,
    @InjectModel(CustomerAssignRecordEntity.name)
    private customerAssignRecordModel: Model<CustomerAssignRecordEntity>,
    private readonly configService: ConfigService<{ app: RootConfigMap }, true>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 用户管理
   * @param query
   * @returns
   */
  async getMembers(query: MemberQueryDTO): Promise<MemberResponse> {
    const page = query.page
    const size = query.size

    const filter: FilterQuery<UserEntity> = {}

    if (query.channelCode) {
      filter.channelCode = query.channelCode
    }

    if (query.customerId) {
      filter.customerId = new Types.ObjectId(query.customerId)
    }

    if (query.phone) {
      filter.phone = { $regex: query.phone, $options: 'i' }
    }

    if (query.createStartTime && query.createEndTime) {
      filter.createdAt = {
        $gt: new Date(Number(query.createStartTime)),
        $lte: new Date(Number(query.createEndTime))
      }
    }

    if (query.lastUseStartTime && query.lastUseEndTime) {
      filter.updatedAt = {
        $gt: new Date(Number(query.lastUseStartTime)),
        $lte: new Date(Number(query.lastUseEndTime))
      }
    }

    if (query.applicationId) {
      filter.sourceAppId = new Types.ObjectId(query.applicationId)
      filter.source = 'open_platform_app'
    }

    const list = await this.userModel
      .find(filter)
      .sort({ createdAt: -1 })
      .skip((query.page - 1) * query.size)
      .limit(query.size)

    const userIds = list.map((item) => new Types.ObjectId(item.id))

    const memberLists = await this.memberModel.find({
      userId: { $in: userIds }
    })

    const followRecordLists = await this.followRecordModel.aggregate([
      {
        $match: {
          userId: { $in: userIds }
        }
      },
      {
        $sort: {
          createdAt: -1 // 按创建时间降序排序
        }
      },
      {
        $group: {
          _id: '$userId',
          followRecordCount: { $sum: 1 },
          firstFollowRecord: { $first: '$content' }
        }
      }
    ])

    const customers = await this.adminModel
      .find({
        role: AdminRole.Customer
      })
      .select('_id name')
      .lean()

    const data = await Promise.all(
      list.map(async (item) => {
        const customerName = item?.customerId
          ? customers.find((c) => c._id.toString() === item.customerId.toString())?.name
          : ''

        const teamArrs = memberLists.filter((c) => c.userId.toString() === item.id.toString())
        const teamIds = teamArrs.map((c) => new Types.ObjectId(c.teamId))
        const teamCount = await this.teamModel.countDocuments({
          _id: { $in: teamIds },
          isDeleted: false
        })

        const firstFollowRecordInfo = followRecordLists.find(
          (c) => c._id.toString() === item.id.toString()
        )

        return {
          id: item.id,
          phone: item.phone,
          avatar: `${process.env.OSS_DOWNLOAD_URL}/${item.avatar}`,
          nickName: item.nickName,
          teamCount: teamCount || 0,
          createdAt: item.createdAt.getTime(),
          updatedAt: item.updatedAt.getTime(),
          channelCode: item.channelCode,
          registrationSource: item.registrationSource,
          followRecordCount: firstFollowRecordInfo?.followRecordCount || 0,
          newfollowRecord: firstFollowRecordInfo?.firstFollowRecord || '',
          customerName: customerName
        }
      })
    )

    const totalSize = await this.userModel.find(filter).countDocuments()

    return {
      totalSize,
      page,
      size,
      totalPage: Math.ceil(totalSize / size),
      data: data
    }
  }

  /**
   * 更换客服
   * @param id
   * @param body
   */
  async updateCustomer(id: string, body: PatchCustomerRequest) {
    const adminUser = this.request.user
    if (
      'role' in adminUser &&
      (adminUser.role !== AdminRole.Admin && adminUser.role !== AdminRole.SuperAdmin)
    ) {
      throw new NotFoundException('无操作权限')
    }
    const user = await this.userModel.findById(new Types.ObjectId(id))
    if (!user) {
      throw new NotFoundException('用户未找到')
    }
    const customer = await this.adminModel.findOne({
      _id: new Types.ObjectId(body.customerId),
      role: AdminRole.Customer
    })
    if (!customer) {
      throw new NotFoundException('客服不存在')
    }
    try {
      user.customerId = new Types.ObjectId(body.customerId)
      await user.save()

      //更换团队冗余客服绑定
      const teams = await this.memberModel
        .find({
          userId: new Types.ObjectId(user._id),
          roles: { $in: [TeamRoleNames.MASTER] }
        })
        .select('teamId')
        .lean()
      if (teams) {
        const teamIds = teams.map((item) => new Types.ObjectId(item.teamId))
        await this.teamModel.updateMany(
          {
            _id: { $in: teamIds }
          },
          {
            $set: {
              customerId: new Types.ObjectId(body.customerId)
            }
          }
        )
      }

      await this.customerAssignRecordModel.create({
        userId: new Types.ObjectId(user.id),
        customerId: new Types.ObjectId(body.customerId)
      })

      const { overdueToken } = this.configService.get<RootConfigMap['app']>('app')
      const authorization = await this.cacheManager.get<string>(user.phone)
      this.cacheManager.set(authorization, user, overdueToken)
    } catch (error) {
      this.logger.error(error)
    }
  }
}
