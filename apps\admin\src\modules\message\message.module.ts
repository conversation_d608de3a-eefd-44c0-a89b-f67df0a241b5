import { Module } from '@nestjs/common'
import { CommonModule } from '@yxr/common'
import { WebhookModule } from '../webhook/webhook.module'
import { MessageController } from './message.controller'
import { MessageService } from './message.service'
import { MessageMongoose } from '@yxr/mongo'

@Module({
  imports: [MessageMongoose, WebhookModule, CommonModule],
  controllers: [MessageController],
  providers: [MessageService]
})
export class MessageModule {}
