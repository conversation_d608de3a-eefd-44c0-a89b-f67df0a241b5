import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsString } from "class-validator";

export class DeviceLogsRequestBodyDTO {
  @ApiProperty({
    type: String,
    required: true,
    description: '设备号',
    example: 'windows 10家庭中文版'
  })
  @IsString()
  @IsNotEmpty({ message: '设备ID不能为空' })
  deviceId: string

  @ApiProperty({
    type: String,
    required: true,
    description: '日志下载OSS地址',
  })
  @IsString()
  @IsNotEmpty({ message: '地址不能为空' })
  downloadLink: string
}
