import { ApiResponseProperty } from '@nestjs/swagger'

export class BaseResponseDTO {
  @ApiResponseProperty()
  stateCode: number
}

export class BaseBadRequestResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 400
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '操作失败'
  })
  message: string
}

export class BaseUnauthorizedResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 401
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '参数错误'
  })
  message: string
}

// 为了保持向后兼容性，保留这个别名
export class BaseBadRequestDTO extends BaseBadRequestResponseDTO {}

export class BaseForbiddenResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 403
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '该账号已被禁用'
  })
  message: string
}

export class BaseNotFoundResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 404
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '资源不存在'
  })
  message: string
}

export class BaseConflictResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 409
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '资源冲突'
  })
  message: string
}

export class BaseInternalServerErrorResponseDTO {
  @ApiResponseProperty({
    type: Number,
    example: 500
  })
  stateCode: number

  @ApiResponseProperty({
    type: String,
    example: '服务器内部错误'
  })
  message: string
}
