import { Body, Controller, Post } from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse
} from '@nestjs/swagger'
import { Anonymous } from '../../../common/decorators/Anonymous'
import { OpenPlatformAccess } from '../../../common/decorators/access-control.decorator'
import { OpenPlatformAuthService } from '../services/auth.service'
import {
  SendCodeRequestDto,
  SendCodeResponseDto,
  RegisterRequestDto,
  RegisterResponseDto,
  LoginRequestDto,
  LoginOkResponseDto,
  UnifiedAuthRequestDto,
  UnifiedAuthResponseDto,
  LogoutRequestDto,
  LogoutOkResponseDto
} from '../dto/auth.dto'
import {
  BaseBadRequestDTO,
  BaseNotFoundResponseDTO,
  BaseForbiddenResponseDTO
} from '../../../common/dto/BaseResponseDTO'

@Controller('open-platform/auth')
@ApiTags('开放平台认证')
export class OpenPlatformAuthController {
  constructor(private readonly authService: OpenPlatformAuthService) {}

  @Post('send-code')
  @Anonymous()
  @ApiOperation({ summary: '发送验证码' })
  @ApiOkResponse({
    description: '验证码发送成功',
    type: SendCodeResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async sendCode(@Body() sendCodeDto: SendCodeRequestDto) {
    const result = await this.authService.sendVerificationCode(sendCodeDto)
    return result
  }

  @Post('register')
  @Anonymous()
  @ApiOperation({ summary: '用户注册' })
  @ApiOkResponse({
    description: '注册成功',
    type: RegisterResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或用户已存在',
    type: BaseBadRequestDTO
  })
  async register(@Body() registerDto: RegisterRequestDto) {
    const result = await this.authService.register(registerDto)
    return result
  }

  @Post('login')
  @Anonymous()
  @ApiOperation({ summary: '用户登录' })
  @ApiOkResponse({
    description: '登录成功',
    type: LoginOkResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或验证码/密码错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '用户不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '账号已被禁用',
    type: BaseForbiddenResponseDTO
  })
  async login(@Body() loginDto: LoginRequestDto) {
    const result = await this.authService.login(loginDto)
    return result
  }

  @Post('auth')
  @Anonymous()
  @ApiOperation({
    summary: '统一认证（登录/注册）',
    description: '根据手机号自动判断是登录还是注册。如果用户不存在则自动注册，如果用户已存在则执行登录。'
  })
  @ApiOkResponse({
    description: '认证成功',
    type: UnifiedAuthResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或验证码错误',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '账号已被禁用',
    type: BaseForbiddenResponseDTO
  })
  async unifiedAuth(@Body() authDto: UnifiedAuthRequestDto) {
    const result = await this.authService.unifiedAuth(authDto)
    return result
  }

  @Post('logout')
  @OpenPlatformAccess()
  @ApiOperation({
    summary: '退出登录',
    description: '清除用户的认证Token缓存，支持退出当前设备或所有设备'
  })
  @ApiOkResponse({
    description: '退出登录成功',
    type: LogoutOkResponseDto
  })
  @ApiBadRequestResponse({
    description: '退出登录失败',
    type: BaseBadRequestDTO
  })
  @ApiForbiddenResponse({
    description: '无效的用户会话',
    type: BaseForbiddenResponseDTO
  })
  async logout(@Body() logoutDto: LogoutRequestDto) {
    const result = await this.authService.logout(logoutDto)
    return result
  }
}
