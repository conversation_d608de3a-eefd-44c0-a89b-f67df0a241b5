import {
  Controller,
  Put,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Request
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse
} from '@nestjs/swagger'
import type { FastifyRequest } from 'fastify'
import { TeamManagementService } from '../services/team-management.service'
import {
  UpdateTeamExpirationRequestDto,
  SetTeamQuotaRequestDto,
  UpdateTeamExpirationApiResponseDto,
  SetTeamQuotaApiResponseDto
} from '../dto/team-management.dto'
import { BaseBadRequestDTO } from '../../../common/dto/BaseResponseDTO'
import { ApplicationAccess } from 'apps/admin/src/common/decorators/access-control.decorator'

@ApiTags('开放平台团队管理')
@Controller('open-platform/team-management')
@ApplicationAccess(true)
@ApiBearerAuth()
export class TeamManagementController {
  constructor(private readonly teamManagementService: TeamManagementService) {}

  @Put('expiration')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '修改团队过期时间',
    description: '允许开放平台应用修改指定团队的VIP过期时间，新的过期时间必须大于当前时间'
  })
  @ApiResponse({
    status: 200,
    description: '修改成功',
    type: UpdateTeamExpirationApiResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或过期时间无效',
    type: BaseBadRequestDTO
  })
  async updateTeamExpiration(
    @Body() updateDto: UpdateTeamExpirationRequestDto,
    @Request() request: FastifyRequest
  ) {
    const sourceAppId = request.session?.applicationId

    return this.teamManagementService.updateTeamExpiration(sourceAppId, updateDto)
  }

  @Put('quota')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '设置团队资源配额',
    description:
      '允许开放平台应用设置指定团队的账号数量限制和流量配额。如果新的账号限制小于当前在线账号数量，将自动冻结多余的账号'
  })
  @ApiOkResponse({ type: SetTeamQuotaApiResponseDto })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  async setTeamQuota(
    @Body() quotaDto: SetTeamQuotaRequestDto,
    @Request() request: FastifyRequest
  ) {
    const sourceAppId = request.session?.applicationId

    return this.teamManagementService.setTeamQuota(sourceAppId, quotaDto)
  }
}
