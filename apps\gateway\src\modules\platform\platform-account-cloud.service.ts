import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import {
  AccountCloudBaseRequst,
  AccountLocationResponse,
  AccountMusicBodyRequest,
  AccountMusicCategoryResponse,
  AccountMusicResponse,
  AccountValidationResponse
} from './platform-account-cloud.dto'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountCookieEntity, PlatformAccountEntity } from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import { PlatformNameEnum } from '@yxr/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService } from '@yxr/huoshan'
import {
  AccountLocationDTO,
  AccountMusicDTO,
  CloudBaseDTO,
  YxrOpenPlatformService
} from '@yxr/yxr-open-platform'

@Injectable()
export class PlatformAccountCloudService {
  constructor(
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @InjectModel(PlatformAccountCookieEntity.name)
    private platformAccountCookieModel: Model<PlatformAccountCookieEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly loggerService: TlsService,
    private readonly yxrOpenPlatformService: YxrOpenPlatformService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  /**
   * 获取音乐
   * @param platformAccountId
   * @param query
   * @returns
   */
  async getPlatformAccountMusic(
    platformAccountId: string,
    query: AccountMusicBodyRequest
  ): Promise<AccountMusicResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }

      const musicData: AccountMusicDTO = {
        callBackData: {
          teamId: platformAccount.teamId.toString(),
          userId: platformAccount.userId.toString()
        },
        platform: platformAccount.platformName,
        keyWord: query.keyWord,
        nextPage: query.nextPage,
        platformAccountId: platformAccount.id.toString(),
        cookie: cookie
      }
      let result = null
      if (query.categoryId && query.categoryName) {
        musicData.categoryId = query.categoryId
        musicData.categoryName = query.categoryName
        result = await this.yxrOpenPlatformService.getMusicByCategory(musicData)
      } else {
        result = await this.yxrOpenPlatformService.searchMusic(musicData)
      }

      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList,
          nextPage: result.data.nextPage
        }
      } else {
        await this.loggerService.error(this.request, '获取音乐失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicData)
        })
        throw new ForbiddenException('音乐获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取音乐失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('音乐获取失败')
    }
  }

  /**
   * 获取音乐分类
   * @param platformAccountId
   * @returns
   */
  async getPlatformAccountMusicCategory(
    platformAccountId: string
  ): Promise<AccountMusicCategoryResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }

      const musicCategoryData: CloudBaseDTO = {
        callBackData: {
          teamId: platformAccount.teamId.toString(),
          userId: platformAccount.userId.toString()
        },
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        cookie: cookie
      }
      const result = await this.yxrOpenPlatformService.musicCategory(musicCategoryData)
      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList
        }
      } else {
        await this.loggerService.error(this.request, '获取音乐分类失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicCategoryData)
        })
        throw new ForbiddenException('音乐分类获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取音乐分类失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('音乐分类获取失败')
    }
  }

  /**
   * 获取账号地理位置
   * @param platformAccountId
   * @returns
   */
  async getPlatformAccountLocation(
    platformAccountId: string,
    query: AccountCloudBaseRequst
  ): Promise<AccountLocationResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }

      const musicCategoryData: AccountLocationDTO = {
        callBackData: {
          teamId: platformAccount.teamId.toString(),
          userId: platformAccount.userId.toString()
        },
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        keyWord: query.keyWord,
        nextPage: query.nextPage,
        cookie: cookie
      }
      const result = await this.yxrOpenPlatformService.location(musicCategoryData)
      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList
        }
      } else {
        await this.loggerService.error(this.request, '获取地理位置失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicCategoryData)
        })
        throw new ForbiddenException('地理位置获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取地理位置失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('地理位置获取失败')
    }
  }

  /**
   * 检测账号有效性
   * @param platformAccountId 平台账号ID
   * @param teamId 团队ID
   * @returns 账号有效性检测结果
   */
  async validateAccount(
    platformAccountId: string,
    teamId: string
  ): Promise<AccountValidationResponse> {
    // 查找账号
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(teamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    try {
      let cookie = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        cookie = platformAccount.token
      } else {
        const { cookie: accountCookie, localStorage } =
          await this.yxrOpenPlatformService.getAccountCookie(platformAccount.id.toString())
        cookie = accountCookie
      }

      const platformData: CloudBaseDTO = {
        callBackData: {
          teamId: platformAccount.teamId.toString(),
          userId: platformAccount.userId.toString(),
          platformAccountId: platformAccount.id.toString()
        },
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        cookie: cookie
      }
      const result = await this.yxrOpenPlatformService.checkStatus(platformData)
      if (result.data.statusCode !== 0) {
        await this.loggerService.error(this.request, '账号有效性检测结果失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(platformData)
        })
      }
      return {
        message: result?.data?.loginStatus === 1 ? '账号有效' : '账号无效',
        loginStatus: result?.data?.loginStatus === 1 ? 1 : 0
      }
    } catch (error) {
      await this.loggerService.error(this.request, '账号有效性检测结果失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('账号有效性检测结果失败')
    }
  }
}
