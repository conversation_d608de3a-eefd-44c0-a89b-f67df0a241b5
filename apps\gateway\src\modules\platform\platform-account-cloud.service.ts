import { ForbiddenException, Inject, Injectable, NotFoundException } from '@nestjs/common'
import {
  AccountCloudBaseRequst,
  AccountLocationDTO,
  AccountLocationResponse,
  AccountMusicBodyRequest,
  AccountMusicCategoryResponse,
  AccountMusicDTO,
  AccountMusicResponse,
  AccountValidationResponse,
  CloudBaseDTO
} from './platform-account-cloud.dto'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { PlatformAccountEntity } from '@yxr/mongo'
import { REQUEST } from '@nestjs/core'
import { FastifyRequest } from 'fastify'
import crypto from 'crypto'
import axios from 'axios'
import { CacheKeyService, PlatformNameEnum } from '@yxr/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Cache } from 'cache-manager'
import { RedisStore } from 'cache-manager-ioredis-yet'
import { TlsService } from '@yxr/huoshan'

@Injectable()
export class PlatformAccountCloudService {
  constructor(
    @InjectModel(PlatformAccountEntity.name)
    private platformAccountModel: Model<PlatformAccountEntity>,
    @Inject(REQUEST) private request: FastifyRequest,
    private readonly loggerService: TlsService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache<RedisStore>
  ) {}

  /**
   * 获取音乐
   * @param platformAccountId
   * @param query
   * @returns
   */
  async getPlatformAccountMusic(
    platformAccountId: string,
    query: AccountMusicBodyRequest
  ): Promise<AccountMusicResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let wxkey = null
      let token = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        const shipinhao = await this.getShipinhaoInfo(
          platformAccount.teamId.toString(),
          platformAccount?.parentId.toString()
        )
        wxkey = shipinhao.wxkey
        token = shipinhao.token
      }

      const musicData: AccountMusicDTO = {
        authToken: `${platformAccount.teamId.toString()}:${platformAccount.userId.toString()}`,
        platform: platformAccount.platformName,
        keyWord: query.keyWord,
        nextPage: query.nextPage,
        platformAccountId: platformAccount.id.toString(),
        platformAccountSpaceId: platformAccount?.spaceId?.toString(),
        parentId: platformAccount?.parentId ? platformAccount.parentId.toString() : null,
        wxKey: wxkey,
        token: token
      }
      let cloudPushUrl = `${process.env.YIXIAOER_CLOUD_PUSH_URL}/configData/searchMusic`
      if (query.categoryId && query.categoryName) {
        cloudPushUrl = `${process.env.YIXIAOER_CLOUD_PUSH_URL}/configData/getMusicByCategory`
        musicData.categoryId = query.categoryId
        musicData.categoryName = query.categoryName
      }

      const result = await this.cloudPostRequest(cloudPushUrl, musicData)
      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList,
          nextPage: result.data.nextPage
        }
      } else {
        await this.loggerService.error(this.request, '获取音乐失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicData)
        })
        throw new ForbiddenException('音乐获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取音乐失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('音乐获取失败')
    }
  }

  /**
   * 获取音乐分类
   * @param platformAccountId
   * @returns
   */
  async getPlatformAccountMusicCategory(
    platformAccountId: string
  ): Promise<AccountMusicCategoryResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let wxkey = null
      let token = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        const shipinhao = await this.getShipinhaoInfo(
          platformAccount.teamId.toString(),
          platformAccount?.parentId.toString()
        )
        wxkey = shipinhao.wxkey
        token = shipinhao.token
      }

      const musicCategoryData: CloudBaseDTO = {
        authToken: `${platformAccount.teamId.toString()}:${platformAccount.userId.toString()}`,
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        platformAccountSpaceId: platformAccount?.spaceId?.toString(),
        parentId: platformAccount?.parentId ? platformAccount.parentId.toString() : null,
        wxKey: wxkey,
        token: token
      }
      const cloudPushUrl = `${process.env.YIXIAOER_CLOUD_PUSH_URL}/configData/getMusicCategory`
      const result = await this.cloudPostRequest(cloudPushUrl, musicCategoryData)
      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList
        }
      } else {
        await this.loggerService.error(this.request, '获取音乐分类失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicCategoryData)
        })
        throw new ForbiddenException('音乐分类获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取音乐分类失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('音乐分类获取失败')
    }
  }

  /**
   * 获取账号地理位置
   * @param platformAccountId
   * @returns
   */
  async getPlatformAccountLocation(
    platformAccountId: string,
    query: AccountCloudBaseRequst
  ): Promise<AccountLocationResponse> {
    const { userId: currentUserId, teamId: currentTeamId } = this.request.session

    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(currentTeamId)
    })
    if (!platformAccount) {
      throw new NotFoundException('媒体账号不存在')
    }

    try {
      let wxkey = null
      let token = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        const shipinhao = await this.getShipinhaoInfo(
          platformAccount.teamId.toString(),
          platformAccount?.parentId.toString()
        )
        wxkey = shipinhao.wxkey
        token = shipinhao.token
      }

      const musicCategoryData: AccountLocationDTO = {
        authToken: `${platformAccount.teamId.toString()}:${platformAccount.userId.toString()}`,
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        platformAccountSpaceId: platformAccount?.spaceId?.toString(),
        parentId: platformAccount?.parentId ? platformAccount.parentId.toString() : null,
        keyWord: query.keyWord,
        nextPage: query.nextPage,
        wxKey: wxkey,
        token: token
      }
      const cloudPushUrl = `${process.env.YIXIAOER_CLOUD_PUSH_URL}/configData/searchLocation`
      const result = await this.cloudPostRequest(cloudPushUrl, musicCategoryData)
      if (result.data.statusCode === 0) {
        return {
          dataList: result.data.dataList
        }
      } else {
        await this.loggerService.error(this.request, '获取地理位置失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(musicCategoryData)
        })
        throw new ForbiddenException('地理位置获取失败')
      }
    } catch (error) {
      await this.loggerService.error(this.request, '获取地理位置失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('地理位置获取失败')
    }
  }

  /**
   * 检测账号有效性
   * @param platformAccountId 平台账号ID
   * @param teamId 团队ID
   * @returns 账号有效性检测结果
   */
  async validateAccount(
    platformAccountId: string,
    teamId: string
  ): Promise<AccountValidationResponse> {
    // 查找账号
    const platformAccount = await this.platformAccountModel.findOne({
      _id: new Types.ObjectId(platformAccountId),
      teamId: new Types.ObjectId(teamId)
    })

    if (!platformAccount) {
      throw new NotFoundException('账号不存在')
    }

    try {
      let wxkey = null
      let token = null
      if (platformAccount.platformName === PlatformNameEnum.视频号 && platformAccount.parentId) {
        const shipinhao = await this.getShipinhaoInfo(
          platformAccount.teamId.toString(),
          platformAccount?.parentId.toString()
        )
        wxkey = shipinhao.wxkey
        token = shipinhao.token
      }

      const platformData: CloudBaseDTO = {
        authToken: `${platformAccount.teamId.toString()}:${platformAccount.userId.toString()}`,
        platform: platformAccount.platformName,
        platformAccountId: platformAccount.id.toString(),
        platformAccountSpaceId: platformAccount?.spaceId?.toString(),
        parentId: platformAccount?.parentId ? platformAccount.parentId.toString() : null,
        wxKey: wxkey,
        token: token
      }
      const cloudPushUrl = `${process.env.YIXIAOER_CLOUD_PUSH_URL}/platformAccount/check-status`
      const result = await this.cloudPostRequest(cloudPushUrl, platformData)
      if (result.data.statusCode !== 0) {
        await this.loggerService.error(this.request, '账号有效性检测结果失败', {
          platformAccountId: platformAccount.id,
          error: JSON.stringify(result.data),
          requestData: JSON.stringify(platformData)
        })
      }
      return {
        message: result?.data?.loginStatus === 1 ? '账号有效' : '账号无效',
        loginStatus: result?.data?.loginStatus === 1 ? 1 : 0
      }
    } catch (error) {
      await this.loggerService.error(this.request, '账号有效性检测结果失败', {
        platformAccountId: platformAccount.id,
        error: error.message
      })
      throw new ForbiddenException('账号有效性检测结果失败')
    }
  }

  /**
   * 云爬虫请求
   * @param cloudPushUrl
   * @param requestData
   * @returns
   */
  private async cloudPostRequest(cloudPushUrl: string, requestData: any) {
    const timestamp = Date.now()
    const tokenString = `${timestamp}yixiaoer_cloud_publish`
    const md5Hash = crypto.createHash('md5').update(tokenString).digest('hex')
    const result = await axios.post(cloudPushUrl, requestData, {
      headers: {
        token: md5Hash,
        timestamp: timestamp
      }
    })

    return result
  }

  private async getShipinhaoInfo(teamId: string, parentId: string) {
    const wxkey = await this.cacheManager.get<string>(
      CacheKeyService.getWeiXinAccountLockKey(teamId.toString(), parentId.toString())
    )
    const shipinhao = await this.platformAccountModel
      .findOne({
        teamId: new Types.ObjectId(teamId),
        parentId: new Types.ObjectId(parentId)
      })
      .sort({ updatedAt: -1 })
      .select('token')
      .lean()

    const token = shipinhao.token

    return { wxkey: wxkey, token: token }
  }
}
