import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query
} from '@nestjs/common'
import {
  ApiTags,
  ApiOperation,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import { AuthorizationService } from '../services/authorization.service'
import {
  CreateAuthorizationRequestDto,
  UpdateAuthorizationRequestDto,
  AuthorizationListRequestDto,
  CreateAuthorizationResponseDto,
  GetAuthorizationResponseDto,
  GetAuthorizationListResponseDto,
  GetChannelUserListResponseDto
} from '../dto/authorization.dto'
import {
  BaseBadRequestDTO,
  BaseNotFoundResponseDTO,
  BaseForbiddenResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../../common/dto/BaseResponseDTO'
import { OpenPlatformAccess } from '../../../common/decorators/access-control.decorator'

@Controller('open-platform/authorizations')
@OpenPlatformAccess()
@ApiTags('开放平台授权管理')
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class AuthorizationController {
  constructor(private readonly authorizationService: AuthorizationService) {}

  @Post()
  @ApiOperation({ summary: '创建授权' })
  @ApiOkResponse({
    description: '授权创建成功',
    type: CreateAuthorizationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误或授权已存在',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '应用或用户不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async createAuthorization(@Body() createDto: CreateAuthorizationRequestDto) {
    const result = await this.authorizationService.createAuthorization(createDto)
    return result
  }

  @Get()
  @ApiOperation({ summary: '获取授权列表' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetAuthorizationListResponseDto
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getAuthorizationList(@Query() queryDto: AuthorizationListRequestDto) {
    const result = await this.authorizationService.getAuthorizationList(queryDto)
    return result
  }

  @Get(':id')
  @ApiOperation({ summary: '获取授权详情' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetAuthorizationResponseDto
  })
  @ApiNotFoundResponse({
    description: '授权不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getAuthorizationById(@Param('id') id: string) {
    const result = await this.authorizationService.getAuthorizationById(id)
    return result
  }

  @Put(':id')
  @ApiOperation({ summary: '更新授权' })
  @ApiOkResponse({
    description: '更新成功',
    type: GetAuthorizationResponseDto
  })
  @ApiBadRequestResponse({
    description: '参数错误',
    type: BaseBadRequestDTO
  })
  @ApiNotFoundResponse({
    description: '授权不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async updateAuthorization(
    @Param('id') id: string,
    @Body() updateDto: UpdateAuthorizationRequestDto
  ) {
    const result = await this.authorizationService.updateAuthorization(id, updateDto)
    return result
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除授权' })
  @ApiOkResponse({
    description: '删除成功'
  })
  @ApiNotFoundResponse({
    description: '授权不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async deleteAuthorization(@Param('id') id: string) {
    return this.authorizationService.deleteAuthorization(id)
  }

  @Get('applications/:applicationId/channel-users')
  @ApiOperation({ summary: '获取可授权的渠道商用户列表' })
  @ApiOkResponse({
    description: '获取成功',
    type: GetChannelUserListResponseDto
  })
  @ApiNotFoundResponse({
    description: '应用不存在',
    type: BaseNotFoundResponseDTO
  })
  @ApiForbiddenResponse({
    description: '权限不足',
    type: BaseForbiddenResponseDTO
  })
  async getChannelUserList(
    @Param('applicationId') applicationId: string,
    @Query() queryDto: any
  ) {
    const result = await this.authorizationService.getChannelUserList(applicationId, queryDto)
    return result
  }
}
