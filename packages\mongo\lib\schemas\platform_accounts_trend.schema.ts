import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'
import { Types } from 'mongoose'

@Schema({
  timestamps: true,
  versionKey: false,
  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})

/**
 * 账号趋势表 每天归档一次
 */
export class PlatformAccountTrendEntity {
  @Prop({
    type: Types.ObjectId,
    index: true,
    required: true
  })
  teamId: Types.ObjectId

  @Prop({
    type: String,
    index: true,
    required: true
  })
  platformName: string

  // 日期
  @Prop({
    type: String,
    index: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  //总粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansTotal: number

  //总播放
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  playTotal: number

  //总点赞
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  likesTotal: number

  //总评论
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  commentsTotal: number

  //总分享
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  sharesTotal: number

  //总收藏
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  favoritesTotal: number

  //净增粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  netFansGrowth: number

  //取关粉丝
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansLost: number

  //涨粉
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fansGrowth: number

  //推荐数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  recommendationsTotal: number

  //作品数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  worksTotal: number

  //主页访问数
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  profileVisits: number

  //完播率
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  completionRate: number

  //曝光量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  impressionsTotal: number

  //粉丝曝光量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  fanImpressions: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  zhuanZanPingTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  readTotal: number

  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  blogTotal: number

  @Prop({
    type: Date
  })
  createdAt?: Date

  @Prop({
    type: Date
  })
  updatedAt?: Date
}

export const PlatformAccountTrendSchema: ModelDefinition = {
  name: PlatformAccountTrendEntity.name,
  schema: SchemaFactory.createForClass(PlatformAccountTrendEntity)
}

export const PlatformAccountTrendMongoose = MongooseModule.forFeature([PlatformAccountTrendSchema])
