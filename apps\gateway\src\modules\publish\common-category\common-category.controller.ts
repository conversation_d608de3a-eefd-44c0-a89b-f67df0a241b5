import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'

import {
  CategorysDetailResponseDTO,
  CategorysGroupResponseDTO,
  CategorysListResponseDTO,
  PostCategoryGroupsRequest,
  PostCategorysRequest
} from './common-category.dto'
import {
  BaseBadRequestResponseDTO,
  BaseForbiddenResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from 'apps/gateway/src/common/dto/BaseResponseDTO'
import { CommonCategorysService } from './common-category.service'
import { CommonCategorysGroupService } from './common-category-group.service'

@Controller('common-categorys')
@ApiTags('常用分类管理')
@ApiUnauthorizedResponse({ type: BaseUnauthorizedResponseDTO, description: '未登录' })
@ApiForbiddenResponse({ type: BaseForbiddenResponseDTO, description: '未授权' })
@ApiBadRequestResponse({ description: '参数错误', type: BaseBadRequestResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
export class CommonCategorysController {
  constructor(
    private readonly categorysGroupService: CommonCategorysGroupService,
    private readonly categorysService: CommonCategorysService
  ) {}

  @Get()
  @ApiOperation({ summary: '获取常用分类列表' })
  @ApiParam({ name: 'groupId', required: false, description: '分组ID 全部分组则不传' })
  @ApiOkResponse({ type: CategorysListResponseDTO, description: '操作成功' })
  async getCategorys(@Query('groupId') groupId: string) {
    return await this.categorysService.getCategorys(groupId)
  }

  @Post()
  @ApiOperation({ summary: '新增常用分类' })
  @ApiOkResponse({ type: CategorysDetailResponseDTO, description: '操作成功' })
  async postCategorys(@Body() body: PostCategorysRequest) {
    return await this.categorysService.postCategorys(body)
  }

  @Delete(':categoryId')
  @ApiOperation({ summary: '删除常用分类' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async deleteCategory(@Param('categoryId') categoryId: string) {
    return await this.categorysService.deleteCategory(categoryId)
  }

  @Get('groups')
  @ApiOperation({ summary: '获取常用分类分组列表' })
  @ApiOkResponse({ type: CategorysListResponseDTO, description: '操作成功' })
  async getCategorysGroups() {
    return await this.categorysGroupService.getCategoryGroupsAsync()
  }

  @Post('groups')
  @ApiOperation({ summary: '创建常用分类分组' })
  @ApiOkResponse({ type: CategorysGroupResponseDTO, description: '操作成功' })
  async postGroups(@Body() body: PostCategoryGroupsRequest) {
    return await this.categorysGroupService.postCategoryGroups(body)
  }

  @Put('groups/:groupId')
  @ApiOperation({ summary: '修改常用分类分组' })
  @ApiOkResponse({ type: CategorysGroupResponseDTO, description: '操作成功' })
  async putGroups(@Param('groupId') groupId: string, @Body() body: PostCategoryGroupsRequest) {
    return await this.categorysGroupService.putCategoryGroupsAsync(groupId, body)
  }

  @Delete('groups/:groupId')
  @ApiOperation({ summary: '删除常用分类分组' })
  @ApiOkResponse()
  async deleteGroups(@Param('groupId') groupId: string) {
    return await this.categorysGroupService.deleteCategoryGroupsAsync(groupId)
  }
}
