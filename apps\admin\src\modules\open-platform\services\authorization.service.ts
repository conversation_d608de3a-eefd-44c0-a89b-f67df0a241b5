import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { REQUEST } from '@nestjs/core'
import type { FastifyRequest } from 'fastify'
import {
  OpenPlatformApplicationEntity,
  OpenPlatformUserEntity,
  OpenPlatformUserRoleEntity,
  OpenPlatformAppAuthorizationEntity
} from '@yxr/mongo'
import {
  OpenPlatformRoleNames,
  OpenPlatformStatus,
  UserType
} from '@yxr/common'
import {
  CreateAuthorizationRequestDto,
  UpdateAuthorizationRequestDto,
  AuthorizationDto,
  AuthorizationListRequestDto,
  AuthorizationListResponseDto,
  ChannelUserDto,
  ChannelUserListResponseDto
} from '../dto/authorization.dto'

@Injectable()
export class AuthorizationService {
  constructor(
    @InjectModel(OpenPlatformApplicationEntity.name)
    private applicationModel: Model<OpenPlatformApplicationEntity>,
    @InjectModel(OpenPlatformUserEntity.name)
    private userModel: Model<OpenPlatformUserEntity>,
    @InjectModel(OpenPlatformUserRoleEntity.name)
    private userRoleModel: Model<OpenPlatformUserRoleEntity>,
    @InjectModel(OpenPlatformAppAuthorizationEntity.name)
    private authorizationModel: Model<OpenPlatformAppAuthorizationEntity>,
    @Inject(REQUEST) private request: FastifyRequest
  ) {}

  /**
   * 创建授权
   */
  async createAuthorization(createDto: CreateAuthorizationRequestDto): Promise<AuthorizationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以创建授权')
    }

    // 检查应用是否存在且用户是否为管理员
    const application = await this.applicationModel.findById(createDto.applicationId)
    if (!application) {
      throw new NotFoundException('应用不存在')
    }

    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: new Types.ObjectId(createDto.applicationId),
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以创建授权')
    }

    // 检查渠道商用户是否存在
    const channelUser = await this.userModel.findById(createDto.channelUserId)
    if (!channelUser) {
      throw new NotFoundException('渠道商用户不存在')
    }

    // 检查是否已存在授权
    const existingAuth = await this.authorizationModel.findOne({
      applicationId: new Types.ObjectId(createDto.applicationId),
      channelUserId: new Types.ObjectId(createDto.channelUserId)
    })

    if (existingAuth) {
      throw new BadRequestException('该渠道商已被授权，请更新现有授权')
    }

    // 创建授权
    const authorization = await this.authorizationModel.create({
      applicationId: new Types.ObjectId(createDto.applicationId),
      channelUserId: new Types.ObjectId(createDto.channelUserId),
      authorizedBy: new Types.ObjectId(session.userId),
      permissions: createDto.permissions,
      status: OpenPlatformStatus.ACTIVE
    })

    return this.formatAuthorizationDto(authorization)
  }

  /**
   * 获取授权列表
   */
  async getAuthorizationList(queryDto: AuthorizationListRequestDto): Promise<AuthorizationListResponseDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看授权列表')
    }

    const { page = 1, size = 10, applicationId, status, keyword } = queryDto
    const skip = (page - 1) * size

    // 获取用户管理的应用ID列表
    const userRoles = await this.userRoleModel.find({
      userId: new Types.ObjectId(session.userId),
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    const managedApplicationIds = userRoles.map(role => role.applicationId)

    if (managedApplicationIds.length === 0) {
      return {
        data: [],
        totalSize: 0,
        totalPage: 0,
        page,
        size
      }
    }

    // 构建查询条件
    const query: any = {
      applicationId: { $in: managedApplicationIds }
    }

    if (applicationId) {
      query.applicationId = new Types.ObjectId(applicationId)
    }

    if (status !== undefined) {
      query.status = status
    }

    // 如果有关键词搜索，需要先查找匹配的用户
    if (keyword) {
      const matchedUsers = await this.userModel.find({
        $or: [
          { phone: { $regex: keyword, $options: 'i' } },
          { nickname: { $regex: keyword, $options: 'i' } }
        ]
      })
      
      if (matchedUsers.length > 0) {
        query.channelUserId = { $in: matchedUsers.map(user => user._id) }
      } else {
        // 没有匹配的用户，返回空结果
        return {
          data: [],
          totalSize: 0,
          totalPage: 0,
          page,
          size
        }
      }
    }

    const [authorizations, total] = await Promise.all([
      this.authorizationModel
        .find(query)
        .skip(skip)
        .limit(size)
        .sort({ createdAt: -1 })
        .exec(),
      this.authorizationModel.countDocuments(query)
    ])

    const authorizationDtos = await Promise.all(
      authorizations.map(auth => this.formatAuthorizationDto(auth))
    )

    return {
      data: authorizationDtos,
      totalSize: total,
      totalPage: Math.ceil(total / size),
      page,
      size
    }
  }

  /**
   * 获取授权详情
   */
  async getAuthorizationById(authorizationId: string): Promise<AuthorizationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看授权详情')
    }

    const authorization = await this.authorizationModel.findById(authorizationId)
    if (!authorization) {
      throw new NotFoundException('授权不存在')
    }

    // 检查权限（只有应用管理员可以查看）
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: authorization.applicationId,
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以查看授权详情')
    }

    return this.formatAuthorizationDto(authorization)
  }

  /**
   * 更新授权
   */
  async updateAuthorization(
    authorizationId: string,
    updateDto: UpdateAuthorizationRequestDto
  ): Promise<AuthorizationDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以更新授权')
    }

    const authorization = await this.authorizationModel.findById(authorizationId)
    if (!authorization) {
      throw new NotFoundException('授权不存在')
    }

    // 检查权限（只有应用管理员可以更新）
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: authorization.applicationId,
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以更新授权')
    }

    const updatedAuthorization = await this.authorizationModel.findByIdAndUpdate(
      authorizationId,
      {
        permissions: updateDto.permissions,
        status: updateDto.status
      },
      { new: true }
    )

    return this.formatAuthorizationDto(updatedAuthorization)
  }

  /**
   * 删除授权
   */
  async deleteAuthorization(authorizationId: string): Promise<void> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以删除授权')
    }

    const authorization = await this.authorizationModel.findById(authorizationId)
    if (!authorization) {
      throw new NotFoundException('授权不存在')
    }

    // 检查权限（只有应用管理员可以删除）
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: authorization.applicationId,
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以删除授权')
    }

    await this.authorizationModel.findByIdAndDelete(authorizationId)
  }

  /**
   * 获取可授权的渠道商用户列表
   */
  async getChannelUserList(applicationId: string, queryDto: any): Promise<ChannelUserListResponseDto> {
    const { session } = this.request
    
    if (session?.userType !== UserType.OPEN_PLATFORM) {
      throw new ForbiddenException('只有开放平台用户可以查看渠道商列表')
    }

    // 检查权限（只有应用管理员可以查看）
    const userRole = await this.userRoleModel.findOne({
      userId: new Types.ObjectId(session.userId),
      applicationId: new Types.ObjectId(applicationId),
      role: OpenPlatformRoleNames.ADMIN,
      status: OpenPlatformStatus.ACTIVE
    })

    if (!userRole) {
      throw new ForbiddenException('只有应用管理员可以查看渠道商列表')
    }

    const { page = 1, limit = 10, keyword } = queryDto
    const skip = (page - 1) * limit

    // 构建查询条件
    const query: any = {}
    if (keyword) {
      query.$or = [
        { phone: { $regex: keyword, $options: 'i' } },
        { nickname: { $regex: keyword, $options: 'i' } }
      ]
    }

    const [users, total] = await Promise.all([
      this.userModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.userModel.countDocuments(query)
    ])

    // 获取已授权的用户ID列表
    const authorizations = await this.authorizationModel.find({
      applicationId: new Types.ObjectId(applicationId),
      status: OpenPlatformStatus.ACTIVE
    })
    const authorizedUserIds = authorizations.map(auth => auth.channelUserId.toString())

    const channelUserDtos = users.map(user => ({
      id: (user as any)._id.toString(),
      phone: user.phone,
      nickname: user.nickname || '',
      isAuthorized: authorizedUserIds.includes((user as any)._id.toString())
    }))

    return {
      list: channelUserDtos,
      total,
      page,
      limit
    }
  }

  /**
   * 格式化授权DTO
   */
  private async formatAuthorizationDto(authorization: OpenPlatformAppAuthorizationEntity): Promise<AuthorizationDto> {
    // 获取应用信息
    const application = await this.applicationModel.findById(authorization.applicationId)
    
    // 获取渠道商用户信息
    const channelUser = await this.userModel.findById(authorization.channelUserId)
    
    // 获取授权人信息
    const authorizedByUser = await this.userModel.findById(authorization.authorizedBy)

    return {
      id: (authorization as any)._id.toString(),
      applicationId: authorization.applicationId.toString(),
      applicationName: application?.name || '',
      applicationAppId: application?.appId || '',
      channelUserId: authorization.channelUserId.toString(),
      channelUserPhone: channelUser?.phone || '',
      channelUserNickname: channelUser?.nickname || '',
      authorizedBy: authorization.authorizedBy.toString(),
      authorizedByPhone: authorizedByUser?.phone || '',
      authorizedByNickname: authorizedByUser?.nickname || '',
      permissions: authorization.permissions,
      status: authorization.status,
      createdAt: authorization.createdAt?.getTime() || 0,
      updatedAt: authorization.updatedAt?.getTime() || 0
    }
  }
}
