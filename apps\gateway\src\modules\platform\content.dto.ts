import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { Type } from 'class-transformer'
import { ContentType } from '@yxr/common'
import { LoginStatus } from '@yxr/mongo'

export class ContentStatisticDataDTO {
  id?: string
  title?: string
  content?: string
  desc?: string
  pageUrl?: string
  cover?: string
  type?: string
  date?: Date
  reCommand?: string
  play?: string
  read?: string
  great?: string
  comment?: string
  share?: string
  collect?: string
  addFans?: string
  finishPlay?: string
  love?: string
  danmu?: string
  coin?: string
  like?: string
  vote?: string
  playTime?: string
}

export class ExportContentStatisticDataDTO {
  pageUrl: string
  reCommand: string
  play: string
  read: string
  great: string
  comment: string
  share: string
  collect: string
  addFans: string
  finishPlay: string
  love: string
  danmu: string
  coin: string
  like: string
  vote: string
  playTime: string
}

export class ContentOverviewDTO {
  @ApiProperty({
    type: String,
    description: '团队ID'
  })
  teamId: string

  @ApiProperty({
    type: String,
    description: '账号ID'
  })
  platformAccountId: string

  @ApiProperty({
    type: String,
    description: '账号头像'
  })
  accountAvatar: string

  @ApiProperty({
    type: String,
    description: '账号昵称'
  })
  accountName: string

  @ApiProperty({
    type: String,
    enum: LoginStatus,
    description: '账号状态'
  })
  accountStatus: LoginStatus

  @ApiProperty({
    type: String,
    description: '发布人名称'
  })
  publishUserName: string

  // 废弃已合并到publishUserName
  @ApiProperty({
    deprecated: true,
    type: String,
    description: '发布人团队昵称'
  })
  remark: string

  @ApiProperty({
    type: Number,
    description: '更新时间'
  })
  updatedAt: number

  @ApiProperty({
    description: '数据原始类型'
  })
  contentData: unknown
}

export class ReportContentRequest {
  @ApiProperty({
    description: '媒体账号ID',
    required: true,
    type: String
  })
  @IsString({ message: '账号格式不匹配' })
  @IsNotEmpty({ message: '账号ID不能为空' })
  platformAccountId: string

  @ApiProperty({
    description: '作品统计(动态结构)',
    example: [
      {
        id: '123333',
        title: '测试标题',
        content: '测试内容',
        desc: '作品描述',
        pageUrl: '作品url地址',
        cover: 'https://xxxxx.png',
        type: 'video|miniVideo|dynamic|article',
        date: '发布时间,格式为2024-12-20 12:23:01',
        reCommand: '推荐、展现',
        play: '播放量',
        read: '阅读量',
        great: '点赞',
        comment: '评论',
        share: '分享',
        collect: '收藏',
        addFans: '涨粉数',
        finishPlay: '完播率',
        love: '爱心',
        danmu: '弹幕数',
        coin: '硬币数'
      }
    ],
    required: true
  })
  @IsArray()
  contentStatisticsData: unknown
}

export class ContentOverviewSummaryRequest {
  @ApiProperty({
    type: String,
    description: '作品类型',
    enum: ContentType,
    example: ContentType.article,
    required: false
  })
  @IsOptional()
  @IsEnum(ContentType, {
    message: `type 必须是以下值之一: ${Object.values(ContentType)}`
  })
  type: ContentType

  @ApiProperty({
    description: '媒体账号ID',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '账号格式不匹配' })
  platformAccountId: string

  @ApiProperty({
    description: '发布人ID',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '账号格式不匹配' })
  publishUserId: string

  @ApiProperty({
    type: String,
    description: '平台查询',
    example: '抖音',
    required: false
  })
  @IsString()
  @IsNotEmpty({ message: '平台名称不能为空' })
  platform: string

  @ApiProperty({
    description: '作品标题',
    required: false,
    type: String
  })
  @IsString({ message: '账号格式不匹配' })
  @IsOptional()
  title: string

  @ApiProperty({
    type: Number,
    description: '发布开始时间',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishStartTime: number

  @ApiProperty({
    type: Number,
    description: '发布结束时间',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishEndTime: number
}

export class ContentOverviewListRequest {
  @ApiProperty({
    description: '媒体账号ID',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '账号格式不匹配' })
  platformAccountId: string

  @ApiProperty({
    description: '发布人ID',
    required: false,
    type: String
  })
  @IsOptional()
  @IsString({ message: '账号格式不匹配' })
  publishUserId: string

  @ApiProperty({
    type: String,
    description: '平台查询',
    example: '抖音',
    required: false
  })
  @IsOptional()
  @IsString()
  platform: string

  @ApiProperty({
    type: String,
    description: '作品类型',
    enum: ContentType,
    example: ContentType.article,
    required: false
  })
  @IsOptional()
  @IsEnum(ContentType, {
    message: `type 必须是以下值之一: ${Object.values(ContentType)}`
  })
  type: ContentType

  @ApiProperty({
    description: '作品标题',
    required: false,
    type: String
  })
  @IsString({ message: '账号格式不匹配' })
  @IsOptional()
  title: string

  @ApiProperty({
    type: Number,
    description: '发布开始时间',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishStartTime: number

  @ApiProperty({
    type: Number,
    description: '发布结束时间',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  publishEndTime: number

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 1,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  page: number = 1

  @ApiProperty({
    type: Number,
    description: '每页数量 <默认 10>',
    example: 10,
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  size: number = 10
}

export class ContentOverviewListResponse {
  @ApiResponseProperty({
    type: [ContentOverviewDTO]
  })
  data: ContentOverviewDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}
export class ContentOverviewListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: ContentOverviewListResponse
  })
  data: ContentOverviewListResponse
}
