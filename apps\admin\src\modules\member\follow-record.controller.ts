import { Body, Controller, Get, Param, Post } from '@nestjs/common'
import {
  ApiBadRequestResponse,
  ApiHeader,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from '@nestjs/swagger'
import {
  BaseBadRequestResponseDTO,
  BaseResponseDTO,
  BaseUnauthorizedResponseDTO
} from '../../common/dto/BaseResponseDTO'
import { CreateFollowRecordDTO, FollowRecordListResponseDTO } from './follow-record.dto'
import { FollowRecordService } from './follow-record.service'
import { AdminOnly } from '../../common/decorators/access-control.decorator'

@Controller('follow-records')
@ApiTags('跟进记录')
@ApiBadRequestResponse({ description: '参数无效', type: BaseBadRequestResponseDTO })
@ApiUnauthorizedResponse({ description: '用户未登录', type: BaseUnauthorizedResponseDTO })
@ApiHeader({ name: 'authorization', required: true })
@AdminOnly()
export class FollowRecordController {
  constructor(private readonly followRecordService: FollowRecordService) {}

  @Get(':followUserId')
  @ApiOperation({ summary: '获取跟进列表' })
  @ApiOkResponse({ description: '操作成功', type: FollowRecordListResponseDTO })
  async getFollowRecords(@Param('followUserId') followUserId: string) {
    return this.followRecordService.getFollowRecords(followUserId)
  }

  @Post(':followUserId')
  @ApiOperation({ summary: '添加跟进内容' })
  @ApiOkResponse({ type: BaseResponseDTO })
  async postFollowRecord(
    @Param('followUserId') followUserId: string,
    @Body() body: CreateFollowRecordDTO
  ) {
    return await this.followRecordService.postFollowRecord(followUserId, body)
  }
}
