import { Modu<PERSON> } from '@nestjs/common'
import { UserController } from './user.controller'
import { UserService } from './user.service'
import { UserInitService } from './user.init'
import { HuoshanModule } from '@yxr/huoshan'
import { AdminMongoose, InterestMongoose } from '@yxr/mongo'
import { AdminOssModule } from '../ali-oss/admin-oss.module'
import { UnifiedAuthService } from '../../common/services/unified-auth.service'

@Module({
  imports: [AdminMongoose, InterestMongoose, HuoshanModule, AdminOssModule],
  controllers: [UserController],
  providers: [UserService, UserInitService, UnifiedAuthService]
})
export class UserModule {}
