import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator'
import { Type } from 'class-transformer'
import { SalesType } from '@yxr/common'

export class TeamDTO {
  @ApiResponseProperty({
    type: String,
    example: '团队ID'
  })
  id: string

  @ApiResponseProperty({
    type: String,
    example: '团队编号'
  })
  code: string

  @ApiResponseProperty({
    type: String,
    example: '团队名称'
  })
  name: string

  @ApiResponseProperty({
    type: String,
    example: '手机号'
  })
  phone: string

  /** @deprecated 请使用 accountCapacityLimit */
  @ApiResponseProperty({
    type: Number,
    example: '10 账号数量上限'
  })
  accountCountLimit: number  // 变更为账号点数

  @ApiProperty({
    description: '10 账号点数上限',
    required: true
  })
  accountCapacityLimit: number

  @ApiResponseProperty({
    type: Number,
    example: '10 账号数量'
  })
  accountCount: number  // 变更为账号点数(此字段需保留)

  @ApiResponseProperty({
    type: Number,
    example: '10 账号点数'
  })
  accountCapacity: number

  @ApiResponseProperty({
    type: Number,
    example: '10 成员数量上限'
  })
  memberCountLimit: number

  @ApiResponseProperty({
    type: Number,
    example: '10 成员数量'
  })
  memberCount: number

  //素材库容量 0时使用默认容量
  @ApiProperty({
    type: Number,
    default: 0
  })
  capacity?: number

  //已使用容量
  @ApiProperty({
    type: Number,
    default: 0
  })
  usedCapacity?: number

  //团队流量 0时使用默认流量
  @ApiProperty({
    type: Number,
    default: 0
  })
  networkTraffic?: number

  //已使用流量
  @ApiProperty({
    type: Number,
    default: 0
  })
  useNetworkTraffic?: number

  @ApiProperty({
    type: Number,
    default: 0
  })
  interestCount?: number

  @ApiProperty({
    type: Boolean,
    default: false
  })
  appPublish?: boolean

  @ApiProperty({
    type: Number,
    default: 0
  })
  isVip?: number

  @ApiProperty({
    description: '团队创建时间',
    type: Date,
    example: '2024-08-06 10:39:59.218'
  })
  createdAt: Date

  @ApiProperty({
    type: Number,
    description: '0 未购买 1新购 2复购',
    enum: SalesType
  })
  salesType: SalesType
}

export class TeamResponse {
  @ApiResponseProperty({
    type: [TeamDTO]
  })
  data: TeamDTO[]

  @ApiResponseProperty({
    type: Number,
    example: 1
  })
  page: number

  @ApiResponseProperty({
    type: Number,
    example: 10
  })
  size: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalSize: number

  @ApiResponseProperty({
    type: Number,
    example: 100
  })
  totalPage: number
}

export class TeamListResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: TeamResponse
  })
  data: TeamResponse
}

export class TeamListRequest {
  @ApiProperty({
    type: Number,
    example: 1,
    description: '页码 <默认 1>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  page: number = 1

  @ApiProperty({
    type: Number,
    example: 10,
    description: '每页数量 <默认 10>',
    required: false
  })
  @Type(() => Number)
  @IsOptional()
  size: number = 10

  @ApiProperty({
    type: String,
    example: '蚁小二team',
    description: '团队名称查询｜团队编号',
    required: false
  })
  @IsOptional()
  @IsString()
  teamName: string

  @ApiProperty({
    type: String,
    example: ***********,
    description: '手机号查询',
    required: false
  })
  @IsString()
  @IsOptional()
  phone: string

  @ApiProperty({
    type: Date,
    description: '创建时间开始时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  startTime: Date

  @ApiProperty({
    type: Date,
    description: '创建时间结束时间 <默认 0>',
    example: 0,
    required: false
  })
  @IsOptional()
  endTime: Date

  @ApiProperty({
    type: Number,
    description: '到期时间开始时间',
    example: 0,
    required: false
  })
  @IsOptional()
  expiredStartTime: number

  @ApiProperty({
    type: Number,
    description: '到期时间结束时间',
    example: 0,
    required: false
  })
  @IsOptional()
  expiredEndTime: number

  @ApiProperty({
    type: String,
    description: 'vip开通 true已开通 false未开通',
    example: true,
    required: false
  })
  @IsOptional()
  @IsString({ message: '格式不正确' })
  isVip: string

  @ApiProperty({
    type: String,
    description: '归属人Id',
    example: 0,
    required: false
  })
  @IsOptional()
  customerId: string

  @ApiProperty({
    type: String,
    description: '应用id',
    example: '67330e084e0bc74cf56035fb',
    required: false
  })
  @IsOptional()
  applicationId: string

  @ApiProperty({
    type: Number,
    description: '销售类型(0:未支付,1:新购,2:复购 4已购买（新购&复购）)',
    example: 0
  })
  @IsOptional()
  @Type(() => Number)
  salesType: number
}

export class orderInfo {
  @ApiProperty({
    type: String,
    description: '订单号',
    example: '202501021824OFJNZ1PA'
  })
  orderNo: string

  @ApiProperty({
    type: Number,
    description: '付款金额',
    example: 1
  })
  refundableAmount: number

  @ApiProperty({
    type: Number,
    description: '可退金额',
    example: 1
  })
  actualAmount: number
}

export class RefundOrderLogsResponse {
  @ApiProperty({
    type: String,
    description: '退款编号',
    example: ''
  })
  refundNo: string

  @ApiProperty({
    type: Number,
    description: '应退金额',
    example: 1
  })
  refundableAmount: number

  @ApiProperty({
    type: Number,
    description: '实退金额',
    example: 1
  })
  actualAmount: number

  @ApiProperty({
    type: String,
    description: '备注',
    example: '重复下单'
  })
  remark: string

  @ApiProperty({
    type: String,
    description: '创建人',
    example: '超级管理员'
  })
  creatorName?: string

  @ApiProperty({
    type: Number,
    description: '退费时间',
    example: '1735870904'
  })
  createdAt: number

  @ApiProperty({
    type: [orderInfo],
    description: '订单列表',
    example: []
  })
  orderInfos: unknown
}

export class RefundOrderLogsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [RefundOrderLogsResponse]
  })
  data: RefundOrderLogsResponse[]
}

export class RefundOrdersResponse {
  @ApiProperty({
    type: String,
    description: '订单号',
    example: '202501021824OFJNZ1PA'
  })
  orderNo: string

  @ApiProperty({
    type: Number,
    description: '付款金额',
    example: 1
  })
  refundAmount: number

  @ApiProperty({
    type: Number,
    description: '可退金额',
    example: 1
  })
  actualRefundAmount: number
}

export class RefundOrdersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [RefundOrdersResponse]
  })
  data: RefundOrdersResponse[]
}

export class RefundOrderRequestBodyDTO {
  @ApiProperty({
    type: Number,
    description: '实际退费金额'
  })
  @IsNumber()
  @IsNotEmpty()
  realityPrice: number

  @ApiProperty({
    type: String,
    description: '备注'
  })
  @IsString()
  @IsOptional()
  remark?: string
}

export class TeamMembersResponse {
  @ApiProperty({
    type: String,
    description: '成员ID',
    example: '202501021824OFJNZ1PA'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '手机号',
    example: '***********'
  })
  phone: string

  @ApiProperty({
    type: String,
    description: '昵称',
    example: '张三'
  })
  nickName: string

  @ApiProperty({
    type: String,
    description: '头像',
    example: 'https://xxxx.com/avatar/202501021824OFJNZ1PA'
  })
  avatar: string
}

export class TeamMembersResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamMembersResponse]
  })
  data: TeamMembersResponse[]
}

export class TeamAccountsResponse {
  @ApiProperty({
    type: String,
    description: '成员ID',
    example: '202501021824OFJNZ1PA'
  })
  id: string

  @ApiProperty({
    type: String,
    description: '平台名称',
    example: '抖音'
  })
  platformName: string

  @ApiProperty({
    type: String,
    description: '账号昵称',
    example: '张三'
  })
  platformAccountName: string

  @ApiProperty({
    type: String,
    description: '平台账号头像',
    example: 'https://xxxx.com/avatar/202501021824OFJNZ1PA'
  })
  platformAvatar: string

  @ApiProperty({
    type: String,
    description: '父级ID，如果不为空说明是微信子账号'
  })
  parentId: string
}

export class TeamAccountsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [TeamAccountsResponse]
  })
  data: TeamAccountsResponse[]
}
