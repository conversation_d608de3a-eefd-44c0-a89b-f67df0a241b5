import { ModelDefinition, MongooseModule, Prop, Schema, SchemaFactory } from '@nestjs/mongoose'
import dayjs from 'dayjs'

@Schema({
  timestamps: true,
  versionKey: false,

  toJSON: {
    transform(_, ret) {
      ret.id = ret._id
      delete ret._id
    }
  }
})
export class DataStatisticEntity {
  // 日期
  @Prop({
    type: String,
    index: true,
    unique: true,
    default: () => dayjs().format('YYYY-MM-DD'),
    transform: (v: number) => dayjs(v).format('YYYY-MM-DD')
  })
  createTime: string

  // 用户注册数量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  registerCount: number

  // 活跃团队发布数量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  teamPublishCount: number

  //app用户发布数量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  appUserPublishCount: number

  //作品发布数量
  @Prop({
    type: Number,
    required: false,
    default: 0
  })
  publishCount: number
}

export const DataStatisticsSchema: ModelDefinition = {
  name: DataStatisticEntity.name,
  schema: SchemaFactory.createForClass(DataStatisticEntity)
}

export const DataStatisticsMongoose = MongooseModule.forFeature([DataStatisticsSchema])
