import { ApiProperty, ApiResponseProperty } from '@nestjs/swagger'
import { BaseResponseDTO } from '../../common/dto/BaseResponseDTO'

export class HomeStatisticDTO {
  @ApiProperty({
    type: Number,
    description: '总粉丝数',
    example: 100
  })
  fansTotal?: number

  @ApiProperty({
    type: Number,
    description: '总播放量',
    example: 100
  })
  playTotal?: number

  @ApiProperty({
    type: Number,
    description: '阅读量',
    example: 100
  })
  readTotal?: number

  @ApiProperty({
    type: Number,
    description: '总评论量',
    example: 100
  })
  commentsTotal?: number

  @ApiProperty({
    type: Number,
    description: '总点赞量',
    example: 100
  })
  likesTotal?: number

  @ApiProperty({
    type: Number,
    description: '总收藏量',
    example: 100
  })
  favoritesTotal?: number
}

export class HomeOverviewResponse {
  @ApiProperty({
    description: '当前最新数据(根据平台来动态返回的)'
  })
  current: HomeStatisticDTO

  @ApiProperty({
    description: '上一次统计对比增量数据(根据平台来动态返回的)'
  })
  increments: HomeStatisticDTO

  @ApiProperty({
    type: Number,
    description: '数据更新时间',
    example: 1231094801
  })
  updatedAt: number
}

export class HomeOverviewResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: HomeOverviewResponse
  })
  data: HomeOverviewResponse
}

export class HomeOverviewTrendsResponse {
  @ApiProperty({
    type: Number,
    description: '粉丝总数'
  })
  fansTotal: number

  @ApiProperty({
    type: Number,
    description: '播放总数'
  })
  playTotal: number

  @ApiProperty({
    type: Number,
    description: '评论总数'
  })
  commentsTotal: number

  @ApiProperty({
    type: Number,
    description: '点赞总数'
  })
  likesTotal: number

  @ApiProperty({
    type: Number,
    description: '收藏总数'
  })
  favoritesTotal: number

  @ApiProperty({
    type: Date,
    description: '时间'
  })
  date: string
}

export class HomeOverviewTrendsResponseDTO extends BaseResponseDTO {
  @ApiResponseProperty({
    type: [HomeOverviewTrendsResponse]
  })
  data: HomeOverviewTrendsResponse[]
}
